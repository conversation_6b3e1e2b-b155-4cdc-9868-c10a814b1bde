# 🚀 快速打包指南

## 📦 **一键完整打包**

### **最简单的方式**
```bash
# 1. 进入项目目录
cd electron-python-grpc-pex-demo

# 2. 执行完整构建
./scripts/build-all.sh

# 3. 验证构建结果
./scripts/verify-build.sh
```

### **使用npm命令**
```bash
# 进入前端目录
cd electron-app

# 完整构建
npm run build:complete

# 验证构建
npm run build:verify
```

## 🌍 **跨平台打包**

### **构建所有平台版本**
```bash
# 构建Windows、macOS、Linux版本
./scripts/build-cross-platform.sh

# 或使用npm命令
cd electron-app
npm run build:cross-platform
```

## 📋 **打包前检查清单**

### **环境要求**
- [ ] Node.js 16+ 已安装
- [ ] Python 3.8+ 已安装
- [ ] Go 1.19+ 已安装
- [ ] Java 17+ 已安装
- [ ] Maven 已安装

### **依赖检查**
```bash
# 自动检查所有依赖
cd electron-app
npm run build:check
```

## 📁 **输出文件说明**

### **构建完成后的文件结构**
```
项目根目录/
├── dist/                           # 后端服务
│   ├── server.pex                  # Python后端 (自包含)
│   ├── go_grpc_server              # Go后端
│   ├── java-grpc-backend.jar       # Java后端
│   └── platforms/                  # 多平台Go后端
│       ├── go_grpc_server_windows_amd64.exe
│       ├── go_grpc_server_darwin_amd64
│       ├── go_grpc_server_darwin_arm64
│       ├── go_grpc_server_linux_amd64
│       └── go_grpc_server_linux_arm64
├── electron-app/dist-electron/     # Electron应用包
│   ├── 字幕处理工具-1.0.0.dmg      # macOS安装包
│   ├── 字幕处理工具 Setup 1.0.0.exe # Windows安装包
│   ├── 字幕处理工具-1.0.0.AppImage  # Linux应用包
│   ├── mac/                        # macOS应用目录
│   ├── win-unpacked/               # Windows应用目录
│   └── linux-unpacked/             # Linux应用目录
└── release/                        # 最终发布包
    ├── 字幕处理工具-1.0.0.dmg
    ├── 字幕处理工具 Setup 1.0.0.exe
    ├── 字幕处理工具-1.0.0.AppImage
    ├── backends/                   # 多平台后端
    └── README.md                   # 使用说明
```

## 🎯 **分步骤打包**

### **如果需要分步骤执行**

#### **1. 构建后端服务**
```bash
# Python后端
cd backend
pip install -r requirements.txt pex
pex -r requirements.txt -e server:serve -o ../dist/server.pex

# Go后端
cd go-backend
go build -o ../dist/go_grpc_server server.go

# Java后端
cd java-backend
mvn clean package spring-boot:repackage
cp target/java-grpc-backend-1.0.0.jar ../dist/java-grpc-backend.jar
```

#### **2. 构建前端**
```bash
cd electron-app
npm install
npm run build:proto
npm run build:css:minify
npm run build:vite
```

#### **3. 打包Electron应用**
```bash
cd electron-app
npm run build:electron
```

## 🔍 **常见问题解决**

### **构建失败排查**

#### **Python后端构建失败**
```bash
# 检查Python环境
python3 --version
pip --version

# 重新安装依赖
cd backend
pip install -r requirements.txt --force-reinstall
```

#### **Go后端构建失败**
```bash
# 检查Go环境
go version

# 清理并重新构建
cd go-backend
go clean
go mod tidy
go build -o ../dist/go_grpc_server server.go
```

#### **Java后端构建失败**
```bash
# 检查Java和Maven环境
java -version
mvn -version

# 清理并重新构建
cd java-backend
mvn clean
mvn package spring-boot:repackage
```

#### **Electron打包失败**
```bash
# 清理node_modules
cd electron-app
rm -rf node_modules package-lock.json
npm install

# 重新构建
npm run build:vite
npm run build:electron
```

### **权限问题**
```bash
# 给脚本添加执行权限
chmod +x scripts/*.sh

# 给后端文件添加执行权限
chmod +x dist/server.pex
chmod +x dist/go_grpc_server
```

## 📤 **交付给用户**

### **最终交付包**
```bash
# 完整构建后，交付release目录
./scripts/build-cross-platform.sh

# 压缩发布包
tar -czf subtitle-processor-v1.0.0.tar.gz release/
```

### **用户安装说明**
1. **Windows用户**: 运行 `字幕处理工具 Setup 1.0.0.exe`
2. **macOS用户**: 打开 `字幕处理工具-1.0.0.dmg` 并拖拽到应用程序文件夹
3. **Linux用户**: 运行 `字幕处理工具-1.0.0.AppImage`

### **系统要求**
- **Windows**: Windows 10 或更高版本
- **macOS**: macOS 10.14 或更高版本
- **Linux**: Ubuntu 18.04+ 或等效版本
- **内存**: 4GB RAM (推荐 8GB)
- **存储**: 500MB 可用空间

## ⚡ **快速命令参考**

```bash
# 完整构建 (推荐)
./scripts/build-all.sh

# 跨平台构建
./scripts/build-cross-platform.sh

# 验证构建
./scripts/verify-build.sh

# 检查依赖
cd electron-app && npm run build:check

# 清理构建
rm -rf dist/* electron-app/dist-electron/*

# 重新开始
./scripts/build-all.sh && ./scripts/verify-build.sh
```

## 🎉 **构建成功标志**

### **看到以下信息表示构建成功**
```
✅ 完整构建流程完成！
🎉 应用包位置: /path/to/project/electron-app/dist-electron/
📋 下一步操作:
1. 测试应用: 运行 ./scripts/verify-build.sh
2. 安装应用: 打开 electron-app/dist-electron/ 中的安装包
3. 分发应用: 将安装包分发给用户
```

### **验证成功标志**
```
🎉 所有验证检查都通过了！构建质量良好。
📋 应用包信息:
应用包总大小: XXX MB
应用包位置: /path/to/project/electron-app/dist-electron/
🚀 可以安全地分发应用包给用户！
```

---

## 📚 **相关文档**
- [完整打包部署指南](./PACKAGING_DEPLOYMENT_GUIDE.md)
- [整体架构说明](./ARCHITECTURE_OVERVIEW.md)
- [前端开发指南](./FRONTEND_DEVELOPMENT_GUIDE.md)
- [后端开发指南](./BACKEND_DEVELOPMENT_GUIDE.md)

---

*快速打包指南版本: v1.0*  
*最后更新: 2024年12月*

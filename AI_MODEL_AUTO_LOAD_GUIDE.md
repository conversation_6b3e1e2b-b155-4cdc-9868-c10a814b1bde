# AI 模型自动获取功能使用指南

## 🎯 功能概述

新增的模型自动获取功能允许用户在配置 AI 服务时自动获取最新的可用模型列表，无需手动输入模型名称，大大提升了配置的便利性和准确性。

## ✨ 主要特性

### 1. **智能模型选择器**
- 下拉选择框显示可用模型
- 支持手动输入自定义模型名称
- 实时获取最新模型列表
- 按字母顺序排序显示

### 2. **自动加载机制**
- API Key 输入完成后自动尝试获取模型列表
- 1 秒防抖延迟，避免频繁请求
- 智能检测配置变更

### 3. **多服务支持**
- **OpenAI**: 通过 `/models` API 获取完整模型列表
- **DeepSeek**: 通过 `/models` API 获取可用模型
- **Gemini**: 提供预定义的常用模型列表

### 4. **用户友好界面**
- 清晰的加载状态指示
- 错误信息详细反馈
- 常用模型备选列表

## 🚀 使用方法

### 方法 1: 自动加载（推荐）
1. **选择服务类型**（OpenAI/DeepSeek/Gemini）
2. **输入 API Key**
3. **等待 1 秒** - 系统自动尝试获取模型列表
4. **从下拉框选择模型** - 如果获取成功，可直接选择

### 方法 2: 手动获取
1. **填写必要的配置信息**（API Key 等）
2. **点击模型字段旁的 🔄 按钮**
3. **等待加载完成**
4. **从下拉框选择模型**

### 方法 3: 手动输入
1. **在"或输入自定义模型"字段中直接输入**
2. **支持任何自定义模型名称**
3. **适用于新发布或特殊模型**

## 📋 支持的模型获取方式

### OpenAI
```
GET https://api.openai.com/v1/models
Authorization: Bearer sk-xxxxxxxxx
```
**返回示例模型**:
- gpt-4o
- gpt-4o-mini
- gpt-4-turbo
- gpt-3.5-turbo
- dall-e-3
- whisper-1

### DeepSeek
```
GET https://api.deepseek.com/models
Authorization: Bearer sk-xxxxxxxxx
```
**返回示例模型**:
- deepseek-chat
- deepseek-coder
- deepseek-math

### Gemini
**预定义模型列表**:
- gemini-1.5-pro-latest
- gemini-1.5-flash-latest
- gemini-1.0-pro
- gemini-1.0-pro-vision

## 🔧 技术实现

### 前端组件
- **模型选择器**: 结合下拉框和文本输入
- **状态管理**: 加载状态、错误处理、模型缓存
- **自动触发**: 监听 API Key 变化自动加载

### 后端 API
- **HTTP 请求**: 调用各服务商的模型列表 API
- **数据解析**: 统一格式化模型信息
- **错误处理**: 网络错误、认证失败等情况

### 数据格式
```javascript
{
  success: true,
  message: "成功获取 15 个模型",
  models: [
    {
      id: "gpt-4o",
      owned_by: "openai",
      created: 1677610602,
      object: "model"
    },
    // ...更多模型
  ]
}
```

## 🎨 界面设计

### 模型选择区域
```
┌─────────────────────────────────────────────────────┐
│ 默认模型 *                                          │
├─────────────────────────────────────────────────────┤
│ [请选择模型 ▼]                              [🔄]    │
│                                                     │
│ 可用模型                                            │
│ ├─ gpt-4o (openai)                                 │
│ ├─ gpt-4o-mini (openai)                           │
│ └─ gpt-3.5-turbo (openai)                         │
│                                                     │
│ 常用模型                                            │
│ ├─ gpt-4o                                          │
│ └─ gpt-3.5-turbo                                   │
│                                                     │
│ 或输入自定义模型: [________________]                │
│                                                     │
│ ✅ 已加载 15 个可用模型                             │
└─────────────────────────────────────────────────────┘
```

## ⚠️ 注意事项

### API 限制
- **OpenAI**: 需要有效的 API Key，某些模型可能需要特殊权限
- **DeepSeek**: 需要账户激活和有效的 API Key
- **Gemini**: 需要启用 Gemini API 服务

### 网络要求
- 稳定的互联网连接
- 能够访问相应的 API 端点
- 某些网络环境可能需要代理配置

### 性能考虑
- 模型列表会被缓存，避免重复请求
- 自动加载有 1 秒防抖延迟
- 超时时间设置为 30 秒

## 🐛 常见问题

### Q: 点击刷新按钮没有反应
**A**: 
1. 确认已填写有效的 API Key
2. 检查网络连接状态
3. 查看控制台是否有错误信息

### Q: 模型列表显示为空
**A**: 
1. 验证 API Key 是否正确且有效
2. 确认账户是否有访问模型列表的权限
3. 检查 Base URL 配置是否正确

### Q: 自动加载不工作
**A**: 
1. 确保 API Key 格式正确
2. 等待 1 秒防抖延迟
3. 检查浏览器控制台的日志信息

### Q: 某些模型不在列表中
**A**: 
1. 使用"输入自定义模型"功能手动输入
2. 检查是否有新模型发布
3. 确认账户是否有访问特定模型的权限

## 🔄 更新和维护

### 模型列表更新
- OpenAI 和 DeepSeek 的模型列表实时从 API 获取
- Gemini 的模型列表需要手动更新代码
- 建议定期检查是否有新模型发布

### 功能扩展
- 可以添加更多 AI 服务提供商
- 支持模型详细信息显示（参数数量、训练时间等）
- 添加模型性能和价格信息

## 📞 技术支持

如果遇到问题，请：
1. 查看浏览器开发者工具的控制台
2. 检查 Electron 主进程日志
3. 确认网络连接和 API 配置

模型自动获取功能大大简化了 AI 服务配置流程，让用户能够轻松选择最适合的模型！

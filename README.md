# Electron + Python gRPC (PEX) + Go gRPC + Java gRPC (Spring Boot + GraalVM) Demo

本项目演示了如何将一个 Python gRPC 后端服务通过 PEX 打包，一个 Go gRPC 后端服务构建为静态二进制文件，以及一个 Java gRPC (Spring Boot) 后端服务（可选择通过 GraalVM 构建为原生可执行文件），并集成到一个 Electron 应用中，实现多后端与前端之间的通信。

## 📚 文档导航

### **🎨 设计文档**
- **[前端设计指南](FRONTEND_DESIGN_GUIDE.md)** - UI设计系统、组件规范、视觉标准

### **🚀 开发文档**
- **[前端开发与部署](FRONTEND_DEVELOPMENT_DEPLOYMENT.md)** - 开发环境、构建部署、最佳实践

### **📋 其他文档**
- **[📋 文档索引](./DOCUMENTATION_INDEX.md)** - 完整的文档结构和导航
- **[⚡ 批量处理功能](./BATCH_PROCESSING_FEATURE.md)** - 最新的批量处理功能实现
- **[🏗️ 项目架构](./Architecture.md)** - 整体架构设计

## 主要特性

*   **Electron 桌面应用**: 使用 Electron 构建跨平台的桌面用户界面。
*   **双后端服务**:
    *   **Python gRPC 后端**: 使用 Python 实现 gRPC 服务作为后端逻辑，通过 PEX 打包。
    *   **Go gRPC 后端**: 使用 Go 实现另一个 gRPC 服务，构建为独立的静态二进制文件。
    *   **Java gRPC 后端**: 使用 Java (Spring Boot) 实现 gRPC 服务，可通过 Maven 构建为 JAR 或使用 GraalVM 构建为原生可执行文件。
*   **PEX 打包**: 将 Python 后端及其依赖打包成单个可执行文件 (`.pex`)。
*   **GoReleaser 构建**: 使用 GoReleaser 将 Go 后端构建为跨平台的静态二进制文件。
*   **进程间通信 (IPC)**: Electron 主进程与渲染进程之间通过 `contextBridge` 和 `ipcMain`/`ipcRenderer` 安全通信。
*   **gRPC 通信**: Electron 主进程作为 gRPC 客户端与 Python 和 Go 后端服务进行通信。
*   **状态监控**: 界面会显示后端服务的准备状态，并在服务就绪前禁用相关操作。
*   **日志展示**: Electron 应用界面会显示来自 Python 和 Go 后端的实时日志。

## 项目结构

```
electron-python-grpc-pex-demo/
├── backend/                  # Python gRPC 服务端相关
│   ├── greeter.proto         # gRPC 服务定义 (Python 使用)
│   ├── server.py             # Python gRPC 服务实现
│   ├── requirements.txt      # Python 依赖
│   └── dist/                 # PEX 构建输出目录 (自动创建)
│       └── server.pex        # 打包后的 Python 后端
├── go-backend/               # Go gRPC 服务端相关
│   ├── greeter.proto         # gRPC 服务定义 (与 Python 后端可共用或类似)
│   ├── server.go             # Go gRPC 服务端实现
│   ├── go.mod                # Go 模块文件
│   ├── go.sum                # Go 模块依赖校验和文件
│   ├── .goreleaser.yml       # GoReleaser 配置文件
│   └── dist/                 # Go 构建输出目录
│       └── go_grpc_server    # 手动放置/复制的 Go 后端静态二进制文件 (供开发模式使用)
├── java-backend/             # Java gRPC 服务端相关 (Spring Boot + GraalVM)
│   ├── src/main/java/        # Java 源代码
│   │   └── com/example/javagrpc/
│   │       ├── JavaGrpcBackendApplication.java # Spring Boot 主应用类
│   │       └── GreeterServiceImpl.java       # gRPC 服务实现
│   ├── src/main/proto/       # gRPC 服务定义 (Java 使用)
│   │   └── greeter.proto
│   ├── src/main/resources/   # 资源文件
│   │   ├── application.properties
│   │   └── META-INF/native-image/ # GraalVM native image 配置
│   │       └── reflect-config.json # 反射配置文件示例
│   ├── pom.xml                 # Maven 项目配置文件
│   └── target/                 # Maven 构建输出目录
│       ├── java-grpc-backend-0.0.1-SNAPSHOT.jar # Spring Boot JAR
│       └── java-grpc-backend   # GraalVM 原生可执行文件 (构建后)
├── electron-app/             # Electron 应用相关
│   ├── main.js               # Electron 主进程 (管理 Python 和 Go 后端)
│   ├── preload.js            # Electron 预加载脚本
│   ├── index.html            # Electron 渲染进程 UI
│   ├── renderer.js           # Electron 渲染进程逻辑
│   └── package.json          # Node.js 项目配置和依赖
└── README.md                 # 项目说明和构建步骤
```

## 先决条件

1.  **Node.js 和 npm/yarn**: 用于运行 Electron 应用和管理 JavaScript 依赖。
    *   从 [nodejs.org](https://nodejs.org/) 下载并安装。
2.  **Python 3**: 用于运行 Python gRPC 服务和 PEX 打包。
    *   从 [python.org](https://python.org/) 下载并安装。
    *   确保 `pip` 已安装并已添加到 PATH。
3.  **PEX**: Python 可执行文件构建工具。
    *   安装 PEX：`pip install pex`
4.  **Go**: 用于开发和构建 Go gRPC 服务。
    *   从 [golang.org](https://golang.org/dl/) 下载并安装。
    *   确保 Go 相关的环境变量 (如 `GOPATH`, `GOROOT`) 配置正确，并且 `go` 命令在 PATH 中。
5.  **Protocol Buffer Compiler (`protoc`)**: 用于从 `.proto` 文件生成 gRPC 代码。
    *   从 [protobuf releases](https://github.com/protocolbuffers/protobuf/releases) 下载并安装。确保 `protoc` 在 PATH 中。
6.  **Go gRPC Plugins**: `protoc` 的 Go 插件。
    *   `protoc-gen-go`: `go install google.golang.org/protobuf/cmd/protoc-gen-go@latest`
    *   `protoc-gen-go-grpc`: `go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest`
    *   确保这些插件安装的路径 (通常是 `$GOPATH/bin` 或 `$HOME/go/bin`) 在 PATH 中。
7.  **GoReleaser** (可选, 用于构建发布版本):
    *   `go install github.com/goreleaser/goreleaser@latest`
    *   确保 GoReleaser 安装的路径在 PATH 中。
8.  **Java Development Kit (JDK)**: 用于开发和构建 Java gRPC 服务。
    *   推荐使用与目标 GraalVM 版本兼容的 JDK (例如，GraalVM for JDK 17 或 21)。
    *   从 [Oracle](https://www.oracle.com/java/technologies/downloads/) 或 [Adoptium](https://adoptium.net/) 下载。
9.  **Apache Maven**: 用于构建 Java 项目。
    *   从 [maven.apache.org](https://maven.apache.org/download.cgi) 下载并安装。
    *   确保 `mvn` 命令在 PATH 中。
10. **GraalVM** (可选, 用于构建原生可执行文件):
    *   用于将 Java 应用编译为原生可执行文件，以获得更快的启动速度和更低的内存占用。
    *   从 [graalvm.org](https://www.graalvm.org/downloads/) 下载并安装。
    *   确保已安装 `native-image` 组件 (`gu install native-image`)。
    *   配置 `GRAALVM_HOME` 环境变量并将其 `bin` 目录添加到 PATH。

## 设置和运行步骤

### 1. 克隆/下载项目 (如果适用)

如果您是从版本控制系统获取此项目，请先克隆它。否则，请确保您已拥有所有项目文件。

### 2. 设置 Python gRPC 后端 (`backend/`)

进入 `backend` 目录：
```bash
cd backend
```

#### a. 创建虚拟环境 (推荐)
```bash
# 例如使用 conda
conda create -n electron-py python=3.12
conda activate electron-py
# 或者使用 venv
# python -m venv venv
# source venv/bin/activate  # Linux/macOS
# .\venv\Scripts\activate   # Windows
```

#### b. 安装 Python 依赖
```bash
pip install -r requirements.txt
```
这将安装 `grpcio` 和 `grpcio-tools`。

#### c. 生成 Python gRPC Stubs
在 `backend` 目录下运行：
```bash
python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. ./greeter.proto
```
这将根据 [`greeter.proto`](backend/greeter.proto:1) 生成 `greeter_pb2.py` 和 `greeter_pb2_grpc.py` 文件。

#### d. 构建 PEX 文件
在 `backend` 目录下运行：
```bash
pex -D . -r requirements.txt -m server:serve -o ./dist/server.pex --no-wheel
```
*   `-D .`: 指定当前目录为 PEX 的工作目录。
*   `-r requirements.txt`: 将依赖项打包进去。
*   `-m server:serve`: 指定 PEX 的入口点为 `server.py` 文件中的 `serve` 函数。
*   `-o ./dist/server.pex`: 指定输出文件路径和名称。`dist` 目录如果不存在会被创建。
*   `--no-wheel`: 有时可以解决一些打包问题。

构建成功后，您应该会在 `backend/dist/` 目录下找到 [`server.pex`](backend/dist/server.pex:1)。

**(可选) 测试 PEX 文件**
```bash
./dist/server.pex
```
按 Ctrl+C 停止。

### 3. 设置 Go gRPC 后端 (`go-backend/`)

进入 `go-backend` 目录：
```bash
# 假设您在项目根目录
cd go-backend
# 如果您在 backend 目录
# cd ../go-backend
```

#### a. 初次设置 / 初始化模块 (如果尚未完成)
如果您是首次设置此 Go 项目，或者 `go.mod` 文件不存在:
```bash
# 将 <your_module_name> 替换为您的模块路径, 例如: github.com/yourusername/electron-go-grpc-demo
go mod init <your_module_name>
```
然后获取必要的 Go 依赖：
```bash
go get google.golang.org/grpc
go get google.golang.org/protobuf/cmd/protoc-gen-go
go get google.golang.org/grpc/cmd/protoc-gen-go-grpc
# go mod tidy # 整理依赖
```
确保 `protoc`, `protoc-gen-go`, `protoc-gen-go-grpc` 已安装并配置在系统 PATH 中。

#### b. 生成 Go gRPC 代码
每当您修改了 [`greeter.proto`](go-backend/greeter.proto:1) 文件后，都需要在 `go-backend` 目录下重新运行此命令：
```bash
protoc --go_out=. --go_opt=paths=source_relative \
       --go-grpc_out=. --go-grpc_opt=paths=source_relative \
       greeter.proto
```
这将根据 [`greeter.proto`](go-backend/greeter.proto:1) 生成 `greeter.pb.go` 和 `greeter_grpc.pb.go` 文件。

#### c. 本地测试 Go 服务 (可选)
在 `go-backend` 目录下编译并运行 Go 服务：
```bash
go build -o go_server_executable .
./go_server_executable
```
服务默认会在例如 50052 端口启动 (具体端口请查看 `server.go` 实现)。按 Ctrl+C 停止。

#### d. 使用 GoReleaser 构建静态二进制文件
GoReleaser 用于创建可分发的二进制文件。配置文件是 [`.goreleaser.yml`](go-backend/.goreleaser.yml:1)。
确保 GoReleaser 已安装: `go install github.com/goreleaser/goreleaser@latest`

在 `go-backend` 目录下运行：
```bash
goreleaser build --snapshot --clean --single-target
# 或者 goreleaser release --snapshot --clean (如果配置了发布步骤)
# 您可能需要根据您的 .goreleaser.yml 配置和目标平台调整命令
# 例如，只构建 macOS amd64: goreleaser build --snapshot --clean --single-target --id=go_grpc_server-darwin-amd64
```

#### e. 手动移动/准备开发用二进制文件
GoReleaser 构建的二进制文件通常位于 `go-backend/dist/<projectname>_<os>_<arch>/<binaryname>`。
例如，在 macOS (arm64) 上可能是 `go-backend/dist/go_grpc_server_darwin_arm64/go_grpc_server`。

**为了让 Electron 在开发模式下能找到并运行 Go 后端**，您需要将适用于您当前开发环境的二进制文件：
1.  **复制**到 `electron-python-grpc-pex-demo/go-backend/dist/` 目录。
2.  **重命名**为 `go_grpc_server` (无扩展名)。

所以，最终在 `go-backend/dist/` 目录下应该有一个名为 `go_grpc_server` 的可执行文件。
Electron 的 `main.js` 在开发模式下会尝试从 `../go-backend/dist/go_grpc_server` 路径启动这个 Go 服务。

### 3.bis 设置 Java gRPC 后端 (`java-backend/`)

Java 后端使用 Spring Boot 和 `grpc-spring-boot-starter` 构建，并可以被编译为 GraalVM 原生可执行文件。

进入 `java-backend` 目录：
```bash
# 假设您在项目根目录
cd java-backend
```

#### a. 生成 Java gRPC Stubs (通过 Maven)
Maven 配置了 `protobuf-maven-plugin`，会在编译过程中自动从 [`src/main/proto/greeter.proto`](java-backend/src/main/proto/greeter.proto:1) 生成 Java gRPC 代码。
通常不需要手动执行此步骤，`mvn compile` 或 `mvn package` 会处理。

#### b. 在 IDE 中运行 (开发模式)
*   可以直接在 IntelliJ IDEA 或 Eclipse 等 IDE 中将 [`JavaGrpcBackendApplication.java`](java-backend/src/main/java/com/example/javagrpc/JavaGrpcBackendApplication.java:1) 作为标准的 Spring Boot 应用运行。
*   服务默认会在 50053 端口启动 (具体端口请查看 `application.properties` 或代码)。

#### c. 使用 Maven 构建 JAR 包
在 `java-backend` 目录下运行：
```bash
mvn package
```
这将生成一个可执行的 Spring Boot JAR 文件，位于 [`java-backend/target/java-grpc-backend-0.0.1-SNAPSHOT.jar`](java-backend/target/java-grpc-backend-0.0.1-SNAPSHOT.jar:1)。
您可以通过 `java -jar target/java-grpc-backend-0.0.1-SNAPSHOT.jar` 运行它。

#### d. 使用 Maven 和 GraalVM 构建原生可执行文件
确保 GraalVM 已正确安装并配置为当前的 JDK (`java -version` 应显示 GraalVM)。
在 `java-backend` 目录下运行：
```bash
mvn package -Pnative
```
*   `-Pnative` 激活 [`pom.xml`](java-backend/pom.xml:1) 中定义的 `native` profile，该 profile 通常会配置 `native-maven-plugin` (GraalVM Build Tools) 来构建原生镜像。
*   **GraalVM 配置**:
    *   [`pom.xml`](java-backend/pom.xml:1) 中的 `native-maven-plugin` 包含重要的 `buildArgs`，例如 `--initialize-at-run-time` (用于延迟初始化某些库以兼容 native image) 和 `-H:ConfigurationFileDirectories` (用于指定 native image 配置文件的目录，如 [`reflect-config.json`](java-backend/src/main/resources/META-INF/native-image/reflect-config.json:1), `resource-config.json` 等)。
    *   反射配置 ([`java-backend/src/main/resources/META-INF/native-image/reflect-config.json`](java-backend/src/main/resources/META-INF/native-image/reflect-config.json:1)) 对于 Spring Boot 和某些库 (如 Netty, BouncyCastle) 的正确运行至关重要，它告诉 GraalVM 编译器哪些类需要反射支持。这些配置可以通过 GraalVM 的追踪代理自动生成，或者手动编写。[`reflect-config.json`](java-backend/src/main/resources/META-INF/native-image/reflect-config.json:1) 文件主要用于声明在运行时需要通过反射访问的类、方法和字段。例如，如果代码中使用了 `Class.forName("com.example.MyClass")` 或 `Method.invoke()`，那么相关的类和成员就需要在此文件中列出，以便 GraalVM 的 `native-image` 工具可以将它们包含在最终的可执行文件中，并允许反射操作。没有这些配置，原生镜像在尝试进行反射调用时可能会抛出 `ClassNotFoundException` 或 `NoSuchMethodException` 等错误。
*   构建成功后，原生可执行文件将位于 [`java-backend/target/java-grpc-backend`](java-backend/target/java-grpc-backend:1) (文件名由 [`pom.xml`](java-backend/pom.xml:1) 中 `native-maven-plugin` 的 `<imageName>` 或 `artifactId` 决定)。

**(可选) 测试原生可执行文件**
```bash
./target/java-grpc-backend
```
按 Ctrl+C 停止。

#### e. 与 Electron 集成说明
Electron 应用 ([`electron-app/main.js`](electron-app/main.js:1)) 在启动时会尝试启动这个 Java 后端进程 (无论是 JAR 还是原生可执行文件，取决于 `main.js` 中的路径配置)。
Electron 主进程通过 gRPC (默认端口 50053，见 [`JAVA_GRPC_ADDRESS`](electron-app/main.js:49) 在 [`electron-app/main.js`](electron-app/main.js:1) 中定义) 与 Java 后端通信。
[`main.js`](electron-app/main.js:1) 中会确定 Java 后端可执行文件的路径：
*   开发模式: `electron-app/../java-backend/target/java-grpc-backend`
*   打包模式: `process.resourcesPath/java-backend/java-grpc-backend`

#### f. Proto 文件关系
*   Java 后端使用 [`java-backend/src/main/proto/greeter.proto`](java-backend/src/main/proto/greeter.proto) 作为其服务定义。
*   Electron 应用作为客户端，也需要访问此 `.proto` 定义 (或其编译版本) 来生成客户端 stub。在当前项目中，Electron 应用使用 [`electron-app/src/proto/greeter.proto`](electron-app/src/proto/greeter.proto)，它应该与 Java 后端的 proto 文件保持一致或兼容。

### 4. 设置 Electron 前端 (`electron-app/`)

进入 `electron-app` 目录：
```bash
# 假设您在项目根目录
cd electron-app
# 如果您在 go-backend 目录
# cd ../electron-app
```

#### a. 安装 Node.js 依赖
```bash
# 推荐使用 nvm 管理 Node 版本, 例如 v20.x
# nvm use v20
npm install
# 或者如果您使用 yarn
# yarn install
```

#### b. 编译 Tailwind CSS (如果使用)
如果项目中包含 `tailwind.config.js` 和 CSS 构建脚本 (如 `build:css` 在 `package.json` 中):
```bash
npm run build:css
```

### 5. 运行 Electron 应用 (开发模式)

确保您仍在 `electron-app` 目录下。
```bash
npm start
```
这将启动 Electron 应用。应用启动时，它会：
1.  尝试运行位于 `electron-python-grpc-pex-demo/backend/dist/server.pex` 的 Python PEX 文件。
2.  尝试运行位于 `electron-python-grpc-pex-demo/go-backend/dist/go_grpc_server` 的 Go 二进制文件。
3.  尝试运行位于 `electron-python-grpc-pex-demo/java-backend/target/java-grpc-backend` (或打包路径) 的 Java 原生可执行文件。

您应该能在 Electron 应用的界面中看到 "Python Backend Logs" 和 "Go Backend Logs" 部分显示来自各自后端进程的日志，并且可以与两个服务进行交互。

**注意：**
*   `main.js` 中的后端路径在开发模式下分别指向 `../backend/dist/server.pex`、`../go-backend/dist/go_grpc_server` 和 `../java-backend/target/java-grpc-backend`。
*   如果任一后端文件未找到或无法执行，Electron 应用的日志区域会显示错误信息。

### 6. 打包 Electron 应用 (生产模式)

在 `electron-app` 目录下运行：
```bash
npm run dist
```
这将使用 `electron-builder` 将您的 Electron 应用打包。

**`electron-builder` 配置要点 (`package.json`):**
您需要确保 `electron-builder` 的配置 (`build` 部分在 `electron-app/package.json` 中) 将两个后端可执行文件都包含为 `extraResources`。

示例 (需要根据实际情况调整 `from` 路径和 `to` 路径):
```json
// ... (其他 package.json 内容) ...
"build": {
  "appId": "com.example.electron.multi.grpc.demo",
  "productName": "ElectronMultiGRSCDemo",
  "files": [
    "main.js",
    "preload.js",
    "index.html",
    "renderer.js",
    "package.json",
    "dist/assets/*" // 如果有编译后的 CSS 等静态资源
  ],
  "extraResources": [
    {
      "from": "../backend/dist/server.pex",
      "to": "backend/server.pex",
      "filter": ["**/*"]
    },
    {
      // 注意: 此处 "from" 路径需要指向一个通用的、已构建好的 Go 二进制文件
      // 通常在 CI/CD 流程中，会为每个目标平台构建 Go 二进制文件，并选择合适的放入
      // 或者，您可以在本地为目标平台构建，并将其放在一个可预测的路径
      // 例如，您可以将 goreleaser 的输出配置为更简单的路径，或者手动复制到特定位置
      "from": "../go-backend/dist_prod/go_grpc_server", // 假设您为生产构建准备了此文件
      "to": "backend/go_grpc_server",
      "filter": ["**/*"]
    },
    {
      "from": "../java-backend/target/java-grpc-backend",
      "to": "java-backend/java-grpc-backend",
      "filter": ["**/*"]
    }
  ],
  // 特定平台的配置...
  "mac": { "category": "public.app-category.developer-tools", "target": ["dmg", "zip"] },
  "win": { "target": "nsis" },
  "linux": { "target": "AppImage" }
}
// ...
```
*   `extraResources` 配置确保 `server.pex` 和 `go_grpc_server` 文件被复制到打包后应用的资源目录中。
*   `main.js` 中的后端路径在打包应用中会通过 `process.resourcesPath` 正确指向这些打包后的文件 (例如 `backend/server.pex`, `go-backend/go_grpc_server`, `java-backend/java-grpc-backend`)。

打包完成后，您可以在 `electron-app/dist_electron/` 目录下找到可执行的应用程序包。

## 故障排除

*   **PEX/Go 二进制文件未找到**:
    *   **Python**: 确保已在 `backend` 目录成功运行 PEX 构建命令，并且 `backend/dist/server.pex` 存在。
    *   **Go (开发模式)**: 确保已按步骤 `3.e` 将 GoReleaser (或其他方式) 构建的二进制文件复制并重命名到 `go-backend/dist/go_grpc_server`。
    *   检查 `electron-app/main.js` 中的后端路径是否正确。
*   **后端无法启动 (权限问题)**:
    *   在 macOS/Linux 上，可执行文件可能需要执行权限。`electron-app/main.js` 中的代码通常会尝试使用 `fs.chmodSync` 设置权限。如果失败，您可能需要手动设置：`chmod +x backend/dist/server.pex` 和 `chmod +x go-backend/dist/go_grpc_server`。
*   **gRPC 连接错误 (ECONNREFUSED)**:
    *   确保相应的 gRPC 服务 (`server.pex` 或 `go_grpc_server`) 已成功启动并在监听其配置的端口 (例如 Python: 50051, Go: 50052)。检查 Electron 应用中的后端日志。
    *   检查防火墙设置。
*   **`_pb2.py` / `_pb2_grpc.py` / `.pb.go` / `_grpc.pb.go` 找不到**:
    *   确保您已在各自的后端目录 (`backend/` 或 `go-backend/`) 中运行了相应的 `protoc` 命令来生成 gRPC stub 文件。
*   **Electron API 调用问题 (如 `electronAPI.sayHelloToPython is not available`)**:
    *   检查预加载脚本 (`preload.js`)、主进程 (`main.js`) 和渲染进程 (`renderer.js`) 的 IPC 通信和 API 暴露逻辑。
    *   查看 Electron 开发者工具的控制台是否有错误。
*   **依赖安装问题**:
    *   Python 依赖 (`backend/requirements.txt`)、Go 依赖 (`go-backend/go.mod`) 和 Node.js 依赖 (`electron-app/package.json`) 是分开的，需要在各自目录中用对应工具 (`pip`, `go mod`, `npm`/`yarn`) 安装。
*   **`.proto` 文件路径问题**:
    *   确保 `electron-app/main.js` 中初始化 gRPC 客户端时，`PROTO_PATH` (或等效逻辑) 正确指向了 `.proto` 文件。
    *   确保 Python 和 Go 的 `protoc` 命令中 `.proto` 文件路径正确。
*   **GoReleaser 问题**:
    *   检查 [`.goreleaser.yml`](go-backend/.goreleaser.yml:1) 配置是否正确。
    *   确保 GoReleaser 和其依赖 (如 `git`) 已正确安装和配置。
*   **Java 后端启动/构建问题**:
    *   **JAR 运行**: 确保 `mvn package` 成功并且 JAR 文件存在。使用 `java -jar ...` 检查是否能独立运行。
    *   **GraalVM 原生构建**:
        *   确保 GraalVM 已正确安装，`native-image` 组件已安装，并且环境变量已配置。
        *   检查 [`pom.xml`](java-backend/pom.xml:1) 中的 `native-maven-plugin` 配置，特别是 `buildArgs`。
        *   [`reflect-config.json`](java-backend/src/main/resources/META-INF/native-image/reflect-config.json:1) 等配置文件是否正确且包含所有必要的反射项。Spring Boot 应用通常需要这些。
        *   查看 Maven 构建日志以获取详细错误信息。
    *   **端口冲突**: 确保 Java 服务配置的端口 (默认 50053) 未被占用。
    *   **Electron 路径**: 检查 [`electron-app/main.js`](electron-app/main.js:1) 中 `javaBackendPath` 是否正确指向了 Java 可执行文件 (JAR 或 native)。
*   **PATH 问题**:
    *   确保 `protoc`, `protoc-gen-go`, `protoc-gen-go-grpc`, `go`, `pex`, `goreleaser` 等命令行工具所在的目录已添加到系统的 PATH 环境变量中。

---
## 新增：推荐使用 NPM 脚本进行自动化构建和运行

为了简化开发流程，本项目在 `electron-app` 目录下的 [`package.json`](electron-app/package.json) 文件中提供了一系列 `npm` 脚本，用于自动化构建所有后端服务、前端资源以及启动应用程序。

### 1. 环境准备

*   确保所有在“[先决条件](#先决条件)”中列出的工具已正确安装和配置。
*   如果您使用 Conda 管理 Python 环境 (例如，您可能有一个名为 `electron` 的环境)，请先激活它：
    ```bash
    conda activate electron
    # 或者您特定的 Conda 环境名
    ```

### 2. 构建所有服务和前端资源 (Fresh Build)

当您对任一后端服务的源代码、`.proto` 文件进行了更改，或者首次设置项目时，建议执行完整的构建流程。

进入 `electron-app` 目录并运行：
```bash
cd electron-app
npm run fresh
```
此 `fresh` 命令 (定义在 [`electron-app/package.json`](electron-app/package.json) 中) 会执行以下操作：
1.  编译 Tailwind CSS。
2.  构建 Python 后端 (生成 Protobuf 代码并打包 PEX)。
3.  构建 Java 后端 (执行 `mvn clean package -Pnative`)。
4.  构建 Go 后端 (生成 Protobuf 代码, 整理依赖并编译可执行文件)。

详细的子命令可以在 [`electron-app/package.json`](electron-app/package.json) 的 `scripts` 部分查看，主要涉及 `build:all`, `build:backends`, `build:backend:python`, `build:backend:java`, `build:backend:go` 等脚本。

### 3. 启动 Electron 应用

在所有后端服务和前端资源构建完成后（或如果您确定它们是最新的），您可以启动 Electron 应用。

确保您仍在 `electron-app` 目录下，然后运行：
```bash
npm start
```
此命令 (定义在 [`electron-app/package.json`](electron-app/package.json) 中) 现在仅负责启动 Electron 应用程序 (`electron .`)。它假定所有后端服务已经通过 `npm run fresh` 构建完毕并准备就绪。Electron 主进程 ([`main.js`](electron-app/main.js)) 会尝试启动这些已构建的后端服务。

---
## 新增：开发过程回顾与问题解决

在整合和优化此多后端项目的过程中，我们遇到并解决了一些值得记录的关键技术问题：

### 1. Go gRPC 服务时间戳逻辑的调整

*   **初始问题**: 在开发 Go gRPC 服务时，对于响应中 `timestamp` 字段的需求最初被误解为不需要时间戳或应留空。
*   **修正过程**: 经过沟通，明确了需求是返回服务处理请求时的**当前时间戳**。
*   **解决方案**:
    *   恢复了 [`go-backend/server.go`](go-backend/server.go) 中生成时间戳的代码，使用 `time.Now().Format(time.RFC3339Nano)` 来获取并格式化当前时间。
    *   确保 `time` 标准库已在文件中正确导入。
    ```go
    // 在 go-backend/server.go 的 SayHello 方法中
    import "time" // 确保导入

    // ...
    currentTimestampStr := time.Now().Format(time.RFC3339Nano)
    reply := &pb.HelloReply{
        // ... 其他字段 ...
        Timestamp:       currentTimestampStr,
    }
    ```

### 2. Go 模块与 Protobuf 文件结构的规范化

*   **初始问题**: Go 后端服务在编译时，无法正确导入本地生成的 Protobuf 包 (`pb`)。错误信息包括 `could not import github.com/monkeyfx/electron-go-grpc-demo/pb (no required module provides package ...)` 或在执行 `go mod tidy` 时提示 `module ./github.com/monkeyfx/electron-go-grpc-demo/pb: reading .../pb/go.mod: no such file or directory`。
*   **解决方案**:
    1.  **规范 `go_package` 选项**: 在 [`go-backend/greeter.proto`](go-backend/greeter.proto) 文件中，将 `option go_package = "./pb";` 设置，确保生成的 Go 代码目标路径为 `pb` 子目录，包名为 `pb`。
    2.  **确保 Protobuf 代码生成到指定目录**: 使用 `protoc` (在 `go-backend` 目录执行) 时，生成的 `greeter.pb.go` 和 `greeter_grpc.pb.go` 文件会自动放置在 `go-backend/pb/` 目录下。
    3.  **统一服务端导入路径**: 在 [`go-backend/server.go`](go-backend/server.go) 中，导入 `pb` 包时使用相对于模块根目录的完整路径: `pb "github.com/monkeyfx/electron-go-grpc-demo/pb"` (其中 `github.com/monkeyfx/electron-go-grpc-demo` 为 [`go-backend/go.mod`](go-backend/go.mod) 中定义的模块名)。
    4.  **正确配置 `go.mod` 中的 `replace` 指令**: 在 [`go-backend/go.mod`](go-backend/go.mod) 文件中，修改 `replace` 指令为 `replace github.com/monkeyfx/electron-go-grpc-demo/pb => ./pb`，将导入路径正确映射到本地的 `pb` 目录。
    5.  **执行 `go mod tidy`**: 在 `go-backend` 目录下执行此命令以清理和同步依赖。

通过这些调整，Go 后端的 Protobuf 集成和模块管理变得更加规范和健壮。

---
## 新增：附录：旧版手动构建命令参考

以下是您在开发过程中使用的一套集中的手动构建命令，作为快速参考：

```bash
# 1. 环境准备 (示例)
conda activate electron

# 2. 构建 Python Backend
cd ./backend
python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. ./greeter.proto
pex -D . -r requirements.txt -m server:serve -o ./dist/server.pex --no-wheel
cd ..

# 3. 构建 Java Backend
cd ./java-backend 
mvn clean package -Pnative
cd ..

# 4. 构建 Go Backend
cd ./go-backend 
protoc --proto_path=. --go_out=. --go-grpc_out=. ./greeter.proto
go mod tidy
go build -o ./dist/go_grpc_server . 
cd ..
# AI 服务配置管理方案设计

**1. 引言**

*   **项目背景**: 本项目是一个 Electron + Python (gRPC) 的前后端分离应用，计划集成多种 AI 大模型服务。
*   **需求**: 需要一个用户友好的配置管理方案，允许用户在前端管理 AI 服务配置（API Key, Base URL, Model 等），并将这些配置安全地传递给后端 Python 服务使用，取代当前的硬编码方式。
*   **设计目标**:
    *   **灵活性与可扩展性**: 方便增删 AI 服务类型及其配置参数。
    *   **安全性**: 妥善保护 API Key 等敏感信息在存储和传输过程中的安全。
    *   **易用性**: 提供清晰的用户界面进行配置管理。
    *   **维护性**: 代码结构清晰，易于维护和迭代。
    *   **单一数据源**: 配置信息由用户在前端统一管理。

**2. 配置项定义**

为了支持多种 AI 服务并保持灵活性，我们定义通用的配置结构和针对特定服务的参数。

*   **核心配置对象结构 (`AIProviderConfig`)**:
    *   `provider_id` (string, e.g., "openai_default_01", "deepseek_chat_main"): 用户定义的唯一标识符，允许用户配置同一类型的多个服务实例。
    *   `provider_type` (enum string, e.g., "OpenAI", "DeepSeek", "Gemini", "CustomLLM"): AI 服务提供商的类型。
    *   `display_name` (string, e.g., "我的主力 OpenAI", "公司内部 DeepSeek"): 用户在界面上看到的友好名称。
    *   `is_enabled` (boolean): 是否启用此服务配置。后端将只考虑已启用的服务。
    *   `credentials` (map<string, string>, sensitive): 存储认证相关的敏感信息。键是参数名（如 "api_key", "api_secret"），值是参数值。这些值在前端存储时应加密，在传输到后端时通过安全通道传递。
    *   `attributes` (map<string, string>): 存储非敏感的附加配置，如 "api_base_url", "default_model", "timeout_seconds" 等。
    *   `metadata` (map<string, string>, optional): 存储其他元数据，如 "created_at", "description" 等。

*   **特定服务示例**:

    *   **OpenAI (`provider_type: "OpenAI"`)**:
        *   `credentials`:
            *   `api_key`: (string)
        *   `attributes`:
            *   `api_base_url`: (string, optional, default: OpenAI 官方)
            *   `default_model`: (string, e.g., "gpt-4o", "gpt-3.5-turbo")
            *   `timeout_seconds`: (integer string, optional)
            *   `max_tokens`: (integer string, optional)
            *   `temperature`: (float string, optional)

    *   **DeepSeek (`provider_type: "DeepSeek"`)**:
        *   `credentials`:
            *   `api_key`: (string)
        *   `attributes`:
            *   `api_base_url`: (string, optional, default: DeepSeek 官方)
            *   `default_model`: (string, e.g., "deepseek-chat", "deepseek-coder")

    *   **Gemini (`provider_type: "Gemini"`)**:
        *   `credentials`:
            *   `api_key`: (string)
        *   `attributes`:
            *   `api_base_url`: (string, optional, default: Google AI Studio)
            *   `default_model`: (string, e.g., "gemini-1.5-pro-latest")

*   **扩展性**:
    *   前端可以维护一个 JSON Schema 或类似定义，描述每种 `provider_type` 支持的 `credentials` 和 `attributes` 字段（包括名称、类型、是否必需、默认值、描述等）。
    *   当用户选择添加或编辑某种类型的服务时，前端根据此 Schema 动态渲染配置表单。
    *   新增 AI 服务类型时，只需更新此 Schema 并在后端添加相应的处理逻辑（如果需要特定适配）。

**3. 配置存储方案**

*   **前端存储 (Electron 应用 - 主进程)**:
    *   **推荐方案**: 使用 `electron-store` 库。
    *   **存储内容**: 一个 `AIProviderConfig` 对象列表。
    *   **优点**:
        *   API 简单直观，易于集成。
        *   数据以 JSON 文件形式存储在用户应用数据目录，方便调试。
        *   跨平台兼容。
    *   **敏感信息处理**:
        *   对于 `credentials` 中的敏感字段（如 `api_key`），在存入 `electron-store` 之前，使用 Electron 的 `safeStorage.encryptString()` API 进行加密。
        *   从 `electron-store` 读取后，在需要使用或传输到后端（通过安全通道）之前，使用 `safeStorage.decryptString()` 进行解密。
        *   `safeStorage` 利用操作系统提供的加密服务（如 macOS Keychain, Windows DPAPI），提供了比纯文本存储更高的安全性。
    *   **文件位置**: `electron-store` 默认将文件存储在 `app.getPath('userData')` 下。

*   **后端存储 (Python 服务)**:
    *   **推荐方案**: **后端原则上不直接持久化存储从前端接收到的 AI 服务配置，尤其是不存储明文的 API Key。**
    *   **运行机制**:
        1.  Python 后端启动时，其内存中的 AI 配置为空。
        2.  当 Electron 前端加载或更新配置后，会通过 gRPC 调用将当前所有已启用的、解密后的（仅限敏感字段）`AIProviderConfig` 对象列表推送给 Python 后端。
        3.  Python 后端将接收到的配置缓存在内存中（例如，一个全局可访问的配置管理模块内的字典）。
        4.  当 Python 服务（如 `TranscriptThread`）需要调用 AI 大模型时，它从内存缓存中查询合适的、已启用的服务配置。
    *   **优点**:
        *   **单一事实来源**: 配置的权威版本始终在前端用户手中，由 `electron-store` 管理。
        *   **安全性**: 避免了在后端文件系统或数据库中持久化存储（可能未加密或弱加密的）敏感 API Key，降低了后端被攻破时敏感信息泄露的风险。
        *   **实时性**: 用户在前端更改并保存配置后，新的配置会立即推送给后端，后端下次调用 AI 服务时即可使用最新配置，无需后端服务重启。
        *   **简化后端**: 后端无需实现复杂的文件读写、加密解密或数据库交互逻辑来管理这些配置。
    *   **注意**: 如果后端服务有强烈的离线运行需求（即在没有前端推送配置的情况下也能独立工作），则此方案需要调整。但根据当前描述，前后端是紧密协作的。

**4. 前后端交互设计**

*   **通信机制**: 扩展现有的 gRPC 服务。Electron 主进程作为 gRPC 客户端，Python 应用作为 gRPC 服务端。
*   **gRPC 服务与方法定义 (在 `.proto` 文件中)**:

    ```protobuf
    // In a new file like ai_config_service.proto or existing common.proto

    message AIProviderConfigProto {
      string provider_id = 1;
      string provider_type = 2;
      string display_name = 3;
      bool is_enabled = 4;
      map<string, string> credentials = 5; // Sensitive values are plaintext here, relying on TLS for gRPC channel security
      map<string, string> attributes = 6;
      map<string, string> metadata = 7;
    }

    message UpdateAIConfigurationsRequest {
      repeated AIProviderConfigProto configs = 1;
    }

    message UpdateAIConfigurationsResponse {
      bool success = 1;
      string message = 2;
    }

    // This service will be implemented by Python backend
    service AIConfigurationService {
      // Called by Electron frontend to push updated configurations to Python backend
      rpc UpdateAIConfigurations (UpdateAIConfigurationsRequest) returns (UpdateAIConfigurationsResponse);
    }
    ```

*   **数据流与时序**:
    1.  **用户操作**: 用户在 Electron 前端 Vue.js 设置页面创建、修改或删除 AI 服务配置。
    2.  **前端保存**:
        *   Vue.js 组件通过 IPC (e.g., `ipcRenderer.invoke`) 将配置数据发送到 Electron 主进程。
        *   主进程处理数据：
            *   对 `credentials` 中的敏感值使用 `safeStorage.encryptString()` 加密。
            *   使用 `electron-store` 将完整的（包含加密敏感值的）`AIProviderConfig` 对象列表保存到本地 JSON 文件。
    3.  **前端推送配置到后端**:
        *   在保存成功后，或在 Python 后端服务连接建立后，主进程准备将配置推送给后端。
        *   主进程从 `electron-store` 读取配置列表。
        *   对于每个配置中的加密敏感值，使用 `safeStorage.decryptString()` 解密得到明文。
        *   构建 `UpdateAIConfigurationsRequest` protobuf 消息，其中 `AIProviderConfigProto` 的 `credentials` 包含明文敏感值。
        *   Electron 主进程通过 **TLS 加密的 gRPC 通道** 调用 Python 后端实现的 `AIConfigurationService.UpdateAIConfigurations` 方法，发送请求。
    4.  **后端处理与缓存**:
        *   Python gRPC 服务端接收到 `UpdateAIConfigurationsRequest`。
        *   调用内部的配置管理模块（例如，`ai_config_manager.py` 中的函数），将接收到的配置列表（`AIProviderConfigProto` 对象）转换为 Python 内部表示（如 dataclass 或字典）并更新到内存缓存中。
        *   Python 后端返回 `UpdateAIConfigurationsResponse` 给前端。
    5.  **后端使用配置**:
        *   当 `TranscriptThread` 或其他 Python 模块需要调用 AI 服务时：
            *   从内存缓存中查询当前已启用且符合需求的 `AIProviderConfig`。
            *   使用配置中的 API Key (此时是明文，因为是从安全通道接收并存在内存中)、Base URL、Model Name 等参数初始化相应的 AI SDK 客户端并发起调用。

**5. 安全性考虑**

*   **前端存储安全**:
    *   使用 `safeStorage` 对 `electron-store` 中存储的敏感信息（API Keys）进行加密。这是核心保护措施。
*   **前后端传输安全**:
    *   **强制要求 gRPC 通道启用 TLS/SSL 加密**。由于解密后的 API Key 会在 gRPC 消息中传输，信道加密至关重要。
    *   Electron 作为 gRPC 客户端，需要配置为信任服务端的证书（或使用公共 CA 证书）。
    *   Python 作为 gRPC 服务端，需要加载 TLS 证书和私钥。
    *   开发环境可使用自签名证书，生产环境建议使用由受信任 CA 签发的证书。
*   **后端安全**:
    *   **内存持有，不持久化明文**: Python 后端在内存中持有从前端接收的（包含明文 API Key 的）配置。严禁将这些明文 API Key 写入日志、文件或任何持久化存储。
    *   **最小权限原则**: 确保运行 Python 后端服务的进程具有完成其任务所需的最小权限。
    *   **代码安全**: 防范 Python 代码中的漏洞，如不安全的输入处理等。
*   **用户意识**: 提醒用户妥善保管其 AI 服务提供商的 API Key，并理解将其输入到应用中的含义。

**6. 后端改造建议 (Python)**

*   **创建 `ai_config_manager.py` 模块**:
    *   职责: 存储和管理从前端推送的 AI 服务配置。
    *   主要功能:
        *   `update_configs(proto_configs_list)`: 接收 gRPC 消息中的配置列表，清空/更新内存缓存。
        *   `get_config_by_id(provider_id: str) -> Optional[AIProviderConfigPython]`: 按 ID 获取配置。
        *   `get_active_configs_by_type(provider_type: str) -> List[AIProviderConfigPython]`: 获取某种类型下所有已启用的配置。
        *   `get_default_config_by_type(provider_type: str) -> Optional[AIProviderConfigPython]`: 获取某种类型的默认或最高优先级的已启用配置。
    *   内部存储: 一个字典，如 `Dict[str, AIProviderConfigPython]`，其中 `AIProviderConfigPython` 是 protobuf 消息在 Python 中的等效数据类。

*   **修改 `subtitle.config.py`**:
    *   移除通过 `os.getenv` 或硬编码方式加载 `OPENAI_API_KEY` 和 `OPENAI_API_BASE_URL` 的逻辑。这些将由新的 `ai_config_manager` 管理。
    *   其他非 AI 服务凭证的配置（如模型名称、默认语言等）可以继续保留在 `subtitle.config.py` 或其引用的新配置系统（如果该系统用于管理此类应用级配置而非用户提供的服务凭证）。

*   **修改 `subtitle.transcriptThread.TranscriptThread`**:
    *   移除 `__init__` 方法中通过 `sub_config` 设置 `os.environ["OPENAI_API_KEY"]` 等代码。
    *   在其需要调用 AI 服务的方法中（如 `transcribe_audio`, `translate_subtitle_content` 等）：
        1.  从 `ai_config_manager` 获取当前任务所需类型（如 "OpenAI", "DeepSeek"）的已启用配置。
        2.  如果找到多个配置，可能需要根据 `provider_id`（如果用户在前端指定了用于特定任务的服务实例）或某种优先级/默认逻辑来选择一个。
        3.  从选定的配置对象中提取 `api_key`, `api_base_url`, `default_model` 等信息。
        4.  使用这些信息来实例化和配置相应的 AI Python SDK 客户端（例如 `openai.OpenAI(...)`）。
        *   **示例 (伪代码)**:
            ```python
            # In TranscriptThread.transcribe_audio
            from . import ai_config_manager

            # ...
            # Assume user wants to use an "OpenAI" type service for transcription
            active_openai_configs = ai_config_manager.get_active_configs_by_type("OpenAI")
            if not active_openai_configs:
                # Handle error: No enabled OpenAI configuration found
                raise ConfigurationError("No enabled OpenAI service configured.")

            # Simple selection: use the first one. Could be more sophisticated.
            chosen_config = active_openai_configs[0]

            api_key = chosen_config.credentials.get("api_key")
            base_url = chosen_config.attributes.get("api_base_url")
            model = chosen_config.attributes.get("default_model") # Or a model specific to this task

            # Pass these to the core transcription function or SDK
            # result = core_transcribe_function(audio_path, api_key=api_key, base_url=base_url, model=model)
            # ...
            ```

*   **修改核心 AI 调用函数 (e.g., in `subtitle.core.transcribe`, `subtitle.core.translate`)**:
    *   确保这些函数接受 `api_key`, `base_url`, `model` 等作为显式参数，而不是依赖全局变量或环境变量。
    *   移除内部直接读取环境变量 `OPENAI_API_KEY` 的逻辑。

*   **实现 gRPC 服务 `AIConfigurationService`**:
    *   在 `backend/server.py` 或一个专门的 gRPC 服务文件中，实现 `AIConfigurationService`。
    *   `UpdateAIConfigurations` 方法的实现将调用 `ai_config_manager.update_configs()`。

**7. 方案的整体优势**

*   **灵活性与可扩展性**: 新 AI 服务可通过更新前端 Schema 和（如果需要）后端适配逻辑轻松添加。支持同类型服务的多个实例。
*   **安全性**: 通过前端 `safeStorage` 加密、TLS 加密的 gRPC 通道以及后端内存使用策略，最大限度保护敏感 API Key。
*   **用户体验**: 用户在前端图形界面集中管理所有 AI 服务，清晰直观。
*   **维护性与解耦**: 配置管理逻辑清晰分离。Python 后端不再硬编码 API Key，更易于测试、部署和维护。
*   **实时性**: 前端配置更改能迅速同步到后端生效。

**8. 流程图 (Mermaid 示例)**

*   **配置数据流**:
    ```mermaid
    graph TD
        A[用户在前端UI输入配置] --> B{Electron渲染进程};
        B -- IPC --> C{Electron主进程};
        C -- 使用 safeStorage 加密敏感信息 --> D[electron-store 保存加密配置];
        C -- 解密敏感信息 (准备发送) --> E{构建gRPC请求};
        E -- TLS加密的gRPC调用 --> F{Python gRPC服务端};
        F -- 更新配置 --> G[Python内存缓存 (ai_config_manager)];
        H[Python业务逻辑 e.g., TranscriptThread] -- 查询配置 --> G;
        H -- 使用配置调用 --> I[外部AI服务];
---

**9. 前端 UI 设计建议**

*   **9.1. 入口位置与导航**
    *   在应用主界面的**常驻侧边栏导航**中，新增一个专门的导航项作为“AI 服务配置”的入口。
    *   **图标与标签**:
        *   **图标**: 建议使用一个直观的图标，例如大脑图标 (🧠), 云服务图标 (☁️), 芯片/CPU 图标 (💡，如果想抽象点表示智能), 或者一个齿轮与云的组合 (⚙️☁️)。选择一个与应用整体图标风格一致的。
        *   **文本标签**: "AI 服务" 或 "模型配置"。
    *   **位置**: 在侧边栏导航项中，可以根据功能重要性将其放置在合适的位置。如果侧边栏有分组，可以放在“工具”或“高级设置”类似的分组下；如果没有分组，则与其他主要功能视图并列。
    *   **交互**:
        *   当用户点击侧边栏的“AI 服务”导航项时，应用的主内容区域会**切换并显示“AI 服务配置页面”**。
        *   此“AI 服务配置页面”将采用我们之前讨论的两栏布局：左侧为已配置的服务列表，右侧为选中服务的配置详情表单或添加新服务的表单。

*   **9.2. 页面整体布局**
    *   **两栏布局**:
        *   **左侧栏 (服务列表区域)**:
            *   显示当前已配置的所有 AI 服务实例列表。每个列表项应简洁展示：
                *   服务提供商图标 (可选, 如 OpenAI, DeepSeek 的 Logo)。
                *   用户设置的 `display_name`。
                *   `provider_type` (例如 "OpenAI", "Gemini")。
                *   一个表示 `is_enabled` 状态的开关 (Toggle Switch)。
            *   列表项右侧提供“编辑”和“删除”图标按钮。
            *   列表区域顶部或底部提供一个醒目的“添加新服务”按钮。
        *   **右侧主区域 (配置详情/编辑区域)**:
            *   当用户在左侧点击“添加新服务”或某个已存在服务的“编辑”按钮时，此区域会显示相应的配置表单。
            *   如果未选择任何服务（例如初始进入页面时），此区域可以显示一些引导信息或提示。

*   **9.3. “添加/编辑服务”表单设计**
    *   **表单标题**: 根据是“添加”还是“编辑”操作，显示如“添加新的 OpenAI 服务”或“编辑 DeepSeek 配置”。
    *   **通用配置项**:
        *   `服务类型 (Provider Type)`:
            *   添加新服务时：一个下拉选择框，列出所有支持的 `provider_type` (如 "OpenAI", "DeepSeek", "Gemini")。
            *   编辑服务时：通常为只读文本，显示当前服务的类型。
        *   `显示名称 (Display Name)`: 文本输入框，必填。允许用户为这个服务实例起一个易于识别的名称。
        *   `服务 ID (Provider ID)`:
            *   可以设计为用户可编辑的文本框，但需要校验唯一性。
            *   或者，系统可以根据用户输入的显示名称和类型自动生成一个建议的、唯一的 ID (例如 `openai_my_main_key_1`), 并允许高级用户修改。
        *   `是否启用 (Is Enabled)`: 一个开关 (Toggle Switch)。

    *   **特定于服务类型的配置项 (动态加载)**:
        *   当用户选择了“服务类型”后，表单下方会动态展示该类型对应的 `credentials` 和 `attributes` 输入字段。
        *   **凭证 (`credentials`) 部分**:
            *   `API Key`: 密码类型的输入框 (输入内容显示为 `••••••`)。旁边可以有一个小图标按钮，点击后可以临时显示/隐藏实际的 Key 内容。应有明确提示此字段的敏感性。
            *   其他如 `API Secret` (如果需要) 也类似处理。
        *   **属性 (`attributes`) 部分**:
            *   `API Base URL`: 文本输入框。可以有 placeholder 显示官方默认地址，如果用户不填则使用默认。
            *   `默认模型 (Default Model)`: 文本输入框。如果可能，未来可以增强为从该服务商动态获取可用模型列表的下拉选择框。
            *   `超时时间 (Timeout Seconds)`: 数字输入框。
            *   其他属性根据其数据类型（文本、数字、布尔、枚举等）使用合适的表单控件。
        *   **字段描述**: 每个输入字段旁边或下方应有简短的文字描述或提示图标，解释该字段的含义、格式要求或是否可选。

    *   **表单操作按钮**:
        *   “保存”按钮: 校验表单输入，如果通过则保存配置。
        *   “取消”按钮: 关闭表单，不保存更改。
        *   (可选) “测试连接”按钮: 对于某些服务，可以在用户输入完凭证后，尝试发起一个简单的 API 请求（如查询余额、列出模型）来验证配置是否正确。测试结果应即时反馈给用户。

*   **9.4. 列表交互与视觉反馈**
    *   **拖拽排序 (可选)**: 如果用户配置的服务较多，可以考虑支持在左侧服务列表中拖拽排序，以调整服务的默认调用优先级（如果业务逻辑需要）。
    *   **状态指示**: 清晰地通过颜色、图标或文本指示服务的启用/禁用状态，以及“测试连接”的结果（如果实现）。
    *   **错误提示**: 表单校验失败、保存失败等情况，应在界面上给出清晰、友好的错误提示信息。

*   **9.5. 技术实现提示 (Vue.js)**
    *   可以使用如 Element Plus, Vuetify, Naive UI 等成熟的 Vue UI 组件库来快速构建界面。
    *   动态表单的实现可以基于一个预定义的 JSON Schema（描述各种 `provider_type` 的字段），然后编写一个 Vue 组件根据 Schema 动态渲染出表单元素。
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 服务测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .service-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .service-enabled {
            border-color: #4CAF50;
            background: #f0fff0;
        }
        .service-disabled {
            border-color: #f44336;
            background: #fff0f0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI 服务配置测试</h1>
        
        <div class="status" id="status">
            准备测试 AI 服务配置...
        </div>

        <button onclick="loadAIServices()">🔄 加载 AI 服务</button>
        <button onclick="testOptimizeStep()">🧪 测试优化步骤</button>
        
        <div id="services-container">
            <h2>AI 服务列表</h2>
            <div id="services-list">
                点击"加载 AI 服务"按钮开始...
            </div>
        </div>

        <div id="optimize-test">
            <h2>优化步骤测试</h2>
            <div id="optimize-result">
                点击"测试优化步骤"按钮开始...
            </div>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        async function loadAIServices() {
            try {
                updateStatus('正在加载 AI 服务配置...', 'info');
                
                // 模拟调用 electronAPI
                if (typeof window !== 'undefined' && window.electronAPI) {
                    const configs = await window.electronAPI.invoke('load-ai-configs');
                    
                    if (Array.isArray(configs)) {
                        displayServices(configs);
                        updateStatus(`成功加载 ${configs.length} 个 AI 服务配置`, 'success');
                    } else {
                        updateStatus('AI 服务配置格式不正确', 'error');
                    }
                } else {
                    updateStatus('electronAPI 不可用，这是一个测试页面', 'error');
                    // 模拟数据用于测试
                    const mockConfigs = [
                        {
                            provider_id: 'openai-test',
                            provider_type: 'OpenAI',
                            display_name: 'OpenAI GPT-4',
                            is_enabled: true,
                            attributes: {
                                default_model: 'gpt-4o',
                                api_base_url: 'https://api.openai.com/v1'
                            }
                        },
                        {
                            provider_id: 'gemini-test',
                            provider_type: 'Gemini',
                            display_name: 'Google Gemini',
                            is_enabled: true,
                            attributes: {
                                default_model: 'gemini-2.0-flash'
                            }
                        }
                    ];
                    displayServices(mockConfigs);
                    updateStatus('使用模拟数据测试 (electronAPI 不可用)', 'success');
                }
            } catch (error) {
                updateStatus(`加载失败: ${error.message}`, 'error');
                console.error('加载 AI 服务时出错:', error);
            }
        }

        function displayServices(configs) {
            const listEl = document.getElementById('services-list');
            
            if (!configs || configs.length === 0) {
                listEl.innerHTML = '<p>未找到 AI 服务配置</p>';
                return;
            }

            const enabledServices = configs.filter(config => config.is_enabled);
            
            listEl.innerHTML = `
                <p><strong>总计:</strong> ${configs.length} 个服务，<strong>已启用:</strong> ${enabledServices.length} 个</p>
                ${configs.map(service => `
                    <div class="service-item ${service.is_enabled ? 'service-enabled' : 'service-disabled'}">
                        <h3>${service.display_name} (${service.provider_type})</h3>
                        <p><strong>ID:</strong> ${service.provider_id}</p>
                        <p><strong>状态:</strong> ${service.is_enabled ? '✅ 已启用' : '❌ 已禁用'}</p>
                        ${service.attributes?.default_model ? `<p><strong>默认模型:</strong> ${service.attributes.default_model}</p>` : ''}
                        ${service.attributes?.api_base_url ? `<p><strong>API 地址:</strong> ${service.attributes.api_base_url}</p>` : ''}
                    </div>
                `).join('')}
            `;
        }

        function testOptimizeStep() {
            const resultEl = document.getElementById('optimize-result');
            resultEl.innerHTML = `
                <p>🧪 模拟优化步骤测试...</p>
                <p>✅ AI 服务选择器应该显示已启用的服务</p>
                <p>✅ 服务信息应该正确显示模型和 API 地址</p>
                <p>✅ 点击"前往AI设置页面配置"应该能跳转到设置页面</p>
                <p><em>在实际应用中测试这些功能...</em></p>
            `;
        }

        // 页面加载时自动测试
        window.addEventListener('load', () => {
            updateStatus('页面已加载，准备测试...', 'info');
        });
    </script>
</body>
</html>

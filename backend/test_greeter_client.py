# backend/test_greeter_client.py

import grpc
import sys
import os

# 将项目根目录添加到 sys.path，以便可以找到 api_protos 模块
# 这使得脚本无论从项目根目录还是从 backend 目录运行都能正确导入
# 例如:
# 从项目根目录运行: python backend/test_greeter_client.py
# 从 backend 目录运行: cd backend; python test_greeter_client.py
try:
    # __file__ 是当前脚本的路径
    # os.path.dirname(__file__) 是脚本所在的目录 (e.g., /path/to/project/backend)
    # os.path.abspath(...) 确保路径是绝对的
    # os.path.join(..., '..') 获取父目录 (e.g., /path/to/project)
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
except NameError:
    # 如果在交互式解释器中运行 __file__ 可能未定义，
    # 这种情况下假设当前工作目录是项目根目录或已相应设置 PYTHONPATH
    print("警告: __file__ 未定义，假设 sys.path 已正确配置以查找 api_protos。")


# 导入 protobuf 生成的模块
# 这些模块是由 .proto 文件编译而来，定义了服务和消息类型
try:
    from api_protos.v1.greeter import greeter_pb2
    from api_protos.v1.greeter import greeter_pb2_grpc
except ImportError as e:
    print(f"错误：无法导入 protobuf 模块。请确保：")
    print(f"1. .proto 文件已编译成 Python 文件 (greeter_pb2.py, greeter_pb2_grpc.py)。")
    print(f"2. 它们位于 'api_protos/v1/greeter/' 目录下。")
    print(f"3. 项目根目录（包含 'api_protos' 的目录）在 PYTHONPATH 中，或者此脚本的 sys.path 修改有效。")
    print(f"ImportError: {e}")
    sys.exit(1)

# 定义运行 gRPC 客户端的函数
def run_greeter_client():
    """
    连接到 Greeter gRPC 服务，发送一个请求，并打印响应。
    """
    # Greeter 服务运行的地址和端口
    server_address = 'localhost:50051'
    
    print(f"客户端：尝试连接到 gRPC 服务器 {server_address}...")

    try:
        # 创建一个到服务器的 gRPC 通道 (channel)
        # grpc.insecure_channel 用于创建不使用 TLS 加密的连接
        # 使用 'with' 语句可以确保通道在使用完毕后自动关闭
        with grpc.insecure_channel(server_address) as channel:
            # 创建 Greeter 服务的存根 (stub)
            # 存根是客户端用于调用服务器方法的对象
            stub = greeter_pb2_grpc.GreeterStub(channel)

            # 构造 HelloRequest 消息
            # HelloRequest 是在 greeter.proto 中定义的消息类型
            user_name = "Test User from Python Client"
            request = greeter_pb2.HelloRequest(name=user_name)

            print(f"客户端：向 Greeter 服务发送 SayHello 请求，名称: '{user_name}'")

            # 调用服务器上的 SayHello RPC 方法
            # 这是一个阻塞调用，会等待服务器的响应
            response = stub.SayHello(request)

            # 打印服务器返回的 HelloReply 消息
            # HelloReply 也是在 greeter.proto 中定义的消息类型
            print(f"客户端：从服务器收到响应: '{response.message}'")

    except grpc.RpcError as e:
        # 捕获并打印 gRPC 相关的错误 (例如，连接失败，服务器错误等)
        # e.code() 返回错误代码 (例如 grpc.StatusCode.UNAVAILABLE)
        # e.details() 返回错误的详细描述
        print(f"客户端：调用 Greeter 服务时发生 RPC 错误:")
        print(f"  状态码: {e.code()}")
        print(f"  详情: {e.details()}")
        print(f"  请确保 gRPC 服务器 ([`backend/server.py`](backend/server.py:53)) 正在运行于 {server_address}")
    except Exception as e:
        # 捕获其他可能的 Python 异常
        print(f"客户端：发生意外错误: {e}")

# 脚本的主执行块
# 当脚本直接运行时 (而不是作为模块导入时)，以下代码块会执行
if __name__ == '__main__':
    print("客户端：启动 Greeter gRPC 客户端测试脚本...")
    run_greeter_client()
    print("客户端：Greeter gRPC 客户端测试脚本执行完毕。")
from concurrent import futures
import grpc
import time
import logging
import sys # 用于确保日志能被 Electron 捕获
import datetime

# 导入新生成的 gRPC Python 代码

# Greeter service
from api_protos.v1.greeter import greeter_pb2
from api_protos.v1.greeter import greeter_pb2_grpc
# Subtitler service (needed for add_SubtitlerServicer_to_server)
from api_protos.v1.subtitler import subtitler_pb2_grpc
from api_protos.v1.ai_config import ai_config_service_pb2_grpc

# 导入新的服务实现
from subtitle_service import SubtitleServiceImpl
from ai_config_service import AIConfigurationServiceServicer

class GreeterServicer(greeter_pb2_grpc.GreeterServicer): # 修改基类
    def SayHello(self, request, context): # 方法签名不变
        logging.info(f"Received SayHello request with name: {request.name}")
        # 确保日志输出到 stdout，以便 Electron 捕获
        print(f"Python Log: Received SayHello request with name: {request.name}", flush=True)
        
        original_name = request.name
        server_id_str = "Python Server" 
        current_timestamp_str = datetime.datetime.now().isoformat()

        # 您可以自定义问候消息的格式
        response_message_str = f"Hello {original_name} from {server_id_str}!"
       
        # 使用新的 greeter_pb2 来创建 Reply 对象
        reply = greeter_pb2.HelloReply(
            message=response_message_str,
            original_request=original_name,
            server_id=server_id_str,
            timestamp=current_timestamp_str
        )
        logging.info(f"Python Server Prepared Reply: message='{reply.message}', original_request='{reply.original_request}', server_id='{reply.server_id}', timestamp='{reply.timestamp}'")
        print(f"Python Server Prepared Reply: message='{reply.message}', original_request='{reply.original_request}', server_id='{reply.server_id}', timestamp='{reply.timestamp}'", flush=True)
        return reply

def serve():
    # 配置日志记录到 stdout
    logging.basicConfig(stream=sys.stdout, level=logging.INFO, format='%(asctime)s - PEX - %(levelname)s - %(message)s')
    
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    # 使用新的 greeter_pb2_grpc 来注册 Greeter 服务
    greeter_pb2_grpc.add_GreeterServicer_to_server(GreeterServicer(), server)
    # 注册 Subtitler 服务
    subtitler_pb2_grpc.add_SubtitlerServicer_to_server(SubtitleServiceImpl(), server)
    ai_config_service_pb2_grpc.add_AIConfigurationServiceServicer_to_server(AIConfigurationServiceServicer(), server)
    
    port = '50051'
    server.add_insecure_port(f'[::]:{port}')
    
    print(f"Python gRPC server starting on port {port}...", flush=True)
    logging.info(f"gRPC server starting on port {port}...")
    server.start()
    print(f"Python gRPC server started and listening on port {port}.", flush=True)
    logging.info(f"gRPC server started and listening on port {port}.")
    
    try:
        while True:
            time.sleep(86400)  # 一天
    except KeyboardInterrupt:
        print("Python gRPC server stopping...", flush=True)
        logging.info("gRPC server stopping...")
        server.stop(0)
        print("Python gRPC server stopped.", flush=True)
        logging.info("gRPC server stopped.")

if __name__ == '__main__':
    serve()
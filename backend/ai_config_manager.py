# backend/ai_config_manager.py
import logging
from typing import Dict, List, Optional
from api_protos.v1.ai_config import ai_config_service_pb2
# 获取模块级 logger
logger = logging.getLogger(__name__)

# 我们可以定义一个 Python 类或 dataclass 来更好地表示配置项，而不是直接使用字典
# 例如:
# from dataclasses import dataclass, field
#
# @dataclass
# class AIProviderConfig:
#     provider_id: str
#     provider_type: str
#     display_name: str
#     is_enabled: bool
#     credentials: Dict[str, str] = field(default_factory=dict)
#     attributes: Dict[str, str] = field(default_factory=dict)
#     metadata: Dict[str, str] = field(default_factory=dict)

# _active_configs: Dict[str, AIProviderConfig] = {}
_active_configs: Dict[str, ai_config_service_pb2.AIProviderConfigProto] = {}

def convert_proto_to_python_config(config_proto: ai_config_service_pb2.AIProviderConfigProto) -> ai_config_service_pb2.AIProviderConfigProto:
    """
    目前此函数直接返回 protobuf 对象。
    如果需要转换为自定义的 Python 类或字典，可以在这里实现。
    """
    # 示例：如果直接用字典
    # return {
    #     "provider_id": config_proto.provider_id,
    #     "provider_type": config_proto.provider_type,
    #     "display_name": config_proto.display_name,
    #     "is_enabled": config_proto.is_enabled,
    #     "credentials": dict(config_proto.credentials),
    #     "attributes": dict(config_proto.attributes),
    #     "metadata": dict(config_proto.metadata),
    # }
    # 如果使用 dataclass:
    # return AIProviderConfig(
    #     provider_id=config_proto.provider_id,
    #     # ... 其他字段 ...
    # )
    logger.debug(f"Converting proto config: {config_proto.provider_id if hasattr(config_proto, 'provider_id') else 'Unknown ID'}")
    # 这是一个简化版本，直接返回 proto 对象或其字典形式，具体取决于后续使用方式
    # 理想情况下，这里会转换为一个定义好的 Python 类实例
    return config_proto # 或显式转换为字典，如果 proto 对象不直接适用

def update_configurations(configs_proto_list: List[ai_config_service_pb2.AIProviderConfigProto]):
    """
    更新内存中缓存的 AI 服务配置。
    configs_proto_list 是从 gRPC 请求中获取的 AIProviderConfigProto 对象列表。
    """
    global _active_configs
    new_configs_cache = {}
    if configs_proto_list:
        for config_proto in configs_proto_list:
            py_config = convert_proto_to_python_config(config_proto)
            # 使用 provider_id 作为键
            # 确保 py_config 有 provider_id 属性或键
            provider_id = py_config.provider_id
            if provider_id:
                new_configs_cache[provider_id] = py_config
            else:
                logger.warning(f"Received a config without a provider_id: {py_config}")
    
    _active_configs = new_configs_cache
    logger.info(f"AI configurations updated. Total active configurations: {len(_active_configs)}")
    if _active_configs:
        logger.info(f"Current active config IDs: {list(_active_configs.keys())}")
        for config_id, config in _active_configs.items():
            logger.info(f"  Config {config_id}: {config.display_name} ({config.provider_type}) - Enabled: {config.is_enabled}")
    else:
        logger.warning("No AI configurations are currently active!")

def get_config_by_id(provider_id: str) -> Optional[ai_config_service_pb2.AIProviderConfigProto]:
    """按 provider_id 获取单个配置。"""
    config = _active_configs.get(provider_id)
    if config:
        logger.debug(f"Retrieved config for ID '{provider_id}': {config.display_name}")
    else:
        logger.debug(f"No config found for ID '{provider_id}'")
    return config

def get_active_configs_by_type(provider_type: str) -> List[ai_config_service_pb2.AIProviderConfigProto]:
    """获取指定类型的所有已启用配置。"""
    # 确保配置对象有 provider_type 和 is_enabled 属性
    filtered_configs = [
        conf for conf in _active_configs.values()
        if conf.provider_type == provider_type and conf.is_enabled
    ]
    logger.debug(f"Found {len(filtered_configs)} active configs for type '{provider_type}'")
    return filtered_configs

def get_all_configs() -> Dict[str, ai_config_service_pb2.AIProviderConfigProto]:
    """获取所有当前缓存的配置。"""
    return _active_configs.copy()

# 可以在这里添加更多辅助函数，例如获取某种类型的默认配置等。
# backend/ai_config_service.py
import logging
import grpc

from api_protos.v1.ai_config import ai_config_service_pb2
from api_protos.v1.ai_config import ai_config_service_pb2_grpc
import ai_config_manager

logger = logging.getLogger(__name__)

class AIConfigurationServiceServicer(ai_config_service_pb2_grpc.AIConfigurationServiceServicer):
    """
    gRPC 服务实现，用于管理 AI 服务配置。
    """
    def UpdateAIConfigurations(self, request: ai_config_service_pb2.UpdateAIConfigurationsRequest, context: grpc.ServicerContext) -> ai_config_service_pb2.UpdateAIConfigurationsResponse:
        """
        接收前端推送的 AI 服务配置列表，并更新到 ai_config_manager。
        """
        try:
            logger.info(f"Received UpdateAIConfigurations request with {len(request.configs)} configurations.")
            if not request.configs:
                logger.warning("UpdateAIConfigurations called with an empty list of configs.")
                # Decide if this is an error or just a no-op to clear configs
                # For now, assume it means to clear existing configs if the list is empty.
                # ai_config_manager.update_configurations([]) 
                # Or, if an empty list is not for clearing, return a specific message.
                # For now, let's treat it as a valid update that might result in zero configs.

            ai_config_manager.update_configurations(request.configs)

            # Log details of received configs for debugging
            for config in request.configs:
                logger.info(f"Received config - ID: {config.provider_id}, Type: {config.provider_type}, Enabled: {config.is_enabled}, Name: {config.display_name}")
                logger.info(f"  Credentials keys: {list(config.credentials.keys())}")
                logger.info(f"  Attributes: {dict(config.attributes)}")

            return ai_config_service_pb2.UpdateAIConfigurationsResponse(
                success=True,
                message="AI configurations updated successfully."
            )
        except Exception as e:
            logger.error(f"Error processing UpdateAIConfigurations: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {e}")
            return ai_config_service_pb2.UpdateAIConfigurationsResponse(
                success=False,
                message=f"Failed to update AI configurations: {e}"
            )
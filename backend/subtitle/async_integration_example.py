# -*- coding: utf-8 -*-
"""
异步处理集成示例

展示如何在现有代码中集成异步处理功能。
"""
import asyncio
import time
from typing import Dict, Any, List, AsyncGenerator

from .async_processor import get_async_task_manager, submit_async_task, TaskPriority
from .async_stream_processor import get_async_stream_processor
from .async_batch_processor import get_async_batch_processor
from .async_grpc_service import get_async_subtitle_service
from .logging_manager import get_logger
from .performance_monitor import monitor_performance

logger = get_logger(__name__)


class AsyncIntegratedSubtitleService:
    """集成异步处理的字幕服务示例"""
    
    def __init__(self):
        self.task_manager = get_async_task_manager()
        self.stream_processor = get_async_stream_processor()
        self.batch_processor = get_async_batch_processor()
        self.grpc_service = get_async_subtitle_service()
        
        logger.info("异步集成字幕服务初始化完成")
    
    async def start(self):
        """启动异步服务"""
        await self.task_manager.start()
        await self.grpc_service.start()
        logger.info("异步集成字幕服务已启动")
    
    async def stop(self):
        """停止异步服务"""
        await self.task_manager.stop()
        await self.grpc_service.stop()
        logger.info("异步集成字幕服务已停止")
    
    @monitor_performance(operation_name="async_single_video_processing")
    async def process_single_video_async(
        self,
        video_path: str,
        config: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """异步单个视频处理"""
        
        logger.info(f"开始异步视频处理: {video_path}")
        
        try:
            # 创建处理流
            stream_id = await self.stream_processor.create_stream(
                metadata={"operation": "single_video", "video_path": video_path}
            )
            
            # 流式处理
            async for progress in self.stream_processor.process_complete_workflow_stream(
                stream_id, video_path, config
            ):
                yield {
                    "type": "progress",
                    "stream_id": stream_id,
                    "stage": progress.stage_name,
                    "percentage": progress.percentage,
                    "message": progress.message,
                    "is_error": progress.is_error,
                    "error_message": progress.error_message,
                    "result": progress.metadata.get("result")
                }
            
        except Exception as e:
            logger.error(f"异步视频处理失败: {e}", exc_info=True)
            yield {
                "type": "error",
                "message": f"视频处理失败: {str(e)}",
                "error": str(e)
            }
    
    @monitor_performance(operation_name="async_batch_video_processing")
    async def process_video_batch_async(
        self,
        video_files: List[str],
        config: Dict[str, Any],
        batch_id: str = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """异步批量视频处理"""
        
        logger.info(f"开始异步批量视频处理: {len(video_files)}个文件")
        
        try:
            # 批量处理
            async for progress in self.batch_processor.process_video_batch(
                video_files, config, batch_id
            ):
                yield {
                    "type": "batch_progress",
                    "batch_id": progress.get("batch_id"),
                    "stage": progress.get("stage"),
                    "progress": progress.get("progress", 0),
                    "message": progress.get("message"),
                    "completed_items": progress.get("completed_items", 0),
                    "failed_items": progress.get("failed_items", 0),
                    "success_rate": progress.get("success_rate", 0),
                    "is_error": progress.get("is_error", False),
                    "final_result": progress.get("final_result")
                }
            
        except Exception as e:
            logger.error(f"异步批量视频处理失败: {e}", exc_info=True)
            yield {
                "type": "error",
                "message": f"批量处理失败: {str(e)}",
                "error": str(e)
            }
    
    @monitor_performance(operation_name="async_folder_processing")
    async def process_folder_async(
        self,
        folder_path: str,
        file_extensions: List[str],
        config: Dict[str, Any]
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """异步文件夹处理"""
        
        logger.info(f"开始异步文件夹处理: {folder_path}")
        
        try:
            # 文件夹批量处理
            async for progress in self.batch_processor.process_folder_batch(
                folder_path, file_extensions, config
            ):
                yield {
                    "type": "folder_progress",
                    "batch_id": progress.get("batch_id"),
                    "stage": progress.get("stage"),
                    "progress": progress.get("progress", 0),
                    "message": progress.get("message"),
                    "completed_items": progress.get("completed_items", 0),
                    "failed_items": progress.get("failed_items", 0),
                    "is_error": progress.get("is_error", False),
                    "final_result": progress.get("final_result")
                }
            
        except Exception as e:
            logger.error(f"异步文件夹处理失败: {e}", exc_info=True)
            yield {
                "type": "error",
                "message": f"文件夹处理失败: {str(e)}",
                "error": str(e)
            }
    
    async def submit_background_task(
        self,
        task_func,
        *args,
        priority: TaskPriority = TaskPriority.NORMAL,
        timeout: float = None,
        use_process_pool: bool = False,
        **kwargs
    ) -> str:
        """提交后台任务"""
        
        task_id = await submit_async_task(
            task_func,
            *args,
            priority=priority,
            timeout=timeout,
            use_process_pool=use_process_pool,
            **kwargs
        )
        
        logger.info(f"后台任务已提交: {task_id}")
        return task_id
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        result = self.task_manager.get_task_result(task_id)
        if result:
            return {
                "task_id": task_id,
                "status": result.status.value,
                "progress": result.progress,
                "start_time": result.start_time,
                "end_time": result.end_time,
                "duration": result.duration,
                "result": result.result,
                "error": str(result.error) if result.error else None
            }
        return {"task_id": task_id, "status": "not_found"}
    
    async def get_stream_status(self, stream_id: str) -> Dict[str, Any]:
        """获取流状态"""
        stream_info = self.stream_processor.get_stream_info(stream_id)
        if stream_info:
            return {
                "stream_id": stream_id,
                "status": stream_info["status"].value,
                "progress": stream_info["progress"],
                "current_stage": stream_info["current_stage"],
                "created_time": stream_info["created_time"],
                "last_update": stream_info["last_update"]
            }
        return {"stream_id": stream_id, "status": "not_found"}
    
    async def get_batch_status(self, batch_id: str) -> Dict[str, Any]:
        """获取批处理状态"""
        batch_result = self.batch_processor.get_batch_result(batch_id)
        if batch_result:
            return {
                "batch_id": batch_id,
                "status": batch_result.status.value,
                "total_items": batch_result.total_items,
                "completed_items": batch_result.completed_items,
                "failed_items": batch_result.failed_items,
                "progress_percentage": batch_result.progress_percentage,
                "success_rate": batch_result.success_rate,
                "duration": batch_result.duration
            }
        return {"batch_id": batch_id, "status": "not_found"}
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        return await self.task_manager.cancel_task(task_id)
    
    async def cancel_stream(self, stream_id: str) -> bool:
        """取消流"""
        return await self.stream_processor.cancel_stream(stream_id)
    
    async def cancel_batch(self, batch_id: str) -> bool:
        """取消批处理"""
        return await self.batch_processor.cancel_batch(batch_id)
    
    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计"""
        return {
            "task_manager": self.task_manager.get_stats(),
            "batch_processor": self.batch_processor.get_stats(),
            "active_streams": len(self.stream_processor.get_active_streams()),
            "active_batches": len(self.batch_processor.get_active_batches())
        }


async def demonstrate_async_integration():
    """演示异步集成功能"""
    
    # 创建异步服务
    service = AsyncIntegratedSubtitleService()
    await service.start()
    
    try:
        print("=== 异步处理集成演示 ===\n")
        
        # 1. 单个视频异步处理
        print("1. 单个视频异步处理:")
        video_path = "/path/to/video.mp4"
        config = {
            "model": "JIANYING",
            "language": "zh",
            "enable_translation": True,
            "target_language": "English"
        }
        
        async for progress in service.process_single_video_async(video_path, config):
            print(f"   进度: {progress}")
            if progress.get("type") == "error":
                break
        
        print()
        
        # 2. 批量视频异步处理
        print("2. 批量视频异步处理:")
        video_files = [
            "/path/to/video1.mp4",
            "/path/to/video2.mp4",
            "/path/to/video3.mp4"
        ]
        
        async for progress in service.process_video_batch_async(video_files, config):
            print(f"   批处理进度: {progress}")
            if progress.get("type") == "error":
                break
        
        print()
        
        # 3. 文件夹异步处理
        print("3. 文件夹异步处理:")
        folder_path = "/path/to/videos"
        file_extensions = ["mp4", "avi", "mov"]
        
        async for progress in service.process_folder_async(folder_path, file_extensions, config):
            print(f"   文件夹处理进度: {progress}")
            if progress.get("type") == "error":
                break
        
        print()
        
        # 4. 后台任务提交
        print("4. 后台任务提交:")
        
        def sample_task(data: str) -> str:
            time.sleep(2)  # 模拟处理时间
            return f"处理完成: {data}"
        
        task_id = await service.submit_background_task(
            sample_task,
            "测试数据",
            priority=TaskPriority.HIGH,
            timeout=10.0
        )
        
        print(f"   任务已提交: {task_id}")
        
        # 等待任务完成
        for i in range(10):
            status = await service.get_task_status(task_id)
            print(f"   任务状态: {status}")
            
            if status.get("status") in ["completed", "failed"]:
                break
            
            await asyncio.sleep(1)
        
        print()
        
        # 5. 系统统计
        print("5. 系统统计:")
        stats = service.get_system_stats()
        print(f"   系统统计: {stats}")
        
    finally:
        await service.stop()


def integration_guide():
    """
    异步处理集成指南
    
    展示如何将异步处理集成到现有代码中。
    """
    
    # 1. 基本异步任务提交
    """
    # 导入异步模块
    from subtitle.async_processor import submit_async_task, TaskPriority
    
    # 提交异步任务
    task_id = await submit_async_task(
        your_function,
        arg1, arg2,
        priority=TaskPriority.HIGH,
        timeout=300,
        use_process_pool=True  # CPU密集型任务
    )
    
    # 等待任务完成
    result = await wait_for_task(task_id)
    """
    
    # 2. 流式处理
    """
    from subtitle.async_stream_processor import get_async_stream_processor
    
    stream_processor = get_async_stream_processor()
    
    # 创建处理流
    stream_id = await stream_processor.create_stream()
    
    # 流式处理
    async for progress in stream_processor.process_audio_transcription_stream(
        stream_id, audio_path, config
    ):
        # 处理进度更新
        print(f"进度: {progress.percentage}% - {progress.message}")
        
        if progress.is_error:
            print(f"错误: {progress.error_message}")
            break
    """
    
    # 3. 批量处理
    """
    from subtitle.async_batch_processor import get_async_batch_processor
    
    batch_processor = get_async_batch_processor()
    
    # 批量视频处理
    async for progress in batch_processor.process_video_batch(
        video_files, config
    ):
        print(f"批处理进度: {progress}")
    """
    
    # 4. gRPC异步服务
    """
    from subtitle.async_grpc_service import get_async_subtitle_service
    
    async def serve():
        service = get_async_subtitle_service()
        await service.start()
        
        # 启动gRPC服务器
        server = grpc.aio.server()
        # 添加服务到服务器
        # server.add_insecure_port('[::]:50051')
        # await server.start()
        # await server.wait_for_termination()
    """
    
    # 5. 监控和管理
    """
    # 获取任务状态
    task_result = get_task_result(task_id)
    
    # 获取流状态
    stream_info = stream_processor.get_stream_info(stream_id)
    
    # 获取批处理状态
    batch_result = batch_processor.get_batch_result(batch_id)
    
    # 取消操作
    await cancel_task(task_id)
    await stream_processor.cancel_stream(stream_id)
    await batch_processor.cancel_batch(batch_id)
    """


if __name__ == "__main__":
    asyncio.run(demonstrate_async_integration())

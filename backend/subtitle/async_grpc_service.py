# -*- coding: utf-8 -*-
"""
异步gRPC服务集成

提供异步gRPC服务实现，支持流式响应和并发处理。
"""
import asyncio
import grpc
from typing import AsyncGenerator, Dict, Any, Optional
import logging

from .async_stream_processor import get_async_stream_processor, StreamProgress
from .async_processor import get_async_task_manager, TaskPriority
from .logging_manager import get_logger
from .performance_monitor import monitor_performance
from .exceptions import SubtitleProcessingError, create_progress_error

# 这里应该导入实际的protobuf定义
# from api_protos.v1.subtitler import subtitler_pb2
# from api_protos.v1.subtitler import subtitler_pb2_grpc

logger = get_logger(__name__)


class AsyncSubtitleService:
    """异步字幕服务"""
    
    def __init__(self):
        self.stream_processor = get_async_stream_processor()
        self.task_manager = get_async_task_manager()
        
        logger.info("异步字幕服务初始化完成")
    
    async def start(self):
        """启动异步服务"""
        await self.task_manager.start()
        logger.info("异步字幕服务已启动")
    
    async def stop(self):
        """停止异步服务"""
        await self.task_manager.stop()
        logger.info("异步字幕服务已停止")
    
    @monitor_performance(operation_name="async_video_to_audio")
    async def VideoToAudioAsync(
        self,
        request,  # subtitler_pb2.VideoToAudioRequest
        context: grpc.aio.ServicerContext
    ) -> AsyncGenerator[Any, None]:  # AsyncGenerator[subtitler_pb2.VideoToAudioResponse, None]
        """异步视频转音频流式响应"""
        
        video_path = request.video_path
        logger.info(f"收到异步视频转音频请求: {video_path}")
        
        try:
            # 创建处理流
            stream_id = await self.stream_processor.create_stream(
                metadata={"operation": "video_to_audio", "video_path": video_path}
            )
            
            # 配置参数
            config = {
                "output_format": getattr(request, 'output_format', 'wav'),
                "sample_rate": getattr(request, 'sample_rate', 44100),
                "channels": getattr(request, 'channels', 2)
            }
            
            # 流式处理
            async for progress in self.stream_processor.process_video_to_audio_stream(
                stream_id, video_path, config
            ):
                # 转换为protobuf响应
                response = self._create_video_to_audio_response(progress)
                yield response
                
                # 检查客户端是否取消
                if context.cancelled():
                    await self.stream_processor.cancel_stream(stream_id)
                    break
            
        except Exception as e:
            logger.error(f"异步视频转音频失败: {e}", exc_info=True)
            
            # 发送错误响应
            error_progress = StreamProgress(
                stream_id="error",
                stage_name="处理错误",
                percentage=0,
                message=f"视频转音频失败: {str(e)}",
                is_error=True,
                error_message=str(e)
            )
            
            response = self._create_video_to_audio_response(error_progress)
            yield response
    
    @monitor_performance(operation_name="async_audio_to_text")
    async def AudioToTextAsync(
        self,
        request,  # subtitler_pb2.AudioToTextRequest
        context: grpc.aio.ServicerContext
    ) -> AsyncGenerator[Any, None]:  # AsyncGenerator[subtitler_pb2.AudioToTextResponse, None]
        """异步音频转录流式响应"""
        
        audio_path = getattr(request, 'audio_path', '')
        logger.info(f"收到异步音频转录请求: {audio_path}")
        
        try:
            # 创建处理流
            stream_id = await self.stream_processor.create_stream(
                metadata={"operation": "audio_to_text", "audio_path": audio_path}
            )
            
            # 配置参数
            config = {
                "model": getattr(request, 'model', 'JIANYING'),
                "language": getattr(request, 'language', 'zh'),
                "use_cache": getattr(request, 'use_cache', True),
                "need_word_timestamp": getattr(request, 'need_word_timestamp', True)
            }
            
            # 流式处理
            async for progress in self.stream_processor.process_audio_transcription_stream(
                stream_id, audio_path, config
            ):
                # 转换为protobuf响应
                response = self._create_audio_to_text_response(progress)
                yield response
                
                # 检查客户端是否取消
                if context.cancelled():
                    await self.stream_processor.cancel_stream(stream_id)
                    break
            
        except Exception as e:
            logger.error(f"异步音频转录失败: {e}", exc_info=True)
            
            # 发送错误响应
            error_progress = StreamProgress(
                stream_id="error",
                stage_name="转录错误",
                percentage=0,
                message=f"音频转录失败: {str(e)}",
                is_error=True,
                error_message=str(e)
            )
            
            response = self._create_audio_to_text_response(error_progress)
            yield response
    
    @monitor_performance(operation_name="async_optimize_subtitles")
    async def OptimizeSubtitlesAsync(
        self,
        request,  # subtitler_pb2.OptimizeSubtitlesRequest
        context: grpc.aio.ServicerContext
    ) -> AsyncGenerator[Any, None]:  # AsyncGenerator[subtitler_pb2.OptimizeSubtitlesResponse, None]
        """异步字幕优化流式响应"""
        
        subtitle_content = getattr(request, 'subtitle_content', '')
        logger.info(f"收到异步字幕优化请求: {len(subtitle_content)}字符")
        
        try:
            # 创建处理流
            stream_id = await self.stream_processor.create_stream(
                metadata={"operation": "optimize_subtitles", "content_length": len(subtitle_content)}
            )
            
            # 配置参数
            config = {
                "model": getattr(request, 'model', 'gemini-2.5-flash'),
                "optimization_level": getattr(request, 'optimization_level', 'standard'),
                "preserve_timing": getattr(request, 'preserve_timing', True)
            }
            
            # 流式处理
            async for progress in self.stream_processor.process_subtitle_optimization_stream(
                stream_id, subtitle_content, config
            ):
                # 转换为protobuf响应
                response = self._create_optimize_subtitles_response(progress)
                yield response
                
                # 检查客户端是否取消
                if context.cancelled():
                    await self.stream_processor.cancel_stream(stream_id)
                    break
            
        except Exception as e:
            logger.error(f"异步字幕优化失败: {e}", exc_info=True)
            
            # 发送错误响应
            error_progress = StreamProgress(
                stream_id="error",
                stage_name="优化错误",
                percentage=0,
                message=f"字幕优化失败: {str(e)}",
                is_error=True,
                error_message=str(e)
            )
            
            response = self._create_optimize_subtitles_response(error_progress)
            yield response
    
    @monitor_performance(operation_name="async_translate_subtitles")
    async def TranslateSubtitlesAsync(
        self,
        request,  # subtitler_pb2.TranslateSubtitlesRequest
        context: grpc.aio.ServicerContext
    ) -> AsyncGenerator[Any, None]:  # AsyncGenerator[subtitler_pb2.TranslateSubtitlesResponse, None]
        """异步字幕翻译流式响应"""
        
        content = getattr(request, 'content', '')
        target_language = getattr(request, 'target_language', 'English')
        logger.info(f"收到异步字幕翻译请求: {len(content)}字符 -> {target_language}")
        
        try:
            # 创建处理流
            stream_id = await self.stream_processor.create_stream(
                metadata={
                    "operation": "translate_subtitles",
                    "content_length": len(content),
                    "target_language": target_language
                }
            )
            
            # 配置参数
            config = {
                "model": getattr(request, 'model', 'gemini-2.5-flash'),
                "source_language": getattr(request, 'source_language', 'auto'),
                "preserve_formatting": getattr(request, 'preserve_formatting', True)
            }
            
            # 流式处理
            async for progress in self.stream_processor.process_translation_stream(
                stream_id, content, target_language, config
            ):
                # 转换为protobuf响应
                response = self._create_translate_subtitles_response(progress)
                yield response
                
                # 检查客户端是否取消
                if context.cancelled():
                    await self.stream_processor.cancel_stream(stream_id)
                    break
            
        except Exception as e:
            logger.error(f"异步字幕翻译失败: {e}", exc_info=True)
            
            # 发送错误响应
            error_progress = StreamProgress(
                stream_id="error",
                stage_name="翻译错误",
                percentage=0,
                message=f"字幕翻译失败: {str(e)}",
                is_error=True,
                error_message=str(e)
            )
            
            response = self._create_translate_subtitles_response(error_progress)
            yield response
    
    @monitor_performance(operation_name="async_complete_workflow")
    async def CompleteWorkflowAsync(
        self,
        request,  # subtitler_pb2.CompleteWorkflowRequest
        context: grpc.aio.ServicerContext
    ) -> AsyncGenerator[Any, None]:  # AsyncGenerator[subtitler_pb2.CompleteWorkflowResponse, None]
        """异步完整工作流流式响应"""
        
        video_path = getattr(request, 'video_path', '')
        logger.info(f"收到异步完整工作流请求: {video_path}")
        
        try:
            # 创建处理流
            stream_id = await self.stream_processor.create_stream(
                metadata={"operation": "complete_workflow", "video_path": video_path}
            )
            
            # 配置参数
            config = {
                "model": getattr(request, 'model', 'JIANYING'),
                "language": getattr(request, 'language', 'zh'),
                "enable_translation": getattr(request, 'enable_translation', False),
                "target_language": getattr(request, 'target_language', 'English'),
                "optimization_level": getattr(request, 'optimization_level', 'standard')
            }
            
            # 流式处理
            async for progress in self.stream_processor.process_complete_workflow_stream(
                stream_id, video_path, config
            ):
                # 转换为protobuf响应
                response = self._create_complete_workflow_response(progress)
                yield response
                
                # 检查客户端是否取消
                if context.cancelled():
                    await self.stream_processor.cancel_stream(stream_id)
                    break
            
        except Exception as e:
            logger.error(f"异步完整工作流失败: {e}", exc_info=True)
            
            # 发送错误响应
            error_progress = StreamProgress(
                stream_id="error",
                stage_name="工作流错误",
                percentage=0,
                message=f"完整工作流失败: {str(e)}",
                is_error=True,
                error_message=str(e)
            )
            
            response = self._create_complete_workflow_response(error_progress)
            yield response
    
    async def GetStreamStatus(
        self,
        request,  # subtitler_pb2.GetStreamStatusRequest
        context: grpc.aio.ServicerContext
    ):  # -> subtitler_pb2.GetStreamStatusResponse
        """获取流状态"""
        
        stream_id = getattr(request, 'stream_id', '')
        
        try:
            stream_info = self.stream_processor.get_stream_info(stream_id)
            
            if stream_info:
                # 创建状态响应
                response = self._create_stream_status_response(stream_id, stream_info)
                return response
            else:
                # 流不存在
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"流不存在: {stream_id}")
                return None
                
        except Exception as e:
            logger.error(f"获取流状态失败: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"获取流状态失败: {str(e)}")
            return None
    
    async def CancelStream(
        self,
        request,  # subtitler_pb2.CancelStreamRequest
        context: grpc.aio.ServicerContext
    ):  # -> subtitler_pb2.CancelStreamResponse
        """取消流处理"""
        
        stream_id = getattr(request, 'stream_id', '')
        
        try:
            success = await self.stream_processor.cancel_stream(stream_id)
            
            # 创建取消响应
            response = self._create_cancel_stream_response(stream_id, success)
            return response
            
        except Exception as e:
            logger.error(f"取消流失败: {e}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"取消流失败: {str(e)}")
            return None
    
    def _create_video_to_audio_response(self, progress: StreamProgress):
        """创建视频转音频响应"""
        # 这里应该创建实际的protobuf响应
        # return subtitler_pb2.VideoToAudioResponse(
        #     stage_name=progress.stage_name,
        #     percentage=progress.percentage,
        #     message=progress.message,
        #     is_error=progress.is_error,
        #     error_message=progress.error_message or "",
        #     result=progress.metadata.get("result")
        # )
        
        # 临时返回字典（实际应该返回protobuf对象）
        return {
            "stage_name": progress.stage_name,
            "percentage": progress.percentage,
            "message": progress.message,
            "is_error": progress.is_error,
            "error_message": progress.error_message or "",
            "result": progress.metadata.get("result")
        }
    
    def _create_audio_to_text_response(self, progress: StreamProgress):
        """创建音频转录响应"""
        return {
            "stage_name": progress.stage_name,
            "percentage": progress.percentage,
            "message": progress.message,
            "is_error": progress.is_error,
            "error_message": progress.error_message or "",
            "result": progress.metadata.get("result")
        }
    
    def _create_optimize_subtitles_response(self, progress: StreamProgress):
        """创建字幕优化响应"""
        return {
            "stage_name": progress.stage_name,
            "percentage": progress.percentage,
            "message": progress.message,
            "is_error": progress.is_error,
            "error_message": progress.error_message or "",
            "result": progress.metadata.get("result")
        }
    
    def _create_translate_subtitles_response(self, progress: StreamProgress):
        """创建字幕翻译响应"""
        return {
            "stage_name": progress.stage_name,
            "percentage": progress.percentage,
            "message": progress.message,
            "is_error": progress.is_error,
            "error_message": progress.error_message or "",
            "result": progress.metadata.get("result")
        }
    
    def _create_complete_workflow_response(self, progress: StreamProgress):
        """创建完整工作流响应"""
        return {
            "stage_name": progress.stage_name,
            "percentage": progress.percentage,
            "message": progress.message,
            "is_error": progress.is_error,
            "error_message": progress.error_message or "",
            "result": progress.metadata.get("result")
        }
    
    def _create_stream_status_response(self, stream_id: str, stream_info: Dict[str, Any]):
        """创建流状态响应"""
        return {
            "stream_id": stream_id,
            "status": stream_info["status"].value,
            "progress": stream_info["progress"],
            "current_stage": stream_info["current_stage"],
            "created_time": stream_info["created_time"],
            "last_update": stream_info["last_update"]
        }
    
    def _create_cancel_stream_response(self, stream_id: str, success: bool):
        """创建取消流响应"""
        return {
            "stream_id": stream_id,
            "success": success,
            "message": "流已取消" if success else "取消失败"
        }


# 全局异步服务实例
_async_subtitle_service = None


def get_async_subtitle_service() -> AsyncSubtitleService:
    """获取全局异步字幕服务"""
    global _async_subtitle_service
    if _async_subtitle_service is None:
        _async_subtitle_service = AsyncSubtitleService()
    return _async_subtitle_service

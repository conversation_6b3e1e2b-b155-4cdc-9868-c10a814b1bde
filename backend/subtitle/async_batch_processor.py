# -*- coding: utf-8 -*-
"""
异步批处理器

提供批量任务处理、队列管理和并发控制功能。
支持大规模文件批处理和智能负载均衡。
"""
import asyncio
import time
import uuid
from typing import List, Dict, Any, Optional, Callable, AsyncGenerator
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import json

from .logging_manager import get_logger
from .async_processor import get_async_task_manager, TaskPriority, TaskResult
from .async_stream_processor import get_async_stream_processor, StreamProgress
from .performance_monitor import monitor_performance
from .exceptions import SubtitleProcessingError

logger = get_logger(__name__)


class BatchStatus(Enum):
    """批处理状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PAUSED = "paused"


@dataclass
class BatchItem:
    """批处理项数据类"""
    item_id: str
    file_path: str
    config: Dict[str, Any]
    priority: TaskPriority = TaskPriority.NORMAL
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.item_id:
            self.item_id = str(uuid.uuid4())


@dataclass
class BatchResult:
    """批处理结果数据类"""
    batch_id: str
    total_items: int
    completed_items: int = 0
    failed_items: int = 0
    cancelled_items: int = 0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    status: BatchStatus = BatchStatus.PENDING
    results: Dict[str, Any] = field(default_factory=dict)
    errors: Dict[str, str] = field(default_factory=dict)
    
    @property
    def progress_percentage(self) -> float:
        if self.total_items == 0:
            return 0.0
        return (self.completed_items + self.failed_items + self.cancelled_items) / self.total_items * 100
    
    @property
    def success_rate(self) -> float:
        processed = self.completed_items + self.failed_items
        if processed == 0:
            return 0.0
        return self.completed_items / processed * 100
    
    @property
    def duration(self) -> Optional[float]:
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class AsyncBatchProcessor:
    """异步批处理器"""
    
    def __init__(self, max_concurrent_batches: int = 3, max_concurrent_items: int = 5):
        self.max_concurrent_batches = max_concurrent_batches
        self.max_concurrent_items = max_concurrent_items
        
        self.task_manager = get_async_task_manager()
        self.stream_processor = get_async_stream_processor()
        
        # 批处理管理
        self.active_batches: Dict[str, BatchResult] = {}
        self.batch_semaphore = asyncio.Semaphore(max_concurrent_batches)
        self.item_semaphore = asyncio.Semaphore(max_concurrent_items)
        
        # 统计信息
        self.stats = {
            "total_batches": 0,
            "completed_batches": 0,
            "failed_batches": 0,
            "total_items_processed": 0,
            "average_batch_duration": 0.0
        }
        
        logger.info(f"异步批处理器初始化完成: 最大并发批次={max_concurrent_batches}, 最大并发项目={max_concurrent_items}")
    
    @monitor_performance(operation_name="batch_video_processing")
    async def process_video_batch(
        self,
        video_files: List[str],
        config: Dict[str, Any],
        batch_id: Optional[str] = None,
        progress_callback: Optional[Callable] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """批量视频处理"""
        
        if batch_id is None:
            batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量视频处理: {batch_id}, 文件数量: {len(video_files)}")
        
        # 创建批处理项
        batch_items = []
        for video_file in video_files:
            item = BatchItem(
                item_id=str(uuid.uuid4()),
                file_path=video_file,
                config=config.copy(),
                priority=TaskPriority.NORMAL
            )
            batch_items.append(item)
        
        # 执行批处理
        async for progress in self._process_batch(
            batch_id, batch_items, self._process_single_video, progress_callback
        ):
            yield progress
    
    @monitor_performance(operation_name="batch_audio_transcription")
    async def process_audio_batch(
        self,
        audio_files: List[str],
        config: Dict[str, Any],
        batch_id: Optional[str] = None,
        progress_callback: Optional[Callable] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """批量音频转录"""
        
        if batch_id is None:
            batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量音频转录: {batch_id}, 文件数量: {len(audio_files)}")
        
        # 创建批处理项
        batch_items = []
        for audio_file in audio_files:
            item = BatchItem(
                item_id=str(uuid.uuid4()),
                file_path=audio_file,
                config=config.copy(),
                priority=TaskPriority.NORMAL
            )
            batch_items.append(item)
        
        # 执行批处理
        async for progress in self._process_batch(
            batch_id, batch_items, self._process_single_audio, progress_callback
        ):
            yield progress
    
    @monitor_performance(operation_name="batch_subtitle_optimization")
    async def process_subtitle_batch(
        self,
        subtitle_contents: List[str],
        config: Dict[str, Any],
        batch_id: Optional[str] = None,
        progress_callback: Optional[Callable] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """批量字幕优化"""
        
        if batch_id is None:
            batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量字幕优化: {batch_id}, 字幕数量: {len(subtitle_contents)}")
        
        # 创建批处理项
        batch_items = []
        for i, content in enumerate(subtitle_contents):
            item = BatchItem(
                item_id=str(uuid.uuid4()),
                file_path=f"subtitle_{i}",  # 虚拟路径
                config={**config, "content": content},
                priority=TaskPriority.NORMAL
            )
            batch_items.append(item)
        
        # 执行批处理
        async for progress in self._process_batch(
            batch_id, batch_items, self._process_single_subtitle, progress_callback
        ):
            yield progress
    
    @monitor_performance(operation_name="batch_folder_processing")
    async def process_folder_batch(
        self,
        folder_path: str,
        file_extensions: List[str],
        config: Dict[str, Any],
        batch_id: Optional[str] = None,
        progress_callback: Optional[Callable] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """批量文件夹处理"""
        
        if batch_id is None:
            batch_id = str(uuid.uuid4())
        
        logger.info(f"开始批量文件夹处理: {batch_id}, 路径: {folder_path}")
        
        try:
            # 扫描文件夹
            folder = Path(folder_path)
            if not folder.exists():
                raise SubtitleProcessingError(f"文件夹不存在: {folder_path}")
            
            # 收集文件
            files = []
            for ext in file_extensions:
                files.extend(folder.glob(f"**/*.{ext}"))
            
            if not files:
                yield {
                    "batch_id": batch_id,
                    "stage": "扫描完成",
                    "message": "未找到匹配的文件",
                    "progress": 100.0,
                    "is_error": True
                }
                return
            
            logger.info(f"找到 {len(files)} 个文件")
            
            # 创建批处理项
            batch_items = []
            for file_path in files:
                item = BatchItem(
                    item_id=str(uuid.uuid4()),
                    file_path=str(file_path),
                    config=config.copy(),
                    priority=TaskPriority.NORMAL,
                    metadata={"file_extension": file_path.suffix}
                )
                batch_items.append(item)
            
            # 根据文件类型选择处理函数
            if any(ext in ['.mp4', '.avi', '.mov', '.mkv'] for ext in file_extensions):
                process_func = self._process_single_video
            elif any(ext in ['.wav', '.mp3', '.flac', '.aac'] for ext in file_extensions):
                process_func = self._process_single_audio
            else:
                process_func = self._process_single_file
            
            # 执行批处理
            async for progress in self._process_batch(
                batch_id, batch_items, process_func, progress_callback
            ):
                yield progress
                
        except Exception as e:
            logger.error(f"批量文件夹处理失败: {e}", exc_info=True)
            yield {
                "batch_id": batch_id,
                "stage": "处理错误",
                "message": f"文件夹处理失败: {str(e)}",
                "progress": 0.0,
                "is_error": True,
                "error_message": str(e)
            }
    
    async def _process_batch(
        self,
        batch_id: str,
        batch_items: List[BatchItem],
        process_func: Callable,
        progress_callback: Optional[Callable] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """执行批处理"""
        
        async with self.batch_semaphore:
            # 创建批处理结果
            batch_result = BatchResult(
                batch_id=batch_id,
                total_items=len(batch_items),
                start_time=time.time(),
                status=BatchStatus.RUNNING
            )
            self.active_batches[batch_id] = batch_result
            
            try:
                # 发送开始信号
                yield {
                    "batch_id": batch_id,
                    "stage": "批处理开始",
                    "message": f"开始处理 {len(batch_items)} 个项目",
                    "progress": 0.0,
                    "total_items": len(batch_items)
                }
                
                # 创建任务
                tasks = []
                for item in batch_items:
                    task = asyncio.create_task(
                        self._process_single_item(batch_id, item, process_func)
                    )
                    tasks.append(task)
                
                # 等待所有任务完成
                completed_count = 0
                for task in asyncio.as_completed(tasks):
                    try:
                        result = await task
                        completed_count += 1
                        
                        # 更新批处理结果
                        if result.get("success", False):
                            batch_result.completed_items += 1
                            batch_result.results[result["item_id"]] = result["result"]
                        else:
                            batch_result.failed_items += 1
                            batch_result.errors[result["item_id"]] = result.get("error", "未知错误")
                        
                        # 发送进度更新
                        progress = completed_count / len(batch_items) * 100
                        yield {
                            "batch_id": batch_id,
                            "stage": "批处理进行中",
                            "message": f"已完成 {completed_count}/{len(batch_items)} 个项目",
                            "progress": progress,
                            "completed_items": batch_result.completed_items,
                            "failed_items": batch_result.failed_items,
                            "success_rate": batch_result.success_rate
                        }
                        
                        # 调用进度回调
                        if progress_callback:
                            try:
                                if asyncio.iscoroutinefunction(progress_callback):
                                    await progress_callback(batch_result)
                                else:
                                    progress_callback(batch_result)
                            except Exception as e:
                                logger.error(f"进度回调执行失败: {e}")
                    
                    except Exception as e:
                        logger.error(f"批处理任务失败: {e}", exc_info=True)
                        batch_result.failed_items += 1
                        completed_count += 1
                
                # 完成批处理
                batch_result.end_time = time.time()
                batch_result.status = BatchStatus.COMPLETED if batch_result.failed_items == 0 else BatchStatus.FAILED
                
                # 更新统计
                self.stats["total_batches"] += 1
                if batch_result.status == BatchStatus.COMPLETED:
                    self.stats["completed_batches"] += 1
                else:
                    self.stats["failed_batches"] += 1
                
                self.stats["total_items_processed"] += batch_result.total_items
                self._update_average_batch_duration(batch_result.duration)
                
                # 发送完成信号
                yield {
                    "batch_id": batch_id,
                    "stage": "批处理完成",
                    "message": f"批处理完成: 成功 {batch_result.completed_items}, 失败 {batch_result.failed_items}",
                    "progress": 100.0,
                    "completed_items": batch_result.completed_items,
                    "failed_items": batch_result.failed_items,
                    "success_rate": batch_result.success_rate,
                    "duration": batch_result.duration,
                    "final_result": {
                        "batch_result": batch_result,
                        "results": batch_result.results,
                        "errors": batch_result.errors
                    }
                }
                
            except Exception as e:
                batch_result.status = BatchStatus.FAILED
                batch_result.end_time = time.time()
                
                logger.error(f"批处理执行失败: {e}", exc_info=True)
                
                yield {
                    "batch_id": batch_id,
                    "stage": "批处理错误",
                    "message": f"批处理失败: {str(e)}",
                    "progress": 0.0,
                    "is_error": True,
                    "error_message": str(e)
                }
    
    async def _process_single_item(
        self,
        batch_id: str,
        item: BatchItem,
        process_func: Callable
    ) -> Dict[str, Any]:
        """处理单个批处理项"""
        
        async with self.item_semaphore:
            try:
                logger.debug(f"开始处理项目: {item.item_id} - {item.file_path}")
                
                # 执行处理函数
                result = await process_func(item)
                
                logger.debug(f"项目处理完成: {item.item_id}")
                
                return {
                    "item_id": item.item_id,
                    "success": True,
                    "result": result
                }
                
            except Exception as e:
                logger.error(f"项目处理失败: {item.item_id} - {e}", exc_info=True)
                
                return {
                    "item_id": item.item_id,
                    "success": False,
                    "error": str(e)
                }
    
    async def _process_single_video(self, item: BatchItem) -> Dict[str, Any]:
        """处理单个视频文件"""
        # 创建流处理
        stream_id = await self.stream_processor.create_stream(
            metadata={"batch_item": item.item_id, "file_path": item.file_path}
        )
        
        # 收集流结果
        final_result = None
        async for progress in self.stream_processor.process_complete_workflow_stream(
            stream_id, item.file_path, item.config
        ):
            if progress.metadata.get("result"):
                final_result = progress.metadata["result"]
        
        return final_result or {"processed": True, "file_path": item.file_path}
    
    async def _process_single_audio(self, item: BatchItem) -> Dict[str, Any]:
        """处理单个音频文件"""
        # 创建流处理
        stream_id = await self.stream_processor.create_stream(
            metadata={"batch_item": item.item_id, "file_path": item.file_path}
        )
        
        # 收集流结果
        final_result = None
        async for progress in self.stream_processor.process_audio_transcription_stream(
            stream_id, item.file_path, item.config
        ):
            if progress.metadata.get("result"):
                final_result = progress.metadata["result"]
        
        return final_result or {"transcribed": True, "file_path": item.file_path}
    
    async def _process_single_subtitle(self, item: BatchItem) -> Dict[str, Any]:
        """处理单个字幕内容"""
        content = item.config.get("content", "")
        
        # 创建流处理
        stream_id = await self.stream_processor.create_stream(
            metadata={"batch_item": item.item_id, "content_length": len(content)}
        )
        
        # 收集流结果
        final_result = None
        async for progress in self.stream_processor.process_subtitle_optimization_stream(
            stream_id, content, item.config
        ):
            if progress.metadata.get("result"):
                final_result = progress.metadata["result"]
        
        return final_result or {"optimized": True, "content_length": len(content)}
    
    async def _process_single_file(self, item: BatchItem) -> Dict[str, Any]:
        """处理单个通用文件"""
        # 根据文件扩展名决定处理方式
        file_path = Path(item.file_path)
        ext = file_path.suffix.lower()
        
        if ext in ['.mp4', '.avi', '.mov', '.mkv', '.flv', '.wmv']:
            return await self._process_single_video(item)
        elif ext in ['.wav', '.mp3', '.flac', '.aac', '.ogg']:
            return await self._process_single_audio(item)
        else:
            # 其他文件类型的默认处理
            return {"processed": True, "file_path": item.file_path, "file_type": ext}
    
    def _update_average_batch_duration(self, duration: Optional[float]):
        """更新平均批处理时间"""
        if duration is None:
            return
        
        completed = self.stats["completed_batches"] + self.stats["failed_batches"]
        if completed == 1:
            self.stats["average_batch_duration"] = duration
        else:
            current_avg = self.stats["average_batch_duration"]
            self.stats["average_batch_duration"] = (current_avg * (completed - 1) + duration) / completed
    
    def get_batch_result(self, batch_id: str) -> Optional[BatchResult]:
        """获取批处理结果"""
        return self.active_batches.get(batch_id)
    
    def get_active_batches(self) -> Dict[str, BatchResult]:
        """获取所有活跃批处理"""
        return self.active_batches.copy()
    
    async def cancel_batch(self, batch_id: str) -> bool:
        """取消批处理"""
        if batch_id in self.active_batches:
            batch_result = self.active_batches[batch_id]
            batch_result.status = BatchStatus.CANCELLED
            
            logger.info(f"批处理已取消: {batch_id}")
            return True
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "active_batches": len(self.active_batches),
            "max_concurrent_batches": self.max_concurrent_batches,
            "max_concurrent_items": self.max_concurrent_items
        }
    
    def cleanup_completed_batches(self, max_age_hours: int = 24):
        """清理已完成的批处理"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        batches_to_remove = []
        for batch_id, batch_result in self.active_batches.items():
            if (batch_result.status in [BatchStatus.COMPLETED, BatchStatus.FAILED, BatchStatus.CANCELLED] and
                batch_result.end_time and
                (current_time - batch_result.end_time) > max_age_seconds):
                batches_to_remove.append(batch_id)
        
        for batch_id in batches_to_remove:
            self.active_batches.pop(batch_id, None)
        
        if batches_to_remove:
            logger.info(f"清理了 {len(batches_to_remove)} 个过期批处理")


# 全局异步批处理器
_async_batch_processor = None


def get_async_batch_processor() -> AsyncBatchProcessor:
    """获取全局异步批处理器"""
    global _async_batch_processor
    if _async_batch_processor is None:
        _async_batch_processor = AsyncBatchProcessor()
    return _async_batch_processor

from subtitle.core.bcut import BcutASR
from subtitle.core.jianying import <PERSON><PERSON><PERSON>ingAS<PERSON>
from subtitle.core.asr_data import ASRData
from subtitle.entities import TranscribeConfig, TranscribeModelEnum


def transcribe(audio_path: str, config: TranscribeConfig, callback=None) -> ASRData:
    """
    使用指定的转录配置对音频文件进行转录

    Args:
        audio_path: 音频文件路径
        config: 转录配置
        callback: 进度回调函数,接收两个参数(progress: int, message: str)

    Returns:
        ASRData: 转录结果数据
    """
    if callback is None:
        def callback(_progress, _message):
            pass

    # 获取ASR模型类
    ASR_MODELS = {
        TranscribeModelEnum.JIANYING: JianYingASR,
        TranscribeModelEnum.BIJIAN: BcutASR,
    }

    asr_class = ASR_MODELS.get(config.transcribe_model)
    if not asr_class:
        raise ValueError(f"无效的转录模型: {config.transcribe_model}")

    # 构建ASR参数
    asr_args = {
        "use_cache": config.use_asr_cache,
        "need_word_time_stamp": config.need_word_time_stamp,
    }
    
    # 创建ASR实例并运行
    asr = asr_class(audio_path, **asr_args)

    asr_data = asr.run(callback=callback)

    # 优化字幕显示时间 #161
    if not config.need_word_time_stamp:
        asr_data.optimize_timing()

    return asr_data

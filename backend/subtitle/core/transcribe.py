from typing import Optional

from subtitle.core.bcut import BcutASR
from subtitle.core.jianying import JianYingASR
from .openai_asr import OpenAIASR
from subtitle.core.asr_data import ASRData
from subtitle.entities import TranscribeConfig, TranscribeModelEnum


def transcribe(audio_path: str, config: TranscribeConfig, callback=None, api_key: Optional[str] = None, base_url: Optional[str] = None, model_name: Optional[str] = None) -> ASRData:
    """
    使用指定的转录配置对音频文件进行转录

    Args:
        audio_path: 音频文件路径
        config: 转录配置
        callback: 进度回调函数,接收两个参数(progress: int, message: str)

    Returns:
        ASRData: 转录结果数据
    """
    if callback is None:
        callback = lambda x, y: None

    # 获取ASR模型类
    ASR_MODELS = {
        TranscribeModelEnum.JIANYING: JianYingASR,
        TranscribeModelEnum.BIJIAN: BcutASR,
        TranscribeModelEnum.OPENAI_API: OpenAIASR,
    }

    asr_class = ASR_MODELS.get(config.transcribe_model)
    if not asr_class:
        raise ValueError(f"无效的转录模型: {config.transcribe_model}")

    # 构建ASR参数
    asr_args = {
        "use_cache": config.use_asr_cache,
        "need_word_time_stamp": config.need_word_time_stamp,
    }
    if config.transcribe_model == TranscribeModelEnum.OPENAI_API:
        if not api_key: # api_key 是 transcribe() 函数的参数
            raise ValueError("OpenAI API 转录需要 API key。")
        asr_args["api_key"] = api_key
        if base_url: # base_url 是可选参数
            asr_args["base_url"] = base_url
        if model_name: # model_name 是可选参数, OpenAIASR 类中应有默认值
            asr_args["model_name"] = model_name
        # "need_word_time_stamp" 已通过 config 对象包含在 asr_args 中。
        # "use_cache" 参数对于 OpenAIASR 可能不适用，或者由其内部处理。
    
    # 创建ASR实例并运行
    asr = asr_class(audio_path, **asr_args)

    asr_data = asr.run(callback=callback)

    # 优化字幕显示时间 #161
    if not config.need_word_time_stamp:
        asr_data.optimize_timing()

    return asr_data

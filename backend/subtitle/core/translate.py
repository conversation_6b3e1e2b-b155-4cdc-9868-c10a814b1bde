import hashlib
from string import Template
from typing import Callable, Dict, Optional, List, Any, Union
import os
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from abc import ABC, abstractmethod
from enum import Enum
from openai import OpenAI
import json
import requests
import re

from subtitle.config import CACHE_PATH
from subtitle.core.asr_data import ASRData, ASRDataSeg
from subtitle.core.prompt import REFLECT_TRANSLATE_PROMPT, TRANSLATE_PROMPT, SINGLE_TRANSLATE_PROMPT
from subtitle.storage import CacheManager
from subtitle.utils import json_repair
from subtitle.utils.logger import setup_logger

logger = setup_logger("subtitle_translator")


class TranslatorType(Enum):
    """翻译器类型"""

    OPENAI = "openai"
    DEEPLX = "deeplx"


class BaseTranslator(ABC):
    """翻译器基类"""

    def __init__(
        self,
        thread_num: int = 10,
        batch_num: int = 20,
        target_language: str = "Chinese",
        retry_times: int = 1,
        timeout: int = 60,
        update_callback: Optional[Callable] = None,
        custom_prompt: Optional[str] = None, # Retained for potential use by subclasses
    ):
        self.thread_num = thread_num
        self.batch_num = batch_num
        self.target_language = target_language
        self.retry_times = retry_times
        self.timeout = timeout
        self.is_running = True
        self.update_callback = update_callback
        self.custom_prompt = custom_prompt 
        self._init_thread_pool()
        self.cache_manager = CacheManager(CACHE_PATH)

    def _init_thread_pool(self):
        """初始化线程池"""
        self.executor = ThreadPoolExecutor(max_workers=self.thread_num)
        import weakref
        self._finalizer = weakref.finalize(self, self._cleanup_resources, self.executor)

    def _cleanup_resources(self, executor):
        """清理资源，由 weakref finalizer 调用"""
        if executor is not None:
            try:
                executor.shutdown(wait=False)
            except Exception:
                pass 

    def translate_subtitle(self, subtitle_data: Union[str, ASRData]) -> ASRData:
        """翻译字幕文件"""
        try:
            if isinstance(subtitle_data, str):
                asr_data = ASRData.from_subtitle_file(subtitle_data)
            else:
                asr_data = subtitle_data

            subtitle_dict = {
                str(i): seg.text for i, seg in enumerate(asr_data.segments, 1)
            }

            chunks = self._split_chunks(subtitle_dict)
            translated_dict = self._parallel_translate(chunks)
            new_segments = self._create_segments(asr_data.segments, translated_dict)

            return ASRData(new_segments)
        except Exception as e:
            logger.error(f"翻译失败：{str(e)}")
            raise RuntimeError(f"翻译失败：{str(e)}")

    def _split_chunks(self, subtitle_dict: Dict[str, str]) -> List[Dict[str, str]]:
        """将字幕分割成块"""
        items = list(subtitle_dict.items())
        return [
            dict(items[i : i + self.batch_num])
            for i in range(0, len(items), self.batch_num)
        ]

    def _parallel_translate(self, chunks: List[Dict[str, str]]) -> Dict[str, str]:
        """并行翻译所有块"""
        futures = []
        translated_dict = {}

        for chunk in chunks:
            future = self.executor.submit(self._safe_translate_chunk, chunk)
            futures.append(future)

        for future in as_completed(futures):
            if not self.is_running:
                logger.info("翻译器已停止运行，退出翻译")
                break
            try:
                result = future.result()
                translated_dict.update(result)
            except Exception as e:
                logger.error(f"翻译块失败：{str(e)}")
                # 对于失败的块，保留原文
                # This needs to access the original chunk associated with the future.
                # For simplicity, this part is not fully robust for error recovery of specific chunks.
                # A more robust way would be to map futures to chunks.
                # For now, if a chunk fails, its translations might be missing.
                # The _safe_translate_chunk re-raises, so this might not be hit often.
                pass


        return translated_dict

    def _safe_translate_chunk(self, chunk: Dict[str, str]) -> Dict[str, str]:
        """安全的翻译块，包含重试逻辑"""
        for i in range(self.retry_times + 1): # Corrected retry logic
            try:
                result = self._translate_chunk(chunk)
                if self.update_callback:
                    self.update_callback(result)
                return result
            except Exception as e:
                if i == self.retry_times: # Last attempt failed
                    logger.error(f"翻译块最终失败 (尝试 {i+1} 次): {str(e)}")
                    raise # Re-raise the last exception
                logger.warning(f"翻译重试 {i+1}/{self.retry_times+1}: {str(e)}")
        return {} # Should not be reached if retry_times >= 0

    @staticmethod
    def _create_segments(
        original_segments: List[ASRDataSeg], translated_dict: Dict[str, str]
    ) -> List[ASRDataSeg]:
        """创建新的字幕段"""
        for i, seg in enumerate(original_segments, 1):
            try:
                seg.translated_text = translated_dict[str(i)] 
            except Exception as e:
                logger.error(f"创建新的字幕段失败 for segment {i}: {str(e)}")
                seg.translated_text = seg.text # Fallback to original text
        return original_segments

    @abstractmethod
    def _translate_chunk(self, subtitle_chunk: Dict[str, str]) -> Dict[str, str]:
        """翻译字幕块"""
        pass

    def stop(self):
        """停止翻译器，清理资源"""
        try:
            if hasattr(self, 'executor') and self.executor:
                self.executor.shutdown(wait=True) # Changed to wait=True for cleaner shutdown
                self.executor = None
            self._safe_log("翻译器已停止")
        except Exception as e:
            self._safe_log(f"停止翻译器时发生错误: {e}", level="error")
        finally:
            self.executor = None # Ensure it's None even if shutdown fails

    def _safe_log(self, message, level="info"):
        """安全地记录日志，避免写入已关闭的处理器"""
        try:
            if logger.handlers and any(not getattr(h, 'closed', False) for h in logger.handlers):
                if level == "error":
                    logger.error(message)
                else:
                    logger.info(message)
        except (ValueError, OSError, AttributeError):
            pass


class OpenAITranslator(BaseTranslator):
    """OpenAI翻译器"""

    def __init__(
        self,
        thread_num: int = 10,
        batch_num: int = 20,
        target_language: str = "Chinese",
        model: str = "gpt-4o-mini",
        custom_prompt: str = "",
        is_reflect: bool = False,
        temperature: float = 0.7,
        timeout: int = 60,
        retry_times: int = 1,
        update_callback: Optional[Callable] = None,
        api_key: Optional[str] = None, # New parameter
        base_url: Optional[str] = None, # New parameter
    ):
        super().__init__(
            thread_num=thread_num,
            batch_num=batch_num,
            target_language=target_language,
            retry_times=retry_times,
            timeout=timeout,
            update_callback=update_callback,
            custom_prompt=custom_prompt # Pass custom_prompt to BaseTranslator
        )
        
        self.api_key = api_key
        self.base_url = base_url
        self._init_client() # Call after api_key and base_url are set

        self.model = model
        # self.custom_prompt = custom_prompt # Already set by super()
        self.is_reflect = is_reflect
        self.temperature = temperature

    def _init_client(self):
        """初始化OpenAI客户端"""
        if not self.api_key:
            raise ValueError("OpenAI API key 必须为 OpenAITranslator 提供。")
        
        # self.base_url 可以为 None, OpenAI 客户端会处理
        self.client = OpenAI(api_key=self.api_key, base_url=self.base_url)
        logger.info(f"OpenAITranslator client initialized. Model: {self.model}")
        if self.base_url:
            logger.info(f"Using custom OpenAI base URL for translator: {self.base_url}")


    def _translate_chunk(self, subtitle_chunk: Dict[str, str]) -> Dict[str, str]:
        """翻译字幕块"""
        logger.info(
            f"[+]正在翻译字幕：{next(iter(subtitle_chunk))} - {next(reversed(subtitle_chunk))}"
        )

        if self.is_reflect:
            prompt = REFLECT_TRANSLATE_PROMPT
        else:
            prompt = TRANSLATE_PROMPT
        
        # Use self.custom_prompt which was set in __init__
        prompt = Template(prompt).safe_substitute(
            target_language=self.target_language, custom_prompt=self.custom_prompt 
        )
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()

        try:
            cache_params = {
                "target_language": self.target_language,
                "is_reflect": self.is_reflect,
                "temperature": self.temperature,
                "prompt_hash": prompt_hash,
            }
            cache_key = f"{json.dumps(subtitle_chunk, ensure_ascii=False)}"
            cache_result = self.cache_manager.get_llm_result(
                cache_key,
                self.model,
                **cache_params,
            )

            result = {}
            if cache_result:
                result = json.loads(cache_result)
            else:
                response = self._call_api(
                    prompt, json.dumps(subtitle_chunk, ensure_ascii=False)
                )
                result = json_repair.loads(response.choices[0].message.content)
                if len(result) != len(subtitle_chunk):
                    logger.warning(f"翻译结果数量不匹配，将使用单条翻译模式重试")
                    return self._translate_chunk_single(subtitle_chunk)
                self.cache_manager.set_llm_result(
                    cache_key,
                    json.dumps(result, ensure_ascii=False),
                    self.model,
                    **cache_params,
                )

            if self.is_reflect:
                result = {k: f"{v['revised_translation']}" for k, v in result.items()}
            else:
                result = {k: f"{v}" for k, v in result.items()}

            return result
        except Exception as e:
            logger.warning(f"批量翻译块失败，尝试单条翻译: {str(e)}") # Changed to warning
            try:
                return self._translate_chunk_single(subtitle_chunk)
            except Exception as final_e: # Renamed to avoid confusion
                logger.error(f"单条翻译也失败：{str(final_e)}")
                # Return original text for the chunk if all fails, to avoid crashing
                # This part needs careful consideration for error propagation vs. partial success
                return {k: f"{v}||TRANSLATION_ERROR" for k,v in subtitle_chunk.items()}


    def _translate_chunk_single(self, subtitle_chunk: Dict[str, str]) -> Dict[str, str]:
        """单条翻译模式"""
        result = {}
        single_prompt = Template(SINGLE_TRANSLATE_PROMPT).safe_substitute(
            target_language=self.target_language
        )
        prompt_hash = hashlib.md5(single_prompt.encode()).hexdigest()
        for idx, text in subtitle_chunk.items():
            try:
                cache_params = {
                    "target_language": self.target_language,
                    "is_reflect": self.is_reflect, # is_reflect might not apply to single prompt
                    "temperature": self.temperature,
                    "prompt_hash": prompt_hash,
                }
                cache_result = self.cache_manager.get_llm_result(
                    f"{text}", self.model, **cache_params
                )

                if cache_result:
                    result[idx] = cache_result
                    continue

                response = self._call_api(single_prompt, text)
                translated_text = response.choices[0].message.content.strip()

                translated_text = re.sub(
                    r"<think>.*?</think>", "", translated_text, flags=re.DOTALL
                )
                translated_text = translated_text.strip()

                self.cache_manager.set_llm_result(
                    f"{text}",
                    translated_text,
                    self.model,
                    **cache_params,
                )
                result[idx] = translated_text
            except Exception as e:
                logger.error(f"单条翻译失败 {idx}: {str(e)}")
                result[idx] = f"{text}||TRANSLATION_ERROR" 
        return result

    def _call_api(self, prompt: str, user_content: str) -> Any: # Changed user_content to str for single
        """调用OpenAI API"""
        messages = [
            {"role": "system", "content": prompt},
            {"role": "user", "content": user_content},
        ]

        return self.client.chat.completions.create(
            model=self.model,
            messages=messages,
            temperature=self.temperature,
            timeout=self.timeout,
        )

    # _parse_response is not directly used if json_repair.loads is used in _translate_chunk
    # def _parse_response(self, response: Any) -> Dict[str, str]:
    #     """解析API响应"""
    #     try:
    #         result = json_repair.loads(response.choices[0].message.content)
    #         if self.is_reflect:
    #             return {k: v["revised_translation"] for k, v in result.items()}
    #         return result
    #     except Exception as e:
    #         raise ValueError(f"解析翻译结果失败：{str(e)}")


class DeepLXTranslator(BaseTranslator):
    """DeepLX翻译器"""

    def __init__(
        self,
        thread_num: int = 10,
        batch_num: int = 20,
        target_language: str = "Chinese",
        retry_times: int = 1,
        timeout: int = 20,
        update_callback: Optional[Callable] = None,
        # New parameters for potential future use, matching OpenAI for consistency
        api_key: Optional[str] = None, # Could be used for endpoint or auth if DeepLX changes
        base_url: Optional[str] = None, # Could be used as endpoint
    ):
        super().__init__(
            thread_num=thread_num,
            batch_num=batch_num,
            target_language=target_language,
            retry_times=retry_times,
            timeout=timeout,
            update_callback=update_callback,
        )
        self.session = requests.Session()
        # Prioritize base_url if provided by new config system, else fallback to env var
        self.endpoint = base_url if base_url else os.getenv("DEEPLX_ENDPOINT", "https://api.deeplx.org/translate")
        if not self.endpoint: # Final check if neither base_url nor env var is set
             raise ValueError("DeepLX endpoint must be provided either via configuration or DEEPLX_ENDPOINT environment variable.")
        logger.info(f"DeepLXTranslator initialized. Endpoint: {self.endpoint}")

        self.lang_map = {
            "简体中文": "zh", "繁体中文": "zh-TW", "英语": "en", "日本語": "ja", "韩语": "ko",
            "法语": "fr", "德语": "de", "西班牙语": "es", "俄语": "ru", "葡萄牙语": "pt",
            "土耳其语": "tr", "Chinese": "zh", "English": "en", "Japanese": "ja", "Korean": "ko",
            "French": "fr", "German": "de", "Spanish": "es", "Russian": "ru",
        }

    def _translate_chunk(self, subtitle_chunk: Dict[str, str]) -> Dict[str, str]:
        """翻译字幕块"""
        result = {}
        if self.target_language in self.lang_map.values():
            target_lang = self.target_language
        else:
            target_lang = self.lang_map.get(self.target_language, "zh").lower()

        for idx, text in subtitle_chunk.items():
            try:
                cache_params = {
                    "target_language": target_lang,
                    "endpoint": self.endpoint,
                }
                cache_result = self.cache_manager.get_translation(
                    text, TranslatorType.DEEPLX.value, **cache_params
                )

                if cache_result:
                    result[idx] = cache_result
                    logger.info(f"使用缓存的DeepLX翻译结果：{idx}")
                    continue

                response = self.session.post(
                    self.endpoint,
                    json={
                        "text": text,
                        "source_lang": "auto",
                        "target_lang": target_lang,
                    },
                    timeout=self.timeout,
                )
                response.raise_for_status()
                translated_text = response.json()["data"]

                self.cache_manager.set_translation(
                    text, translated_text, TranslatorType.DEEPLX.value, **cache_params
                )
                result[idx] = translated_text
            except Exception as e:
                logger.error(f"DeepLX翻译失败 {idx}: {str(e)}")
                result[idx] = f"{text}||TRANSLATION_ERROR"
        return result


class TranslatorFactory:
    """翻译器工厂类"""

    @staticmethod
    def create_translator(
        translator_type: TranslatorType,
        thread_num: int = 5,
        batch_num: int = 10,
        target_language: str = "Chinese",
        model: str = "gpt-4o-mini", # Default for OpenAI, ignored by DeepLX
        custom_prompt: str = "",    # OpenAI specific
        temperature: float = 0.7,   # OpenAI specific
        is_reflect: bool = False,   # OpenAI specific
        update_callback: Optional[Callable] = None,
        api_key: Optional[str] = None,    # New
        base_url: Optional[str] = None,   # New (can serve as endpoint for DeepLX)
    ) -> BaseTranslator:
        """创建翻译器实例"""
        try:
            if translator_type == TranslatorType.OPENAI:
                return OpenAITranslator(
                    thread_num=thread_num,
                    batch_num=batch_num,
                    target_language=target_language,
                    model=model,
                    custom_prompt=custom_prompt,
                    is_reflect=is_reflect,
                    temperature=temperature,
                    update_callback=update_callback,
                    api_key=api_key,      
                    base_url=base_url     
                )
            elif translator_type == TranslatorType.DEEPLX:
                # DeepLX batch_num can be different, e.g. 5 as in original code
                # Pass base_url as potential endpoint, api_key might be unused or for future auth
                return DeepLXTranslator(
                    thread_num=thread_num,
                    batch_num=5, # Or pass batch_num if it should be configurable
                    target_language=target_language,
                    update_callback=update_callback,
                    api_key=api_key, # Pass along, DeepLXTranslator can ignore if not needed
                    base_url=base_url  # This will be used as endpoint if provided
                )
            else:
                raise ValueError(f"不支持的翻译器类型：{translator_type}")
        except Exception as e:
            logger.error(f"创建翻译器失败：{str(e)}")
            raise

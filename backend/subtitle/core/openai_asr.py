# backend/subtitle/core/openai_asr.py
import logging
import os
from typing import Optional, Dict, Any

# 从 openai >= 1.0.0 版本开始，导入方式如下
try:
    from openai import OpenAI, APIConnectionError, RateLimitError, APIStatusError
except ImportError:
    # Fallback or error for older versions if necessary, though project likely uses >=1.0
    OpenAI = None 
    APIConnectionError = RateLimitError = APIStatusError = Exception # Placeholder exceptions

from subtitle.core.asr_data import ASRData, ASRDataSeg
from subtitle.utils.logger import setup_logger

logger = setup_logger("openai_asr")

class OpenAIASR:
    def __init__(self, audio_path: str, api_key: str, base_url: Optional[str] = None, 
                 model_name: str = "whisper-1", need_word_time_stamp: bool = True, **kwargs):
        if OpenAI is None:
            raise ImportError("OpenAI SDK not installed or failed to import. Please install with 'pip install openai'")
        
        self.audio_path = audio_path
        self.api_key = api_key
        self.base_url = base_url
        self.model_name = model_name
        self.need_word_time_stamp = need_word_time_stamp
        # kwargs can be used for other specific parameters if needed

        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url # OpenAI client handles None base_url correctly
        )
        logger.info(f"OpenAIASR initialized. Model: {self.model_name}, Word timestamps: {self.need_word_time_stamp}")
        if self.base_url:
            logger.info(f"Using custom OpenAI base URL: {self.base_url}")


    def run(self, callback=None) -> ASRData:
        if callback is None:
            callback = lambda p, m: None

        callback(0, "开始使用 OpenAI API 进行转录...")
        logger.info(f"Transcribing {self.audio_path} with OpenAI model {self.model_name}")

        try:
            with open(self.audio_path, "rb") as audio_file:
                transcript_params: Dict[str, Any] = {
                    "model": self.model_name,
                    "file": audio_file,
                    "response_format": "verbose_json"
                }
                if self.need_word_time_stamp:
                    transcript_params["timestamp_granularities"] = ["word", "segment"]
                else:
                    transcript_params["timestamp_granularities"] = ["segment"]


                response = self.client.audio.transcriptions.create(**transcript_params)
            
            callback(80, "从 OpenAI API 收到转录响应。")
            logger.debug(f"OpenAI API full response text: {response.text[:500]}...") # Log part of raw text for debug

            segments_data = []
            if response.segments:
                for segment_dict in response.segments:
                    text = segment_dict.get("text", "").strip()
                    start_ms = int(segment_dict.get("start", 0) * 1000)
                    end_ms = int(segment_dict.get("end", 0) * 1000)

                    # Create ASRDataSeg with basic parameters
                    asr_segment = ASRDataSeg(
                        text=text,
                        start_time=start_ms,
                        end_time=end_ms
                    )
                    segments_data.append(asr_segment)

            asr_data_result = ASRData(segments=segments_data)
            
            logger.info(f"OpenAI transcription successful. Segments: {len(segments_data)}")
            callback(100, "OpenAI API 转录完成。")
            return asr_data_result

        except APIConnectionError as e:
            logger.error(f"OpenAI API connection error: {e}")
            callback(100, f"错误: OpenAI API 连接失败: {e}")
            raise  # Re-raise to be handled by TranscriptThread
        except RateLimitError as e:
            logger.error(f"OpenAI API rate limit exceeded: {e}")
            callback(100, f"错误: OpenAI API 速率限制: {e}")
            raise
        except APIStatusError as e:
            logger.error(f"OpenAI API status error: {e.status_code} - {e.response}")
            callback(100, f"错误: OpenAI API 状态错误 ({e.status_code}): {e.message}")
            raise
        except Exception as e:
            logger.error(f"An unexpected error occurred during OpenAI transcription: {e}", exc_info=True)
            callback(100, f"错误: OpenAI 转录时发生意外错误: {e}")
            raise

if __name__ == '__main__':
    # Basic test (requires a dummy audio file and OpenAI API key set as an environment variable)
    # This is a placeholder for more robust testing
    logger.info("Testing OpenAIASR (requires dummy.wav and OPENAI_API_KEY env var)")
    if not os.getenv("OPENAI_API_KEY"):
        logger.warning("OPENAI_API_KEY environment variable not set. Skipping test.")
    else:
        # Create a dummy wav file for testing if it doesn't exist
        dummy_file = "dummy.wav"
        if not os.path.exists(dummy_file):
            try:
                import wave
                import audioop
                # Create a very short, silent WAV file
                with wave.open(dummy_file, 'w') as wf:
                    wf.setnchannels(1)
                    wf.setsampwidth(2)
                    wf.setframerate(16000)
                    # 1 second of silence
                    for _ in range(16000):
                        wf.writeframesraw(audioop.lin2lin(b'\x00\x00', 2, 2)) # Zero amplitude
                logger.info(f"Created dummy audio file: {dummy_file}")
            except Exception as e_dummy:
                logger.error(f"Could not create dummy audio file: {e_dummy}")
                dummy_file = None

        if dummy_file and os.path.exists(dummy_file):
            try:
                asr = OpenAIASR(audio_path=dummy_file, api_key=os.environ["OPENAI_API_KEY"])
                def progress_callback(percent, message):
                    print(f"Progress: {percent}% - {message}")
                
                result = asr.run(callback=progress_callback)
                print("\nTranscription Result:")
                for i, seg in enumerate(result.segments):
                    print(f"  Segment {i} ({seg.start_time}-{seg.end_time}): {seg.text}")
            except Exception as e_test:
                print(f"Test failed: {e_test}")
            finally:
                if os.path.exists(dummy_file): # Clean up dummy file
                    # os.remove(dummy_file)
                    logger.info(f"Test complete. Dummy file '{dummy_file}' can be manually removed.") 
                    # Kept for manual inspection if needed
# -*- coding: utf-8 -*-
"""
字幕处理核心逻辑模块。

该模块定义了 TranscriptThread 类，它封装了从视频处理到字幕生成、优化和翻译的完整流程。
此类中的方法被 SubtitleServiceImpl 调用，以执行具体的字幕处理任务，并通过生成器 (Generator)
流式返回处理进度和结果。

主要功能包括：
- 从视频提取音频。
- 音频转录为文本（ASR）。
- 从文本生成/优化字幕结构。
- 翻译字幕内容。
- 将上述步骤串联起来，形成完整的处理流水线。

该模块依赖于 `subtitle.core` 中的各个子模块（如 asr_data, optimize, split, transcribe, translate）
以及 `subtitle.utils` 中的辅助函数。
"""
import copy
import datetime
import os
import tempfile
from pathlib import Path
from typing import Dict, Any, Generator
import traceback
from enum import Enum # 确保 Enum 被导入

from subtitle.core.asr_data import ASRData, ASRDataSeg
from subtitle.core.optimize import SubtitleOptimizer
from subtitle.core.split import SubtitleSplitter
from subtitle.core.transcribe import transcribe as transcribe_core_func
from subtitle.core.translate import TranslatorType, TranslatorFactory
from subtitle.entities import TranscribeConfig, TranscribeModelEnum
from subtitle import config as sub_config
import ai_config_manager
import logging
from subtitle.utils.logger import setup_logger
from subtitle.utils.video_utils import video2audio

# 设置模块级 logger
logger = setup_logger("transcript_thread")
logger.setLevel(logging.DEBUG) 

def _create_progress(
    stage_name: str,
    percentage: int,
    message: str,
    is_error: bool = False,
    error_message: str = "",
    final_result_payload: Any = None,
    result_field_name: str = None
) -> Dict:
    """
    辅助函数，用于方便地创建进度更新字典。

    Args:
        stage_name (str): 当前处理阶段的名称。
        percentage (int): 当前阶段的完成百分比 (0-100)。
        message (str): 描述当前进度的消息。
        is_error (bool, optional): 是否发生错误。默认为 False。
        error_message (str, optional): 错误信息（如果 is_error 为 True）。默认为 ""。
        final_result_payload (Any, optional): 最终结果的负载。
            例如，对于 video_to_audio，可能是 {"audio_path": "..."}。
        result_field_name (str, optional): Protobuf oneof 字段的名称，用于包装 final_result_payload。
            例如 "video_to_audio_response"。

    Returns:
        Dict: 包含进度信息的字典。
    """
    update = {
        "stage_name": stage_name,
        "percentage": percentage,
        "message": message,
        "is_error": is_error,
        "error_message": error_message,
    }
    if final_result_payload is not None and result_field_name:
        update["final_result"] = {result_field_name: final_result_payload}
    elif final_result_payload is not None and not result_field_name:
        if isinstance(final_result_payload, dict):
            logger.warning(f"_create_progress: final_result_payload 是一个字典但没有提供 result_field_name。将 payload 合并到顶层。Payload: {final_result_payload}")
            update.update(final_result_payload) 
        else:
            logger.error(f"_create_progress: final_result_payload 已提供但没有 result_field_name，并且不是字典。Payload 类型: {type(final_result_payload)}。Payload 已被忽略。")
            
    return update

class TranscriptThread:
    """
    封装字幕处理流程的核心类。

    该类的方法通常是生成器，用于流式报告处理进度。
    它管理配置、路径设置以及调用底层的字幕处理模块。
    """
    def __init__(self):
        """
        初始化 TranscriptThread 实例。

        设置默认转录配置、输出目录基路径。
        """
        self.transcribe_config = TranscribeConfig(
            transcribe_model=getattr(TranscribeModelEnum, sub_config.DEFAULT_TRANSCRIBE_MODEL_NAME, TranscribeModelEnum.JIANYING), # 添加回退
            transcribe_language=sub_config.DEFAULT_TRANSCRIBE_LANGUAGE,
            use_asr_cache=sub_config.DEFAULT_USE_ASR_CACHE,
            need_word_time_stamp=sub_config.DEFAULT_NEED_WORD_TIMESTAMP
        )
        self.subtitle_dir_base = Path("outputs/subtitles") 
        self.subtitle_dir_base.mkdir(parents=True, exist_ok=True)

        self._current_progress_yield_callback = None
        self.asr_data_from_transcription: ASRData = None

    def _setup_paths_for_video(self, video_path_str: str):
        logger.info(f"调用 _setup_paths_for_video，参数: '{video_path_str}'")
        video_path_obj = Path(video_path_str)
        if not video_path_obj.exists():
            logger.error(f"视频文件不存在: {video_path_str}")
            raise ValueError(f"视频文件不存在: {video_path_str}")
        if not video_path_obj.is_file():
            logger.error(f"提供的路径不是一个文件: {video_path_str}")
            raise ValueError(f"提供的路径不是一个文件: {video_path_str}")
        
        self.video_path = video_path_obj
        self.subtitle_dir = self.subtitle_dir_base / video_path_obj.stem
        self.subtitle_dir.mkdir(parents=True, exist_ok=True)
        
        self.raw_srt_output_path = self.subtitle_dir / f"{sub_config.RAW_SUBTITLE_PREFIX}{video_path_obj.stem}{sub_config.SUBTITLE_SUFFIX}"
        self.optimized_subtitle_path = self.subtitle_dir / f"{sub_config.OPTIMIZED_SUBTITLE_PREFIX}{video_path_obj.stem}{sub_config.SUBTITLE_SUFFIX}"
        self.final_text_path = self.subtitle_dir / f"{sub_config.FINAL_TEXT_PREFIX}{video_path_obj.stem}{sub_config.TEXT_SUFFIX}"
        
        self.asr_data_from_transcription = None
        logger.debug(f"为视频 '{video_path_obj.name}' 设置的路径: raw_srt='{self.raw_srt_output_path}', optimized_srt='{self.optimized_subtitle_path}'")

    def _internal_progress_callback(self, value: float, message: str, stage_prefix: str = ""):
        progress = min(int(value), 100) 
        full_message = f"{stage_prefix}: {message}"
        logger.info(f"核心模块进度: {progress}% - {full_message}")
        if self._current_progress_yield_callback:
            try:
                self._current_progress_yield_callback(stage_prefix, progress, message)
            except Exception as e:
                logger.error(f"在 _internal_progress_callback 中尝试 yield 进度时发生错误: {e}")

    def extract_audio_from_video(self, video_path_str: str) -> Generator[Dict, None, None]:
        stage_name = "视频提取音频"
        temp_file_path = None 
        logger.info(f"调用 extract_audio_from_video，视频路径: '{video_path_str}'")
        try:
            if not video_path_str or not video_path_str.strip():
                err_msg = "视频文件路径不能为空。"
                logger.error(f"{stage_name}: {err_msg} (输入: '{video_path_str}')")
                yield _create_progress(stage_name, 0, err_msg, is_error=True, error_message=err_msg)
                return
            
            self._setup_paths_for_video(video_path_str) 
            yield _create_progress(stage_name, 0, f"开始从视频 '{self.video_path.name}' 提取音频...")
            
            temp_dir = tempfile.gettempdir()
            with tempfile.NamedTemporaryFile(suffix=".wav", dir=temp_dir, delete=False) as temp_file_obj:
                temp_file_path = temp_file_obj.name
            yield _create_progress(stage_name, 20, f"创建临时音频文件: {Path(temp_file_path).name}")

            is_success = video2audio(str(self.video_path), output=temp_file_path)
            
            if not is_success:
                err_msg = "使用 ffmpeg 进行音频转换失败。"
                yield _create_progress(stage_name, 50, err_msg, is_error=True, error_message=err_msg)
                return

            yield _create_progress(stage_name, 80, f"音频转换成功，临时文件: {Path(temp_file_path).name}")
            
            if not temp_file_path: 
                err_msg = "内部错误：未能生成有效的临时音频文件路径。"
                yield _create_progress(stage_name, 90, err_msg, is_error=True, error_message=err_msg)
                return

            final_payload = {"audio_path": temp_file_path}
            yield _create_progress(stage_name, 100, "音频提取完成。", final_result_payload=final_payload, result_field_name="video_to_audio_response")

        except Exception as e:
            tb_str = traceback.format_exc()
            logger.error(f"{stage_name} 发生错误: {e}\n{tb_str}")
            if temp_file_path and Path(temp_file_path).exists():
                try:
                    os.remove(temp_file_path)
                except OSError as ose:
                    logger.error(f"清理临时音频文件 {temp_file_path} 失败: {ose}")
            yield _create_progress(stage_name, 0, f"错误: {e}", is_error=True, error_message=f"{e}\n{tb_str}")

    def transcribe_audio(self, audio_path: str = None, audio_data: bytes = None, request_word_timestamps: bool = None) -> Generator[Dict, None, None]:
        stage_name = "音频转文字"
        temp_audio_file_for_transcription = None 
        transcribe_stage_prefix = "语音识别中" 
        progress_updates_queue = [] 
        def queue_progress_adapter(stage_prefix_ignored, percent, message):
            progress_updates_queue.append(_create_progress(transcribe_stage_prefix, percent, message))
        self._current_progress_yield_callback = queue_progress_adapter

        try:
            if audio_data and not audio_path:
                with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp_f:
                    tmp_f.write(audio_data)
                    audio_path = tmp_f.name
                temp_audio_file_for_transcription = audio_path 
                yield _create_progress(stage_name, 5, f"音频数据已保存到临时文件: {Path(audio_path).name}")
            elif not audio_path:
                raise ValueError("必须提供 audio_path (音频文件路径) 或 audio_data (音频字节数据)。")

            # 从 ai_config_manager 获取 ASR 服务配置
            # TODO: 使服务类型的选择更加灵活，可能基于 self.transcribe_config.transcribe_model 或其他参数
            asr_service_type_to_use = "OpenAI" # 示例：后续应根据实际情况确定或配置
            
            active_asr_configs = ai_config_manager.get_active_configs_by_type(asr_service_type_to_use)
            if not active_asr_configs:
                error_msg = f"错误: 未找到已启用的AI服务配置 (类型: {asr_service_type_to_use})。"
                logger.error(f"{stage_name}: {error_msg}")
                yield _create_progress(stage_name, 0, error_msg, is_error=True, error_message=error_msg)
                return

            # TODO: 实现更智能的配置选择逻辑 (例如，基于优先级或特定请求参数)
            chosen_asr_config = active_asr_configs[0] # 简单选择第一个可用的
            logger.info(f"使用AI服务配置: ID='{chosen_asr_config.provider_id}', 名称='{chosen_asr_config.display_name}'")

            api_key = chosen_asr_config.credentials.get("api_key")
            base_url = chosen_asr_config.attributes.get("api_base_url") 
            model_name = chosen_asr_config.attributes.get("default_model") 

            if not api_key:
                error_msg = f"错误: AI服务配置 '{chosen_asr_config.provider_id}' ({chosen_asr_config.display_name}) 缺少 API key。"
                logger.error(f"{stage_name}: {error_msg}")
                yield _create_progress(stage_name, 0, error_msg, is_error=True, error_message=error_msg)
                return
            
            effective_model_name = model_name if model_name else self.transcribe_config.transcribe_model.value
            yield _create_progress(stage_name, 10, f"开始使用模型 '{effective_model_name}' (服务: {chosen_asr_config.display_name}) 对 '{Path(audio_path).name}' 进行语音识别...")

            current_transcribe_config = copy.deepcopy(self.transcribe_config)
            if request_word_timestamps is not None:
                current_transcribe_config.need_word_time_stamp = request_word_timestamps
            
            logger.info(f"进行转录，配置: need_word_time_stamp={current_transcribe_config.need_word_time_stamp}")

            asr_data: ASRData = transcribe_core_func(
                audio_path,
                current_transcribe_config, 
                api_key=api_key,
                base_url=base_url,
                model_name=effective_model_name,
                callback=lambda val, msg: self._internal_progress_callback(val, msg, stage_prefix=transcribe_stage_prefix),
            )

            for prog_update in progress_updates_queue:
                yield prog_update
            progress_updates_queue.clear()

            if not asr_data or not asr_data.segments:
                yield _create_progress(stage_name, 90, "语音识别未生成有效文本。", is_error=True, error_message="识别结果为空")
                return

            yield _create_progress(stage_name, 90, "语音识别完成。")
            
            if hasattr(self, 'raw_srt_output_path') and self.raw_srt_output_path:
                 self.raw_srt_output_path.parent.mkdir(parents=True, exist_ok=True)
                 asr_data.save(save_path=str(self.raw_srt_output_path)) 
                 logger.info(f"原始SRT字幕文件已保存到: {self.raw_srt_output_path} (词级时间戳={current_transcribe_config.need_word_time_stamp})")
                 yield _create_progress(stage_name, 95, f"原始SRT已保存: {self.raw_srt_output_path.name}")

            self.asr_data_from_transcription = asr_data
            logger.info(f"已存储转录得到的 ASRData。词级时间戳: {current_transcribe_config.need_word_time_stamp}")

            final_payload = {"segments": asr_data.to_json()}
            yield _create_progress(stage_name, 100, "音频转文字成功。", final_result_payload=final_payload, result_field_name="audio_to_text_response")

        except Exception as e:
            tb_str = traceback.format_exc()
            logger.error(f"{stage_name} 发生错误: {e}\n{tb_str}")
            yield _create_progress(stage_name, 0, f"错误: {e}", is_error=True, error_message=f"{e}\n{tb_str}")
        finally:
            self._current_progress_yield_callback = None 
            if temp_audio_file_for_transcription and Path(temp_audio_file_for_transcription).exists():
                try:
                    os.remove(temp_audio_file_for_transcription)
                except OSError as ose:
                    logger.error(f"清理转录用的临时音频文件 {temp_audio_file_for_transcription} 失败: {ose}")
    
    def generate_subtitles_from_text(self, text: str = None, audio_path: str = None) -> Generator[Dict, None, None]:
        stage_name = "字幕生成与优化"
        progress_updates_queue = []
        def queue_progress_adapter(stage_prefix, percent, message):
            progress_updates_queue.append(_create_progress(stage_prefix, percent, message))
        self._current_progress_yield_callback = queue_progress_adapter

        try:
            yield _create_progress(stage_name, 0, "开始字幕生成/优化流程...")
            asr_data_loaded = False
            asr_data: ASRData = None 
            
            if self.asr_data_from_transcription:
                asr_data = self.asr_data_from_transcription
                logger.info(f"使用来自 transcribe_audio 的缓存 ASRData。")
                yield _create_progress(stage_name, 5, "使用缓存的语音识别结果。")
                asr_data_loaded = True
            
            if not asr_data_loaded and hasattr(self, 'raw_srt_output_path') and self.raw_srt_output_path and self.raw_srt_output_path.exists():
                try:
                    asr_data = ASRData.from_subtitle_file(self.raw_srt_output_path)
                    logger.info(f"从 {self.raw_srt_output_path} 加载 ASRData。")
                    yield _create_progress(stage_name, 10, f"从 '{self.raw_srt_output_path.name}' 加载字幕数据。")
                    asr_data_loaded = True
                except Exception as e:
                    logger.warning(f"从 {self.raw_srt_output_path} 加载 ASRData 失败: {e}。")
                    asr_data_loaded = False
            
            if not asr_data_loaded:
                if text:
                    logger.info("未找到缓存或文件中的 ASRData。从提供的文本创建 ASRData。")
                    segments = []
                    lines = text.splitlines()
                    current_time_ms = 0
                    for line_text in lines:
                        if line_text.strip():
                            seg_duration_ms = len(line_text.strip().split()) * 500 
                            segments.append(ASRDataSeg(text=line_text.strip(), start_time=current_time_ms, end_time=current_time_ms + seg_duration_ms))
                            current_time_ms += seg_duration_ms + 100 
                    asr_data = ASRData(segments)
                    yield _create_progress(stage_name, 10, "从纯文本创建了基础字幕结构（无精确时间戳）。")
                else: 
                    err_msg = "需要有效的语音识别结果或文本输入进行字幕生成/优化。"
                    yield _create_progress(stage_name, 0, err_msg, is_error=True, error_message=err_msg)
                    return
            
            if not asr_data: 
                err_msg = "未能加载或创建 ASR 数据。"
                yield _create_progress(stage_name, 0, err_msg, is_error=True, error_message=err_msg)
                return

            split_stage = "字幕断句"
            yield _create_progress(split_stage, 0, "开始字幕断句...")
            splitter = SubtitleSplitter(
                thread_num=sub_config.SPLITTER_THREAD_NUM, model=sub_config.SPLITTER_MODEL_NAME,
                temperature=sub_config.SPLITTER_TEMPERATURE, timeout=sub_config.SPLITTER_TIMEOUT,
                retry_times=sub_config.SPLITTER_RETRY_TIMES
            )
            asr_data_split = splitter.split_subtitle(copy.deepcopy(asr_data)) 
            yield _create_progress(split_stage, 100, "字幕断句完成。")

            optimize_stage = "字幕优化"
            yield _create_progress(optimize_stage, 0, "开始字幕优化...")

            # --- 新的优化器配置获取逻辑 ---
            # 获取优化器使用的 AI 配置，映射配置名称到 provider_type
            default_optimizer_service_name = sub_config.DEFAULT_TRANSLATOR_SERVICE_NAME  # "OPENAI"

            # 映射配置名称到 AI 配置管理器的 provider_type
            service_name_to_provider_type = {
                "OPENAI": "OpenAI",
                "DEEPLX": "DeepSeek",  # 或者可以是其他映射
                # 可以根据需要添加更多映射
            }

            optimizer_provider_type = service_name_to_provider_type.get(default_optimizer_service_name, "OpenAI")
            active_optimizer_configs = ai_config_manager.get_active_configs_by_type(optimizer_provider_type)

            if not active_optimizer_configs:
                error_msg = f"错误: 未找到已启用的优化服务配置 (类型: {optimizer_provider_type})。"
                logger.error(f"{optimize_stage}: {error_msg}")
                yield _create_progress(optimize_stage, 0, error_msg, is_error=True, error_message=error_msg)
                return

            chosen_optimizer_config = active_optimizer_configs[0]
            logger.info(f"使用优化服务配置: ID='{chosen_optimizer_config.provider_id}', 名称='{chosen_optimizer_config.display_name}'")

            optimizer_api_key = chosen_optimizer_config.credentials.get("api_key")
            optimizer_base_url = chosen_optimizer_config.attributes.get("api_base_url")
            optimizer_model = chosen_optimizer_config.attributes.get("default_model")

            if optimizer_provider_type == "OpenAI" and not optimizer_api_key:
                 error_msg = f"错误: OpenAI优化服务配置 '{chosen_optimizer_config.provider_id}' 缺少 API key。"
                 logger.error(f"{optimize_stage}: {error_msg}")
                 yield _create_progress(optimize_stage, 0, error_msg, is_error=True, error_message=error_msg)
                 return

            effective_optimizer_model = optimizer_model if optimizer_model else sub_config.OPTIMIZER_MODEL_NAME
            # --- 结束新的优化器配置获取逻辑 ---

            self.finished_subtitle_length_optimizer = 0
            self.total_subtitle_length_optimizer = len(asr_data_split.segments)

            def optimizer_progress_adapter(result_batch_dict: Dict):
                self.finished_subtitle_length_optimizer += len(result_batch_dict)
                percent = 0
                if self.total_subtitle_length_optimizer > 0:
                    percent = min(int((self.finished_subtitle_length_optimizer / self.total_subtitle_length_optimizer) * 100), 100)
                if self._current_progress_yield_callback:
                   self._current_progress_yield_callback(optimize_stage, percent, f"已优化 {self.finished_subtitle_length_optimizer}/{self.total_subtitle_length_optimizer} 段")

            optimizer = SubtitleOptimizer(
                custom_prompt="",
                model=effective_optimizer_model,
                api_key=optimizer_api_key,
                base_url=optimizer_base_url,
                batch_num=sub_config.OPTIMIZER_BATCH_NUM,
                thread_num=sub_config.OPTIMIZER_THREAD_NUM,
                update_callback=optimizer_progress_adapter
            )
            yield _create_progress(optimize_stage, 10, f"使用 '{chosen_optimizer_config.display_name}' 服务进行优化...")
            asr_data_optimized = optimizer.optimize_subtitle(asr_data_split)
            
            for prog_update in progress_updates_queue: 
                yield prog_update
            progress_updates_queue.clear()
            yield _create_progress(optimize_stage, 100, "字幕优化完成。")

            if hasattr(self, 'optimized_subtitle_path') and self.optimized_subtitle_path:
                asr_data_optimized.save(save_path=str(self.optimized_subtitle_path))
                yield _create_progress(stage_name, 90, f"优化字幕已保存: {self.optimized_subtitle_path.name}")
            
            srt_content = asr_data_optimized.to_srt()
            ass_content = asr_data_optimized.to_ass()

            final_payload = {"srt_content": srt_content, "ass_content": ass_content}
            yield _create_progress(stage_name, 100, "字幕生成/优化成功。", final_result_payload=final_payload, result_field_name="generate_subtitles_response")

        except Exception as e:
            tb_str = traceback.format_exc()
            logger.error(f"{stage_name} 发生错误: {e}\n{tb_str}")
            yield _create_progress(stage_name, 0, f"错误: {e}", is_error=True, error_message=f"{e}\n{tb_str}")
        finally:
            self._current_progress_yield_callback = None

    def translate_subtitle_content(self, subtitle_content: str, target_language: str, input_format: str = "srt") -> Generator[Dict, None, None]:
        logger.info(f"收到字幕翻译请求：目标语言 '{target_language}', 输入格式 '{input_format}'")
        stage_name = "字幕翻译"
        
        progress_updates_queue = []
        def queue_progress_adapter(stage_prefix, percent, message):
            progress_updates_queue.append(_create_progress(stage_prefix, percent, message))
        self._current_progress_yield_callback = queue_progress_adapter

        try:
            yield _create_progress(stage_name, 0, f"开始将字幕翻译为 '{target_language}'...")

            if input_format.lower() == "srt":
                asr_data = ASRData.from_srt(subtitle_content)
            elif input_format.lower() == "ass":
                asr_data = ASRData.from_ass(subtitle_content)
            elif input_format.lower() == "text": 
                segments = []
                lines = subtitle_content.splitlines()
                for line_text in lines:
                    if line_text.strip(): 
                        segments.append(ASRDataSeg(text=line_text.strip(), start_time=0, end_time=0))
                asr_data = ASRData(segments)
            else:
                raise ValueError(f"不支持的字幕输入格式: {input_format}")
            yield _create_progress(stage_name, 10, f"从 {input_format.upper()} 内容加载字幕数据成功。")

            # --- 新的配置获取逻辑 ---
            # 获取翻译器使用的 AI 配置，映射配置名称到 provider_type
            default_translator_service_name = sub_config.DEFAULT_TRANSLATOR_SERVICE_NAME  # "OPENAI"

            # 映射配置名称到 AI 配置管理器的 provider_type（复用优化器的映射）
            service_name_to_provider_type = {
                "OPENAI": "OpenAI",
                "DEEPLX": "DeepSeek",  # 或者可以是其他映射
                # 可以根据需要添加更多映射
            }

            translator_provider_type = service_name_to_provider_type.get(default_translator_service_name, "OpenAI")
            active_translator_configs = ai_config_manager.get_active_configs_by_type(translator_provider_type)

            if not active_translator_configs:
                error_msg = f"错误: 未找到已启用的翻译服务配置 (类型: {translator_provider_type})。"
                logger.error(f"{stage_name}: {error_msg}")
                yield _create_progress(stage_name, 0, error_msg, is_error=True, error_message=error_msg)
                return

            chosen_translator_config = active_translator_configs[0]
            logger.info(f"使用翻译服务配置: ID='{chosen_translator_config.provider_id}', 名称='{chosen_translator_config.display_name}'")

            api_key_from_config = chosen_translator_config.credentials.get("api_key")
            base_url_from_config = chosen_translator_config.attributes.get("api_base_url")
            model_from_config = chosen_translator_config.attributes.get("default_model")

            if translator_provider_type == "OpenAI" and not api_key_from_config:
                 error_msg = f"错误: OpenAI翻译服务配置 '{chosen_translator_config.provider_id}' 缺少 API key。"
                 logger.error(f"{stage_name}: {error_msg}")
                 yield _create_progress(stage_name, 0, error_msg, is_error=True, error_message=error_msg)
                 return

            effective_translator_model = model_from_config if model_from_config else getattr(sub_config, "DEFAULT_TRANSLATOR_MODEL_NAME", sub_config.SPLITTER_MODEL_NAME)

            # 映射 AI 配置的 provider_type 到 TranslatorType 枚举
            provider_type_to_translator_type = {
                "OpenAI": "OPENAI",
                "DeepSeek": "OPENAI",  # DeepSeek 也使用 OpenAI 兼容的接口
                "Gemini": "OPENAI",    # Gemini 也使用 OpenAI 兼容的接口
            }

            translator_type_name = provider_type_to_translator_type.get(translator_provider_type, "OPENAI")

            try:
                final_translator_type_enum = TranslatorType[translator_type_name]
            except KeyError:
                logger.error(f"配置的翻译服务类型 '{translator_type_name}' 在核心 TranslatorType 枚举中不存在。")
                raise ValueError(f"不支持的翻译服务类型: {translator_type_name}")
            # --- 结束新的配置获取逻辑 ---

            self.finished_subtitle_length_translator = 0 
            self.total_subtitle_length_translator = len(asr_data.segments)

            def translator_progress_adapter(result_batch_dict: Dict):
                self.finished_subtitle_length_translator += len(result_batch_dict)
                percent = 0
                if self.total_subtitle_length_translator > 0:
                    percent = min(int((self.finished_subtitle_length_translator / self.total_subtitle_length_translator) * 100), 100)
                if self._current_progress_yield_callback: 
                   self._current_progress_yield_callback(stage_name, percent, f"已翻译 {self.finished_subtitle_length_translator}/{self.total_subtitle_length_translator} 段")

            translator = TranslatorFactory.create_translator(
                translator_type=final_translator_type_enum,
                target_language=target_language,
                model=effective_translator_model,
                api_key=api_key_from_config,    
                base_url=base_url_from_config,  
                update_callback=translator_progress_adapter 
            )
            
            logger.info(f"准备调用翻译服务，目标语言: '{target_language}'，使用服务类型: '{translator_provider_type}', 模型: '{effective_translator_model}'")
            yield _create_progress(stage_name, 20, f"使用 '{chosen_translator_config.display_name}' 服务进行翻译...")
            translated_asr_data = translator.translate_subtitle(copy.deepcopy(asr_data)) 
            
            for prog_update in progress_updates_queue: 
                yield prog_update
            progress_updates_queue.clear()

            if not translated_asr_data or not translated_asr_data.segments:
                yield _create_progress(stage_name, 90, "翻译未生成有效结果。", is_error=True, error_message="翻译结果为空")
                return

            yield _create_progress(stage_name, 90, "字幕翻译完成。")
            
            translated_srt_content = translated_asr_data.to_srt(layout="仅译文") 

            if hasattr(self, 'subtitle_dir') and self.subtitle_dir and hasattr(self, 'video_path') and self.video_path:
                video_stem = self.video_path.stem
                save_path_srt = self.subtitle_dir / f"{sub_config.TRANSLATED_FILE_PREFIX}{video_stem}-TranslatedTo-{target_language}{sub_config.SUBTITLE_SUFFIX}"
                translated_asr_data.save(save_path=str(save_path_srt), layout="仅译文") 
                logger.info(f"翻译后的字幕已保存到: {save_path_srt}")
                yield _create_progress(stage_name, 95, f"翻译字幕文件已保存: {save_path_srt.name}")

            final_payload = {"translated_subtitle_content": translated_srt_content}
            yield _create_progress(stage_name, 100, "字幕翻译成功。", final_result_payload=final_payload, result_field_name="translate_subtitles_response")

        except Exception as e:
            tb_str = traceback.format_exc()
            logger.error(f"{stage_name} 发生错误: {e}\n{tb_str}")
            yield _create_progress(stage_name, 0, f"错误: {e}", is_error=True, error_message=f"{e}\n{tb_str}")
        finally:
            self._current_progress_yield_callback = None

    def process_video_to_translated_subtitles(self, video_path_str: str, target_language: str, request_word_timestamps: bool = None) -> Generator[Dict, None, None]:
        logger.info(f"开始完整流程：视频到翻译字幕。视频路径='{video_path_str}', 目标语言='{target_language}', 请求词级时间戳='{request_word_timestamps}'")
        
        audio_path_result = None
        temp_audio_file_to_clean = None
        successful_audio_to_text_payload = None 

        for progress_update in self.extract_audio_from_video(video_path_str):
            yield progress_update
            if progress_update.get("is_error"): return
            if progress_update.get("final_result", {}).get("video_to_audio_response"):
                audio_path_result = progress_update["final_result"]["video_to_audio_response"]["audio_path"]
                if audio_path_result and tempfile.gettempdir() in str(Path(audio_path_result).parent):
                    temp_audio_file_to_clean = audio_path_result
        
        if not audio_path_result:
            yield _create_progress("完整流程", 0, "音频提取失败，中止流程。", is_error=True, error_message="未能从视频中提取音频路径")
            return

        asr_data_json_result = None
        for progress_update in self.transcribe_audio(audio_path=audio_path_result, request_word_timestamps=request_word_timestamps):
            yield progress_update 
            if progress_update.get("is_error"): return
            if not progress_update.get("is_error") and progress_update.get("final_result", {}).get("audio_to_text_response"):
                successful_audio_to_text_payload = progress_update["final_result"]["audio_to_text_response"]
                asr_data_json_result = successful_audio_to_text_payload.get("segments")
        
        if not asr_data_json_result: 
            yield _create_progress("完整流程", 0, "音频转文字失败或未生成有效分段，中止流程。", is_error=True, error_message="未能从音频转录文本或结果为空")
            return
        
        original_srt_content_for_translation = None
        for progress_update in self.generate_subtitles_from_text(text=None):
            yield progress_update
            if progress_update.get("is_error"): return
            if progress_update.get("final_result", {}).get("generate_subtitles_response"):
                original_srt_content_for_translation = progress_update["final_result"]["generate_subtitles_response"]["srt_content"]

        if not original_srt_content_for_translation:
             yield _create_progress("完整流程", 0, "字幕优化/生成失败，中止流程。", is_error=True, error_message="未能生成或优化原始字幕内容")
             return

        translated_subtitle_final_content = None
        for progress_update in self.translate_subtitle_content(
            subtitle_content=original_srt_content_for_translation,
            target_language=target_language,
            input_format="srt"
        ):
            yield progress_update
            if progress_update.get("is_error"): return
            if progress_update.get("final_result", {}).get("translate_subtitles_response"):
                translated_subtitle_final_content = progress_update["final_result"]["translate_subtitles_response"]["translated_subtitle_content"]

        if not translated_subtitle_final_content:
            yield _create_progress("完整流程", 0, "字幕翻译失败，中止流程。", is_error=True, error_message="未能翻译字幕内容")
            return
            
        final_payload_full_process = {"translated_subtitle_content": translated_subtitle_final_content}
        yield _create_progress(
            "完整流程处理完成", 100,
            "视频到翻译字幕流程成功完成。",
            final_result_payload=final_payload_full_process,
            result_field_name="process_video_to_translated_subtitles_response"
        )

        if successful_audio_to_text_payload:
            logger.info("完整流程结束，重新发送 audio_to_text_response 以确保 segments 可用。")
            yield _create_progress(
                stage_name="音频转文字结果(最终)", 
                percentage=100,
                message="音频转文字的最终分段结果。",
                final_result_payload=successful_audio_to_text_payload, 
                result_field_name="audio_to_text_response"
            )

        if temp_audio_file_to_clean and Path(temp_audio_file_to_clean).exists():
            try:
                os.remove(temp_audio_file_to_clean)
            except OSError as e:
                logger.error(f"完整流程：删除临时音频文件 {temp_audio_file_to_clean} 失败: {e}")


# --- 主程序入口，用于模块独立测试 ---
if __name__ == '__main__':
    logger.info("开始 TranscriptThread 模块测试示例...")
    tt = TranscriptThread()
    
    example_video_dir = Path(tempfile.gettempdir()) / "transcript_thread_test_assets"
    example_video_dir.mkdir(parents=True, exist_ok=True)
    example_video = example_video_dir / "example_video_for_tt.mp4"

    if not example_video.exists():
        try:
            import subprocess
            subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True, shell=False)
            subprocess.run([
                "ffmpeg", "-y", "-f", "lavfi", "-i", "color=c=black:s=320x240:d=2", 
                "-f", "lavfi", "-i", "anullsrc=channel_layout=stereo:sample_rate=44100:d=2",
                "-c:v", "libx264", "-c:a", "aac", "-shortest", str(example_video)
            ], capture_output=True, check=True, shell=False) 
            logger.info(f"已创建用于测试的虚拟视频: {example_video}")
        except FileNotFoundError:
            logger.error("ffmpeg 未找到。无法创建虚拟视频。")
            exit()
        except subprocess.CalledProcessError as e_sub:
            logger.error(f"ffmpeg 执行失败: {e_sub}.")
            exit()
        except Exception as e_create:
            logger.error(f"创建虚拟视频时发生未知错误: {e_create}。")
            exit()

    if example_video.exists():
        logger.info("\n--- 测试 extract_audio_from_video ---")
        audio_file_path_for_next_step = None
        temp_audio_to_clean_main = None
        for progress in tt.extract_audio_from_video(str(example_video)):
            logger.info(f"服务层进度: {progress}")
            if progress.get("final_result", {}).get("video_to_audio_response"):
                audio_file_path_for_next_step = progress["final_result"]["video_to_audio_response"]["audio_path"]
                if audio_file_path_for_next_step and tempfile.gettempdir() in str(Path(audio_file_path_for_next_step).parent):
                    temp_audio_to_clean_main = audio_file_path_for_next_step 
        
        if audio_file_path_for_next_step:
            logger.info(f"\n--- 测试 transcribe_audio (使用: {audio_file_path_for_next_step}) ---")
            asr_segments_json_for_next_step = None
            for progress in tt.transcribe_audio(audio_path=audio_file_path_for_next_step, request_word_timestamps=True):
                logger.info(f"服务层进度: {progress}")
                if progress.get("final_result", {}).get("audio_to_text_response"):
                    asr_segments_json_for_next_step = progress["final_result"]["audio_to_text_response"].get("segments")
            
            if asr_segments_json_for_next_step and hasattr(tt, 'raw_srt_output_path') and tt.raw_srt_output_path.exists():
                logger.info(f"\n--- 测试 generate_subtitles_from_text (使用转录结果，从 {tt.raw_srt_output_path} 加载) ---")
                optimized_srt_for_next_step = None
                for progress in tt.generate_subtitles_from_text(text=None): 
                     logger.info(f"服务层进度: {progress}")
                     if progress.get("final_result", {}).get("generate_subtitles_response"):
                        optimized_srt_for_next_step = progress["final_result"]["generate_subtitles_response"]["srt_content"]

                if optimized_srt_for_next_step:
                    logger.info(f"\n--- 测试 translate_subtitle_content (使用优化后的 SRT) ---")
                    for progress in tt.translate_subtitle_content(subtitle_content=optimized_srt_for_next_step, target_language="es", input_format="srt"):
                        logger.info(f"服务层进度: {progress}")
            else:
                logger.warning("由于转录失败或原始 SRT 文件未找到，跳过 generate_subtitles_from_text 和 translate_subtitle_content 测试。")
            
            if temp_audio_to_clean_main and Path(temp_audio_to_clean_main).exists():
                try:
                    os.remove(temp_audio_to_clean_main)
                except OSError as e:
                    logger.error(f"清理测试用音频文件时出错: {e}")
        else:
            logger.warning("由于音频提取失败，跳过后续依赖音频文件的测试。")
        
        logger.info("\n\n--- 测试完整流程 process_video_to_translated_subtitles ---")
        tt_full = TranscriptThread() 
        for progress in tt_full.process_video_to_translated_subtitles(str(example_video), "fr", request_word_timestamps=True):
            logger.info(f"服务层完整流程进度: {progress}")

        logger.info("TranscriptThread 模块测试示例结束。")
    else:
        logger.warning(f"示例视频 {example_video} 未找到或无法创建。跳过测试。")

# -*- coding: utf-8 -*-
"""
自定义异常类模块

提供细化的异常处理，便于错误分类和用户友好的错误信息。
"""
from typing import Dict, Any, Optional
import traceback


class SubtitleProcessingError(Exception):
    """字幕处理基础异常类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: str = None, 
        details: Dict[str, Any] = None,
        user_message: str = None,
        recoverable: bool = True
    ):
        """
        初始化异常
        
        Args:
            message: 技术错误信息
            error_code: 错误代码，用于程序处理
            details: 错误详细信息字典
            user_message: 用户友好的错误信息
            recoverable: 是否可恢复的错误
        """
        self.message = message
        self.error_code = error_code or self.__class__.__name__.upper()
        self.details = details or {}
        self.user_message = user_message or self._generate_user_message()
        self.recoverable = recoverable
        self.traceback_info = traceback.format_exc()
        
        super().__init__(self.message)
    
    def _generate_user_message(self) -> str:
        """生成用户友好的错误信息"""
        return f"处理过程中发生错误: {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于序列化"""
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "user_message": self.user_message,
            "details": self.details,
            "recoverable": self.recoverable,
            "traceback": self.traceback_info if self.details.get("include_traceback") else None
        }


class FileProcessingError(SubtitleProcessingError):
    """文件处理错误"""
    
    def _generate_user_message(self) -> str:
        file_path = self.details.get("file_path", "未知文件")
        operation = self.details.get("operation", "处理")
        return f"文件{operation}失败: {file_path}"


class VideoProcessingError(FileProcessingError):
    """视频处理错误"""
    
    def _generate_user_message(self) -> str:
        return f"视频处理失败: {self.details.get('file_path', '未知视频文件')}"


class AudioExtractionError(VideoProcessingError):
    """音频提取错误"""
    
    def _generate_user_message(self) -> str:
        return f"从视频提取音频失败: {self.details.get('video_path', '未知视频')}"


class TranscriptionError(SubtitleProcessingError):
    """转录错误"""
    
    def _generate_user_message(self) -> str:
        model = self.details.get("model", "未知模型")
        return f"语音转录失败 (模型: {model})"


class ASRServiceError(TranscriptionError):
    """ASR服务错误"""
    
    def _generate_user_message(self) -> str:
        service = self.details.get("service", "ASR服务")
        return f"{service}连接失败，请检查网络连接或API配置"


class TranslationError(SubtitleProcessingError):
    """翻译错误"""
    
    def _generate_user_message(self) -> str:
        target_lang = self.details.get("target_language", "目标语言")
        return f"翻译到{target_lang}失败"


class APIError(SubtitleProcessingError):
    """API调用错误"""
    
    def __init__(self, message: str, api_name: str = None, status_code: int = None, **kwargs):
        self.api_name = api_name
        self.status_code = status_code
        details = kwargs.get("details", {})
        details.update({
            "api_name": api_name,
            "status_code": status_code
        })
        kwargs["details"] = details
        super().__init__(message, **kwargs)
    
    def _generate_user_message(self) -> str:
        api_name = self.api_name or "API服务"
        if self.status_code:
            return f"{api_name}调用失败 (状态码: {self.status_code})"
        return f"{api_name}调用失败"


class OpenAIAPIError(APIError):
    """OpenAI API错误"""
    
    def _generate_user_message(self) -> str:
        if self.status_code == 401:
            return "OpenAI API密钥无效，请检查配置"
        elif self.status_code == 429:
            return "OpenAI API调用频率超限，请稍后重试"
        elif self.status_code == 500:
            return "OpenAI服务暂时不可用，请稍后重试"
        return f"OpenAI API调用失败 (错误码: {self.status_code})"


class ConfigurationError(SubtitleProcessingError):
    """配置错误"""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        self.config_key = config_key
        details = kwargs.get("details", {})
        details["config_key"] = config_key
        kwargs["details"] = details
        kwargs["recoverable"] = False  # 配置错误通常不可恢复
        super().__init__(message, **kwargs)
    
    def _generate_user_message(self) -> str:
        if self.config_key:
            return f"配置项 '{self.config_key}' 设置错误: {self.message}"
        return f"配置错误: {self.message}"


class ValidationError(SubtitleProcessingError):
    """数据验证错误"""
    
    def __init__(self, message: str, field_name: str = None, field_value: Any = None, **kwargs):
        self.field_name = field_name
        self.field_value = field_value
        details = kwargs.get("details", {})
        details.update({
            "field_name": field_name,
            "field_value": str(field_value) if field_value is not None else None
        })
        kwargs["details"] = details
        super().__init__(message, **kwargs)
    
    def _generate_user_message(self) -> str:
        if self.field_name:
            return f"参数 '{self.field_name}' 验证失败: {self.message}"
        return f"数据验证失败: {self.message}"


class CacheError(SubtitleProcessingError):
    """缓存错误"""
    
    def _generate_user_message(self) -> str:
        return "缓存操作失败，将跳过缓存继续处理"


class NetworkError(SubtitleProcessingError):
    """网络错误"""
    
    def _generate_user_message(self) -> str:
        return "网络连接失败，请检查网络设置"


class TimeoutError(SubtitleProcessingError):
    """超时错误"""
    
    def __init__(self, message: str, timeout_seconds: int = None, **kwargs):
        self.timeout_seconds = timeout_seconds
        details = kwargs.get("details", {})
        details["timeout_seconds"] = timeout_seconds
        kwargs["details"] = details
        super().__init__(message, **kwargs)
    
    def _generate_user_message(self) -> str:
        if self.timeout_seconds:
            return f"操作超时 ({self.timeout_seconds}秒)，请稍后重试"
        return "操作超时，请稍后重试"


class ResourceError(SubtitleProcessingError):
    """资源错误 (内存、磁盘空间等)"""
    
    def __init__(self, message: str, resource_type: str = None, **kwargs):
        self.resource_type = resource_type
        details = kwargs.get("details", {})
        details["resource_type"] = resource_type
        kwargs["details"] = details
        kwargs["recoverable"] = False  # 资源错误通常需要用户干预
        super().__init__(message, **kwargs)
    
    def _generate_user_message(self) -> str:
        if self.resource_type == "memory":
            return "内存不足，请关闭其他程序后重试"
        elif self.resource_type == "disk":
            return "磁盘空间不足，请清理磁盘空间后重试"
        return f"系统资源不足: {self.message}"


# 异常工厂函数
def create_file_error(message: str, file_path: str, operation: str = "处理") -> FileProcessingError:
    """创建文件处理错误"""
    return FileProcessingError(
        message=message,
        details={"file_path": file_path, "operation": operation}
    )


def create_api_error(
    message: str, 
    api_name: str, 
    status_code: int = None,
    response_data: Dict = None
) -> APIError:
    """创建API错误"""
    details = {"response_data": response_data} if response_data else {}
    return APIError(
        message=message,
        api_name=api_name,
        status_code=status_code,
        details=details
    )


def create_config_error(message: str, config_key: str) -> ConfigurationError:
    """创建配置错误"""
    return ConfigurationError(
        message=message,
        config_key=config_key
    )


def create_validation_error(
    message: str, 
    field_name: str, 
    field_value: Any = None
) -> ValidationError:
    """创建验证错误"""
    return ValidationError(
        message=message,
        field_name=field_name,
        field_value=field_value
    )

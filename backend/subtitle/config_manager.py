# -*- coding: utf-8 -*-
"""
配置管理模块

使用 Pydantic 进行配置验证和管理，支持环境变量覆盖。
提供类型安全的配置访问和验证。
"""
import os
from pathlib import Path
from typing import Optional, List
from pydantic import BaseSettings, Field, validator
import logging

logger = logging.getLogger(__name__)


class ServerConfig(BaseSettings):
    """服务器配置"""
    
    # gRPC服务器配置
    grpc_port: int = Field(default=50051, env="GRPC_PORT", description="gRPC服务器端口")
    grpc_max_workers: int = Field(default=10, env="GRPC_MAX_WORKERS", description="gRPC最大工作线程数")
    grpc_max_message_length: int = Field(default=100 * 1024 * 1024, env="GRPC_MAX_MESSAGE_LENGTH", description="gRPC最大消息长度")
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL", description="日志级别")
    log_format: str = Field(default="%(asctime)s - %(name)s - %(levelname)s - %(message)s", env="LOG_FORMAT")
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是 {valid_levels} 之一')
        return v.upper()


class AIServiceConfig(BaseSettings):
    """AI服务配置"""
    
    # OpenAI配置
    openai_api_key: Optional[str] = Field(default=None, env="OPENAI_API_KEY", description="OpenAI API密钥")
    openai_base_url: str = Field(
        default="https://generativelanguage.googleapis.com/v1beta/openai/",
        env="OPENAI_BASE_URL",
        description="OpenAI API基础URL"
    )
    openai_timeout: int = Field(default=60, env="OPENAI_TIMEOUT", description="OpenAI API超时时间(秒)")
    
    # 模型配置
    default_model: str = Field(default="gemini-2.5-flash-preview-04-17", env="DEFAULT_MODEL")
    
    @validator('openai_api_key')
    def validate_api_key(cls, v):
        if v and len(v) < 10:
            logger.warning("OpenAI API密钥长度可能不正确")
        return v


class TranscriptionConfig(BaseSettings):
    """转录配置"""
    
    # 转录模型配置
    transcribe_model: str = Field(default="JIANYING", env="TRANSCRIBE_MODEL", description="转录模型名称")
    transcribe_language: str = Field(default="en", env="TRANSCRIBE_LANGUAGE", description="转录语言")
    use_asr_cache: bool = Field(default=True, env="USE_ASR_CACHE", description="是否使用ASR缓存")
    need_word_timestamp: bool = Field(default=True, env="NEED_WORD_TIMESTAMP", description="是否需要词级时间戳")
    
    # 缓存配置
    cache_ttl: int = Field(default=3600, env="CACHE_TTL", description="缓存TTL(秒)")
    cache_max_size: int = Field(default=1000, env="CACHE_MAX_SIZE", description="缓存最大条目数")


class ProcessingConfig(BaseSettings):
    """处理配置"""
    
    # 分割器配置
    splitter_model: str = Field(default="gemini-2.5-flash-preview-04-17", env="SPLITTER_MODEL")
    splitter_temperature: float = Field(default=0.3, env="SPLITTER_TEMPERATURE", ge=0.0, le=2.0)
    splitter_timeout: int = Field(default=60, env="SPLITTER_TIMEOUT", gt=0)
    splitter_retry_times: int = Field(default=1, env="SPLITTER_RETRY_TIMES", ge=0)
    splitter_thread_num: int = Field(default=4, env="SPLITTER_THREAD_NUM", gt=0)
    
    # 优化器配置
    optimizer_model: str = Field(default="gemini-2.5-flash-preview-04-17", env="OPTIMIZER_MODEL")
    optimizer_batch_num: int = Field(default=10, env="OPTIMIZER_BATCH_NUM", gt=0)
    optimizer_thread_num: int = Field(default=5, env="OPTIMIZER_THREAD_NUM", gt=0)
    
    # 翻译器配置
    translator_model: str = Field(default="gemini-2.5-flash-preview-04-17", env="TRANSLATOR_MODEL")
    translator_thread_num: int = Field(default=4, env="TRANSLATOR_THREAD_NUM", gt=0)
    translator_batch_num: int = Field(default=10, env="TRANSLATOR_BATCH_NUM", gt=0)
    translator_service: str = Field(default="OPENAI", env="TRANSLATOR_SERVICE")
    target_language: str = Field(default="Chinese", env="TARGET_LANGUAGE")
    
    # DeepL配置
    deeplx_api_endpoint: Optional[str] = Field(default=None, env="DEEPLX_API_ENDPOINT")


class PathConfig(BaseSettings):
    """路径配置"""
    
    # 基础路径
    root_path: Path = Field(default_factory=lambda: Path(__file__).parent)
    work_path: Path = Field(default_factory=lambda: Path(__file__).parent.parent / "work-dir")
    appdata_path: Path = Field(default_factory=lambda: Path(__file__).parent.parent / "AppData")
    
    # 输出路径
    output_base_dir: str = Field(default="outputs/subtitles", env="OUTPUT_BASE_DIR")
    default_save_dir: str = Field(default="~/Downloads/Subtitles", env="DEFAULT_SAVE_DIR")
    
    # 缓存路径
    cache_dir: Optional[Path] = Field(default=None, env="CACHE_DIR")
    log_dir: Optional[Path] = Field(default=None, env="LOG_DIR")
    model_dir: Optional[Path] = Field(default=None, env="MODEL_DIR")
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 设置默认路径
        if self.cache_dir is None:
            self.cache_dir = self.appdata_path / "cache"
        if self.log_dir is None:
            self.log_dir = self.appdata_path / "logs"
        if self.model_dir is None:
            self.model_dir = self.appdata_path / "models"
        
        # 创建必要的目录
        for path in [self.work_path, self.appdata_path, self.cache_dir, self.log_dir, self.model_dir]:
            path.mkdir(parents=True, exist_ok=True)


class FileConfig(BaseSettings):
    """文件配置"""
    
    # 文件前缀和后缀
    raw_subtitle_prefix: str = Field(default="【原始字幕】", env="RAW_SUBTITLE_PREFIX")
    split_subtitle_prefix: str = Field(default="【断句字幕】", env="SPLIT_SUBTITLE_PREFIX")
    optimized_subtitle_prefix: str = Field(default="【优化字幕】", env="OPTIMIZED_SUBTITLE_PREFIX")
    translated_file_prefix: str = Field(default="【翻译】", env="TRANSLATED_FILE_PREFIX")
    final_text_prefix: str = Field(default="【最终纯文字】", env="FINAL_TEXT_PREFIX")
    
    subtitle_suffix: str = Field(default=".srt", env="SUBTITLE_SUFFIX")
    text_suffix: str = Field(default=".txt", env="TEXT_SUFFIX")
    
    # 支持的格式
    supported_video_formats: List[str] = Field(
        default=["mp4", "avi", "mov", "mkv", "flv", "wmv"],
        env="SUPPORTED_VIDEO_FORMATS"
    )
    supported_audio_formats: List[str] = Field(
        default=["wav", "mp3", "flac", "aac", "ogg"],
        env="SUPPORTED_AUDIO_FORMATS"
    )
    supported_subtitle_formats: List[str] = Field(
        default=["srt", "ass", "vtt", "txt", "json"],
        env="SUPPORTED_SUBTITLE_FORMATS"
    )


class CacheConfig(BaseSettings):
    """缓存配置"""

    # 缓存后端配置
    cache_backend: str = Field(default="auto", env="CACHE_BACKEND", description="缓存后端类型: auto, redis, file")

    # Redis配置
    redis_host: str = Field(default="localhost", env="REDIS_HOST")
    redis_port: int = Field(default=6379, env="REDIS_PORT", gt=0, le=65535)
    redis_db: int = Field(default=0, env="REDIS_DB", ge=0)
    redis_password: Optional[str] = Field(default=None, env="REDIS_PASSWORD")
    redis_timeout: int = Field(default=5, env="REDIS_TIMEOUT", gt=0)

    # 文件缓存配置
    file_cache_max_size: int = Field(default=1000, env="FILE_CACHE_MAX_SIZE", gt=0)

    # 缓存TTL配置
    transcription_cache_ttl: int = Field(default=7200, env="TRANSCRIPTION_CACHE_TTL", gt=0)  # 2小时
    translation_cache_ttl: int = Field(default=86400, env="TRANSLATION_CACHE_TTL", gt=0)    # 24小时
    subtitle_cache_ttl: int = Field(default=3600, env="SUBTITLE_CACHE_TTL", gt=0)           # 1小时

    # 缓存策略
    enable_transcription_cache: bool = Field(default=True, env="ENABLE_TRANSCRIPTION_CACHE")
    enable_translation_cache: bool = Field(default=True, env="ENABLE_TRANSLATION_CACHE")
    enable_subtitle_cache: bool = Field(default=True, env="ENABLE_SUBTITLE_CACHE")

    @validator('cache_backend')
    def validate_cache_backend(cls, v):
        valid_backends = ['auto', 'redis', 'file']
        if v.lower() not in valid_backends:
            raise ValueError(f'缓存后端必须是 {valid_backends} 之一')
        return v.lower()


class MonitoringConfig(BaseSettings):
    """监控配置"""

    # 监控开关
    enable_monitoring: bool = Field(default=True, env="ENABLE_MONITORING", description="是否启用监控")
    enable_performance_monitoring: bool = Field(default=True, env="ENABLE_PERFORMANCE_MONITORING")
    enable_system_monitoring: bool = Field(default=True, env="ENABLE_SYSTEM_MONITORING")
    enable_health_checks: bool = Field(default=True, env="ENABLE_HEALTH_CHECKS")

    # 监控间隔
    system_metrics_interval: int = Field(default=60, env="SYSTEM_METRICS_INTERVAL", gt=0, description="系统指标收集间隔(秒)")
    health_check_interval: int = Field(default=300, env="HEALTH_CHECK_INTERVAL", gt=0, description="健康检查间隔(秒)")

    # 性能阈值
    performance_duration_threshold: float = Field(default=5.0, env="PERFORMANCE_DURATION_THRESHOLD", gt=0)
    performance_memory_threshold: float = Field(default=100.0, env="PERFORMANCE_MEMORY_THRESHOLD", gt=0)

    # 系统阈值
    cpu_warning_threshold: float = Field(default=80.0, env="CPU_WARNING_THRESHOLD", ge=0, le=100)
    memory_warning_threshold: float = Field(default=80.0, env="MEMORY_WARNING_THRESHOLD", ge=0, le=100)
    disk_warning_threshold: float = Field(default=85.0, env="DISK_WARNING_THRESHOLD", ge=0, le=100)

    # 指标保留
    metrics_retention_hours: int = Field(default=24, env="METRICS_RETENTION_HOURS", gt=0)
    max_performance_metrics: int = Field(default=1000, env="MAX_PERFORMANCE_METRICS", gt=0)


class LoggingConfig(BaseSettings):
    """日志配置"""

    # 日志级别
    log_level: str = Field(default="INFO", env="LOG_LEVEL", description="日志级别")

    # 日志输出
    enable_console_logging: bool = Field(default=True, env="ENABLE_CONSOLE_LOGGING")
    enable_file_logging: bool = Field(default=True, env="ENABLE_FILE_LOGGING")
    enable_structured_logging: bool = Field(default=True, env="ENABLE_STRUCTURED_LOGGING")

    # 文件日志配置
    log_file_max_size: int = Field(default=10485760, env="LOG_FILE_MAX_SIZE", gt=0, description="日志文件最大大小(字节)")
    log_file_backup_count: int = Field(default=5, env="LOG_FILE_BACKUP_COUNT", ge=0)

    # 日志格式
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        env="LOG_FORMAT"
    )

    # 特殊日志器
    enable_performance_logging: bool = Field(default=True, env="ENABLE_PERFORMANCE_LOGGING")
    enable_error_logging: bool = Field(default=True, env="ENABLE_ERROR_LOGGING")
    enable_audit_logging: bool = Field(default=False, env="ENABLE_AUDIT_LOGGING")

    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是 {valid_levels} 之一')
        return v.upper()


class AsyncConfig(BaseSettings):
    """异步处理配置"""

    # 异步处理开关
    enable_async_processing: bool = Field(default=True, env="ENABLE_ASYNC_PROCESSING", description="是否启用异步处理")
    enable_stream_processing: bool = Field(default=True, env="ENABLE_STREAM_PROCESSING")
    enable_batch_processing: bool = Field(default=True, env="ENABLE_BATCH_PROCESSING")

    # 任务管理器配置
    max_thread_workers: int = Field(default=4, env="MAX_THREAD_WORKERS", gt=0, le=20)
    max_process_workers: int = Field(default=2, env="MAX_PROCESS_WORKERS", gt=0, le=8)
    enable_process_pool: bool = Field(default=True, env="ENABLE_PROCESS_POOL")

    # 并发控制
    max_concurrent_tasks: int = Field(default=10, env="MAX_CONCURRENT_TASKS", gt=0, le=50)
    max_concurrent_streams: int = Field(default=5, env="MAX_CONCURRENT_STREAMS", gt=0, le=20)
    max_concurrent_batches: int = Field(default=3, env="MAX_CONCURRENT_BATCHES", gt=0, le=10)
    max_concurrent_batch_items: int = Field(default=5, env="MAX_CONCURRENT_BATCH_ITEMS", gt=0, le=20)

    # 任务超时配置
    default_task_timeout: int = Field(default=300, env="DEFAULT_TASK_TIMEOUT", gt=0)  # 5分钟
    transcription_task_timeout: int = Field(default=1800, env="TRANSCRIPTION_TASK_TIMEOUT", gt=0)  # 30分钟
    translation_task_timeout: int = Field(default=600, env="TRANSLATION_TASK_TIMEOUT", gt=0)  # 10分钟
    optimization_task_timeout: int = Field(default=300, env="OPTIMIZATION_TASK_TIMEOUT", gt=0)  # 5分钟

    # 重试配置
    default_max_retries: int = Field(default=3, env="DEFAULT_MAX_RETRIES", ge=0, le=10)
    enable_exponential_backoff: bool = Field(default=True, env="ENABLE_EXPONENTIAL_BACKOFF")
    base_retry_delay: float = Field(default=1.0, env="BASE_RETRY_DELAY", gt=0)
    max_retry_delay: float = Field(default=60.0, env="MAX_RETRY_DELAY", gt=0)

    # 队列配置
    task_queue_size: int = Field(default=1000, env="TASK_QUEUE_SIZE", gt=0)
    stream_buffer_size: int = Field(default=100, env="STREAM_BUFFER_SIZE", gt=0)

    # 清理配置
    task_cleanup_interval: int = Field(default=3600, env="TASK_CLEANUP_INTERVAL", gt=0)  # 1小时
    task_max_age_hours: int = Field(default=24, env="TASK_MAX_AGE_HOURS", gt=0)
    stream_max_age_hours: int = Field(default=12, env="STREAM_MAX_AGE_HOURS", gt=0)
    batch_max_age_hours: int = Field(default=48, env="BATCH_MAX_AGE_HOURS", gt=0)


class SubtitleConfig(BaseSettings):
    """主配置类，整合所有子配置"""

    # 版本信息
    version: str = Field(default="v1.3.3", env="APP_VERSION")
    app_name: str = Field(default="VideoCaptioner", env="APP_NAME")

    # 子配置
    server: ServerConfig = Field(default_factory=ServerConfig)
    ai_service: AIServiceConfig = Field(default_factory=AIServiceConfig)
    transcription: TranscriptionConfig = Field(default_factory=TranscriptionConfig)
    processing: ProcessingConfig = Field(default_factory=ProcessingConfig)
    paths: PathConfig = Field(default_factory=PathConfig)
    files: FileConfig = Field(default_factory=FileConfig)
    cache: CacheConfig = Field(default_factory=CacheConfig)
    monitoring: MonitoringConfig = Field(default_factory=MonitoringConfig)
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    async_processing: AsyncConfig = Field(default_factory=AsyncConfig)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        
    def setup_environment(self):
        """设置环境变量"""
        if self.ai_service.openai_base_url:
            os.environ["OPENAI_BASE_URL"] = self.ai_service.openai_base_url
        if self.ai_service.openai_api_key:
            os.environ["OPENAI_API_KEY"] = self.ai_service.openai_api_key
        else:
            logger.warning("OPENAI_API_KEY 未配置。依赖 OpenAI 的功能可能无法工作。")
    
    def validate_config(self) -> bool:
        """验证配置完整性"""
        errors = []
        
        # 检查必要的API密钥
        if not self.ai_service.openai_api_key:
            errors.append("缺少 OPENAI_API_KEY")
        
        # 检查路径是否可写
        try:
            test_file = self.paths.work_path / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()
        except Exception as e:
            errors.append(f"工作目录不可写: {e}")
        
        if errors:
            logger.error(f"配置验证失败: {', '.join(errors)}")
            return False
        
        logger.info("配置验证通过")
        return True


# 全局配置实例
config = SubtitleConfig()

# 设置环境变量
config.setup_environment()

# 验证配置
if not config.validate_config():
    logger.warning("配置验证失败，某些功能可能无法正常工作")

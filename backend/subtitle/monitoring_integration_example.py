# -*- coding: utf-8 -*-
"""
监控和日志集成示例

展示如何在现有的字幕处理代码中集成监控和日志功能。
"""
import time
from typing import Dict, Any, Generator

from .logging_manager import get_logger, log_operation, log_performance, log_error
from .performance_monitor import monitor_performance, monitor_memory_usage
from .monitoring_dashboard import get_monitoring_dashboard, start_monitoring
from .exceptions import SubtitleProcessingError, TranscriptionError

# 获取日志器
logger = get_logger(__name__)


class MonitoredTranscriptThread:
    """集成监控的转录线程示例"""
    
    def __init__(self):
        self.logger = get_logger("transcript_thread")
        
        # 启动监控
        start_monitoring()
        
        self.logger.info("监控转录线程初始化完成", extra={
            "component": "transcript_thread",
            "initialization": True
        })
    
    @monitor_performance(
        operation_name="video_to_audio_extraction",
        duration_threshold_seconds=30.0,
        memory_threshold_mb=200.0
    )
    def extract_audio_from_video(self, video_path: str) -> Dict[str, Any]:
        """带监控的视频音频提取"""
        
        # 记录操作开始
        log_operation(
            operation="extract_audio_from_video",
            logger_name="transcript_thread",
            video_path=video_path,
            file_size_mb=self._get_file_size_mb(video_path)
        )
        
        try:
            # 模拟音频提取过程
            self.logger.info(f"开始提取音频: {video_path}")
            
            # 这里应该是实际的音频提取逻辑
            time.sleep(2)  # 模拟处理时间
            
            audio_path = video_path.replace('.mp4', '.wav')
            
            result = {
                "audio_path": audio_path,
                "duration": 120.5,
                "sample_rate": 44100,
                "channels": 2
            }
            
            self.logger.info(f"音频提取完成: {audio_path}", extra={
                "operation": "extract_audio_from_video",
                "success": True,
                "audio_duration": result["duration"],
                "sample_rate": result["sample_rate"]
            })
            
            return result
            
        except Exception as e:
            # 记录错误
            log_error(
                error=e,
                operation="extract_audio_from_video",
                logger_name="transcript_thread",
                video_path=video_path
            )
            raise TranscriptionError(
                message=f"音频提取失败: {str(e)}",
                details={"video_path": video_path}
            )
    
    @monitor_performance(
        operation_name="audio_transcription",
        duration_threshold_seconds=60.0,
        memory_threshold_mb=500.0
    )
    def transcribe_audio_with_monitoring(
        self,
        audio_path: str,
        config: Dict[str, Any]
    ) -> Generator[Dict[str, Any], None, None]:
        """带监控的音频转录"""
        
        operation_start_time = time.time()
        
        # 记录操作开始
        self.logger.info("开始音频转录", extra={
            "operation": "audio_transcription",
            "audio_path": audio_path,
            "model": config.get("model", "unknown"),
            "language": config.get("language", "unknown"),
            "file_size_mb": self._get_file_size_mb(audio_path)
        })
        
        try:
            # 模拟转录过程
            stages = [
                ("音频预处理", 10),
                ("语音识别", 50),
                ("文本后处理", 80),
                ("结果整理", 100)
            ]
            
            for stage_name, percentage in stages:
                # 模拟处理时间
                time.sleep(1)
                
                yield {
                    "stage_name": stage_name,
                    "percentage": percentage,
                    "message": f"正在{stage_name}...",
                    "is_error": False
                }
                
                # 记录阶段完成
                self.logger.debug(f"转录阶段完成: {stage_name}", extra={
                    "operation": "audio_transcription",
                    "stage": stage_name,
                    "percentage": percentage
                })
            
            # 模拟转录结果
            transcription_result = {
                "segments": [
                    {
                        "text": "这是一个示例转录文本",
                        "start_time": 0,
                        "end_time": 3000,
                        "confidence": 0.95
                    }
                ],
                "language": config.get("language", "zh"),
                "model": config.get("model", "JIANYING")
            }
            
            # 记录成功完成
            operation_duration = time.time() - operation_start_time
            
            self.logger.info("音频转录完成", extra={
                "operation": "audio_transcription",
                "success": True,
                "duration": operation_duration,
                "segments_count": len(transcription_result["segments"]),
                "total_text_length": sum(len(seg["text"]) for seg in transcription_result["segments"])
            })
            
            # 记录性能指标
            log_performance(
                operation="audio_transcription",
                duration=operation_duration,
                success=True,
                segments_count=len(transcription_result["segments"]),
                audio_path=audio_path
            )
            
            yield {
                "stage_name": "转录完成",
                "percentage": 100,
                "message": "转录成功完成",
                "is_error": False,
                "final_result": {
                    "audio_to_text_response": transcription_result
                }
            }
            
        except Exception as e:
            operation_duration = time.time() - operation_start_time
            
            # 记录错误
            self.logger.error("音频转录失败", extra={
                "operation": "audio_transcription",
                "success": False,
                "duration": operation_duration,
                "error_type": type(e).__name__,
                "error_message": str(e),
                "audio_path": audio_path
            }, exc_info=True)
            
            # 记录性能指标（失败）
            log_performance(
                operation="audio_transcription",
                duration=operation_duration,
                success=False,
                error_type=type(e).__name__,
                audio_path=audio_path
            )
            
            yield {
                "stage_name": "转录错误",
                "percentage": 0,
                "message": f"转录失败: {str(e)}",
                "is_error": True,
                "error_message": str(e)
            }
    
    @monitor_memory_usage(
        operation_name="subtitle_optimization",
        threshold_mb=100.0,
        force_gc=True
    )
    def optimize_subtitle_with_monitoring(
        self,
        subtitle_content: str,
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """带监控的字幕优化"""
        
        self.logger.info("开始字幕优化", extra={
            "operation": "subtitle_optimization",
            "content_length": len(subtitle_content),
            "model": config.get("model", "unknown")
        })
        
        try:
            # 模拟优化过程
            time.sleep(3)
            
            optimized_content = f"[优化] {subtitle_content}"
            
            result = {
                "optimized_content": optimized_content,
                "original_length": len(subtitle_content),
                "optimized_length": len(optimized_content),
                "improvement_score": 0.85
            }
            
            self.logger.info("字幕优化完成", extra={
                "operation": "subtitle_optimization",
                "success": True,
                "original_length": result["original_length"],
                "optimized_length": result["optimized_length"],
                "improvement_score": result["improvement_score"]
            })
            
            return result
            
        except Exception as e:
            log_error(
                error=e,
                operation="subtitle_optimization",
                logger_name="transcript_thread",
                content_length=len(subtitle_content)
            )
            raise
    
    def _get_file_size_mb(self, file_path: str) -> float:
        """获取文件大小(MB)"""
        try:
            from pathlib import Path
            return Path(file_path).stat().st_size / 1024 / 1024
        except Exception:
            return 0.0
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        dashboard = get_monitoring_dashboard()
        return dashboard.get_dashboard_data()
    
    def generate_monitoring_report(self) -> str:
        """生成监控报告"""
        dashboard = get_monitoring_dashboard()
        return dashboard.generate_dashboard_report()


def demonstrate_monitoring_integration():
    """演示监控集成"""
    
    # 创建监控转录线程
    transcript_thread = MonitoredTranscriptThread()
    
    # 1. 视频音频提取（带监控）
    print("=== 视频音频提取 ===")
    try:
        audio_result = transcript_thread.extract_audio_from_video("/path/to/video.mp4")
        print(f"音频提取成功: {audio_result}")
    except Exception as e:
        print(f"音频提取失败: {e}")
    
    # 2. 音频转录（带监控）
    print("\n=== 音频转录 ===")
    config = {
        "model": "JIANYING",
        "language": "zh",
        "use_cache": True
    }
    
    for progress in transcript_thread.transcribe_audio_with_monitoring("/path/to/audio.wav", config):
        print(f"转录进度: {progress}")
    
    # 3. 字幕优化（带监控）
    print("\n=== 字幕优化 ===")
    try:
        optimization_result = transcript_thread.optimize_subtitle_with_monitoring(
            "这是一个测试字幕内容",
            {"model": "gemini-2.5-flash"}
        )
        print(f"优化结果: {optimization_result}")
    except Exception as e:
        print(f"优化失败: {e}")
    
    # 4. 获取监控状态
    print("\n=== 监控状态 ===")
    monitoring_status = transcript_thread.get_monitoring_status()
    print(f"系统状态: {monitoring_status['health_status']['overall_status']}")
    print(f"CPU使用率: {monitoring_status['system_metrics']['cpu']['percent']}%")
    print(f"内存使用率: {monitoring_status['system_metrics']['memory']['percent']}%")
    
    # 5. 生成监控报告
    print("\n=== 监控报告 ===")
    report = transcript_thread.generate_monitoring_report()
    print(report)


def integration_guide():
    """
    集成指南
    
    展示如何将监控和日志集成到现有代码中。
    """
    
    # 1. 在类初始化中添加日志器
    """
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        start_monitoring()  # 启动监控
    """
    
    # 2. 使用性能监控装饰器
    """
    @monitor_performance(
        operation_name="custom_operation",
        duration_threshold_seconds=10.0,
        memory_threshold_mb=100.0
    )
    def your_method(self):
        # 你的代码逻辑
        pass
    """
    
    # 3. 记录操作日志
    """
    def your_operation(self, param1, param2):
        # 记录操作开始
        log_operation(
            operation="your_operation",
            logger_name="your_logger",
            param1=param1,
            param2=param2
        )
        
        try:
            # 执行操作
            result = do_something()
            
            # 记录成功
            self.logger.info("操作成功", extra={
                "operation": "your_operation",
                "success": True,
                "result_size": len(result)
            })
            
            return result
            
        except Exception as e:
            # 记录错误
            log_error(
                error=e,
                operation="your_operation",
                logger_name="your_logger"
            )
            raise
    """
    
    # 4. 获取监控数据
    """
    def get_system_status(self):
        dashboard = get_monitoring_dashboard()
        return dashboard.get_dashboard_data()
    """


if __name__ == "__main__":
    demonstrate_monitoring_integration()

# -*- coding: utf-8 -*-
"""
监控仪表板模块

提供系统监控、健康检查和实时指标展示功能。
"""
import time
import threading
import psutil
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import deque, defaultdict
import json

from .logging_manager import get_logger
from .performance_monitor import get_performance_collector, PerformanceAnalyzer
from .cache_manager import get_cache_manager, get_transcription_cache, get_translation_cache

logger = get_logger(__name__)


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, collection_interval: int = 60):
        self.collection_interval = collection_interval
        self.metrics_history: deque = deque(maxlen=1440)  # 24小时数据 (每分钟一个点)
        self.is_running = False
        self._thread: Optional[threading.Thread] = None
        self._lock = threading.RLock()
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_running:
            return
        
        self.is_running = True
        self._thread = threading.Thread(target=self._collect_metrics, daemon=True)
        self._thread.start()
        logger.info("系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self._thread and self._thread.is_alive():
            self._thread.join(timeout=5)
        logger.info("系统监控已停止")
    
    def _collect_metrics(self):
        """收集系统指标"""
        while self.is_running:
            try:
                metrics = self._get_system_metrics()
                with self._lock:
                    self.metrics_history.append(metrics)
                
                time.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"收集系统指标失败: {e}")
                time.sleep(self.collection_interval)
    
    def _get_system_metrics(self) -> Dict[str, Any]:
        """获取当前系统指标"""
        try:
            # CPU指标
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            # 内存指标
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_gb = memory.used / 1024 / 1024 / 1024
            memory_total_gb = memory.total / 1024 / 1024 / 1024
            
            # 磁盘指标
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            disk_used_gb = disk.used / 1024 / 1024 / 1024
            disk_total_gb = disk.total / 1024 / 1024 / 1024
            
            # 进程指标
            process = psutil.Process()
            process_memory_mb = process.memory_info().rss / 1024 / 1024
            process_cpu_percent = process.cpu_percent()
            thread_count = process.num_threads()
            
            return {
                "timestamp": time.time(),
                "datetime": datetime.now().isoformat(),
                "cpu": {
                    "percent": cpu_percent,
                    "count": cpu_count
                },
                "memory": {
                    "percent": memory_percent,
                    "used_gb": round(memory_used_gb, 2),
                    "total_gb": round(memory_total_gb, 2)
                },
                "disk": {
                    "percent": disk_percent,
                    "used_gb": round(disk_used_gb, 2),
                    "total_gb": round(disk_total_gb, 2)
                },
                "process": {
                    "memory_mb": round(process_memory_mb, 2),
                    "cpu_percent": process_cpu_percent,
                    "thread_count": thread_count
                }
            }
        except Exception as e:
            logger.error(f"获取系统指标失败: {e}")
            return {
                "timestamp": time.time(),
                "datetime": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前系统指标"""
        return self._get_system_metrics()
    
    def get_metrics_history(self, hours: int = 1) -> List[Dict[str, Any]]:
        """获取历史指标"""
        cutoff_time = time.time() - (hours * 3600)
        with self._lock:
            return [m for m in self.metrics_history if m.get("timestamp", 0) >= cutoff_time]
    
    def get_metrics_summary(self, hours: int = 1) -> Dict[str, Any]:
        """获取指标摘要"""
        history = self.get_metrics_history(hours)
        if not history:
            return {"message": "没有历史数据"}
        
        # 计算平均值和峰值
        cpu_values = [m.get("cpu", {}).get("percent", 0) for m in history if "cpu" in m]
        memory_values = [m.get("memory", {}).get("percent", 0) for m in history if "memory" in m]
        disk_values = [m.get("disk", {}).get("percent", 0) for m in history if "disk" in m]
        
        return {
            "time_range": f"最近 {hours} 小时",
            "data_points": len(history),
            "cpu": {
                "avg_percent": round(sum(cpu_values) / len(cpu_values), 2) if cpu_values else 0,
                "max_percent": max(cpu_values) if cpu_values else 0,
                "min_percent": min(cpu_values) if cpu_values else 0
            },
            "memory": {
                "avg_percent": round(sum(memory_values) / len(memory_values), 2) if memory_values else 0,
                "max_percent": max(memory_values) if memory_values else 0,
                "min_percent": min(memory_values) if memory_values else 0
            },
            "disk": {
                "avg_percent": round(sum(disk_values) / len(disk_values), 2) if disk_values else 0,
                "max_percent": max(disk_values) if disk_values else 0,
                "min_percent": min(disk_values) if disk_values else 0
            }
        }


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.checks = {
            "system": self._check_system_health,
            "cache": self._check_cache_health,
            "performance": self._check_performance_health,
            "logging": self._check_logging_health
        }
    
    def check_health(self, components: List[str] = None) -> Dict[str, Any]:
        """执行健康检查"""
        if components is None:
            components = list(self.checks.keys())
        
        health_report = {
            "overall_status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "checks": {},
            "warnings": [],
            "errors": []
        }
        
        for component in components:
            if component in self.checks:
                try:
                    check_result = self.checks[component]()
                    health_report["checks"][component] = check_result
                    
                    # 更新整体状态
                    if check_result.get("status") == "error":
                        health_report["overall_status"] = "error"
                        health_report["errors"].append(f"{component}: {check_result.get('message', '未知错误')}")
                    elif check_result.get("status") == "warning" and health_report["overall_status"] == "healthy":
                        health_report["overall_status"] = "warning"
                        health_report["warnings"].append(f"{component}: {check_result.get('message', '警告')}")
                        
                except Exception as e:
                    health_report["checks"][component] = {
                        "status": "error",
                        "message": f"健康检查失败: {str(e)}"
                    }
                    health_report["overall_status"] = "error"
                    health_report["errors"].append(f"{component}: 检查失败")
        
        return health_report
    
    def _check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        try:
            # CPU检查
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            issues = []
            status = "healthy"
            
            if cpu_percent > 90:
                issues.append(f"CPU使用率过高: {cpu_percent}%")
                status = "warning"
            
            if memory.percent > 90:
                issues.append(f"内存使用率过高: {memory.percent}%")
                status = "warning"
            
            if disk.percent > 90:
                issues.append(f"磁盘使用率过高: {disk.percent}%")
                status = "warning"
            
            return {
                "status": status,
                "message": "; ".join(issues) if issues else "系统状态正常",
                "details": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "disk_percent": disk.percent
                }
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"系统检查失败: {str(e)}"
            }
    
    def _check_cache_health(self) -> Dict[str, Any]:
        """检查缓存健康状态"""
        try:
            cache_manager = get_cache_manager()
            transcription_cache = get_transcription_cache()
            translation_cache = get_translation_cache()
            
            # 获取缓存统计
            cache_stats = cache_manager.get_stats()
            trans_stats = transcription_cache.get_stats()
            transl_stats = translation_cache.get_stats()
            
            issues = []
            status = "healthy"
            
            # 检查缓存命中率
            if "hit_rate" in trans_stats and trans_stats["hit_rate"] < 30:
                issues.append(f"转录缓存命中率较低: {trans_stats['hit_rate']}%")
                status = "warning"
            
            # 检查缓存大小
            if cache_stats.get("backend") == "file":
                total_size_mb = cache_stats.get("total_size_mb", 0)
                if total_size_mb > 1000:  # 1GB
                    issues.append(f"文件缓存占用空间过大: {total_size_mb}MB")
                    status = "warning"
            
            return {
                "status": status,
                "message": "; ".join(issues) if issues else "缓存状态正常",
                "details": {
                    "cache_backend": cache_stats.get("backend", "unknown"),
                    "transcription_hit_rate": trans_stats.get("hit_rate", 0),
                    "translation_hit_rate": transl_stats.get("hit_rate", 0)
                }
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"缓存检查失败: {str(e)}"
            }
    
    def _check_performance_health(self) -> Dict[str, Any]:
        """检查性能健康状态"""
        try:
            analyzer = PerformanceAnalyzer()
            analysis = analyzer.analyze_performance(hours=1)
            
            issues = []
            status = "healthy"
            
            # 检查成功率
            success_rate = analysis.get("success_rate", 100)
            if success_rate < 95:
                issues.append(f"操作成功率较低: {success_rate:.1f}%")
                status = "warning"
            
            # 检查平均执行时间
            avg_duration = analysis.get("avg_duration", 0)
            if avg_duration > 10:
                issues.append(f"平均执行时间过长: {avg_duration:.2f}秒")
                status = "warning"
            
            # 检查慢操作数量
            slow_ops_count = len(analysis.get("slow_operations", []))
            if slow_ops_count > 10:
                issues.append(f"慢操作过多: {slow_ops_count}个")
                status = "warning"
            
            return {
                "status": status,
                "message": "; ".join(issues) if issues else "性能状态正常",
                "details": {
                    "success_rate": success_rate,
                    "avg_duration": avg_duration,
                    "slow_operations_count": slow_ops_count,
                    "total_operations": analysis.get("total_operations", 0)
                }
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"性能检查失败: {str(e)}"
            }
    
    def _check_logging_health(self) -> Dict[str, Any]:
        """检查日志健康状态"""
        try:
            from .logging_manager import get_logging_manager
            
            logging_manager = get_logging_manager()
            log_stats = logging_manager.get_log_stats()
            
            issues = []
            status = "healthy"
            
            if not log_stats.get("setup_complete", False):
                issues.append("日志系统未正确初始化")
                status = "error"
            
            handlers_count = log_stats.get("handlers_count", 0)
            if handlers_count == 0:
                issues.append("没有配置日志处理器")
                status = "warning"
            
            return {
                "status": status,
                "message": "; ".join(issues) if issues else "日志状态正常",
                "details": log_stats
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"日志检查失败: {str(e)}"
            }


class MonitoringDashboard:
    """监控仪表板"""
    
    def __init__(self):
        self.system_monitor = SystemMonitor()
        self.health_checker = HealthChecker()
        self.performance_analyzer = PerformanceAnalyzer()
    
    def start(self):
        """启动监控"""
        self.system_monitor.start_monitoring()
        logger.info("监控仪表板已启动")
    
    def stop(self):
        """停止监控"""
        self.system_monitor.stop_monitoring()
        logger.info("监控仪表板已停止")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        return {
            "timestamp": datetime.now().isoformat(),
            "system_metrics": self.system_monitor.get_current_metrics(),
            "system_summary": self.system_monitor.get_metrics_summary(hours=1),
            "health_status": self.health_checker.check_health(),
            "performance_analysis": self.performance_analyzer.analyze_performance(hours=1),
            "cache_stats": {
                "transcription": get_transcription_cache().get_stats(),
                "translation": get_translation_cache().get_stats()
            }
        }
    
    def generate_dashboard_report(self) -> str:
        """生成仪表板报告"""
        data = self.get_dashboard_data()
        
        report = f"""
# 系统监控仪表板报告

## 时间: {data['timestamp']}

## 系统状态
- 整体健康状态: {data['health_status']['overall_status'].upper()}
- CPU使用率: {data['system_metrics'].get('cpu', {}).get('percent', 0):.1f}%
- 内存使用率: {data['system_metrics'].get('memory', {}).get('percent', 0):.1f}%
- 磁盘使用率: {data['system_metrics'].get('disk', {}).get('percent', 0):.1f}%

## 性能概览
- 总操作数: {data['performance_analysis'].get('total_operations', 0)}
- 成功率: {data['performance_analysis'].get('success_rate', 0):.1f}%
- 平均执行时间: {data['performance_analysis'].get('avg_duration', 0):.2f}秒

## 缓存状态
- 转录缓存命中率: {data['cache_stats']['transcription'].get('hit_rate', 0):.1f}%
- 翻译缓存命中率: {data['cache_stats']['translation'].get('hit_rate', 0):.1f}%

## 警告和错误
"""
        
        warnings = data['health_status'].get('warnings', [])
        errors = data['health_status'].get('errors', [])
        
        if warnings:
            report += "### 警告:\n"
            for warning in warnings:
                report += f"- {warning}\n"
        
        if errors:
            report += "### 错误:\n"
            for error in errors:
                report += f"- {error}\n"
        
        if not warnings and not errors:
            report += "- 无警告或错误\n"
        
        return report


# 全局监控实例
_monitoring_dashboard = None


def get_monitoring_dashboard() -> MonitoringDashboard:
    """获取全局监控仪表板"""
    global _monitoring_dashboard
    if _monitoring_dashboard is None:
        _monitoring_dashboard = MonitoringDashboard()
    return _monitoring_dashboard


def start_monitoring():
    """启动监控"""
    get_monitoring_dashboard().start()


def stop_monitoring():
    """停止监控"""
    get_monitoring_dashboard().stop()


def get_dashboard_data() -> Dict[str, Any]:
    """获取仪表板数据"""
    return get_monitoring_dashboard().get_dashboard_data()


def generate_dashboard_report() -> str:
    """生成仪表板报告"""
    return get_monitoring_dashboard().generate_dashboard_report()

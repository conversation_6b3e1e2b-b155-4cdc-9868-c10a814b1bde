# app/core/storage/models.py
from datetime import datetime, UTC
from sqlalchemy.orm import declarative_base, Mapped, mapped_column
from sqlalchemy import String, Integer, DateTime, JSON, Index

Base = declarative_base()


def utc_now():
    """返回当前的 UTC 时间"""
    return datetime.now(UTC)


class ASRCache(Base):
    """语音识别缓存表"""

    __tablename__ = "asr_cache"

    id = mapped_column(Integer, primary_key=True)
    crc32_hex = mapped_column(String(8), nullable=False, index=True)
    asr_type = mapped_column(String(50), nullable=False)  # ASR服务类型
    result_data = mapped_column(JSON, nullable=False)  # ASR结果数据
    created_at = mapped_column(DateTime, default=utc_now)
    updated_at = mapped_column(DateTime, default=utc_now, onupdate=utc_now)

    __table_args__ = (
        Index("idx_asr_lookup", crc32_hex, asr_type),
    )


class TranslationCache(Base):
    """翻译结果缓存表"""

    __tablename__ = "translation_cache"

    id = mapped_column(Integer, primary_key=True)
    source_text = mapped_column(String(255), nullable=False)
    translated_text = mapped_column(String(255), nullable=False)
    translator_type = mapped_column(String(50), nullable=False)
    params = mapped_column(JSON)
    content_hash = mapped_column(String(32), nullable=False)
    created_at = mapped_column(DateTime, default=utc_now)

    __table_args__ = (Index("idx_translation_lookup", content_hash, translator_type),)

    def __repr__(self):
        return f"<Translation(id={self.id}, translator={self.translator_type})>"


class LLMCache(Base):
    """LLM调用结果缓存表"""

    __tablename__ = "llm_cache"

    id = mapped_column(Integer, primary_key=True)
    prompt = mapped_column(String(255), nullable=False)
    result = mapped_column(String(255), nullable=False)
    model_name = mapped_column(String(100), nullable=False)
    params = mapped_column(JSON)
    content_hash = mapped_column(String(32), nullable=False)
    created_at = mapped_column(DateTime, default=utc_now)

    __table_args__ = (Index("idx_llm_lookup", content_hash, model_name),)

    def __repr__(self):
        return f"<LlmResult(id={self.id}, model={self.model_name})>"


class UsageStatistics(Base):
    """使用统计表"""

    __tablename__ = "usage_statistics"

    id = mapped_column(Integer, primary_key=True)
    operation_type = mapped_column(String(50), nullable=False)
    service_name = mapped_column(String(50), nullable=False)
    call_count = mapped_column(Integer, default=0)
    token_count = mapped_column(Integer, default=0)
    last_updated = mapped_column(DateTime, default=utc_now)

    __table_args__ = (
        Index("idx_usage_lookup", operation_type, service_name),
    )

    def __repr__(self):
        return f"<UsageStatistics({self.operation_type}:{self.service_name})>"


class DailyServiceUsage(Base):
    """每日服务使用次数表"""

    __tablename__ = "daily_service_usage"

    id = mapped_column(Integer, primary_key=True)
    service_name = mapped_column(String(50), nullable=False)  # 服务名称
    usage_date = mapped_column(String(10), nullable=False)  # 使用日期，改用 String 类型
    usage_count = mapped_column(Integer, default=0)  # 使用次数
    daily_limit = mapped_column(Integer, nullable=False)  # 每日限制次数
    created_at = mapped_column(DateTime, default=utc_now)
    updated_at = mapped_column(DateTime, default=utc_now, onupdate=utc_now)

    __table_args__ = (
        Index("idx_daily_usage_lookup", service_name, usage_date),
    )

    def __repr__(self):
        return f"<DailyServiceUsage(service={self.service_name}, date={self.usage_date}, count={self.usage_count})>"

    def __init__(self, **kwargs):
        """初始化时去除时分秒，只保留日期"""
        if "usage_date" in kwargs:
            if isinstance(kwargs["usage_date"], datetime):
                kwargs["usage_date"] = kwargs["usage_date"].strftime('%Y-%m-%d')
        super().__init__(**kwargs)

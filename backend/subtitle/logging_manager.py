# -*- coding: utf-8 -*-
"""
结构化日志管理模块

提供统一的日志配置、结构化日志记录和日志分析功能。
支持多种输出格式和日志级别管理。
"""
import logging
import logging.handlers
import json
import time
import traceback
import threading
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
import sys
import os

try:
    import structlog
    STRUCTLOG_AVAILABLE = True
except ImportError:
    STRUCTLOG_AVAILABLE = False

try:
    from .config_manager import config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False


class StructuredFormatter(logging.Formatter):
    """结构化日志格式化器"""
    
    def __init__(self, include_extra: bool = True):
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化日志记录为JSON"""
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.thread,
            "thread_name": record.threadName,
            "process": record.process
        }
        
        # 添加异常信息
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": traceback.format_exception(*record.exc_info)
            }
        
        # 添加额外字段
        if self.include_extra and hasattr(record, '__dict__'):
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                              'filename', 'module', 'lineno', 'funcName', 'created', 
                              'msecs', 'relativeCreated', 'thread', 'threadName', 
                              'processName', 'process', 'getMessage', 'exc_info', 'exc_text', 'stack_info']:
                    try:
                        # 确保值可以JSON序列化
                        json.dumps(value)
                        extra_fields[key] = value
                    except (TypeError, ValueError):
                        extra_fields[key] = str(value)
            
            if extra_fields:
                log_data["extra"] = extra_fields
        
        return json.dumps(log_data, ensure_ascii=False, separators=(',', ':'))


class ColoredFormatter(logging.Formatter):
    """彩色控制台日志格式化器"""
    
    COLORS = {
        'DEBUG': '\033[36m',     # 青色
        'INFO': '\033[32m',      # 绿色
        'WARNING': '\033[33m',   # 黄色
        'ERROR': '\033[31m',     # 红色
        'CRITICAL': '\033[35m',  # 紫色
        'RESET': '\033[0m'       # 重置
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """格式化带颜色的日志"""
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        
        # 基础格式
        formatted = f"{color}[{record.levelname}]{reset} "
        formatted += f"{datetime.fromtimestamp(record.created).strftime('%H:%M:%S')} "
        formatted += f"{record.name} - {record.getMessage()}"
        
        # 添加额外信息
        if hasattr(record, 'operation'):
            formatted += f" | 操作: {record.operation}"
        if hasattr(record, 'duration'):
            formatted += f" | 耗时: {record.duration:.2f}s"
        if hasattr(record, 'error_code'):
            formatted += f" | 错误码: {record.error_code}"
        
        # 添加异常信息
        if record.exc_info:
            formatted += f"\n{color}异常详情:{reset}\n"
            formatted += ''.join(traceback.format_exception(*record.exc_info))
        
        return formatted


class LoggingManager:
    """日志管理器"""
    
    def __init__(self):
        self.loggers: Dict[str, logging.Logger] = {}
        self.handlers: List[logging.Handler] = []
        self._lock = threading.RLock()
        self._setup_complete = False
        
    def setup_logging(
        self,
        log_level: str = "INFO",
        log_dir: Optional[Path] = None,
        enable_console: bool = True,
        enable_file: bool = True,
        enable_structured: bool = True,
        max_file_size: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5
    ):
        """设置日志配置"""
        with self._lock:
            if self._setup_complete:
                return
            
            # 获取日志目录
            if log_dir is None:
                if CONFIG_AVAILABLE:
                    log_dir = config.paths.log_dir
                else:
                    log_dir = Path.cwd() / "logs"
            
            log_dir = Path(log_dir)
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # 设置根日志级别
            root_logger = logging.getLogger()
            root_logger.setLevel(getattr(logging, log_level.upper()))
            
            # 清除现有处理器
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # 控制台处理器
            if enable_console:
                console_handler = logging.StreamHandler(sys.stdout)
                console_handler.setLevel(getattr(logging, log_level.upper()))
                console_handler.setFormatter(ColoredFormatter())
                root_logger.addHandler(console_handler)
                self.handlers.append(console_handler)
            
            # 文件处理器
            if enable_file:
                # 应用日志文件
                app_log_file = log_dir / "subtitle_app.log"
                app_handler = logging.handlers.RotatingFileHandler(
                    app_log_file,
                    maxBytes=max_file_size,
                    backupCount=backup_count,
                    encoding='utf-8'
                )
                app_handler.setLevel(getattr(logging, log_level.upper()))
                
                if enable_structured:
                    app_handler.setFormatter(StructuredFormatter())
                else:
                    app_handler.setFormatter(logging.Formatter(
                        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                    ))
                
                root_logger.addHandler(app_handler)
                self.handlers.append(app_handler)
                
                # 错误日志文件
                error_log_file = log_dir / "subtitle_error.log"
                error_handler = logging.handlers.RotatingFileHandler(
                    error_log_file,
                    maxBytes=max_file_size,
                    backupCount=backup_count,
                    encoding='utf-8'
                )
                error_handler.setLevel(logging.ERROR)
                error_handler.setFormatter(StructuredFormatter())
                root_logger.addHandler(error_handler)
                self.handlers.append(error_handler)
                
                # 性能日志文件
                perf_log_file = log_dir / "subtitle_performance.log"
                perf_handler = logging.handlers.RotatingFileHandler(
                    perf_log_file,
                    maxBytes=max_file_size,
                    backupCount=backup_count,
                    encoding='utf-8'
                )
                perf_handler.setLevel(logging.INFO)
                perf_handler.setFormatter(StructuredFormatter())
                
                # 创建性能日志器
                perf_logger = logging.getLogger("performance")
                perf_logger.addHandler(perf_handler)
                perf_logger.setLevel(logging.INFO)
                perf_logger.propagate = False
                
                self.handlers.append(perf_handler)
            
            self._setup_complete = True
            
            # 记录初始化完成
            logger = self.get_logger("logging_manager")
            logger.info("日志系统初始化完成", extra={
                "log_level": log_level,
                "log_dir": str(log_dir),
                "enable_console": enable_console,
                "enable_file": enable_file,
                "enable_structured": enable_structured
            })
    
    def get_logger(self, name: str) -> logging.Logger:
        """获取指定名称的日志器"""
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        return self.loggers[name]
    
    def get_performance_logger(self) -> logging.Logger:
        """获取性能日志器"""
        return logging.getLogger("performance")
    
    def log_operation(
        self,
        logger: logging.Logger,
        operation: str,
        level: str = "INFO",
        **kwargs
    ):
        """记录操作日志"""
        extra_data = {
            "operation": operation,
            "timestamp": time.time(),
            **kwargs
        }
        
        log_level = getattr(logging, level.upper())
        logger.log(log_level, f"操作: {operation}", extra=extra_data)
    
    def log_performance(
        self,
        operation: str,
        duration: float,
        success: bool = True,
        **kwargs
    ):
        """记录性能日志"""
        perf_logger = self.get_performance_logger()
        
        extra_data = {
            "operation": operation,
            "duration": duration,
            "success": success,
            "performance_metric": True,
            **kwargs
        }
        
        if success:
            perf_logger.info(f"性能指标: {operation} 完成", extra=extra_data)
        else:
            perf_logger.warning(f"性能指标: {operation} 失败", extra=extra_data)
    
    def log_error(
        self,
        logger: logging.Logger,
        error: Exception,
        operation: str = None,
        **kwargs
    ):
        """记录错误日志"""
        extra_data = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            **kwargs
        }
        
        if operation:
            extra_data["operation"] = operation
        
        logger.error(f"错误: {str(error)}", exc_info=True, extra=extra_data)
    
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            "setup_complete": self._setup_complete,
            "handlers_count": len(self.handlers),
            "loggers_count": len(self.loggers),
            "handlers": []
        }
        
        for handler in self.handlers:
            handler_info = {
                "type": type(handler).__name__,
                "level": handler.level,
                "formatter": type(handler.formatter).__name__ if handler.formatter else None
            }
            
            if hasattr(handler, 'baseFilename'):
                handler_info["file"] = handler.baseFilename
                if hasattr(handler, 'maxBytes'):
                    handler_info["max_size"] = handler.maxBytes
                if hasattr(handler, 'backupCount'):
                    handler_info["backup_count"] = handler.backupCount
            
            stats["handlers"].append(handler_info)
        
        return stats
    
    def cleanup(self):
        """清理日志资源"""
        with self._lock:
            for handler in self.handlers:
                handler.close()
            self.handlers.clear()
            self.loggers.clear()
            self._setup_complete = False


# 全局日志管理器实例
_logging_manager = None


def get_logging_manager() -> LoggingManager:
    """获取全局日志管理器"""
    global _logging_manager
    if _logging_manager is None:
        _logging_manager = LoggingManager()
        
        # 自动设置日志
        log_level = "INFO"
        log_dir = None
        
        if CONFIG_AVAILABLE:
            log_level = config.server.log_level
            log_dir = config.paths.log_dir
        
        _logging_manager.setup_logging(
            log_level=log_level,
            log_dir=log_dir
        )
    
    return _logging_manager


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的日志器"""
    return get_logging_manager().get_logger(name)


def get_performance_logger() -> logging.Logger:
    """获取性能日志器"""
    return get_logging_manager().get_performance_logger()


# 便捷函数
def log_operation(operation: str, logger_name: str = None, **kwargs):
    """记录操作日志"""
    logger = get_logger(logger_name or "app")
    get_logging_manager().log_operation(logger, operation, **kwargs)


def log_performance(operation: str, duration: float, success: bool = True, **kwargs):
    """记录性能日志"""
    get_logging_manager().log_performance(operation, duration, success, **kwargs)


def log_error(error: Exception, operation: str = None, logger_name: str = None, **kwargs):
    """记录错误日志"""
    logger = get_logger(logger_name or "app")
    get_logging_manager().log_error(logger, error, operation, **kwargs)

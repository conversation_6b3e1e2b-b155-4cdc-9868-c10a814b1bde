# -*- coding: utf-8 -*-
"""
异步处理核心模块

提供异步任务处理、并发控制和流式响应功能。
支持CPU密集型和I/O密集型任务的异步执行。
"""
import asyncio
import threading
import time
import uuid
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed
from typing import Dict, Any, Optional, Callable, AsyncGenerator, List, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import logging
import queue
import weakref

from .logging_manager import get_logger
from .performance_monitor import monitor_performance
from .exceptions import SubtitleProcessingError

logger = get_logger(__name__)


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class TaskResult:
    """任务结果数据类"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[Exception] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    progress: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def duration(self) -> Optional[float]:
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None
    
    @property
    def is_completed(self) -> bool:
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]


@dataclass
class AsyncTask:
    """异步任务数据类"""
    task_id: str
    func: Callable
    args: tuple
    kwargs: dict
    priority: TaskPriority = TaskPriority.NORMAL
    timeout: Optional[float] = None
    retry_count: int = 0
    max_retries: int = 3
    created_time: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())


class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(
        self,
        max_workers: int = 4,
        max_process_workers: int = 2,
        enable_process_pool: bool = True
    ):
        self.max_workers = max_workers
        self.max_process_workers = max_process_workers
        self.enable_process_pool = enable_process_pool
        
        # 线程池和进程池
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=max_process_workers) if enable_process_pool else None
        
        # 任务管理
        self.tasks: Dict[str, AsyncTask] = {}
        self.task_results: Dict[str, TaskResult] = {}
        self.task_futures: Dict[str, asyncio.Future] = {}
        
        # 任务队列
        self.task_queue = asyncio.PriorityQueue()
        self.running_tasks: Dict[str, asyncio.Task] = {}
        
        # 控制标志
        self.is_running = False
        self.worker_tasks: List[asyncio.Task] = []
        
        # 统计信息
        self.stats = {
            "total_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "cancelled_tasks": 0,
            "average_duration": 0.0
        }
        
        logger.info(f"异步任务管理器初始化完成: {max_workers}个线程, {max_process_workers}个进程")
    
    async def start(self):
        """启动任务管理器"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # 启动工作协程
        for i in range(self.max_workers):
            worker_task = asyncio.create_task(self._worker(f"worker-{i}"))
            self.worker_tasks.append(worker_task)
        
        logger.info(f"异步任务管理器已启动: {len(self.worker_tasks)}个工作协程")
    
    async def stop(self):
        """停止任务管理器"""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # 取消所有运行中的任务
        for task_id, task in self.running_tasks.items():
            task.cancel()
            logger.info(f"取消任务: {task_id}")
        
        # 等待工作协程完成
        if self.worker_tasks:
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        
        # 关闭线程池和进程池
        self.thread_pool.shutdown(wait=True)
        if self.process_pool:
            self.process_pool.shutdown(wait=True)
        
        logger.info("异步任务管理器已停止")
    
    async def _worker(self, worker_name: str):
        """工作协程"""
        logger.debug(f"工作协程启动: {worker_name}")
        
        while self.is_running:
            try:
                # 从队列获取任务
                priority, task_id = await asyncio.wait_for(
                    self.task_queue.get(),
                    timeout=1.0
                )
                
                if task_id not in self.tasks:
                    continue
                
                task = self.tasks[task_id]
                logger.debug(f"工作协程 {worker_name} 开始执行任务: {task_id}")
                
                # 执行任务
                await self._execute_task(task)
                
                # 标记任务完成
                self.task_queue.task_done()
                
            except asyncio.TimeoutError:
                # 队列为空，继续等待
                continue
            except Exception as e:
                logger.error(f"工作协程 {worker_name} 执行任务时发生错误: {e}", exc_info=True)
        
        logger.debug(f"工作协程停止: {worker_name}")
    
    async def _execute_task(self, task: AsyncTask):
        """执行单个任务"""
        task_id = task.task_id
        
        # 创建任务结果
        result = TaskResult(
            task_id=task_id,
            status=TaskStatus.RUNNING,
            start_time=time.time(),
            metadata=task.metadata.copy()
        )
        self.task_results[task_id] = result
        
        try:
            # 根据任务类型选择执行方式
            if task.metadata.get("use_process_pool", False) and self.process_pool:
                # 使用进程池执行CPU密集型任务
                loop = asyncio.get_event_loop()
                future = loop.run_in_executor(
                    self.process_pool,
                    task.func,
                    *task.args,
                    **task.kwargs
                )
            else:
                # 使用线程池执行I/O密集型任务
                loop = asyncio.get_event_loop()
                future = loop.run_in_executor(
                    self.thread_pool,
                    task.func,
                    *task.args,
                    **task.kwargs
                )
            
            # 设置超时
            if task.timeout:
                task_result = await asyncio.wait_for(future, timeout=task.timeout)
            else:
                task_result = await future
            
            # 任务成功完成
            result.status = TaskStatus.COMPLETED
            result.result = task_result
            result.progress = 100.0
            result.end_time = time.time()
            
            # 更新统计信息
            self.stats["completed_tasks"] += 1
            self._update_average_duration(result.duration)
            
            logger.info(f"任务执行成功: {task_id}, 耗时: {result.duration:.2f}秒")
            
        except asyncio.TimeoutError:
            # 任务超时
            result.status = TaskStatus.FAILED
            result.error = TimeoutError(f"任务超时: {task.timeout}秒")
            result.end_time = time.time()
            
            self.stats["failed_tasks"] += 1
            logger.warning(f"任务执行超时: {task_id}, 超时时间: {task.timeout}秒")
            
        except Exception as e:
            # 任务执行失败
            result.status = TaskStatus.FAILED
            result.error = e
            result.end_time = time.time()
            
            # 检查是否需要重试
            if task.retry_count < task.max_retries:
                task.retry_count += 1
                logger.warning(f"任务执行失败，准备重试: {task_id}, 重试次数: {task.retry_count}/{task.max_retries}")
                
                # 重新加入队列
                await self.task_queue.put((task.priority.value, task_id))
                return
            
            self.stats["failed_tasks"] += 1
            logger.error(f"任务执行失败: {task_id}, 错误: {str(e)}", exc_info=True)
        
        finally:
            # 清理运行中的任务记录
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    def _update_average_duration(self, duration: Optional[float]):
        """更新平均执行时间"""
        if duration is None:
            return
        
        completed = self.stats["completed_tasks"]
        if completed == 1:
            self.stats["average_duration"] = duration
        else:
            current_avg = self.stats["average_duration"]
            self.stats["average_duration"] = (current_avg * (completed - 1) + duration) / completed
    
    async def submit_task(
        self,
        func: Callable,
        *args,
        task_id: Optional[str] = None,
        priority: TaskPriority = TaskPriority.NORMAL,
        timeout: Optional[float] = None,
        use_process_pool: bool = False,
        max_retries: int = 3,
        metadata: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> str:
        """提交异步任务"""
        
        if not self.is_running:
            await self.start()
        
        # 创建任务
        task = AsyncTask(
            task_id=task_id or str(uuid.uuid4()),
            func=func,
            args=args,
            kwargs=kwargs,
            priority=priority,
            timeout=timeout,
            max_retries=max_retries,
            metadata=metadata or {}
        )
        
        # 设置进程池使用标志
        task.metadata["use_process_pool"] = use_process_pool
        
        # 保存任务
        self.tasks[task.task_id] = task
        
        # 创建初始结果
        self.task_results[task.task_id] = TaskResult(
            task_id=task.task_id,
            status=TaskStatus.PENDING,
            metadata=task.metadata.copy()
        )
        
        # 加入队列
        await self.task_queue.put((priority.value, task.task_id))
        
        # 更新统计
        self.stats["total_tasks"] += 1
        
        logger.info(f"任务已提交: {task.task_id}, 优先级: {priority.name}")
        return task.task_id
    
    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果"""
        return self.task_results.get(task_id)
    
    def get_task_status(self, task_id: str) -> Optional[TaskStatus]:
        """获取任务状态"""
        result = self.get_task_result(task_id)
        return result.status if result else None
    
    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> TaskResult:
        """等待任务完成"""
        start_time = time.time()
        
        while True:
            result = self.get_task_result(task_id)
            if result and result.is_completed:
                return result
            
            if timeout and (time.time() - start_time) >= timeout:
                raise TimeoutError(f"等待任务超时: {task_id}")
            
            await asyncio.sleep(0.1)
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            task.cancel()
            
            # 更新结果
            if task_id in self.task_results:
                result = self.task_results[task_id]
                result.status = TaskStatus.CANCELLED
                result.end_time = time.time()
            
            self.stats["cancelled_tasks"] += 1
            logger.info(f"任务已取消: {task_id}")
            return True
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            "pending_tasks": self.task_queue.qsize(),
            "running_tasks": len(self.running_tasks),
            "thread_pool_workers": self.max_workers,
            "process_pool_workers": self.max_process_workers if self.process_pool else 0,
            "is_running": self.is_running
        }
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的任务"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        tasks_to_remove = []
        for task_id, result in self.task_results.items():
            if (result.is_completed and 
                result.end_time and 
                (current_time - result.end_time) > max_age_seconds):
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            self.task_results.pop(task_id, None)
            self.tasks.pop(task_id, None)
        
        if tasks_to_remove:
            logger.info(f"清理了 {len(tasks_to_remove)} 个过期任务")


# 全局异步任务管理器
_async_task_manager = None


def get_async_task_manager() -> AsyncTaskManager:
    """获取全局异步任务管理器"""
    global _async_task_manager
    if _async_task_manager is None:
        _async_task_manager = AsyncTaskManager()
    return _async_task_manager


async def submit_async_task(
    func: Callable,
    *args,
    task_id: Optional[str] = None,
    priority: TaskPriority = TaskPriority.NORMAL,
    timeout: Optional[float] = None,
    use_process_pool: bool = False,
    **kwargs
) -> str:
    """提交异步任务的便捷函数"""
    manager = get_async_task_manager()
    return await manager.submit_task(
        func, *args,
        task_id=task_id,
        priority=priority,
        timeout=timeout,
        use_process_pool=use_process_pool,
        **kwargs
    )


async def wait_for_task(task_id: str, timeout: Optional[float] = None) -> TaskResult:
    """等待任务完成的便捷函数"""
    manager = get_async_task_manager()
    return await manager.wait_for_task(task_id, timeout)


def get_task_result(task_id: str) -> Optional[TaskResult]:
    """获取任务结果的便捷函数"""
    manager = get_async_task_manager()
    return manager.get_task_result(task_id)

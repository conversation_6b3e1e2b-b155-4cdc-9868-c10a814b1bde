# -*- coding: utf-8 -*-
"""
带缓存的转录服务

集成缓存机制的转录服务，提供智能缓存策略和性能优化。
"""
import logging
import hashlib
from pathlib import Path
from typing import Optional, Dict, Any, Generator

from .cache_manager import get_transcription_cache, cache_decorator
from .exceptions import TranscriptionError, CacheError

logger = logging.getLogger(__name__)


class CachedTranscriptionService:
    """带缓存的转录服务"""
    
    def __init__(self):
        self.cache = get_transcription_cache()
        logger.info("带缓存的转录服务初始化完成")
    
    def _get_audio_file_hash(self, audio_path: str) -> str:
        """获取音频文件哈希值"""
        try:
            path = Path(audio_path)
            if not path.exists():
                return ""
            
            # 使用文件大小和修改时间作为简单哈希
            stat = path.stat()
            content = f"{path.name}:{stat.st_size}:{stat.st_mtime}"
            return hashlib.md5(content.encode()).hexdigest()
        except Exception as e:
            logger.warning(f"获取音频文件哈希失败: {e}")
            return ""
    
    def _should_use_cache(self, audio_path: str, config: Dict[str, Any]) -> bool:
        """判断是否应该使用缓存"""
        # 检查配置中的缓存设置
        use_cache = config.get("use_asr_cache", True)
        if not use_cache:
            return False
        
        # 检查文件是否存在
        if not Path(audio_path).exists():
            return False
        
        return True
    
    def get_cached_transcription(
        self,
        audio_path: str,
        model: str,
        language: str,
        config: Dict[str, Any] = None
    ) -> Optional[Any]:
        """获取缓存的转录结果"""
        if not self._should_use_cache(audio_path, config or {}):
            return None
        
        try:
            # 生成包含文件哈希的缓存键
            file_hash = self._get_audio_file_hash(audio_path)
            cache_config = {
                "file_hash": file_hash,
                "config": config or {}
            }
            
            result = self.cache.get_transcription(
                audio_path=audio_path,
                model=model,
                language=language,
                additional_config=cache_config
            )
            
            if result:
                logger.info(f"转录缓存命中: {Path(audio_path).name}")
                return result
            else:
                logger.debug(f"转录缓存未命中: {Path(audio_path).name}")
                return None
                
        except Exception as e:
            logger.error(f"获取转录缓存失败: {e}")
            return None
    
    def set_cached_transcription(
        self,
        result: Any,
        audio_path: str,
        model: str,
        language: str,
        config: Dict[str, Any] = None,
        ttl: Optional[int] = None
    ) -> bool:
        """设置转录缓存"""
        if not self._should_use_cache(audio_path, config or {}):
            return False
        
        try:
            # 生成包含文件哈希的缓存键
            file_hash = self._get_audio_file_hash(audio_path)
            cache_config = {
                "file_hash": file_hash,
                "config": config or {}
            }
            
            success = self.cache.set_transcription(
                result=result,
                audio_path=audio_path,
                model=model,
                language=language,
                additional_config=cache_config,
                ttl=ttl
            )
            
            if success:
                logger.info(f"转录结果已缓存: {Path(audio_path).name}")
            else:
                logger.warning(f"转录结果缓存失败: {Path(audio_path).name}")
            
            return success
            
        except Exception as e:
            logger.error(f"设置转录缓存失败: {e}")
            return False
    
    def transcribe_with_cache(
        self,
        audio_path: str,
        model: str,
        language: str,
        config: Dict[str, Any] = None,
        transcribe_func=None,
        progress_callback=None
    ) -> Generator[Dict[str, Any], None, None]:
        """带缓存的转录处理"""
        
        # 尝试从缓存获取
        cached_result = self.get_cached_transcription(
            audio_path=audio_path,
            model=model,
            language=language,
            config=config
        )
        
        if cached_result is not None:
            # 返回缓存结果
            yield {
                "stage_name": "转录缓存",
                "percentage": 100,
                "message": "从缓存加载转录结果",
                "is_error": False,
                "final_result": {
                    "audio_to_text_response": cached_result
                }
            }
            return
        
        # 缓存未命中，执行实际转录
        if transcribe_func is None:
            raise TranscriptionError("未提供转录函数")
        
        try:
            # 执行转录
            transcription_result = None
            for progress in transcribe_func(audio_path, config, progress_callback):
                yield progress
                
                # 检查是否是最终结果
                if progress.get("final_result") and not progress.get("is_error"):
                    transcription_result = progress.get("final_result", {}).get("audio_to_text_response")
            
            # 保存到缓存
            if transcription_result:
                self.set_cached_transcription(
                    result=transcription_result,
                    audio_path=audio_path,
                    model=model,
                    language=language,
                    config=config
                )
                
        except Exception as e:
            logger.error(f"转录处理失败: {e}")
            yield {
                "stage_name": "转录错误",
                "percentage": 0,
                "message": f"转录失败: {str(e)}",
                "is_error": True,
                "error_message": str(e)
            }
    
    def clear_transcription_cache(self, audio_path: Optional[str] = None) -> bool:
        """清理转录缓存"""
        try:
            if audio_path:
                # 清理特定文件的缓存
                # 这里需要遍历可能的模型和语言组合
                # 实际实现中可能需要更复杂的逻辑
                logger.info(f"清理特定文件缓存: {audio_path}")
                return True
            else:
                # 清理所有转录缓存
                return self.cache.clear_all()
        except Exception as e:
            logger.error(f"清理转录缓存失败: {e}")
            return False
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            return self.cache.get_stats()
        except Exception as e:
            logger.error(f"获取缓存统计失败: {e}")
            return {"error": str(e)}


# 装饰器示例
@cache_decorator(
    cache_manager=get_transcription_cache(),
    ttl=7200,  # 2小时
    key_args=["audio_path", "model", "language"]
)
def cached_simple_transcribe(audio_path: str, model: str, language: str) -> Dict[str, Any]:
    """简单的缓存转录函数示例"""
    # 这里应该是实际的转录逻辑
    logger.info(f"执行转录: {audio_path} with {model}")
    
    # 模拟转录结果
    return {
        "segments": [
            {"text": "示例转录文本", "start_time": 0, "end_time": 1000}
        ],
        "model": model,
        "language": language
    }


class CacheHealthChecker:
    """缓存健康检查器"""
    
    def __init__(self):
        self.transcription_cache = get_transcription_cache()
    
    def check_cache_health(self) -> Dict[str, Any]:
        """检查缓存健康状态"""
        health_report = {
            "overall_status": "healthy",
            "checks": {},
            "recommendations": []
        }
        
        try:
            # 检查缓存连接
            stats = self.transcription_cache.get_stats()
            health_report["checks"]["connection"] = {
                "status": "ok",
                "backend": stats.get("backend", "unknown")
            }
            
            # 检查缓存性能
            hit_rate = stats.get("hit_rate", 0)
            if hit_rate < 30:
                health_report["recommendations"].append(
                    "缓存命中率较低，建议检查缓存策略"
                )
            
            # 检查缓存大小
            if stats.get("backend") == "file":
                total_size_mb = stats.get("total_size_mb", 0)
                if total_size_mb > 1000:  # 1GB
                    health_report["recommendations"].append(
                        "文件缓存占用空间较大，建议清理旧缓存"
                    )
            
            # 检查错误率
            if "error" in stats:
                health_report["overall_status"] = "warning"
                health_report["checks"]["errors"] = {
                    "status": "warning",
                    "message": stats["error"]
                }
            
        except Exception as e:
            health_report["overall_status"] = "error"
            health_report["checks"]["connection"] = {
                "status": "error",
                "message": str(e)
            }
        
        return health_report
    
    def optimize_cache(self) -> Dict[str, Any]:
        """优化缓存性能"""
        optimization_report = {
            "actions_taken": [],
            "recommendations": []
        }
        
        try:
            stats = self.transcription_cache.get_stats()
            
            # 如果是文件缓存且文件过多，建议清理
            if stats.get("backend") == "file":
                total_files = stats.get("total_files", 0)
                if total_files > 500:
                    optimization_report["recommendations"].append(
                        "建议定期清理旧的缓存文件"
                    )
            
            # 如果是Redis缓存，检查内存使用
            if stats.get("backend") == "redis":
                used_memory = stats.get("used_memory", 0)
                if used_memory > 100 * 1024 * 1024:  # 100MB
                    optimization_report["recommendations"].append(
                        "Redis内存使用较高，建议调整TTL策略"
                    )
            
        except Exception as e:
            optimization_report["error"] = str(e)
        
        return optimization_report


# 全局实例
_cached_transcription_service = None


def get_cached_transcription_service() -> CachedTranscriptionService:
    """获取全局缓存转录服务实例"""
    global _cached_transcription_service
    if _cached_transcription_service is None:
        _cached_transcription_service = CachedTranscriptionService()
    return _cached_transcription_service

# =============================================================================
# 开发环境专用依赖
# 包含所有开发、测试、调试工具
# =============================================================================

# 继承生产环境依赖
-r requirements.txt

# 开发工具
black>=23.0.0,<24.0.0
flake8>=6.0.0,<7.0.0
mypy>=1.0.0,<2.0.0
isort>=5.12.0,<6.0.0

# 测试增强
pytest-cov>=4.0.0,<5.0.0
pytest-xdist>=3.0.0,<4.0.0
pytest-benchmark>=4.0.0,<5.0.0

# 调试工具
ipdb>=0.13.0,<1.0.0
pdb++>=0.10.0,<1.0.0

# 性能分析
memory-profiler>=0.61.0,<1.0.0
line-profiler>=4.0.0,<5.0.0

# 文档生成
sphinx>=7.0.0,<8.0.0
sphinx-rtd-theme>=1.3.0,<2.0.0

# 代码质量检查
bandit>=1.7.0,<2.0.0
safety>=2.3.0,<3.0.0

# 预提交钩子
pre-commit>=3.0.0,<4.0.0

# backend/subtitle_processor.py
from typing import List, Dict, Optional, Any, Union, TypedDict
from pathlib import Path
import logging
import os
import json # 导入 json 模块

from api_protos.v1.subtitler import subtitler_pb2
from subtitle.core.asr_data import ASRData, ASRDataSeg

logger = logging.getLogger(__name__)

# 默认保存路径
DEFAULT_SUBTITLE_SAVE_DIR = Path.home() / "Downloads" / "Subtitles"

# 为 ass_style_options 定义 TypedDict
class AssStyleOptionsDict(TypedDict):
    style_block: str
    # 如果未来 ass_style_options 字典可能包含其他已知键，可以在这里添加
    # 例如: font_name: NotRequired[str] (需要 from typing import NotRequired)

class SubtitleProcessor:
    def __init__(self, logger_instance: Optional[logging.Logger] = None):
        """
        构造函数。
        Args:
            logger_instance: 可选的日志记录器实例。
        """
        self.logger = logger_instance if logger_instance else logger

    def prepare_asr_data_from_grpc(
        self,
        segments_proto_list: List[subtitler_pb2.SubtitleSegment],
    ) -> ASRData:
        """
        将 gRPC 请求中的字幕片段数据转换为 ASRData 实例。
        主要依赖 segments_proto_list，因为预期所有字幕数据都带有时间戳。

        Args:
            segments_proto_list: 从 gRPC 请求中提取的字幕片段列表
                                 (例如 request.segments)。每个元素应有
                                 original_text, translated_text, start_time, end_time 属性。

        Returns:
            一个 ASRData 对象。如果 segments_proto_list 为空或无效，
            可能返回空的 ASRData。
        """
        asr_data_segments: List[ASRDataSeg] = []
        if not segments_proto_list:
            self.logger.warning("prepare_asr_data_from_grpc: segments_proto_list 为空。返回空的 ASRData。")
            return ASRData(segments=asr_data_segments)

        for seg_proto in segments_proto_list:
            original_text = getattr(seg_proto, 'original_text', "")
            translated_text = getattr(seg_proto, 'translated_text', "")
            start_time = getattr(seg_proto, 'start_time', 0) # 假设单位是毫秒
            end_time = getattr(seg_proto, 'end_time', 0)     # 假设单位是毫秒

            try:
                start_time_ms = int(start_time)
                end_time_ms = int(end_time)
            except (ValueError, TypeError):
                self.logger.error(f"无效的时间戳值: start_time='{start_time}', end_time='{end_time}'. 跳过此片段。")
                continue
            
            if (original_text or translated_text) and end_time_ms > start_time_ms:
                 asr_data_segments.append(
                    ASRDataSeg(
                        text=original_text,
                        translated_text=translated_text,
                        start_time=start_time_ms,
                        end_time=end_time_ms,
                    )
                )
            else:
                self.logger.debug(f"跳过无效或空的字幕片段: original='{original_text}', translated='{translated_text}', start={start_time_ms}, end={end_time_ms}")
        
        return ASRData(segments=asr_data_segments)

    def generate_subtitle_file_package(
        self,
        asr_data: ASRData,
        original_filename_or_title: str, # 从 SaveSubtitleRequest.file_name 或 BatchSaveSubtitleRequest.file_name_prefix 传入
        target_format: str,
        target_layout: str,
        auto_save_to_default: bool,
        ass_style_options: Optional[Union[AssStyleOptionsDict, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        生成字幕文件内容或保存到文件，并返回包含文件信息的字典。
        """
        if not asr_data or not asr_data.has_data():
            self.logger.warning(f"generate_subtitle_file_package: ASRData 为空或无数据，无法生成字幕文件 (format: {target_format}, layout: {target_layout}).")
            return None

        content_str = ""
        actual_format = target_format.lower()
        
        ass_style_str: Optional[str] = None
        if actual_format == 'ass' and ass_style_options:
            if isinstance(ass_style_options, dict) and 'style_block' in ass_style_options:
                 ass_style_str = ass_style_options['style_block']
            elif isinstance(ass_style_options, str):
                 ass_style_str = ass_style_options
            else:
                self.logger.info("ASS样式选项已提供但格式未知或无 'style_block'，将使用 ASRData 默认样式。")

        try:
            if actual_format == 'srt':
                content_str = asr_data.to_srt(layout=target_layout)
            elif actual_format == 'ass':
                content_str = asr_data.to_ass(layout=target_layout, style_str=ass_style_str)
            elif actual_format == 'txt':
                content_str = asr_data.to_txt(layout=target_layout)
            elif actual_format == 'json':
                 json_data = asr_data.to_json()
                 content_str = json.dumps(json_data, ensure_ascii=False, indent=2)
            else:
                self.logger.error(f"不支持的目标字幕格式: {target_format}")
                return None
        except Exception as e:
            self.logger.error(f"生成字幕内容时出错 (format: {actual_format}, layout: {target_layout}): {e}", exc_info=True)
            return None

        # 后处理：确保“仅译文”在没有有效译文时输出空内容
        if target_layout == "仅译文":
            has_actual_translation = any(seg.translated_text and seg.translated_text.strip() for seg in asr_data.segments)
            if not has_actual_translation:
                self.logger.info(f"布局为 '仅译文' 但 ASRData 中没有有效译文内容。将强制输出空字幕文件 for {original_filename_or_title} format {actual_format}.")
                content_str = "" # 强制内容为空

        base_name = original_filename_or_title
        safe_layout_name = target_layout.replace(" ", "_") # 简单替换，可以更复杂
        file_name = f"{base_name}-{safe_layout_name}.{actual_format}"

        file_data_bytes = content_str.encode('utf-8')
        file_size = len(file_data_bytes)
        
        result_package = {
            'file_path': None,
            'file_name': file_name,
            'file_data': None,
            'file_size': file_size,
            'saved_to_default': False,
            'format': actual_format,
            'layout': target_layout,
            'original_filename_or_title': base_name
        }

        if auto_save_to_default:
            try:
                DEFAULT_SUBTITLE_SAVE_DIR.mkdir(parents=True, exist_ok=True)
                save_path = DEFAULT_SUBTITLE_SAVE_DIR / file_name
                with open(save_path, 'wb') as f: # 以二进制模式写入字节
                    f.write(file_data_bytes)
                result_package['file_path'] = str(save_path)
                result_package['saved_to_default'] = True
                self.logger.info(f"字幕文件已保存到: {save_path}")
            except Exception as e:
                self.logger.error(f"保存字幕文件到默认路径失败: {e}", exc_info=True)
                # 保存失败，但仍然可以返回文件数据供下载
                result_package['file_data'] = file_data_bytes
        else:
            result_package['file_data'] = file_data_bytes
            self.logger.info(f"字幕文件包已准备好供下载: {file_name}")

        return result_package

    def process_save_subtitle(
        self,
        segments_proto_list: List[subtitler_pb2.SubtitleSegment],
        original_filename_or_title: str,
        target_format: str,
        target_layout: str,
        auto_save_to_default: bool,
        ass_style_options: Optional[Union[AssStyleOptionsDict, str]] = None
    ) -> Optional[Dict[str, Any]]:
        """
        处理单个字幕保存请求。
        """
        self.logger.info(f"Process_save_subtitle: format={target_format}, layout={target_layout}, original_filename_or_title={original_filename_or_title}")
        asr_data = self.prepare_asr_data_from_grpc(
            segments_proto_list=segments_proto_list
        )

        if not asr_data or not asr_data.has_data():
            self.logger.warning("ASRData 准备失败或无数据，无法处理保存字幕。")
            return None

        return self.generate_subtitle_file_package(
            asr_data=asr_data,
            original_filename_or_title=original_filename_or_title,
            target_format=target_format,
            target_layout=target_layout,
            auto_save_to_default=auto_save_to_default,
            ass_style_options=ass_style_options
        )

    def process_batch_save_subtitles(
        self,
        segments_proto_list: List[subtitler_pb2.SubtitleSegment],
        original_filename_or_title: str,
        formats_to_generate: List[str],
        layouts_to_generate: List[str],
        translation_requested: bool,
        auto_save_to_default: bool,
        ass_style_options: Optional[Union[AssStyleOptionsDict, str]] = None
    ) -> List[Dict[str, Any]]:
        """
        处理批量字幕保存请求。
        """
        self.logger.info(f"Process_batch_save_subtitles: original_filename_or_title={original_filename_or_title}, translation_requested={translation_requested}")
        self.logger.debug(f"  Formats: {formats_to_generate}, Layouts: {layouts_to_generate}")

        asr_data = self.prepare_asr_data_from_grpc(
            segments_proto_list=segments_proto_list
        )

        if not asr_data: # ASRData 可以是空的，但不能是 None
            self.logger.warning("ASRData 准备失败，无法处理批量保存字幕。")
            return []
        
        results: List[Dict[str, Any]] = []

        for fmt in formats_to_generate:
            for layout in layouts_to_generate:
                self.logger.debug(f"  Processing batch item: Format='{fmt}', Layout='{layout}'")

                # Eligibility Check
                # 1. 检查 ASRData 是否有内容 (如果 ASRData 本身为空，则不应生成任何内容)
                if not asr_data.has_data():
                    self.logger.info(f"    Skipping {fmt}/{layout}: ASRData is empty.")
                    continue

                # 2. 根据布局和翻译请求判断是否生成
                # ASRData.segments 列表中的每个 ASRDataSeg 对象有 .text (原文) 和 .translated_text (译文)
                has_original_text_in_asr = any(seg.text and seg.text.strip() for seg in asr_data.segments)
                has_translated_text_in_asr = any(seg.translated_text and seg.translated_text.strip() for seg in asr_data.segments)

                self.logger.debug(f"    ASRData content check for {fmt}/{layout}: has_original={has_original_text_in_asr}, has_translated={has_translated_text_in_asr}, translation_requested_by_user={translation_requested}")

                eligible_to_generate = False
                if layout == "仅原文":
                    if has_original_text_in_asr:
                        eligible_to_generate = True
                elif layout == "仅译文":
                    if translation_requested and has_translated_text_in_asr:
                        eligible_to_generate = True
                    elif not translation_requested and has_original_text_in_asr:
                        # 如果用户未请求翻译，但要求“仅译文”布局，这通常意味着他们期望看到原文（如果译文不存在）
                        # ASRData 的 to_... 方法在仅译文且译文为空时，会回退到原文
                        # 所以，如果原文存在，也认为是 eligible，让 ASRData.to_... 内部处理回退
                        eligible_to_generate = True
                    elif translation_requested and not has_translated_text_in_asr and has_original_text_in_asr:
                        # 用户请求了翻译，但翻译文本不存在，但原文存在。
                        # ASRData 的 to_... 方法在仅译文且译文为空时，会回退到原文。
                        # 这种情况下，生成的文件将是原文。
                        eligible_to_generate = True


                elif layout in ["原文在上", "译文在上"]:
                    if translation_requested and has_original_text_in_asr and has_translated_text_in_asr:
                        eligible_to_generate = True
                    elif not translation_requested and has_original_text_in_asr:
                        # 如果用户未请求翻译，但要求双语布局，通常只显示原文
                        # ASRData 的 to_... 方法在双语布局且译文为空时，只显示原文
                        eligible_to_generate = True
                    elif translation_requested and has_original_text_in_asr and not has_translated_text_in_asr:
                        # 用户请求了翻译，原文存在，但译文不存在。
                        # ASRData 的 to_... 方法在双语布局且译文为空时，只显示原文。
                        eligible_to_generate = True
                
                if not eligible_to_generate:
                    self.logger.info(f"    Skipping {fmt}/{layout} due to eligibility check.")
                    continue
                
                self.logger.debug(f"    Eligible to generate for {fmt}/{layout}.")
                package = self.generate_subtitle_file_package(
                    asr_data=asr_data,
                    original_filename_or_title=original_filename_or_title,
                    target_format=fmt,
                    target_layout=layout,
                    auto_save_to_default=auto_save_to_default,
                    ass_style_options=ass_style_options
                )
                if package:
                    results.append(package)
        
        self.logger.info(f"Batch processing complete. Generated {len(results)} file packages.")
        return results
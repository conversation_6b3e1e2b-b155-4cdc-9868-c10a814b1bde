# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: api-protos/v1/ai_config/ai_config_service.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'api-protos/v1/ai_config/ai_config_service.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n/api-protos/v1/ai_config/ai_config_service.proto\x12\x0cv1.ai_config\"\xde\x03\n\x15\x41IProviderConfigProto\x12\x13\n\x0bprovider_id\x18\x01 \x01(\t\x12\x15\n\rprovider_type\x18\x02 \x01(\t\x12\x14\n\x0c\x64isplay_name\x18\x03 \x01(\t\x12\x12\n\nis_enabled\x18\x04 \x01(\x08\x12I\n\x0b\x63redentials\x18\x05 \x03(\x0b\x32\x34.v1.ai_config.AIProviderConfigProto.CredentialsEntry\x12G\n\nattributes\x18\x06 \x03(\x0b\x32\x33.v1.ai_config.AIProviderConfigProto.AttributesEntry\x12\x43\n\x08metadata\x18\x07 \x03(\x0b\x32\x31.v1.ai_config.AIProviderConfigProto.MetadataEntry\x1a\x32\n\x10\x43redentialsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x31\n\x0f\x41ttributesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"U\n\x1dUpdateAIConfigurationsRequest\x12\x34\n\x07\x63onfigs\x18\x01 \x03(\x0b\x32#.v1.ai_config.AIProviderConfigProto\"B\n\x1eUpdateAIConfigurationsResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t2\x8d\x01\n\x16\x41IConfigurationService\x12s\n\x16UpdateAIConfigurations\x12+.v1.ai_config.UpdateAIConfigurationsRequest\x1a,.v1.ai_config.UpdateAIConfigurationsResponseBu\n\x1d\x63om.example.grpc.v1.ai_configP\x01ZRgithub.com/monkeyfx/electron-python-grpc-pex-demo/gen/go/v1/ai_config;ai_config_pbb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'api_protos.v1.ai_config.ai_config_service_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\035com.example.grpc.v1.ai_configP\001ZRgithub.com/monkeyfx/electron-python-grpc-pex-demo/gen/go/v1/ai_config;ai_config_pb'
  _globals['_AIPROVIDERCONFIGPROTO_CREDENTIALSENTRY']._loaded_options = None
  _globals['_AIPROVIDERCONFIGPROTO_CREDENTIALSENTRY']._serialized_options = b'8\001'
  _globals['_AIPROVIDERCONFIGPROTO_ATTRIBUTESENTRY']._loaded_options = None
  _globals['_AIPROVIDERCONFIGPROTO_ATTRIBUTESENTRY']._serialized_options = b'8\001'
  _globals['_AIPROVIDERCONFIGPROTO_METADATAENTRY']._loaded_options = None
  _globals['_AIPROVIDERCONFIGPROTO_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_AIPROVIDERCONFIGPROTO']._serialized_start=66
  _globals['_AIPROVIDERCONFIGPROTO']._serialized_end=544
  _globals['_AIPROVIDERCONFIGPROTO_CREDENTIALSENTRY']._serialized_start=394
  _globals['_AIPROVIDERCONFIGPROTO_CREDENTIALSENTRY']._serialized_end=444
  _globals['_AIPROVIDERCONFIGPROTO_ATTRIBUTESENTRY']._serialized_start=446
  _globals['_AIPROVIDERCONFIGPROTO_ATTRIBUTESENTRY']._serialized_end=495
  _globals['_AIPROVIDERCONFIGPROTO_METADATAENTRY']._serialized_start=497
  _globals['_AIPROVIDERCONFIGPROTO_METADATAENTRY']._serialized_end=544
  _globals['_UPDATEAICONFIGURATIONSREQUEST']._serialized_start=546
  _globals['_UPDATEAICONFIGURATIONSREQUEST']._serialized_end=631
  _globals['_UPDATEAICONFIGURATIONSRESPONSE']._serialized_start=633
  _globals['_UPDATEAICONFIGURATIONSRESPONSE']._serialized_end=699
  _globals['_AICONFIGURATIONSERVICE']._serialized_start=702
  _globals['_AICONFIGURATIONSERVICE']._serialized_end=843
# @@protoc_insertion_point(module_scope)

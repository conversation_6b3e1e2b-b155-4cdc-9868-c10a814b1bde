# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: api-protos/v1/greeter/greeter.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'api-protos/v1/greeter/greeter.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from api_protos.v1.common import common_pb2 as api__protos_dot_v1_dot_common_dot_common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#api-protos/v1/greeter/greeter.proto\x12\x17monkeyfx.api.v1.greeter\x1a!api-protos/v1/common/common.proto\"\x1c\n\x0cHelloRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"]\n\nHelloReply\x12\x0f\n\x07message\x18\x01 \x01(\t\x12\x18\n\x10original_request\x18\x02 \x01(\t\x12\x11\n\tserver_id\x18\x03 \x01(\t\x12\x11\n\ttimestamp\x18\x04 \x01(\t2a\n\x07Greeter\x12V\n\x08SayHello\x12%.monkeyfx.api.v1.greeter.HelloRequest\x1a#.monkeyfx.api.v1.greeter.HelloReplyBs\n com.monkeyfx.grpc.api.v1.greeterB\x0cGreeterProtoP\x01Z?github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/greeterb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'api_protos.v1.greeter.greeter_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n com.monkeyfx.grpc.api.v1.greeterB\014GreeterProtoP\001Z?github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/greeter'
  _globals['_HELLOREQUEST']._serialized_start=99
  _globals['_HELLOREQUEST']._serialized_end=127
  _globals['_HELLOREPLY']._serialized_start=129
  _globals['_HELLOREPLY']._serialized_end=222
  _globals['_GREETER']._serialized_start=224
  _globals['_GREETER']._serialized_end=321
# @@protoc_insertion_point(module_scope)

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: api-protos/v1/common/common.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'api-protos/v1/common/common.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!api-protos/v1/common/common.proto\x12\x16monkeyfx.api.v1.common\"-\n\x0c\x42\x61seResponse\x12\x0c\n\x04\x63ode\x18\x01 \x01(\x05\x12\x0f\n\x07message\x18\x02 \x01(\tBp\n\x1f\x63om.monkeyfx.grpc.api.v1.commonB\x0b\x43ommonProtoP\x01Z>github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/commonb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'api_protos.v1.common.common_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'\n\037com.monkeyfx.grpc.api.v1.commonB\013CommonProtoP\001Z>github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/common'
  _globals['_BASERESPONSE']._serialized_start=61
  _globals['_BASERESPONSE']._serialized_end=106
# @@protoc_insertion_point(module_scope)

# -*- coding: utf-8 -*-
"""
SubtitleProcessor 单元测试模块。

测试字幕处理器的核心功能，包括：
- prepare_asr_data_from_grpc: gRPC数据转换为ASRData
- generate_subtitle_file_package: 生成字幕文件包
- process_save_subtitle: 处理单个字幕保存
- process_batch_save_subtitles: 处理批量字幕保存
"""

import pytest
from unittest.mock import Mock, patch, mock_open
import tempfile
import os
import json
from pathlib import Path

# 导入被测试的模块
from subtitle_processor import SubtitleProcessor, DEFAULT_SUBTITLE_SAVE_DIR
from api_protos.v1.subtitler import subtitler_pb2
from subtitle.core.asr_data import ASRData, ASRDataSeg


class TestSubtitleProcessor:
    """SubtitleProcessor 测试类"""

    def setup_method(self):
        """每个测试方法执行前的设置"""
        self.processor = SubtitleProcessor()
        self.temp_dir = tempfile.mkdtemp()

    def teardown_method(self):
        """每个测试方法执行后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_test_segments_proto(self):
        """创建测试用的字幕片段protobuf列表"""
        return [
            subtitler_pb2.SubtitleSegment(
                start_time=0,
                end_time=3000,
                original_text="第一句话",
                translated_text="First sentence"
            ),
            subtitler_pb2.SubtitleSegment(
                start_time=3000,
                end_time=6000,
                original_text="第二句话",
                translated_text="Second sentence"
            ),
            subtitler_pb2.SubtitleSegment(
                start_time=6000,
                end_time=9000,
                original_text="第三句话",
                translated_text="Third sentence"
            )
        ]

    def test_prepare_asr_data_from_grpc_success(self):
        """测试成功从gRPC数据转换为ASRData"""
        segments_proto = self.create_test_segments_proto()
        
        asr_data = self.processor.prepare_asr_data_from_grpc(segments_proto)
        
        # 验证转换结果
        assert isinstance(asr_data, ASRData)
        assert len(asr_data.segments) == 3
        
        # 验证第一个片段
        seg1 = asr_data.segments[0]
        assert seg1.text == "第一句话"
        assert seg1.translated_text == "First sentence"
        assert seg1.start_time == 0
        assert seg1.end_time == 3000
        
        # 验证第二个片段
        seg2 = asr_data.segments[1]
        assert seg2.text == "第二句话"
        assert seg2.translated_text == "Second sentence"
        assert seg2.start_time == 3000
        assert seg2.end_time == 6000

    def test_prepare_asr_data_from_grpc_empty_list(self):
        """测试空列表转换"""
        asr_data = self.processor.prepare_asr_data_from_grpc([])
        
        assert isinstance(asr_data, ASRData)
        assert len(asr_data.segments) == 0

    def test_prepare_asr_data_from_grpc_invalid_timestamps(self):
        """测试无效时间戳的处理"""
        invalid_segments = [
            subtitler_pb2.SubtitleSegment(
                start_time=5000,  # 结束时间小于开始时间
                end_time=3000,
                original_text="无效时间戳",
                translated_text="Invalid timestamp"
            ),
            subtitler_pb2.SubtitleSegment(
                start_time=0, 
                end_time=6000,
                original_text="非数字时间戳",
                translated_text="Non-numeric timestamp"
            ),
            subtitler_pb2.SubtitleSegment(
                start_time=1000,
                end_time=4000,
                original_text="有效片段",
                translated_text="Valid segment"
            )
        ]
        
        asr_data = self.processor.prepare_asr_data_from_grpc(invalid_segments)
        
        assert len(asr_data.segments) == 2
        assert asr_data.segments[0].text == "非数字时间戳" 
        assert asr_data.segments[1].text == "有效片段"   

    def test_prepare_asr_data_from_grpc_empty_text(self):
        """测试空文本片段的处理"""
        segments_with_empty_text = [
            subtitler_pb2.SubtitleSegment(
                start_time=0,
                end_time=3000,
                original_text="",  # 空原文
                translated_text=""  # 空译文
            ),
            subtitler_pb2.SubtitleSegment(
                start_time=3000,
                end_time=6000,
                original_text="有效文本",
                translated_text="Valid text"
            )
        ]
        
        asr_data = self.processor.prepare_asr_data_from_grpc(segments_with_empty_text)
        
        assert len(asr_data.segments) == 1
        assert asr_data.segments[0].text == "有效文本"

    @patch('subtitle_processor.ASRData')
    def test_generate_subtitle_file_package_srt_format(self, mock_asr_data_class):
        """测试生成SRT格式字幕文件包"""
        mock_asr_data = Mock()
        mock_asr_data.has_data.return_value = True
        mock_asr_data.to_srt.return_value = "1\n00:00:00,000 --> 00:00:03,000\n测试字幕\n\n"
        
        result = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data,
            original_filename_or_title="test_video",
            target_format="srt",
            target_layout="原文在上",
            auto_save_to_default=False
        )
        
        assert result is not None
        assert result['file_name'] == "test_video-原文在上.srt"
        assert result['format'] == "srt"
        assert result['layout'] == "原文在上"
        assert result['file_size'] > 0
        assert result['saved_to_default'] == False
        assert result['file_data'] is not None
        
        mock_asr_data.to_srt.assert_called_once_with(layout="原文在上")

    @patch('subtitle_processor.ASRData')
    def test_generate_subtitle_file_package_ass_format(self, mock_asr_data_class):
        """测试生成ASS格式字幕文件包"""
        mock_asr_data = Mock()
        mock_asr_data.has_data.return_value = True
        mock_asr_data.to_ass.return_value = "[Script Info]\nTitle: Test\n\n[V4+ Styles]\n..."
        mock_segment = Mock()
        mock_segment.translated_text = "Some translated text"
        mock_asr_data.segments = [mock_segment]
        
        ass_style_options = {"style_block": "Style: Default,Arial,20,&H00FFFFFF,..."}
        
        result = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data,
            original_filename_or_title="test_video",
            target_format="ASS",
            target_layout="仅译文",
            auto_save_to_default=False,
            ass_style_options=ass_style_options
        )
        
        assert result is not None
        assert result['file_name'] == "test_video-仅译文.ass"
        assert result['format'] == "ass"
        
        mock_asr_data.to_ass.assert_called_once_with(
            layout="仅译文", 
            style_str="Style: Default,Arial,20,&H00FFFFFF,..."
        )

    @patch('subtitle_processor.ASRData')
    def test_generate_subtitle_file_package_json_format(self, mock_asr_data_class):
        """测试生成JSON格式字幕文件包"""
        mock_asr_data = Mock()
        mock_asr_data.has_data.return_value = True
        mock_asr_data.to_json.return_value = {
            "1": {"original_subtitle": "测试", "start_time": 0, "end_time": 3000}
        }
        
        result = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data,
            original_filename_or_title="test_video",
            target_format="json",
            target_layout="原文在上",
            auto_save_to_default=False
        )
        
        assert result is not None
        assert result['file_name'] == "test_video-原文在上.json"
        assert result['format'] == "json"
        
        json_content = result['file_data'].decode('utf-8')
        parsed_json = json.loads(json_content)
        assert "1" in parsed_json

    @patch('subtitle_processor.ASRData')
    def test_generate_subtitle_file_package_txt_format(self, mock_asr_data_class):
        """测试生成TXT格式字幕文件包"""
        mock_asr_data = Mock()
        mock_asr_data.has_data.return_value = True
        mock_asr_data.to_txt.return_value = "测试字幕文本内容"
        
        result = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data,
            original_filename_or_title="test_video",
            target_format="txt",
            target_layout="原文在上",
            auto_save_to_default=False
        )
        
        assert result is not None
        assert result['file_name'] == "test_video-原文在上.txt"
        assert result['format'] == "txt"
        
        mock_asr_data.to_txt.assert_called_once_with(layout="原文在上")

    def test_generate_subtitle_file_package_unsupported_format(self):
        """测试不支持的格式"""
        mock_asr_data = Mock()
        mock_asr_data.has_data.return_value = True
        
        result = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data,
            original_filename_or_title="test_video",
            target_format="unsupported",
            target_layout="原文在上",
            auto_save_to_default=False
        )
        
        assert result is None

    def test_generate_subtitle_file_package_empty_asr_data(self):
        """测试空的ASRData"""
        mock_asr_data = Mock()
        mock_asr_data.has_data.return_value = False
        
        result = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data,
            original_filename_or_title="test_video",
            target_format="srt",
            target_layout="原文在上",
            auto_save_to_default=False
        )
        
        assert result is None

    @patch('subtitle_processor.ASRData')
    def test_generate_subtitle_file_package_only_translation_no_data(self, mock_asr_data_class):
        """测试"仅译文"布局但没有译文数据的情况"""
        mock_asr_data = Mock()
        mock_asr_data.has_data.return_value = True
        mock_asr_data.segments = [
            Mock(translated_text=""),
            Mock(translated_text=None),
            Mock(translated_text="   ")
        ]
        mock_asr_data.to_srt.return_value = "some content"
        
        result = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data,
            original_filename_or_title="test_video",
            target_format="srt",
            target_layout="仅译文",
            auto_save_to_default=False
        )
        
        assert result is not None
        assert result['file_data'] == b""

    @patch('subtitle_processor.DEFAULT_SUBTITLE_SAVE_DIR')
    @patch('builtins.open', new_callable=mock_open)
    def test_generate_subtitle_file_package_auto_save_success(self, mock_file, mock_save_dir):
        """测试自动保存到默认目录成功"""
        mock_save_dir.__truediv__.return_value = Path("/fake/path/test.srt")
        
        mock_asr_data = Mock()
        mock_asr_data.has_data.return_value = True
        mock_asr_data.to_srt.return_value = "test content"
        
        result = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data,
            original_filename_or_title="test_video",
            target_format="srt",
            target_layout="原文在上",
            auto_save_to_default=True
        )
        
        assert result is not None
        assert result['saved_to_default'] == True
        assert result['file_path'] is not None
        
        mock_save_dir.mkdir.assert_called_once_with(parents=True, exist_ok=True)
        mock_file.assert_called_once()

    @patch('subtitle_processor.DEFAULT_SUBTITLE_SAVE_DIR')
    @patch('builtins.open', new_callable=mock_open)
    def test_generate_subtitle_file_package_auto_save_failure(self, mock_file, mock_save_dir):
        """测试自动保存失败的情况"""
        mock_file.side_effect = IOError("Permission denied")
        mock_save_dir.__truediv__ = Mock(return_value=Path("/fake/path/test.srt"))
        
        mock_asr_data = Mock()
        mock_asr_data.has_data.return_value = True
        mock_asr_data.to_srt.return_value = "test content"
        
        result = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data,
            original_filename_or_title="test_video",
            target_format="srt",
            target_layout="原文在上",
            auto_save_to_default=True
        )
        
        assert result is not None
        assert result['saved_to_default'] == False
        assert result['file_path'] is None
        assert result['file_data'] is not None

    @patch('subtitle_processor.ASRData')
    def test_generate_subtitle_file_package_srt_bilingual_layouts(self, mock_asr_data_class):
        """测试生成SRT格式字幕文件包 - 双语布局（英译中）"""
        
        mock_asr_data_instance = Mock(spec=ASRData)
        mock_asr_data_instance.has_data.return_value = True
        
        seg1_orig = "Hello world"
        seg1_trans = "你好世界"
        seg2_orig = "This is a test."
        seg2_trans = "这是一个测试。"
        
        mock_asr_data_instance.segments = [
            Mock(spec=ASRDataSeg, text=seg1_orig, translated_text=seg1_trans, start_time=0, end_time=2000),
            Mock(spec=ASRDataSeg, text=seg2_orig, translated_text=seg2_trans, start_time=2000, end_time=4000)
        ]

        def mock_to_srt(layout, **kwargs):
            if layout == "原文在上":
                return f"1\n00:00:00,000 --> 00:00:02,000\n{seg1_orig}\n{seg1_trans}\n\n" \
                       f"2\n00:00:02,000 --> 00:00:04,000\n{seg2_orig}\n{seg2_trans}\n\n"
            elif layout == "译文在上":
                return f"1\n00:00:00,000 --> 00:00:02,000\n{seg1_trans}\n{seg1_orig}\n\n" \
                       f"2\n00:00:02,000 --> 00:00:04,000\n{seg2_trans}\n{seg2_orig}\n\n"
            return "" 
        mock_asr_data_instance.to_srt = Mock(side_effect=mock_to_srt)

        result_orig_top = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data_instance,
            original_filename_or_title="test_bilingual",
            target_format="srt",
            target_layout="原文在上",
            auto_save_to_default=False
        )
        
        assert result_orig_top is not None
        assert result_orig_top['file_name'] == "test_bilingual-原文在上.srt"
        assert result_orig_top['file_data'] is not None
        
        parsed_srt_orig_top = pytest.parse_srt_content(result_orig_top['file_data'].decode('utf-8'))
        assert len(parsed_srt_orig_top) == 2
        
        assert len(parsed_srt_orig_top[0]['lines']) == 2
        assert pytest.is_predominantly_english(parsed_srt_orig_top[0]['lines'][0])
        assert parsed_srt_orig_top[0]['lines'][0] == seg1_orig
        assert pytest.is_predominantly_chinese(parsed_srt_orig_top[0]['lines'][1])
        assert parsed_srt_orig_top[0]['lines'][1] == seg1_trans
        
        assert len(parsed_srt_orig_top[1]['lines']) == 2
        assert pytest.is_predominantly_english(parsed_srt_orig_top[1]['lines'][0])
        assert parsed_srt_orig_top[1]['lines'][0] == seg2_orig
        assert pytest.is_predominantly_chinese(parsed_srt_orig_top[1]['lines'][1])
        assert parsed_srt_orig_top[1]['lines'][1] == seg2_trans

        mock_asr_data_instance.to_srt.assert_any_call(layout="原文在上")

        result_trans_top = self.processor.generate_subtitle_file_package(
            asr_data=mock_asr_data_instance,
            original_filename_or_title="test_bilingual",
            target_format="srt",
            target_layout="译文在上",
            auto_save_to_default=False
        )

        assert result_trans_top is not None
        assert result_trans_top['file_name'] == "test_bilingual-译文在上.srt"
        assert result_trans_top['file_data'] is not None

        parsed_srt_trans_top = pytest.parse_srt_content(result_trans_top['file_data'].decode('utf-8'))
        assert len(parsed_srt_trans_top) == 2

        assert len(parsed_srt_trans_top[0]['lines']) == 2
        assert pytest.is_predominantly_chinese(parsed_srt_trans_top[0]['lines'][0])
        assert parsed_srt_trans_top[0]['lines'][0] == seg1_trans
        assert pytest.is_predominantly_english(parsed_srt_trans_top[0]['lines'][1])
        assert parsed_srt_trans_top[0]['lines'][1] == seg1_orig

        assert len(parsed_srt_trans_top[1]['lines']) == 2
        assert pytest.is_predominantly_chinese(parsed_srt_trans_top[1]['lines'][0])
        assert parsed_srt_trans_top[1]['lines'][0] == seg2_trans
        assert pytest.is_predominantly_english(parsed_srt_trans_top[1]['lines'][1])
        assert parsed_srt_trans_top[1]['lines'][1] == seg2_orig
        
        mock_asr_data_instance.to_srt.assert_any_call(layout="译文在上")

    def test_process_save_subtitle_success(self):
        """测试处理单个字幕保存成功"""
        segments_proto = self.create_test_segments_proto()
        
        with patch.object(self.processor, 'prepare_asr_data_from_grpc') as mock_prepare, \
             patch.object(self.processor, 'generate_subtitle_file_package') as mock_generate:
            
            mock_asr_data = Mock()
            mock_asr_data.has_data.return_value = True
            mock_prepare.return_value = mock_asr_data
            
            mock_file_package = {
                'file_name': 'test.srt',
                'file_data': b'test data',
                'file_size': 9
            }
            mock_generate.return_value = mock_file_package
            
            result = self.processor.process_save_subtitle(
                segments_proto_list=segments_proto,
                original_filename_or_title="test_video",
                target_format="srt",
                target_layout="原文在上",
                auto_save_to_default=False
            )
            
            assert result == mock_file_package
            
            mock_prepare.assert_called_once_with(segments_proto_list=segments_proto)
            mock_generate.assert_called_once_with(
                asr_data=mock_asr_data,
                original_filename_or_title="test_video",
                target_format="srt",
                target_layout="原文在上",
                auto_save_to_default=False,
                ass_style_options=None
            )

    def test_process_save_subtitle_no_data(self):
        """测试处理字幕保存时没有数据"""
        with patch.object(self.processor, 'prepare_asr_data_from_grpc') as mock_prepare:
            mock_asr_data = Mock()
            mock_asr_data.has_data.return_value = False
            mock_prepare.return_value = mock_asr_data
            
            result = self.processor.process_save_subtitle(
                segments_proto_list=[],
                original_filename_or_title="test_video",
                target_format="srt",
                target_layout="原文在上",
                auto_save_to_default=False
            )
            
            assert result is None

    def test_process_batch_save_subtitles_success(self):
        """测试批量保存字幕成功"""
        segments_proto = self.create_test_segments_proto()
        
        with patch.object(self.processor, 'prepare_asr_data_from_grpc') as mock_prepare, \
             patch.object(self.processor, 'generate_subtitle_file_package') as mock_generate:
            
            mock_asr_data = Mock()
            mock_asr_data.has_data.return_value = True
            mock_segment = Mock()
            mock_segment.text = "Original"
            mock_segment.translated_text = "Translated"
            mock_asr_data.segments = [mock_segment, mock_segment]
            mock_prepare.return_value = mock_asr_data
            
            mock_file_packages = [
                {'file_name': 'test-srt.srt', 'format': 'srt'},
                {'file_name': 'test-ass.ass', 'format': 'ass'},
                {'file_name': 'test-txt.txt', 'format': 'txt'}
            ]
            mock_generate.side_effect = mock_file_packages
            
            result = self.processor.process_batch_save_subtitles(
                segments_proto_list=segments_proto,
                original_filename_or_title="test_video",
                formats_to_generate=["srt", "ass", "txt"],
                layouts_to_generate=["原文在上"],
                translation_requested=True,
                auto_save_to_default=False
            )
            
            assert len(result) == 3
            assert result[0]['file_name'] == 'test-srt.srt'
            assert result[1]['file_name'] == 'test-ass.ass'
            assert result[2]['file_name'] == 'test-txt.txt'
            
            assert mock_generate.call_count == 3

    def test_process_batch_save_subtitles_no_data(self):
        """测试批量保存时没有数据"""
        with patch.object(self.processor, 'prepare_asr_data_from_grpc') as mock_prepare:
            mock_asr_data = Mock()
            mock_asr_data.has_data.return_value = False
            mock_prepare.return_value = mock_asr_data
            
            result = self.processor.process_batch_save_subtitles(
                segments_proto_list=[],
                original_filename_or_title="test_video",
                formats_to_generate=["srt"],
                layouts_to_generate=["原文在上"],
                translation_requested=False,
                auto_save_to_default=False
            )
            
            assert result == []

    def test_process_batch_save_subtitles_with_multiple_layouts(self):
        """测试批量保存时使用多种布局"""
        segments_proto = self.create_test_segments_proto()
        
        with patch.object(self.processor, 'prepare_asr_data_from_grpc') as mock_prepare, \
             patch.object(self.processor, 'generate_subtitle_file_package') as mock_generate:
            
            mock_asr_data = Mock()
            mock_asr_data.has_data.return_value = True
            mock_segment = Mock()
            mock_segment.text = "Original"
            mock_segment.translated_text = "Translated"
            mock_asr_data.segments = [mock_segment, mock_segment]
            mock_prepare.return_value = mock_asr_data
            
            mock_file_packages = [
                {'file_name': 'test-原文在上.srt', 'layout': '原文在上'},
                {'file_name': 'test-仅译文.srt', 'layout': '仅译文'},
                {'file_name': 'test-原文在上.ass', 'layout': '原文在上'},
                {'file_name': 'test-仅译文.ass', 'layout': '仅译文'}
            ]
            mock_generate.side_effect = mock_file_packages
            
            result = self.processor.process_batch_save_subtitles(
                segments_proto_list=segments_proto,
                original_filename_or_title="test_video",
                formats_to_generate=["srt", "ass"],
                layouts_to_generate=["原文在上", "仅译文"],
                translation_requested=True,
                auto_save_to_default=False
            )
            
            assert len(result) == 4
            assert mock_generate.call_count == 4

    def test_process_batch_save_subtitles_filter_translation_not_requested(self):
        """测试批量保存时过滤不需要翻译的布局"""
        segments_proto = self.create_test_segments_proto()
        
        with patch.object(self.processor, 'prepare_asr_data_from_grpc') as mock_prepare, \
             patch.object(self.processor, 'generate_subtitle_file_package') as mock_generate:
            
            mock_asr_data = Mock()
            mock_asr_data.has_data.return_value = True
            mock_segment_with_original = Mock()
            mock_segment_with_original.text = "Original Text"
            mock_segment_with_original.translated_text = "" 
            mock_asr_data.segments = [mock_segment_with_original]
            mock_prepare.return_value = mock_asr_data
            
            mock_generate.return_value = {'file_name': 'test.srt'}
            
            result = self.processor.process_batch_save_subtitles(
                segments_proto_list=segments_proto,
                original_filename_or_title="test_video",
                formats_to_generate=["srt"],
                layouts_to_generate=["原文在上", "仅译文"],
                translation_requested=False,
                auto_save_to_default=False
            )
            
            assert len(result) == 2 
            assert mock_generate.call_count == 2
            
            args_list = mock_generate.call_args_list
            assert args_list[0][1]['target_layout'] == "原文在上"
            assert args_list[1][1]['target_layout'] == "仅译文"

    def test_logger_configuration(self):
        """测试日志配置"""
        processor1 = SubtitleProcessor()
        assert processor1.logger is not None
        
        custom_logger = Mock()
        processor2 = SubtitleProcessor(logger_instance=custom_logger)
        assert processor2.logger == custom_logger


if __name__ == "__main__":
    pytest.main([__file__])

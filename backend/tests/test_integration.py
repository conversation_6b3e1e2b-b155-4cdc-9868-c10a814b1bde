# -*- coding: utf-8 -*-
"""
集成测试模块。

测试三个核心业务逻辑组件的协作：
- SubtitleServiceImpl
- SubtitleProcessor  
- TranscriptThread

以及端到端的业务流程。
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os
from pathlib import Path

# 导入被测试的模块
from subtitle_service import SubtitleServiceImpl
from subtitle_processor import SubtitleProcessor
from subtitle.transcriptThread import TranscriptThread
from subtitle.core.asr_data import ASRData, ASRDataSeg
from api_protos.v1.subtitler import subtitler_pb2


@pytest.mark.integration
class TestSubtitleServiceIntegration:
    """字幕服务集成测试类"""

    def setup_method(self):
        """每个测试方法执行前的设置"""
        self.service = SubtitleServiceImpl()
        self.temp_dir = tempfile.mkdtemp()
        self.temp_video_path = os.path.join(self.temp_dir, "test_video.mp4")
        self.temp_audio_path = os.path.join(self.temp_dir, "test_audio.wav")
        
        # 创建测试文件
        Path(self.temp_video_path).touch()
        Path(self.temp_audio_path).touch()

    def teardown_method(self):
        """每个测试方法执行后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def create_test_asr_data(self):
        """创建测试用的ASRData对象"""
        segments = [
            ASRDataSeg(
                text="这是第一句话",
                translated_text="This is the first sentence",
                start_time=0,
                end_time=3000
            ),
            ASRDataSeg(
                text="这是第二句话",
                translated_text="This is the second sentence",
                start_time=3000,
                end_time=6000
            )
        ]
        return ASRData(segments=segments)

    @patch('subtitle_service.TranscriptThread')
    def test_video_to_audio_to_text_pipeline(self, mock_transcript_thread_class):
        """测试视频转音频再转文字的流水线"""
        # 设置TranscriptThread mock
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        
        # 模拟video2audio流程
        video_to_audio_progress = [
            {
                "stage_name": "提取音频中",
                "percentage": 50,
                "message": "正在提取音频...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "提取音频完成", 
                "percentage": 100,
                "message": "音频提取完成",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "video_to_audio_response": {
                        "audio_path": self.temp_audio_path,
                        "audio_data": b"mock_audio_data"
                    }
                }
            }
        ]
        
        # 模拟audio2text流程
        audio_to_text_progress = [
            {
                "stage_name": "语音识别中",
                "percentage": 75,
                "message": "正在识别语音...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "语音识别完成",
                "percentage": 100,
                "message": "识别完成",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "audio_to_text_response": {
                        "segments": [
                            {"text": "这是第一句话", "start_time": 0, "end_time": 3000},
                            {"text": "这是第二句话", "start_time": 3000, "end_time": 6000}
                        ]
                    }
                }
            }
        ]
        
        mock_thread.extract_audio_from_video.return_value = iter(video_to_audio_progress)
        mock_thread.transcribe_audio.return_value = iter(audio_to_text_progress)
        
        # 执行video2audio
        video_request = subtitler_pb2.VideoToAudioRequest(video_path=self.temp_video_path)
        video_responses = list(self.service.VideoToAudio(video_request, Mock()))
        
        # 验证video2audio结果
        assert len(video_responses) == 2
        assert video_responses[-1].HasField("video_to_audio_response")
        extracted_audio_path = video_responses[-1].video_to_audio_response.audio_path
        
        # 执行audio2text
        audio_request = subtitler_pb2.AudioToTextRequest(
            audio_path=extracted_audio_path,
            request_word_timestamps=False
        )
        audio_responses = list(self.service.AudioToText(audio_request, Mock()))
        
        # 验证audio2text结果
        assert len(audio_responses) == 2
        assert audio_responses[-1].HasField("audio_to_text_response")
        assert len(audio_responses[-1].audio_to_text_response.segments) == 2
        
        # 验证调用链
        mock_thread.extract_audio_from_video.assert_called_once_with(self.temp_video_path)
        mock_thread.transcribe_audio.assert_called_once_with(
            audio_path=extracted_audio_path,
            audio_data=b"",
            request_word_timestamps=False
        )

    @patch('subtitle_service.TranscriptThread')
    def test_generate_and_save_subtitle_pipeline(self, mock_transcript_thread_class):
        """测试生成字幕并保存的流水线"""
        # 设置TranscriptThread mock
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        
        # 模拟生成字幕流程
        generate_progress = [
            {
                "stage_name": "字幕生成完成",
                "percentage": 100,
                "message": "字幕生成成功",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "generate_subtitles_response": {
                        "srt_content": "1\n00:00:00,000 --> 00:00:03,000\n这是第一句话\n\n2\n00:00:03,000 --> 00:00:06,000\n这是第二句话\n\n",
                        "ass_content": "[Script Info]\nTitle: Test\n\n[V4+ Styles]\n..."
                    }
                }
            }
        ]
        
        mock_thread.generate_subtitles_from_text.return_value = iter(generate_progress)
        
        # 执行生成字幕
        generate_request = subtitler_pb2.GenerateSubtitlesRequest(
            text="这是第一句话。这是第二句话。",
            audio_path=self.temp_audio_path
        )
        generate_responses = list(self.service.GenerateSubtitles(generate_request, Mock()))
        
        # 验证生成结果
        assert len(generate_responses) == 1
        assert generate_responses[0].HasField("generate_subtitles_response")
        srt_content = generate_responses[0].generate_subtitles_response.srt_content
        
        # 模拟保存字幕
        with patch.object(self.service.processor, 'process_save_subtitle') as mock_save:
            mock_file_package = {
                'file_path': '/test/path/test.srt',
                'file_name': 'test.srt',
                'file_data': srt_content.encode('utf-8'),
                'file_size': len(srt_content.encode('utf-8')),
                'saved_to_default': True,
                'format': 'srt',
                'layout': '原文在上',
                'original_filename_or_title': 'test'
            }
            mock_save.return_value = mock_file_package
            
            # 从SRT内容创建字幕片段
            segments = [
                subtitler_pb2.SubtitleSegment(
                    start_time=0,
                    end_time=3000,
                    original_text="这是第一句话",
                    translated_text=""
                ),
                subtitler_pb2.SubtitleSegment(
                    start_time=3000,
                    end_time=6000,
                    original_text="这是第二句话",
                    translated_text=""
                )
            ]
            
            save_request = subtitler_pb2.SaveSubtitleRequest(
                segments=segments,
                file_name="test",
                format="srt",
                layout="原文在上",
                auto_save_to_default=True
            )
            save_response = self.service.SaveSubtitle(save_request, Mock())
            
            # 验证保存结果
            assert save_response.file_name == "test.srt"
            assert save_response.saved_to_default == True
            assert save_response.file_size > 0

    @patch('subtitle_service.TranscriptThread')
    def test_end_to_end_video_to_translated_subtitles(self, mock_transcript_thread_class):
        """测试端到端的视频到翻译字幕流程"""
        # 设置TranscriptThread mock
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        
        # 模拟完整流程的进度
        complete_progress = [
            {
                "stage_name": "提取音频",
                "percentage": 20,
                "message": "正在从视频提取音频...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "语音识别",
                "percentage": 40,
                "message": "正在进行语音识别...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "字幕优化",
                "percentage": 60,
                "message": "正在优化字幕...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "字幕翻译",
                "percentage": 80,
                "message": "正在翻译字幕...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "处理完成",
                "percentage": 100,
                "message": "翻译字幕生成完成",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "process_video_to_translated_subtitles_response": {
                        "translated_subtitle_content": "1\n00:00:00,000 --> 00:00:03,000\nThis is the first sentence\n\n2\n00:00:03,000 --> 00:00:06,000\nThis is the second sentence\n\n"
                    }
                }
            }
        ]
        
        mock_thread.process_video_to_translated_subtitles.return_value = iter(complete_progress)
        
        # 执行完整流程
        request = subtitler_pb2.ProcessVideoToTranslatedSubtitlesRequest(
            video_path=self.temp_video_path,
            target_language="english"
        )
        responses = list(self.service.ProcessVideoToTranslatedSubtitles(request, Mock()))
        
        # 验证完整流程结果
        assert len(responses) == 5
        
        # 验证进度递增
        percentages = [resp.percentage for resp in responses]
        assert percentages == [20, 40, 60, 80, 100]
        
        # 验证最终结果
        final_response = responses[-1]
        assert final_response.HasField("process_video_to_translated_subtitles_response")
        assert "This is the first sentence" in final_response.process_video_to_translated_subtitles_response.translated_subtitle_content
        
        # 验证TranscriptThread调用
        # SubtitleServiceImpl.ProcessVideoToTranslatedSubtitles 会传递 request.request_word_timestamps
        # 对于默认的 ProcessVideoToTranslatedSubtitlesRequest，request_word_timestamps 默认为 False (protobuf bool default)
        mock_thread.process_video_to_translated_subtitles.assert_called_once_with(
            video_path_str=self.temp_video_path,
            target_language="english"
            # request_word_timestamps 参数已从 subtitle_service 调用 transcript_thread 时移除
        )

    def test_subtitle_processor_integration_with_asr_data(self):
        """测试SubtitleProcessor与ASRData的集成"""
        processor = SubtitleProcessor()
        
        # 创建测试的protobuf segments
        segments_proto = [
            subtitler_pb2.SubtitleSegment(
                start_time=0,
                end_time=3000,
                original_text="测试原文1",
                translated_text="Test translation 1"
            ),
            subtitler_pb2.SubtitleSegment(
                start_time=3000,
                end_time=6000,
                original_text="测试原文2",
                translated_text="Test translation 2"
            )
        ]
        
        # 测试protobuf到ASRData的转换
        asr_data = processor.prepare_asr_data_from_grpc(segments_proto)
        
        assert isinstance(asr_data, ASRData)
        assert len(asr_data.segments) == 2
        assert asr_data.segments[0].text == "测试原文1"
        assert asr_data.segments[0].translated_text == "Test translation 1"
        
        # 测试生成多种格式的字幕文件
        with patch('subtitle_processor.DEFAULT_SUBTITLE_SAVE_DIR') as mock_save_dir:
            mock_save_dir.mkdir = Mock()
            
            formats = ['srt', 'ass', 'txt', 'json']
            layouts = ['原文在上', '译文在上', '仅原文', '仅译文']
            
            file_packages = processor.process_batch_save_subtitles(
                segments_proto_list=segments_proto,
                original_filename_or_title="integration_test",
                formats_to_generate=formats,
                layouts_to_generate=layouts,
                translation_requested=True,
                auto_save_to_default=False
            )
            
            # 验证生成的文件数量: 4 formats × 4 layouts = 16 files
            assert len(file_packages) == 16
            
            # 验证每种格式都有生成
            generated_formats = set(pkg['format'] for pkg in file_packages)
            assert generated_formats == set(formats)
            
            # 验证每种布局都有生成
            generated_layouts = set(pkg['layout'] for pkg in file_packages)
            assert generated_layouts == set(layouts)

    @patch('subtitle.transcriptThread.video2audio')
    @patch('subtitle.transcriptThread.transcribe_core_func')
    def test_transcript_thread_integration_with_real_asr_data(self, mock_transcribe, mock_video2audio):
        """测试TranscriptThread与真实ASRData结构的集成"""
        # 设置mocks
        mock_video2audio.return_value = self.temp_audio_path
        
        test_asr_data = self.create_test_asr_data()
        mock_transcribe.return_value = test_asr_data
        
        thread = TranscriptThread()
        
        # 测试音频提取和转录的集成
        extract_progress = list(thread.extract_audio_from_video(self.temp_video_path))
        transcribe_progress = list(thread.transcribe_audio(audio_path=self.temp_audio_path))
        
        # 验证音频提取结果
        assert len(extract_progress) >= 2
        final_extract = extract_progress[-1]
        assert final_extract["percentage"] == 100
        assert "final_result" in final_extract
        
        # 验证转录结果
        assert len(transcribe_progress) >= 2
        final_transcribe = transcribe_progress[-1]
        assert final_transcribe["percentage"] == 100
        assert "final_result" in final_transcribe
        
        # 验证ASR数据被正确缓存
        assert thread.asr_data_from_transcription is not None
        assert len(thread.asr_data_from_transcription.segments) == 2

    def test_error_handling_integration(self):
        """测试错误处理的集成"""
        # 测试SubtitleServiceImpl的错误处理
        with patch('subtitle_service.TranscriptThread') as mock_thread_class:
            mock_thread = Mock()
            mock_thread_class.return_value = mock_thread
            
            # 模拟TranscriptThread抛出异常
            mock_thread.extract_audio_from_video.side_effect = Exception("集成测试错误")
            
            request = subtitler_pb2.VideoToAudioRequest(video_path=self.temp_video_path)
            responses = list(self.service.VideoToAudio(request, Mock()))
            
            # 验证错误被正确处理和传播
            assert len(responses) == 1
            assert responses[0].is_error == True
            assert "集成测试错误" in responses[0].error_message

    @pytest.mark.slow
    def test_performance_integration(self):
        """测试性能集成（标记为慢速测试）"""
        import time
        
        # 测试大量字幕片段的处理性能
        large_segments = []
        for i in range(100):  # 100个字幕片段
            large_segments.append(
                subtitler_pb2.SubtitleSegment(
                    start_time=i * 3000,
                    end_time=(i + 1) * 3000,
                    original_text=f"测试字幕 {i+1}",
                    translated_text=f"Test subtitle {i+1}"
                )
            )
        
        processor = SubtitleProcessor()
        
        # 测量转换时间
        start_time = time.time()
        asr_data = processor.prepare_asr_data_from_grpc(large_segments)
        conversion_time = time.time() - start_time
        
        # 验证结果正确性
        assert len(asr_data.segments) == 100
        assert asr_data.segments[0].text == "测试字幕 1"
        assert asr_data.segments[99].text == "测试字幕 100"
        
        # 性能断言（转换100个片段应该在1秒内完成）
        assert conversion_time < 1.0, f"转换时间过长: {conversion_time}秒"


@pytest.mark.integration 
class TestCrossComponentIntegration:
    """跨组件集成测试类"""

    def test_service_processor_interaction(self):
        """测试Service和Processor的交互"""
        service = SubtitleServiceImpl()
        
        # 创建测试数据
        segments = [
            subtitler_pb2.SubtitleSegment(
                start_time=0,
                end_time=3000,
                original_text="跨组件测试",
                translated_text="Cross-component test"
            )
        ]
        
        # 通过Service调用Processor
        with patch.object(service.processor, 'process_save_subtitle') as mock_save:
            mock_save.return_value = {
                'file_name': 'cross_test.srt',
                'file_data': b'test data',
                'file_size': 9
            }
            
            request = subtitler_pb2.SaveSubtitleRequest(
                segments=segments,
                file_name="cross_test",
                format="srt",
                layout="原文在上"
            )
            
            response = service.SaveSubtitle(request, Mock())
            
            # 验证Service正确调用了Processor
            mock_save.assert_called_once()
            assert response.file_name == 'cross_test.srt'

    def test_service_thread_interaction(self):
        """测试Service和TranscriptThread的交互"""
        service = SubtitleServiceImpl()
        
        with patch('subtitle_service.TranscriptThread') as mock_thread_class:
            mock_thread = Mock()
            mock_thread_class.return_value = mock_thread
            
            # 模拟TranscriptThread返回的进度
            mock_thread.extract_audio_from_video.return_value = iter([
                {
                    "stage_name": "完成",
                    "percentage": 100,
                    "message": "成功",
                    "is_error": False,
                    "error_message": "",
                    "final_result": {
                        "video_to_audio_response": {
                            "audio_path": "/test/audio.wav"
                        }
                    }
                }
            ])
            
            request = subtitler_pb2.VideoToAudioRequest(video_path="/test/video.mp4")
            responses = list(service.VideoToAudio(request, Mock()))
            
            # 验证Service正确调用了TranscriptThread
            mock_thread.extract_audio_from_video.assert_called_once_with("/test/video.mp4")
            assert len(responses) == 1
            assert responses[0].HasField("video_to_audio_response")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

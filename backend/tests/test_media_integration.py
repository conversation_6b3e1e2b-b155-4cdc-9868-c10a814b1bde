#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
媒体处理集成测试模块。

使用实际的媒体文件（视频和音频）测试字幕服务的核心功能。
包括：
- 视频到音频的转换
- 音频到文字的转录
- 完整的视频到字幕的处理流程
"""

import pytest
import os
from pathlib import Path
import shutil
from unittest.mock import Mock
import json

# 导入被测试的模块
from subtitle_service import SubtitleServiceImpl
from api_protos.v1.subtitler import subtitler_pb2
from subtitle.core.asr_data import ASRData, ASRDataSeg

# 注意：此文件中的 setup_environment 和 setup_test_env fixture 已被移除。
# 全局环境变量现在由 backend/tests/conftest.py 中的 setup_test_env 统一管理。

@pytest.fixture(scope="module")
def media_files():
    """提供测试用的媒体文件路径"""
    project_root = Path(__file__).parent.parent.parent
    video_path = project_root / "sample" / "13.Conclusion.mp4"
    audio_path = project_root / "sample" / "tmpeje_e6jj.wav"
    
    if not video_path.exists():
        pytest.skip(f"测试视频文件不存在: {video_path}")
    if not audio_path.exists():
        pytest.skip(f"测试音频文件不存在: {audio_path}")
    
    return {
        'video_path': str(video_path),
        'audio_path': str(audio_path)
    }

@pytest.fixture(scope="function")
def temp_output_dir():
    """创建临时输出目录"""
    # 使用更具体的文件名以避免潜在冲突
    temp_dir = Path.home() / f"temp_subtitle_test_media_integration_{os.getpid()}"
    temp_dir.mkdir(parents=True, exist_ok=True)
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.mark.media
class TestMediaProcessing:
    """媒体处理测试类"""

    def setup_method(self):
        """每个测试方法执行前的设置"""
        self.service = SubtitleServiceImpl()

    def test_video_to_audio_conversion(self, media_files, temp_output_dir):
        """测试视频到音频的转换"""
        request = subtitler_pb2.VideoToAudioRequest(
            video_path=media_files['video_path']
        )
        responses = list(self.service.VideoToAudio(request, Mock()))
        assert len(responses) >= 2
        final_response = responses[-1]
        assert final_response.HasField("video_to_audio_response")
        assert final_response.video_to_audio_response.audio_path
        assert os.path.exists(final_response.video_to_audio_response.audio_path)
        audio_size = os.path.getsize(final_response.video_to_audio_response.audio_path)
        assert audio_size > 0, "生成的音频文件不应为空"

    def test_audio_to_text_transcription(self, media_files, monkeypatch):
        """测试音频到文字的转录"""
        monkeypatch.setenv("ASR_LANGUAGE", "en") 

        request = subtitler_pb2.AudioToTextRequest(
            audio_path=media_files['audio_path'],
            request_word_timestamps=True 
        )
        responses = list(self.service.AudioToText(request, Mock()))
        assert len(responses) >= 2
        final_response = responses[-1]
        assert final_response.HasField("audio_to_text_response")
        segments = final_response.audio_to_text_response.segments
        assert len(segments) > 0, "应该识别出至少一个字幕片段"
        
        all_text = ""
        for segment in segments:
            assert hasattr(segment, 'text')
            assert hasattr(segment, 'start_time_ms')
            assert hasattr(segment, 'end_time_ms')
            assert segment.text
            all_text += segment.text + " "
            assert segment.start_time_ms >= 0
            assert segment.end_time_ms > segment.start_time_ms
        
        assert pytest.is_predominantly_english(all_text), \
            f"ASR转录结果应主要为英文 (ASR_LANGUAGE=en)。检测内容: {all_text[:200]}"

    @pytest.mark.slow
    def test_complete_video_processing(self, media_files, monkeypatch):
        """测试完整的视频处理流程（标记为慢速测试）"""
        monkeypatch.setenv("ASR_LANGUAGE", "en")
        # 确保 conftest.py 中的 OPENAI_API_KEY 是有效的，否则翻译会失败

        request = subtitler_pb2.ProcessVideoToTranslatedSubtitlesRequest(
            video_path=media_files['video_path'], 
            target_language="chinese" 
        )
        responses = list(self.service.ProcessVideoToTranslatedSubtitles(request, Mock()))
        
        stages = set()
        process_video_response_found = False
        audio_to_text_final_response_found = False

        for response in responses:
            stages.add(response.stage_name)
            if response.HasField("process_video_to_translated_subtitles_response"):
                process_video_response_found = True
                # 可以选择在这里验证翻译后的SRT内容
                translated_content = response.process_video_to_translated_subtitles_response.translated_subtitle_content
                assert translated_content, "翻译后的字幕内容不应为空 (来自 process_video_to_translated_subtitles_response)"
                assert "00:" in translated_content, "翻译后的SRT应包含时间戳"
                assert "-->" in translated_content, "翻译后的SRT应包含时间戳分隔符"
                parsed_srt = pytest.parse_srt_content(translated_content)
                assert len(parsed_srt) > 0, "解析后的翻译SRT条目不应为空"
                all_text_lines = [line for entry in parsed_srt for line in entry['lines']]
                full_text_content = "\n".join(all_text_lines)
                assert pytest.is_predominantly_chinese(full_text_content), \
                    f"翻译后的字幕内容应主要为中文。内容：\n{full_text_content[:500]}"


        required_stages = {"视频提取音频", "音频转文字", "字幕断句", "字幕翻译", "字幕生成与优化", "完整流程处理完成", "音频转文字结果(最终)"}
        assert required_stages.issubset(stages), f"缺少必要的处理阶段。当前阶段: {stages}. 检查翻译API密钥是否有效。"
        assert process_video_response_found, "在流程中未找到 process_video_to_translated_subtitles_response"
        
        final_response = responses[-1] # 这个应该是 audio_to_text_response
        if final_response.is_error:
            # 即使最后一个是 audio_to_text_response，如果它标记错误，也应该失败
            pytest.fail(f"处理流程最终响应报告错误: {final_response.error_message} (阶段: {final_response.stage_name})")

        assert final_response.stage_name == "音频转文字结果(最终)", f"最终响应的阶段名应为 '音频转文字结果(最终)'，实际为 '{final_response.stage_name}'"
        assert final_response.HasField("audio_to_text_response"), \
            f"最终响应没有 'audio_to_text_response' 字段。响应: {final_response}"
        
        # 验证 audio_to_text_response 中的 segments (原始英文转录)
        segments = final_response.audio_to_text_response.segments
        assert len(segments) > 0, "最终的 audio_to_text_response 中应包含至少一个字幕片段"
        
        all_original_text = ""
        for segment in segments:
            assert segment.text, "最终 audio_to_text_response 中的片段文本不应为空"
            all_original_text += segment.text + " "
            assert segment.start_time_ms >= 0
            assert segment.end_time_ms > segment.start_time_ms
        
        assert pytest.is_predominantly_english(all_original_text), \
            f"最终 audio_to_text_response 的转录结果应主要为英文。检测内容: {all_original_text[:200]}"


    def test_batch_subtitle_generation(self, media_files, temp_output_dir, monkeypatch):
        """测试批量字幕生成（英译中，原文在上/译文在上）"""
        monkeypatch.setenv("ASR_LANGUAGE", "en")

        audio_request = subtitler_pb2.AudioToTextRequest(
            audio_path=media_files['audio_path'],
            request_word_timestamps=True
        )
        audio_responses = list(self.service.AudioToText(audio_request, Mock()))
        raw_segments = audio_responses[-1].audio_to_text_response.segments
        assert len(raw_segments) > 0, "ASR转录应返回片段"

        processed_segments_for_batch = []
        for i, seg in enumerate(raw_segments):
            original_text = seg.text if seg.text and seg.text.strip() else f"Original English Text {i+1}"
            if not pytest.is_predominantly_english(original_text):
                original_text = f"Placeholder English Original {i+1}"
            # 进一步增加中文字符数量，并减少附加的英文文本长度，确保中文字符占比高于0.5
            translated_text = f"这是一段主要的中文翻译内容_{i+1}_{original_text[:3]}"
            processed_segments_for_batch.append(
                subtitler_pb2.SubtitleSegment(
                    start_time=seg.start_time_ms,
                    end_time=seg.end_time_ms,
                    original_text=original_text, 
                    translated_text=translated_text
                )
            )
        
        save_request = subtitler_pb2.BatchSaveSubtitleRequest(
            segments=processed_segments_for_batch,
            file_name_prefix="media_test_batch_en_to_zh",
            formats=["srt"], 
            layouts=["原文在上", "译文在上"], 
            translation_requested=True, 
            # target_language="chinese", # 移除此行，BatchSaveSubtitleRequest 没有这个字段
            auto_save_to_default=True,
            # output_directory=str(temp_output_dir), # 移除此行，使用默认或mock的输出目录
            content_sources=["transcript", "translation"]
        )
        
        response = self.service.BatchSaveSubtitle(save_request, Mock())
        
        assert len(response.files) == 2, f"应该为每种布局生成一个SRT文件，实际生成 {len(response.files)} 个"
        
        files_by_layout = {}
        for file_info in response.files:
            assert file_info.file_size > 0, f"{file_info.file_name} 不应为空"
            file_to_check = Path(file_info.file_path)
            assert file_to_check.exists(), f"文件 {file_info.file_name} (路径: {file_to_check}) 未找到或未保存"
            assert file_info.format == "srt", f"文件格式应为 SRT: {file_info.format}"
            files_by_layout[file_info.layout] = str(file_to_check)

        assert "原文在上" in files_by_layout, "未找到“原文在上”布局的文件"
        assert "译文在上" in files_by_layout, "未找到“译文在上”布局的文件"

        srt_content_orig_top = Path(files_by_layout["原文在上"]).read_text(encoding='utf-8')
        parsed_srt_orig_top = pytest.parse_srt_content(srt_content_orig_top)
        assert len(parsed_srt_orig_top) > 0, "原文在上SRT解析条目不应为空"
        assert len(parsed_srt_orig_top) == len(processed_segments_for_batch), "原文在上SRT条目数应与输入片段数一致"

        for entry_idx, entry in enumerate(parsed_srt_orig_top):
            assert len(entry['lines']) >= 2, f"原文在上布局，条目 {entry_idx+1} 应至少有两行文本，实际: {entry['lines']}"
            original_line = entry['lines'][0]
            translated_line = entry['lines'][1]
            expected_original = processed_segments_for_batch[entry_idx].original_text
            expected_translated = processed_segments_for_batch[entry_idx].translated_text
            assert original_line.strip() == expected_original.strip(), f"原文在上，条目 {entry_idx+1}：原文行内容不匹配。预期: '{expected_original}', 实际: '{original_line}'"
            assert translated_line.strip() == expected_translated.strip(), f"原文在上，条目 {entry_idx+1}：译文行内容不匹配。预期: '{expected_translated}', 实际: '{translated_line}'"
            assert pytest.is_predominantly_english(original_line), f"原文在上，条目 {entry_idx+1}：原文行应为英文，但检测到: '{original_line}' (预期: '{expected_original}')"
            assert pytest.is_predominantly_chinese(translated_line), f"原文在上，条目 {entry_idx+1}：译文行应为中文，但检测到: '{translated_line}' (预期: '{expected_translated}')"

        srt_content_trans_top = Path(files_by_layout["译文在上"]).read_text(encoding='utf-8')
        parsed_srt_trans_top = pytest.parse_srt_content(srt_content_trans_top)
        assert len(parsed_srt_trans_top) > 0, "译文在上SRT解析条目不应为空"
        assert len(parsed_srt_trans_top) == len(processed_segments_for_batch), "译文在上SRT条目数应与输入片段数一致"

        for entry_idx, entry in enumerate(parsed_srt_trans_top):
            assert len(entry['lines']) >= 2, f"译文在上布局，条目 {entry_idx+1} 应至少有两行文本，实际: {entry['lines']}"
            translated_line = entry['lines'][0]
            original_line = entry['lines'][1]
            expected_original = processed_segments_for_batch[entry_idx].original_text
            expected_translated = processed_segments_for_batch[entry_idx].translated_text
            assert translated_line.strip() == expected_translated.strip(), f"译文在上，条目 {entry_idx+1}：译文行内容不匹配。预期: '{expected_translated}', 实际: '{translated_line}'"
            assert original_line.strip() == expected_original.strip(), f"译文在上，条目 {entry_idx+1}：原文行内容不匹配。预期: '{expected_original}', 实际: '{original_line}'"
            assert pytest.is_predominantly_chinese(translated_line), f"译文在上，条目 {entry_idx+1}：译文行应为中文，但检测到: '{translated_line}' (预期: '{expected_translated}')"
            assert pytest.is_predominantly_english(original_line), f"译文在上，条目 {entry_idx+1}：原文行应为英文，但检测到: '{original_line}' (预期: '{expected_original}')"

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
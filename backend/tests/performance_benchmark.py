# -*- coding: utf-8 -*-
"""
性能基准测试

提供系统性能基准测试，包括吞吐量、延迟、并发性能等指标。
"""
import asyncio
import time
import statistics
import concurrent.futures
from typing import List, Dict, Any, Callable
from dataclasses import dataclass, field
import json
import threading
from pathlib import Path

import sys
sys.path.append(str(Path(__file__).parent.parent))

from subtitle.async_processor import get_async_task_manager, TaskPriority
from subtitle.async_stream_processor import get_async_stream_processor
from subtitle.async_batch_processor import get_async_batch_processor
from subtitle.cache_manager import get_cache_manager
from subtitle.logging_manager import get_logger

logger = get_logger(__name__)


@dataclass
class BenchmarkResult:
    """基准测试结果"""
    test_name: str
    total_operations: int
    total_time: float
    throughput: float  # ops/sec
    avg_latency: float  # seconds
    min_latency: float
    max_latency: float
    p95_latency: float
    p99_latency: float
    success_rate: float
    error_count: int
    memory_usage_mb: float
    cpu_usage_percent: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "test_name": self.test_name,
            "performance_metrics": {
                "total_operations": self.total_operations,
                "total_time": self.total_time,
                "throughput": self.throughput,
                "latency": {
                    "avg": self.avg_latency,
                    "min": self.min_latency,
                    "max": self.max_latency,
                    "p95": self.p95_latency,
                    "p99": self.p99_latency
                },
                "success_rate": self.success_rate,
                "error_count": self.error_count
            },
            "resource_usage": {
                "memory_mb": self.memory_usage_mb,
                "cpu_percent": self.cpu_usage_percent
            },
            "metadata": self.metadata
        }


class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self):
        self.results: List[BenchmarkResult] = []
        self.task_manager = get_async_task_manager()
        self.stream_processor = get_async_stream_processor()
        self.batch_processor = get_async_batch_processor()
        self.cache_manager = get_cache_manager()
        
        logger.info("性能基准测试器初始化完成")
    
    async def benchmark_async_task_throughput(
        self,
        num_tasks: int = 100,
        concurrency: int = 10
    ) -> BenchmarkResult:
        """异步任务吞吐量基准测试"""
        
        def simple_task(task_id: int) -> str:
            # 模拟轻量级计算
            result = sum(i * i for i in range(100))
            return f"task_{task_id}_result_{result}"
        
        latencies = []
        errors = 0
        start_time = time.time()
        
        # 创建信号量控制并发
        semaphore = asyncio.Semaphore(concurrency)
        
        async def run_single_task(task_id: int):
            async with semaphore:
                task_start = time.time()
                try:
                    await self.task_manager.submit_task(
                        simple_task, task_id,
                        priority=TaskPriority.NORMAL
                    )
                    latency = time.time() - task_start
                    latencies.append(latency)
                except Exception as e:
                    nonlocal errors
                    errors += 1
                    logger.error(f"任务 {task_id} 失败: {e}")
        
        # 并发执行所有任务
        tasks = [run_single_task(i) for i in range(num_tasks)]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # 计算统计指标
        if latencies:
            avg_latency = statistics.mean(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)
            p95_latency = statistics.quantiles(latencies, n=20)[18]  # 95th percentile
            p99_latency = statistics.quantiles(latencies, n=100)[98]  # 99th percentile
        else:
            avg_latency = min_latency = max_latency = p95_latency = p99_latency = 0
        
        result = BenchmarkResult(
            test_name="async_task_throughput",
            total_operations=num_tasks,
            total_time=total_time,
            throughput=num_tasks / total_time,
            avg_latency=avg_latency,
            min_latency=min_latency,
            max_latency=max_latency,
            p95_latency=p95_latency,
            p99_latency=p99_latency,
            success_rate=(num_tasks - errors) / num_tasks * 100,
            error_count=errors,
            memory_usage_mb=self._get_memory_usage(),
            cpu_usage_percent=self._get_cpu_usage(),
            metadata={
                "concurrency": concurrency,
                "task_type": "simple_computation"
            }
        )
        
        self.results.append(result)
        return result
    
    async def benchmark_stream_processing(
        self,
        num_streams: int = 20,
        stream_duration: int = 5
    ) -> BenchmarkResult:
        """流处理性能基准测试"""
        
        latencies = []
        errors = 0
        start_time = time.time()
        
        async def process_single_stream(stream_id: int):
            stream_start = time.time()
            try:
                # 创建流
                stream_id_str = await self.stream_processor.create_stream(
                    metadata={"benchmark": True, "stream_id": stream_id}
                )
                
                # 模拟流处理
                for progress in range(0, 101, 20):
                    await self.stream_processor.update_stream_progress(
                        stream_id_str,
                        f"阶段_{progress//20}",
                        progress,
                        f"处理进度 {progress}%"
                    )
                    await asyncio.sleep(stream_duration / 5)  # 模拟处理时间
                
                latency = time.time() - stream_start
                latencies.append(latency)
                
            except Exception as e:
                nonlocal errors
                errors += 1
                logger.error(f"流 {stream_id} 处理失败: {e}")
        
        # 并发处理所有流
        tasks = [process_single_stream(i) for i in range(num_streams)]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        
        # 计算统计指标
        if latencies:
            avg_latency = statistics.mean(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)
            p95_latency = statistics.quantiles(latencies, n=20)[18] if len(latencies) >= 20 else max_latency
            p99_latency = statistics.quantiles(latencies, n=100)[98] if len(latencies) >= 100 else max_latency
        else:
            avg_latency = min_latency = max_latency = p95_latency = p99_latency = 0
        
        result = BenchmarkResult(
            test_name="stream_processing",
            total_operations=num_streams,
            total_time=total_time,
            throughput=num_streams / total_time,
            avg_latency=avg_latency,
            min_latency=min_latency,
            max_latency=max_latency,
            p95_latency=p95_latency,
            p99_latency=p99_latency,
            success_rate=(num_streams - errors) / num_streams * 100,
            error_count=errors,
            memory_usage_mb=self._get_memory_usage(),
            cpu_usage_percent=self._get_cpu_usage(),
            metadata={
                "stream_duration": stream_duration,
                "concurrent_streams": num_streams
            }
        )
        
        self.results.append(result)
        return result
    
    def benchmark_cache_performance(
        self,
        num_operations: int = 1000,
        num_threads: int = 10
    ) -> BenchmarkResult:
        """缓存性能基准测试"""
        
        latencies = []
        errors = 0
        start_time = time.time()
        
        def cache_worker(worker_id: int, operations_per_worker: int):
            worker_latencies = []
            worker_errors = 0
            
            for i in range(operations_per_worker):
                op_start = time.time()
                try:
                    key = f"benchmark_key_{worker_id}_{i}"
                    value = {"data": f"value_{worker_id}_{i}", "timestamp": time.time()}
                    
                    # 写操作
                    self.cache_manager.set(key, value)
                    
                    # 读操作
                    cached_value = self.cache_manager.get(key)
                    
                    if cached_value != value:
                        worker_errors += 1
                    
                    latency = time.time() - op_start
                    worker_latencies.append(latency)
                    
                except Exception as e:
                    worker_errors += 1
                    logger.error(f"缓存操作失败: {e}")
            
            return worker_latencies, worker_errors
        
        # 使用线程池并发测试
        operations_per_worker = num_operations // num_threads
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
            futures = [
                executor.submit(cache_worker, i, operations_per_worker)
                for i in range(num_threads)
            ]
            
            for future in concurrent.futures.as_completed(futures):
                worker_latencies, worker_errors = future.result()
                latencies.extend(worker_latencies)
                errors += worker_errors
        
        total_time = time.time() - start_time
        actual_operations = len(latencies)
        
        # 计算统计指标
        if latencies:
            avg_latency = statistics.mean(latencies)
            min_latency = min(latencies)
            max_latency = max(latencies)
            p95_latency = statistics.quantiles(latencies, n=20)[18] if len(latencies) >= 20 else max_latency
            p99_latency = statistics.quantiles(latencies, n=100)[98] if len(latencies) >= 100 else max_latency
        else:
            avg_latency = min_latency = max_latency = p95_latency = p99_latency = 0
        
        result = BenchmarkResult(
            test_name="cache_performance",
            total_operations=actual_operations,
            total_time=total_time,
            throughput=actual_operations / total_time,
            avg_latency=avg_latency,
            min_latency=min_latency,
            max_latency=max_latency,
            p95_latency=p95_latency,
            p99_latency=p99_latency,
            success_rate=(actual_operations - errors) / actual_operations * 100 if actual_operations > 0 else 0,
            error_count=errors,
            memory_usage_mb=self._get_memory_usage(),
            cpu_usage_percent=self._get_cpu_usage(),
            metadata={
                "num_threads": num_threads,
                "operations_per_worker": operations_per_worker,
                "cache_backend": self.cache_manager.get_stats().get("backend", "unknown")
            }
        )
        
        self.results.append(result)
        return result
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量(MB)"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
    
    def _get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        try:
            import psutil
            return psutil.cpu_percent(interval=0.1)
        except ImportError:
            return 0.0
    
    async def run_all_benchmarks(self) -> List[BenchmarkResult]:
        """运行所有基准测试"""
        logger.info("🚀 开始运行性能基准测试...")
        
        # 1. 异步任务吞吐量测试
        logger.info("📊 测试异步任务吞吐量...")
        await self.benchmark_async_task_throughput(num_tasks=100, concurrency=10)
        
        # 2. 流处理性能测试
        logger.info("🌊 测试流处理性能...")
        await self.benchmark_stream_processing(num_streams=10, stream_duration=2)
        
        # 3. 缓存性能测试
        logger.info("💾 测试缓存性能...")
        self.benchmark_cache_performance(num_operations=500, num_threads=5)
        
        logger.info("✅ 所有基准测试完成")
        return self.results
    
    def generate_benchmark_report(self) -> str:
        """生成基准测试报告"""
        if not self.results:
            return "❌ 没有基准测试结果"
        
        report = """# 🚀 性能基准测试报告

## 📊 测试概览

"""
        
        for result in self.results:
            report += f"""
### {result.test_name.replace('_', ' ').title()}

**性能指标**:
- 总操作数: {result.total_operations:,}
- 总耗时: {result.total_time:.2f}秒
- 吞吐量: {result.throughput:.2f} ops/sec
- 平均延迟: {result.avg_latency*1000:.2f}ms
- P95延迟: {result.p95_latency*1000:.2f}ms
- P99延迟: {result.p99_latency*1000:.2f}ms
- 成功率: {result.success_rate:.2f}%

**资源使用**:
- 内存使用: {result.memory_usage_mb:.2f}MB
- CPU使用: {result.cpu_usage_percent:.2f}%

"""
        
        # 性能等级评估
        report += """
## 🎯 性能等级评估

"""
        
        for result in self.results:
            if result.test_name == "async_task_throughput":
                if result.throughput > 50:
                    grade = "🟢 优秀"
                elif result.throughput > 20:
                    grade = "🟡 良好"
                else:
                    grade = "🔴 需要优化"
                report += f"- **异步任务处理**: {grade} ({result.throughput:.1f} ops/sec)\n"
            
            elif result.test_name == "stream_processing":
                if result.avg_latency < 2.0:
                    grade = "🟢 优秀"
                elif result.avg_latency < 5.0:
                    grade = "🟡 良好"
                else:
                    grade = "🔴 需要优化"
                report += f"- **流处理性能**: {grade} ({result.avg_latency:.2f}s 平均延迟)\n"
            
            elif result.test_name == "cache_performance":
                if result.throughput > 1000:
                    grade = "🟢 优秀"
                elif result.throughput > 500:
                    grade = "🟡 良好"
                else:
                    grade = "🔴 需要优化"
                report += f"- **缓存性能**: {grade} ({result.throughput:.0f} ops/sec)\n"
        
        # 优化建议
        report += """
## 💡 优化建议

"""
        
        for result in self.results:
            if result.success_rate < 95:
                report += f"- ⚠️ {result.test_name}: 成功率较低({result.success_rate:.1f}%)，建议检查错误处理\n"
            
            if result.test_name == "async_task_throughput" and result.throughput < 20:
                report += f"- 🔧 异步任务: 吞吐量较低，建议增加工作线程数或优化任务逻辑\n"
            
            if result.test_name == "cache_performance" and result.avg_latency > 0.01:
                report += f"- 💾 缓存性能: 延迟较高({result.avg_latency*1000:.1f}ms)，建议检查缓存后端配置\n"
        
        report += f"""
---
*基准测试报告生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}*
*测试环境: Python {sys.version.split()[0]}*
"""
        
        return report
    
    def save_results(self, filename: str = "benchmark_results.json"):
        """保存测试结果到文件"""
        results_data = {
            "timestamp": time.time(),
            "test_environment": {
                "python_version": sys.version,
                "platform": sys.platform
            },
            "results": [result.to_dict() for result in self.results]
        }
        
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📊 基准测试结果已保存到: {filename}")


async def run_performance_benchmark():
    """运行性能基准测试的主函数"""
    benchmark = PerformanceBenchmark()
    
    try:
        # 运行所有基准测试
        results = await benchmark.run_all_benchmarks()
        
        # 生成报告
        report = benchmark.generate_benchmark_report()
        print(report)
        
        # 保存结果
        benchmark.save_results()
        
        # 保存报告
        with open("benchmark_report.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        print("📊 基准测试报告已保存到 benchmark_report.md")
        
        return results
        
    except Exception as e:
        logger.error(f"基准测试失败: {e}", exc_info=True)
        return []


if __name__ == "__main__":
    asyncio.run(run_performance_benchmark())

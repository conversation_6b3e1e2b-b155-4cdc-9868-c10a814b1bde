# -*- coding: utf-8 -*-
"""
pytest 配置文件。

提供测试的通用配置、夹具(fixtures)和工具函数。
"""

import pytest
import tempfile
import shutil
import os
import sys
import logging
from pathlib import Path
from unittest.mock import Mock, patch

# 确保项目根目录在 Python 路径中
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))


@pytest.fixture(scope="session")
def temp_workspace():
    """创建临时工作目录，会话结束后清理"""
    temp_dir = tempfile.mkdtemp(prefix="subtitle_test_")
    yield temp_dir
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def temp_files(temp_workspace):
    """创建临时测试文件"""
    temp_dir = Path(temp_workspace)
    
    # 创建测试文件
    video_file = temp_dir / "test_video.mp4"
    audio_file = temp_dir / "test_audio.wav"
    srt_file = temp_dir / "test_subtitle.srt"
    
    video_file.touch()
    audio_file.touch()
    
    # 创建测试SRT内容
    srt_content = """1
00:00:00,000 --> 00:00:03,000
第一句测试字幕

2
00:00:03,000 --> 00:00:06,000
第二句测试字幕

"""
    srt_file.write_text(srt_content, encoding='utf-8')
    
    return {
        'video_path': str(video_file),
        'audio_path': str(audio_file),
        'srt_path': str(srt_file),
        'temp_dir': str(temp_dir)
    }


@pytest.fixture
def mock_asr_data():
    """创建模拟的ASRData对象"""
    from subtitle.core.asr_data import ASRData, ASRDataSeg
    
    segments = [
        ASRDataSeg(
            text="测试原文1",
            translated_text="Test translation 1",
            start_time=0,
            end_time=3000
        ),
        ASRDataSeg(
            text="测试原文2",
            translated_text="Test translation 2", 
            start_time=3000,
            end_time=6000
        )
    ]
    return ASRData(segments=segments)


@pytest.fixture
def mock_progress_callback():
    """创建模拟的进度回调函数"""
    callback_calls = []
    
    def callback(stage, percent, message):
        callback_calls.append({
            'stage': stage,
            'percent': percent,
            'message': message
        })
    
    callback.calls = callback_calls
    return callback


@pytest.fixture
def mock_environment_variables():
    """模拟环境变量"""
    env_vars = {
        'OPENAI_API_KEY': 'test_openai_key',
        'OPENAI_BASE_URL': 'https://test.openai.com',
        'DEEPLX_API_ENDPOINT': 'https://test.deeplx.com'
    }
    
    with patch.dict(os.environ, env_vars):
        yield env_vars


@pytest.fixture
def mock_logger():
    """创建模拟的日志记录器"""
    logger = Mock()
    logger.info = Mock()
    logger.warning = Mock()
    logger.error = Mock()
    logger.debug = Mock()
    return logger


class MockGrpcContext:
    """模拟的gRPC上下文对象"""
    def __init__(self):
        self.code = None
        self.details = None
        
    def set_code(self, code):
        self.code = code
        
    def set_details(self, details):
        self.details = details


@pytest.fixture
def mock_grpc_context():
    """创建模拟的gRPC上下文"""
    return MockGrpcContext()


def create_mock_protobuf_segments():
    """创建模拟的protobuf字幕片段"""
    from api_protos.v1.subtitler import subtitler_pb2
    
    return [
        subtitler_pb2.SubtitleSegment(
            start_time=0,
            end_time=3000,
            original_text="测试原文1",
            translated_text="Test translation 1"
        ),
        subtitler_pb2.SubtitleSegment(
            start_time=3000,
            end_time=6000,
            original_text="测试原文2",
            translated_text="Test translation 2"
        )
    ]


@pytest.fixture
def mock_protobuf_segments():
    """创建模拟的protobuf字幕片段"""
    return create_mock_protobuf_segments()


# 测试标记
pytest_plugins = []

def pytest_configure(config):
    """pytest配置"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "requires_external_service: marks tests that require external services"
    )
    config.addinivalue_line(
        "markers", "media: marks tests that use actual media files"
    )


# 自动使用的夹具
@pytest.fixture(autouse=True)
def setup_logging():
    """设置和清理日志配置"""
    # 保存原始处理器
    root_logger = logging.getLogger()
    original_handlers = root_logger.handlers[:]
    original_level = root_logger.level
    
    # 设置测试专用的日志处理器
    test_handler = logging.StreamHandler()
    test_handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    root_logger.addHandler(test_handler)
    root_logger.setLevel(logging.INFO)
    
    yield
    
    try:
        # 确保所有日志消息都被刷新
        root_logger.handlers[0].flush()
        
        # 清理所有处理器
        for handler in root_logger.handlers[:]:
            handler.flush()
            handler.close()
            root_logger.removeHandler(handler)
        
        # 恢复原始处理器和日志级别
        root_logger.setLevel(original_level)
        for handler in original_handlers:
            if handler not in root_logger.handlers:
                root_logger.addHandler(handler)
    except Exception as e:
        print(f"清理日志处理器时出错: {e}")


@pytest.fixture(autouse=True)
def cleanup_components():
    """确保测试后清理所有组件"""
    yield
    # 在这里添加任何需要清理的组件
    from subtitle.core.translate import BaseTranslator
    from subtitle.core.optimize import SubtitleOptimizer
    from subtitle.core.split import SubtitleSplitter
    
    try:
        # 清理翻译器
        if hasattr(BaseTranslator, '_instance'):
            BaseTranslator._instance.stop()
            BaseTranslator._instance = None
            
        # 清理优化器
        if hasattr(SubtitleOptimizer, '_instance'):
            SubtitleOptimizer._instance.stop()
            SubtitleOptimizer._instance = None
            
        # 清理分割器
        if hasattr(SubtitleSplitter, '_instance'):
            SubtitleSplitter._instance.stop()
            SubtitleSplitter._instance = None
    except Exception as e:
        print(f"清理组件时出错: {e}")


@pytest.fixture(autouse=True) 
def clean_temp_files():
    """每个测试后清理临时文件"""
    yield
    # 清理可能创建的临时文件
    temp_dirs = ['/tmp', '/var/tmp']
    for temp_dir in temp_dirs:
        if os.path.exists(temp_dir):
            for item in os.listdir(temp_dir):
                if item.startswith('subtitle_test_') or item.startswith('pytest_'):
                    item_path = os.path.join(temp_dir, item)
                    try:
                        if os.path.isdir(item_path):
                            shutil.rmtree(item_path, ignore_errors=True)
                        else:
                            os.remove(item_path)
                    except:
                        pass  # 忽略清理错误


# 跳过条件
def pytest_runtest_setup(item):
    """测试运行前的设置"""
    # 如果测试标记为需要外部服务但没有相应的环境变量，则跳过
    if item.get_closest_marker("requires_external_service"):
        if not os.environ.get('OPENAI_API_KEY') and not os.environ.get('RUN_INTEGRATION_TESTS'):
            pytest.skip("需要外部服务但未配置相应的环境变量")


# 自定义断言帮助函数
def assert_progress_structure(progress_dict):
    """断言进度字典的结构是否正确"""
    required_keys = ['stage_name', 'percentage', 'message', 'is_error', 'error_message']
    for key in required_keys:
        assert key in progress_dict, f"进度字典缺少必需的键: {key}"
    
    assert isinstance(progress_dict['percentage'], int), "百分比应该是整数"
    assert 0 <= progress_dict['percentage'] <= 100, "百分比应该在0-100之间"
    assert isinstance(progress_dict['is_error'], bool), "is_error应该是布尔值"


def assert_file_package_structure(file_package):
    """断言文件包字典的结构是否正确"""
    required_keys = ['file_name', 'file_size', 'format', 'layout']
    for key in required_keys:
        assert key in file_package, f"文件包缺少必需的键: {key}"
    
    assert isinstance(file_package['file_size'], int), "文件大小应该是整数"
    assert file_package['file_size'] >= 0, "文件大小应该非负"


# 添加自定义断言到pytest命名空间
pytest.assert_progress_structure = assert_progress_structure
pytest.assert_file_package_structure = assert_file_package_structure

@pytest.fixture(scope="session", autouse=True)
def setup_test_env():
    """设置测试环境变量"""
    # 设置必要的环境变量
    os.environ['PYTHONUNBUFFERED'] = '1'
    os.environ['OPENAI_BASE_URL'] = 'https://generativelanguage.googleapis.com/v1beta/openai/'
    os.environ['OPENAI_API_KEY'] = 'AIzaSyBMBrRcLktxJVEwYKmLgJyVmq6wv26aC2A'
    
    # ASR配置
    os.environ['ASR_TYPE'] = 'JIANYING'
    os.environ['ASR_MODEL'] = 'LARGE'
    os.environ['ASR_LANGUAGE'] = 'zh'
    os.environ['ASR_ENABLE_CACHE'] = 'true'
    
    # 翻译配置
    os.environ['TRANSLATOR_TYPE'] = 'OPENAI'
    os.environ['TRANSLATOR_MODEL'] = 'gemini-2.0-flash-lite	'
    os.environ['TRANSLATOR_ENABLE_CACHE'] = 'true'
    
    # 线程配置
    os.environ['SPLITTER_THREAD_NUM'] = '4'
    os.environ['OPTIMIZER_THREAD_NUM'] = '4'
    os.environ['TRANSLATOR_THREAD_NUM'] = '4'
    
    # 模型配置
    os.environ['SPLITTER_MODEL_NAME'] = 'gemini-2.0-flash-lite'
    os.environ['OPTIMIZER_MODEL_NAME'] = 'gemini-2.0-flash-lite'
    os.environ['TRANSLATOR_MODEL_NAME'] = 'gemini-2.0-flash-lite' 
import re
from typing import List, Tuple, Dict, Any

# SRT 解析和语言验证辅助函数

def parse_srt_content(srt_content: str) -> List[Dict[str, Any]]:
    """
    解析SRT文件内容。

    Args:
        srt_content: SRT格式的字符串内容。

    Returns:
        一个字典列表，每个字典代表一个字幕条目，包含:
        'index': 字幕序号 (int)
        'start_time': 开始时间字符串 (str)
        'end_time': 结束时间字符串 (str)
        'lines': 文本行列表 (List[str])
    """
    if not srt_content:
        return []

    entries = []
    # 修复可能存在的\r\n问题，统一使用\n
    srt_content = srt_content.replace('\r\n', '\n')
    # 按双换行符分割字幕条目
    blocks = srt_content.strip().split('\n\n')

    for block in blocks:
        if not block.strip():
            continue
        
        lines = block.strip().split('\n')
        if len(lines) < 2: # 至少需要序号和时间戳
            continue

        try:
            index = int(lines[0])
            time_match = re.match(r'(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})', lines[1])
            if not time_match:
                # 有时时间戳行可能包含其他元数据，尝试更宽松的匹配
                time_match_flexible = re.search(r'(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})', lines[1])
                if not time_match_flexible:
                    # print(f"无法解析时间戳行: {lines[1]}")
                    continue # 跳过格式不正确的条目
                start_time, end_time = time_match_flexible.groups()
            else:
                start_time, end_time = time_match.groups()

            text_lines = lines[2:]
            entries.append({
                'index': index,
                'start_time': start_time,
                'end_time': end_time,
                'lines': text_lines
            })
        except ValueError:
            # print(f"无法解析序号: {lines[0]}")
            continue # 跳过格式不正确的条目
        except IndexError:
            # print(f"字幕条目行数不足: {block}")
            continue # 跳过格式不正确的条目
            
    return entries

def is_predominantly_english(text: str) -> bool:
    """
    判断字符串是否主要由英文字符组成。
    允许ASCII字母、数字、常见标点和空格。
    """
    if not text:
        return False
    # 统计非英文字符（粗略）
    non_english_chars = 0
    total_chars = 0
    for char in text:
        if '\u4e00' <= char <= '\u9fff': # 中文字符
            non_english_chars += 1
        # 可以添加其他非英文主要字符集的判断
        if char.strip(): # 只统计非空白字符
            total_chars +=1
            
    if total_chars == 0: # 如果全是空白，不算英文
        return False
        
    # 如果中文字符占比小于某个阈值（例如20%），则认为是英文
    # 这个阈值可以调整
    return (non_english_chars / total_chars) < 0.2 if total_chars > 0 else True


def is_predominantly_chinese(text: str) -> bool:
    """
    判断字符串是否主要由中文字符组成。
    """
    if not text:
        return False
    chinese_chars = 0
    total_chars = 0
    for char in text:
        if '\u4e00' <= char <= '\u9fff':
            chinese_chars += 1
        if char.strip(): # 只统计非空白字符
            total_chars += 1
            
    if total_chars == 0: # 如果全是空白，不算中文
        return False
        
    # 如果中文字符占比大于某个阈值（例如50%），则认为是中文
    # 这个阈值可以调整
    return (chinese_chars / total_chars) >= 0.5 if total_chars > 0 else False

# 将辅助函数添加到pytest命名空间，方便在测试中使用
pytest.parse_srt_content = parse_srt_content
pytest.is_predominantly_english = is_predominantly_english
pytest.is_predominantly_chinese = is_predominantly_chinese
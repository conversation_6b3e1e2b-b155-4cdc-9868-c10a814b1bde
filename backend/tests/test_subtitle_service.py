# -*- coding: utf-8 -*-
"""
SubtitleServiceImpl 单元测试模块。

测试 gRPC 服务实现的各个方法，包括：
- VideoToAudio: 视频转音频
- AudioToText: 音频转文本
- GenerateSubtitles: 生成字幕
- TranslateSubtitles: 翻译字幕
- ProcessVideoToTranslatedSubtitles: 视频到翻译字幕的完整流程
- SaveSubtitle: 保存单个字幕文件
- BatchSaveSubtitle: 批量保存字幕文件
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import tempfile
import os
from pathlib import Path
import grpc
from grpc_testing import server_from_dictionary, strict_real_time

# 导入被测试的模块
from subtitle_service import SubtitleServiceImpl, _convert_progress_dict_to_pb
from api_protos.v1.subtitler import subtitler_pb2, subtitler_pb2_grpc
from subtitle.core.asr_data import ASRData, ASRDataSeg


class TestSubtitleServiceImpl:
    """SubtitleServiceImpl 测试类"""

    def setup_method(self):
        """每个测试方法执行前的设置"""
        self.service = SubtitleServiceImpl()
        self.temp_dir = tempfile.mkdtemp()
        self.temp_video_path = os.path.join(self.temp_dir, "test_video.mp4")
        self.temp_audio_path = os.path.join(self.temp_dir, "test_audio.wav")
        
        # 创建测试文件
        Path(self.temp_video_path).touch()
        Path(self.temp_audio_path).touch()

    def teardown_method(self):
        """每个测试方法执行后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_convert_progress_dict_to_pb_basic(self):
        """测试进度字典转换为 ProgressUpdate protobuf 消息"""
        progress_dict = {
            "stage_name": "测试阶段",
            "percentage": 50,
            "message": "测试消息",
            "is_error": False,
            "error_message": ""
        }
        
        pb_msg = _convert_progress_dict_to_pb(progress_dict)
        
        assert pb_msg.stage_name == "测试阶段"
        assert pb_msg.percentage == 50
        assert pb_msg.message == "测试消息"
        assert pb_msg.is_error == False
        assert pb_msg.error_message == ""

    def test_convert_progress_dict_to_pb_with_error(self):
        """测试包含错误的进度字典转换"""
        progress_dict = {
            "stage_name": "错误阶段",
            "percentage": 0,
            "message": "发生错误",
            "is_error": True,
            "error_message": "测试错误信息"
        }
        
        pb_msg = _convert_progress_dict_to_pb(progress_dict)
        
        assert pb_msg.stage_name == "错误阶段"
        assert pb_msg.is_error == True
        assert pb_msg.error_message == "测试错误信息"

    def test_convert_progress_dict_to_pb_with_video_to_audio_result(self):
        """测试包含 video_to_audio 结果的进度字典转换"""
        progress_dict = {
            "stage_name": "提取音频完成",
            "percentage": 100,
            "message": "音频提取成功",
            "is_error": False,
            "error_message": "",
            "final_result": {
                "video_to_audio_response": {
                    "audio_path": "/path/to/audio.wav",
                    "audio_data": b"mock_audio_data"
                }
            }
        }
        
        pb_msg = _convert_progress_dict_to_pb(progress_dict)
        
        assert pb_msg.HasField("video_to_audio_response")
        assert pb_msg.video_to_audio_response.audio_path == "/path/to/audio.wav"
        assert pb_msg.video_to_audio_response.audio_data == b"mock_audio_data"

    def test_convert_progress_dict_to_pb_with_audio_to_text_result(self):
        """测试包含 audio_to_text 结果的进度字典转换"""
        progress_dict = {
            "stage_name": "语音识别完成",
            "percentage": 100,
            "message": "识别成功",
            "is_error": False,
            "error_message": "",
            "final_result": {
                "audio_to_text_response": {
                    "segments": [
                        {"text": "hello", "start_time": 0, "end_time": 1000},
                        {"text": "world", "start_time": 1000, "end_time": 2000}
                    ]
                }
            }
        }
        
        pb_msg = _convert_progress_dict_to_pb(progress_dict)
        
        assert pb_msg.HasField("audio_to_text_response")
        assert len(pb_msg.audio_to_text_response.segments) == 2
        assert pb_msg.audio_to_text_response.segments[0].text == "hello"
        assert pb_msg.audio_to_text_response.segments[0].start_time_ms == 0
        assert pb_msg.audio_to_text_response.segments[0].end_time_ms == 1000

    @patch('subtitle_service.TranscriptThread')
    def test_video_to_audio_success(self, mock_transcript_thread_class):
        """测试 VideoToAudio 方法成功场景"""
        # 设置 mock
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        
        mock_progress_data = [
            {
                "stage_name": "提取音频中",
                "percentage": 50,
                "message": "正在提取音频...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "提取音频完成",
                "percentage": 100,
                "message": "音频提取完成",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "video_to_audio_response": {
                        "audio_path": self.temp_audio_path,
                        "audio_data": b"mock_audio_data"
                    }
                }
            }
        ]
        
        mock_thread.extract_audio_from_video.return_value = iter(mock_progress_data)
        
        # 创建请求
        request = subtitler_pb2.VideoToAudioRequest(video_path=self.temp_video_path)
        context = Mock()
        
        # 执行测试
        responses = list(self.service.VideoToAudio(request, context))
        
        # 验证结果
        assert len(responses) == 2
        assert responses[0].stage_name == "提取音频中"
        assert responses[0].percentage == 50
        assert responses[1].stage_name == "提取音频完成"
        assert responses[1].percentage == 100
        assert responses[1].HasField("video_to_audio_response")
        
        # 验证调用
        mock_thread.extract_audio_from_video.assert_called_once_with(self.temp_video_path)

    @patch('subtitle_service.TranscriptThread')
    def test_video_to_audio_error(self, mock_transcript_thread_class):
        """测试 VideoToAudio 方法错误场景"""
        # 设置 mock 抛出异常
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        mock_thread.extract_audio_from_video.side_effect = Exception("提取音频失败")
        
        # 创建请求
        request = subtitler_pb2.VideoToAudioRequest(video_path=self.temp_video_path)
        context = Mock()
        
        # 执行测试
        responses = list(self.service.VideoToAudio(request, context))
        
        # 验证结果
        assert len(responses) == 1
        assert responses[0].is_error == True
        assert "提取音频失败" in responses[0].error_message
        assert responses[0].stage_name == "视频提取音频错误"

    @patch('subtitle_service.TranscriptThread')
    def test_audio_to_text_with_path(self, mock_transcript_thread_class):
        """测试 AudioToText 方法使用音频路径"""
        # 设置 mock
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        
        mock_progress_data = [
            {
                "stage_name": "语音识别完成",
                "percentage": 100,
                "message": "识别完成",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "audio_to_text_response": {
                        "segments": [
                            {"text": "测试文本", "start_time": 0, "end_time": 3000}
                        ]
                    }
                }
            }
        ]
        
        mock_thread.transcribe_audio.return_value = iter(mock_progress_data)
        
        # 创建请求
        request = subtitler_pb2.AudioToTextRequest(
            audio_path=self.temp_audio_path,
            request_word_timestamps=True
        )
        context = Mock()
        
        # 执行测试
        responses = list(self.service.AudioToText(request, context))
        
        # 验证结果
        assert len(responses) == 1
        assert responses[0].HasField("audio_to_text_response")
        assert len(responses[0].audio_to_text_response.segments) == 1
        assert responses[0].audio_to_text_response.segments[0].text == "测试文本"
        
        # 验证调用参数
        mock_thread.transcribe_audio.assert_called_once_with(
            audio_path=self.temp_audio_path,
            audio_data=b"",
            request_word_timestamps=True
        )

    @patch('subtitle_service.TranscriptThread')
    def test_audio_to_text_with_data(self, mock_transcript_thread_class):
        """测试 AudioToText 方法使用音频数据"""
        # 设置 mock
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        
        mock_progress_data = [
            {
                "stage_name": "语音识别完成",
                "percentage": 100,
                "message": "识别完成",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "audio_to_text_response": {
                        "segments": [
                            {"text": "来自数据的文本", "start_time": 500, "end_time": 2500}
                        ]
                    }
                }
            }
        ]
        
        mock_thread.transcribe_audio.return_value = iter(mock_progress_data)
        
        # 创建请求
        test_audio_data = b"mock_audio_binary_data"
        request = subtitler_pb2.AudioToTextRequest(
            audio_data=test_audio_data,
            request_word_timestamps=False
        )
        context = Mock()
        
        # 执行测试
        responses = list(self.service.AudioToText(request, context))
        
        # 验证结果
        assert len(responses) == 1
        assert responses[0].audio_to_text_response.segments[0].text == "来自数据的文本"
        
        # 验证调用参数
        mock_thread.transcribe_audio.assert_called_once_with(
            audio_path="",
            audio_data=test_audio_data,
            request_word_timestamps=False
        )

    @patch('subtitle_service.TranscriptThread')
    def test_generate_subtitles(self, mock_transcript_thread_class):
        """测试 GenerateSubtitles 方法"""
        # 设置 mock
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        
        mock_progress_data = [
            {
                "stage_name": "字幕生成完成",
                "percentage": 100,
                "message": "字幕生成成功",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "generate_subtitles_response": {
                        "srt_content": "1\n00:00:00,000 --> 00:00:03,000\n测试字幕\n\n",
                        "ass_content": "[Script Info]\nTitle: Test\n\n[V4+ Styles]\n..."
                    }
                }
            }
        ]
        
        mock_thread.generate_subtitles_from_text.return_value = iter(mock_progress_data)
        
        # 创建请求
        request = subtitler_pb2.GenerateSubtitlesRequest(
            text="测试文本",
            audio_path=self.temp_audio_path
        )
        context = Mock()
        
        # 执行测试
        responses = list(self.service.GenerateSubtitles(request, context))
        
        # 验证结果
        assert len(responses) == 1
        assert responses[0].HasField("generate_subtitles_response")
        assert "测试字幕" in responses[0].generate_subtitles_response.srt_content
        assert "[Script Info]" in responses[0].generate_subtitles_response.ass_content
        
        # 验证调用
        mock_thread.generate_subtitles_from_text.assert_called_once_with(
            text="测试文本", audio_path=self.temp_audio_path
        )

    @patch('subtitle_service.TranscriptThread')
    def test_translate_subtitles(self, mock_transcript_thread_class):
        """测试 TranslateSubtitles 方法"""
        # 设置 mock
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        
        mock_progress_data = [
            {
                "stage_name": "翻译完成",
                "percentage": 100,
                "message": "翻译成功",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "translate_subtitles_response": {
                        "translated_subtitle_content": "1\n00:00:00,000 --> 00:00:03,000\nTranslated text\n\n"
                    }
                }
            }
        ]
        
        mock_thread.translate_subtitle_content.return_value = iter(mock_progress_data)
        
        # 创建请求
        request = subtitler_pb2.TranslateSubtitlesRequest(
            subtitle_content="1\n00:00:00,000 --> 00:00:03,000\n原始文本\n\n",
            target_language="english"
        )
        context = Mock()
        
        # 执行测试
        responses = list(self.service.TranslateSubtitles(request, context))
        
        # 验证结果
        assert len(responses) == 1
        assert responses[0].HasField("translate_subtitles_response")
        assert "Translated text" in responses[0].translate_subtitles_response.translated_subtitle_content
        
        # 验证调用
        mock_thread.translate_subtitle_content.assert_called_once_with(
            subtitle_content="1\n00:00:00,000 --> 00:00:03,000\n原始文本\n\n",
            target_language="english",
            input_format="srt"
        )

    @patch('subtitle_service.TranscriptThread')
    def test_process_video_to_translated_subtitles(self, mock_transcript_thread_class):
        """测试 ProcessVideoToTranslatedSubtitles 完整流程"""
        # 设置 mock
        mock_thread = Mock()
        mock_transcript_thread_class.return_value = mock_thread
        
        mock_progress_data = [
            {
                "stage_name": "视频处理中",
                "percentage": 25,
                "message": "提取音频...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "语音识别中",
                "percentage": 50,
                "message": "转录音频...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "翻译字幕中",
                "percentage": 75,
                "message": "翻译中...",
                "is_error": False,
                "error_message": ""
            },
            {
                "stage_name": "处理完成",
                "percentage": 100,
                "message": "翻译字幕生成完成",
                "is_error": False,
                "error_message": "",
                "final_result": {
                    "process_video_to_translated_subtitles_response": {
                        "translated_subtitle_content": "1\n00:00:00,000 --> 00:00:03,000\nTranslated subtitle\n\n"
                    }
                }
            }
        ]
        
        mock_thread.process_video_to_translated_subtitles.return_value = iter(mock_progress_data)
        
        # 创建请求
        request = subtitler_pb2.ProcessVideoToTranslatedSubtitlesRequest(
            video_path=self.temp_video_path,
            target_language="english"
        )
        context = Mock()
        
        # 执行测试
        responses = list(self.service.ProcessVideoToTranslatedSubtitles(request, context))
        
        # 验证结果
        assert len(responses) == 4
        assert responses[0].percentage == 25
        assert responses[1].percentage == 50
        assert responses[2].percentage == 75
        assert responses[3].percentage == 100
        assert responses[3].HasField("process_video_to_translated_subtitles_response")
        
        # 验证调用
        mock_thread.process_video_to_translated_subtitles.assert_called_once_with(
            video_path_str=self.temp_video_path,
            target_language="english"
            # request_word_timestamps 参数已从调用中移除
        )

    def test_save_subtitle(self):
        """测试 SaveSubtitle 方法"""
        # 创建测试字幕片段
        segments = [
            subtitler_pb2.SubtitleSegment(
                start_time=0,
                end_time=3000,
                original_text="测试原文",
                translated_text="Test translation"
            )
        ]
        
        # 模拟 processor 的返回值
        mock_file_package = {
            'file_path': '/path/to/test.srt',
            'file_name': 'test.srt',
            'file_data': b'subtitle data',
            'file_size': 13,
            'saved_to_default': True,
            'format': 'srt',
            'layout': '原文在上',
            'original_filename_or_title': 'test'
        }
        
        with patch.object(self.service.processor, 'process_save_subtitle', return_value=mock_file_package):
            # 创建请求
            request = subtitler_pb2.SaveSubtitleRequest(
                segments=segments,
                file_name="test",
                format="srt",
                layout="原文在上",
                auto_save_to_default=True
            )
            context = Mock()
            
            # 执行测试
            response = self.service.SaveSubtitle(request, context)
            
            # 验证结果
            assert response.file_path == '/path/to/test.srt'
            assert response.file_name == 'test.srt'
            assert response.file_data == b'subtitle data'
            assert response.file_size == 13
            assert response.saved_to_default == True

    def test_save_subtitle_failure(self):
        """测试 SaveSubtitle 失败场景"""
        # 模拟 processor 返回 None (失败)
        with patch.object(self.service.processor, 'process_save_subtitle', return_value=None):
            # 创建请求
            request = subtitler_pb2.SaveSubtitleRequest(
                segments=[],
                file_name="test",
                format="srt",
                layout="原文在上"
            )
            # 创建一个 mock gRPC context
            context = Mock(spec=grpc.ServicerContext)
            
            # 执行测试
            # SaveSubtitle 在失败时会调用 context.abort()，这不会在服务器端引发标准的 Python 异常
            # 而是会终止 RPC。我们需要检查 context.abort 是否被调用。
            self.service.SaveSubtitle(request, context)

            # 验证 context.abort 被以正确的参数调用
            context.abort.assert_called_once_with(
                grpc.StatusCode.INTERNAL, "未能处理并保存字幕。"
            )

    def test_batch_save_subtitle(self):
        """测试 BatchSaveSubtitle 方法"""
        # 创建测试字幕片段
        segments = [
            subtitler_pb2.SubtitleSegment(
                start_time=0,
                end_time=3000,
                original_text="测试原文",
                translated_text="Test translation"
            )
        ]
        
        # 模拟 processor 的返回值
        mock_file_packages = [
            {
                'file_path': '/path/to/test-srt.srt',
                'file_name': 'test-srt.srt',
                'file_data': b'srt data',
                'file_size': 8,
                'saved_to_default': False,
                'format': 'srt',
                'layout': '原文在上',
                'original_filename_or_title': 'test'
            },
            {
                'file_path': '/path/to/test-ass.ass',
                'file_name': 'test-ass.ass',
                'file_data': b'ass data',
                'file_size': 8,
                'saved_to_default': False,
                'format': 'ass',
                'layout': '原文在上',
                'original_filename_or_title': 'test'
            }
        ]
        
        with patch.object(self.service.processor, 'process_batch_save_subtitles', return_value=mock_file_packages):
            # 创建请求
            request = subtitler_pb2.BatchSaveSubtitleRequest(
                segments=segments,
                file_name_prefix="test",
                formats=["srt", "ass"],
                layouts=["原文在上"],
                # include_original 和 include_translated 字段在 BatchSaveSubtitleRequest 中不存在，已移除
                # 这些逻辑现在由 content_sources 和 translation_requested 字段处理 (如果需要)
                # 根据当前的 proto 定义，我们可能需要调整测试用例以反映这一点，
                # 但首先移除无效字段。
                auto_save_to_default=False
            )
            context = Mock()
            
            # 执行测试
            response = self.service.BatchSaveSubtitle(request, context)
            
            # 验证结果
            assert len(response.files) == 2
            assert response.files[0].file_name == 'test-srt.srt'
            assert response.files[1].file_name == 'test-ass.ass'

    def test_batch_save_subtitle_empty_result(self):
        """测试 BatchSaveSubtitle 空结果场景"""
        with patch.object(self.service.processor, 'process_batch_save_subtitles', return_value=[]):
            # 创建请求
            request = subtitler_pb2.BatchSaveSubtitleRequest(
                segments=[],
                file_name_prefix="test",
                formats=["srt"],
                layouts=["原文在上"]
            )
            context = Mock()
            
            # 执行测试
            response = self.service.BatchSaveSubtitle(request, context)
            
            # 验证结果
            assert len(response.files) == 0

    def test_handle_streaming_request_with_progress_error(self):
        """测试流式请求处理中的进度错误"""
        def error_generator():
            yield {
                "stage_name": "测试阶段",
                "percentage": 50,
                "message": "正常进度",
                "is_error": False,
                "error_message": ""
            }
            yield {
                "stage_name": "错误阶段",
                "percentage": 0,
                "message": "发生错误",
                "is_error": True,
                "error_message": "测试错误"
            }
            # 这个不应该被处理，因为前面有错误
            yield {
                "stage_name": "不会到达",
                "percentage": 100,
                "message": "不会被处理",
                "is_error": False,
                "error_message": ""
            }
        
        # 执行测试
        responses = list(self.service._handle_streaming_request(error_generator, "测试错误处理"))
        
        # 验证结果：应该只有两个响应，第三个被错误中断
        assert len(responses) == 2
        assert responses[0].is_error == False
        assert responses[1].is_error == True
        assert responses[1].error_message == "测试错误"

    def test_handle_streaming_request_with_exception(self):
        """测试流式请求处理中的异常"""
        def exception_generator():
            yield {
                "stage_name": "正常阶段",
                "percentage": 50,
                "message": "正常进度",
                "is_error": False,
                "error_message": ""
            }
            raise ValueError("模拟异常")
        
        # 执行测试
        responses = list(self.service._handle_streaming_request(exception_generator, "异常测试"))
        
        # 验证结果：一个正常响应，一个异常响应
        assert len(responses) == 2
        assert responses[0].is_error == False
        assert responses[1].is_error == True
        assert "模拟异常" in responses[1].error_message
        assert responses[1].stage_name == "异常测试"


if __name__ == "__main__":
    pytest.main([__file__]) 
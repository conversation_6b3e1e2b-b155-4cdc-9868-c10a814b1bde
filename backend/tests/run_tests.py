#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本。

提供多种测试运行选项：
- 运行所有测试
- 运行单元测试
- 运行集成测试
- 运行慢速测试
- 生成覆盖率报告
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

# 确保项目根目录在Python路径中
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


# 测试文件说明
TEST_FILE_DESCRIPTIONS = {
    "test_subtitle_service.py": {
        "name": "字幕服务单元测试",
        "desc": "测试 gRPC 服务层 SubtitleServiceImpl 的各个 RPC 方法，确保它们正确调用依赖、处理参数和响应，并管理 gRPC 上下文。"
    },
    "test_subtitle_processor.py": {
        "name": "字幕处理器单元测试",
        "desc": "单元测试 SubtitleProcessor 类，验证其在字幕生成、优化、翻译等核心处理步骤中的逻辑正确性。"
    },
    "test_transcript_thread.py": {
        "name": "转录线程单元测试",
        "desc": "测试 TranscriptThread 类，确保其能够正确地管理字幕处理流程，包括启动、停止、状态更新和错误处理。"
    },
    "test_integration.py": {
        "name": "组件间协作集成测试",
        "desc": "进行集成测试，验证 SubtitleServiceImpl, SubtitleProcessor, 和 TranscriptThread 等主要组件间的交互是否按预期工作。"
    },
    "test_media_integration.py": {
        "name": "媒体处理集成测试",
        "desc": "进行涉及实际媒体文件处理的集成测试，确保整个字幕处理流程在真实场景下的正确性和鲁棒性。"
    }
}

# 测试标记说明
TEST_MARKER_DESCRIPTIONS = {
    "unit": "单元测试",
    "integration": "集成测试",
    "slow": "慢速测试",
    "fast": "快速测试 (排除慢速测试)",
    "media": "媒体处理相关测试"
}

def run_command(cmd, description):
    """运行命令并处理结果"""
    exec_description = description
    detailed_desc = ""
    test_file = None
    marker = None

    # 从命令中提取测试文件和标记
    for i, part in enumerate(cmd):
        if part.endswith(".py") and part.startswith("test_"):
            test_file = os.path.basename(part)
        elif part == "-m" and i + 1 < len(cmd):
            marker = cmd[i+1]

    if test_file and test_file in TEST_FILE_DESCRIPTIONS:
        file_info = TEST_FILE_DESCRIPTIONS[test_file]
        exec_description = file_info["name"]
        if marker and marker in TEST_MARKER_DESCRIPTIONS:
            exec_description += f" ({test_file}, 标记: {marker})"
            detailed_desc = file_info["desc"] + f" (通过标记 '{marker}' 筛选)"
        elif marker: # 未知标记
            exec_description += f" ({test_file}, 标记: {marker})"
            detailed_desc = file_info["desc"] + f" (通过未知标记 '{marker}' 筛选)"
        else:
            exec_description += f" ({test_file})"
            detailed_desc = file_info["desc"]
    elif marker and marker in TEST_MARKER_DESCRIPTIONS:
        exec_description = f"{TEST_MARKER_DESCRIPTIONS[marker]}"
        detailed_desc = f"运行所有标记为 '{marker}' 的测试。"
    elif marker: # 未知标记
        exec_description = f"标记为 '{marker}' 的测试"
        detailed_desc = f"运行所有标记为 '{marker}' 的测试。"

    print(f"\n{'='*60}")
    print(f"正在执行: {exec_description}")
    if detailed_desc:
        print(f"说明: {detailed_desc}")
    print(f"命令: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            cmd,
            check=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent  # 确保命令在脚本所在目录执行
        )
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        print(f"✅ {description} 成功完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        print(f"返回代码: {e.returncode}")
        return False


def main():
    parser = argparse.ArgumentParser(description="运行字幕服务测试")
    parser.add_argument(
        '--type', 
        choices=['all', 'unit', 'integration', 'slow', 'fast'],
        default='all',
        help='测试类型'
    )
    parser.add_argument(
        '--coverage', 
        action='store_true',
        help='生成覆盖率报告'
    )
    parser.add_argument(
        '--verbose', '-v',
        action='store_true',
        help='详细输出'
    )
    parser.add_argument(
        '--parallel', '-n',
        type=int,
        help='并行运行测试的进程数'
    )
    parser.add_argument(
        '--file',
        help='运行特定的测试文件'
    )
    parser.add_argument(
        '--test',
        help='运行特定的测试方法'
    )
    parser.add_argument(
        '--failfast', '-x',
        action='store_true',
        help='遇到第一个失败后停止'
    )
    parser.add_argument(
        '--lf',
        action='store_true',
        help='只运行上次失败的测试'
    )
    
    args = parser.parse_args()
    
    # 切换到tests目录
    os.chdir(Path(__file__).parent)
    
    # 构建pytest命令
    cmd = ['python', '-m', 'pytest']
    
    # 添加详细输出
    if args.verbose:
        cmd.append('-v')
    
    # 添加并行运行
    if args.parallel:
        cmd.extend(['-n', str(args.parallel)])
    
    # 添加failfast
    if args.failfast:
        cmd.append('-x')
    
    # 添加last failed
    if args.lf:
        cmd.append('--lf')
    
    # 添加覆盖率
    if args.coverage:
        cmd.extend([
            '--cov=../subtitle_service',
            '--cov=../subtitle_processor', 
            '--cov=../subtitle',
            '--cov-report=html',
            '--cov-report=term-missing'
        ])
    
    # 根据测试类型添加标记
    description_prefix = "即将执行"
    if args.type == 'unit':
        cmd.extend(['-m', 'unit'])
        print(f"\n{description_prefix}所有单元测试...")
    elif args.type == 'integration':
        cmd.extend(['-m', 'integration'])
        print(f"\n{description_prefix}所有集成测试...")
    elif args.type == 'slow':
        cmd.extend(['-m', 'slow'])
        print(f"\n{description_prefix}所有标记为 'slow' 的测试...")
    elif args.type == 'fast':
        cmd.extend(['-m', 'not slow']) # 'not slow' 意味着排除慢速测试，即快速测试
        print(f"\n{description_prefix}所有快速测试 (排除标记为 'slow' 的测试)...")
    elif args.type == 'all' and not args.file and not args.test:
        print(f"\n{description_prefix}所有测试...")
    
    # 添加特定文件或测试
    if args.file:
        cmd.append(args.file)
    elif args.test:
        cmd.extend(['-k', args.test])
    else:
        # 默认运行所有测试文件
        cmd.append('.')
    
    # 运行测试
    success = run_command(cmd, f"运行{args.type}测试")
    
    if args.coverage and success:
        print(f"\n📊 覆盖率报告已生成到 htmlcov/index.html")
    
    # 运行lint检查
    if args.type in ['all', 'fast'] and not args.file and not args.test:
        print(f"\n🔍 运行代码质量检查...")
        
        # 检查导入
        import_cmd = ['python', '-c', 
                     'import subtitle_service; import subtitle_processor; import subtitle.transcriptThread; print("✅ 所有模块导入成功")']
        run_command(import_cmd, "模块导入检查")
    
    return 0 if success else 1


def run_specific_tests():
    """运行特定的测试组合"""
    print("🧪 字幕服务测试套件")
    print("=" * 60)
    
    test_scenarios = [
        {
            'name': '快速单元测试',
            'cmd': ['python', '-m', 'pytest', '-v', '-m', 'not slow', 'test_subtitle_service.py', 'test_subtitle_processor.py'],
            'description': '运行核心业务逻辑的快速单元测试',
            'detailed_desc': '此场景将运行核心业务逻辑的快速单元测试，主要覆盖 subtitle_service.py (gRPC 服务层) 和 subtitle_processor.py (核心处理逻辑) 中的功能。'
        },
        {
            'name': '流程集成测试',
            'cmd': ['python', '-m', 'pytest', '-v', 'test_transcript_thread.py'],
            'description': '测试字幕处理流程编排',
            'detailed_desc': '此场景测试 TranscriptThread 类，确保其能够正确地管理字幕处理流程，包括启动、停止、状态更新和错误处理。'
        },
        {
            'name': '端到端集成测试',
            'cmd': ['python', '-m', 'pytest', '-v', '-m', 'integration', 'test_integration.py'],
            'description': '测试组件间的协作',
            'detailed_desc': '此场景进行集成测试，验证 SubtitleServiceImpl, SubtitleProcessor, 和 TranscriptThread 等主要组件间的交互是否按预期工作。'
        },
        {
            'name': '媒体处理测试',
            'cmd': ['python', '-m', 'pytest', '-v', '-m', 'media', 'test_media_integration.py'],
            'description': '使用实际媒体文件测试字幕处理',
            'detailed_desc': '此场景进行涉及实际媒体文件处理的集成测试，确保整个字幕处理流程在真实场景下的正确性和鲁棒性。'
        }
    ]
    
    results = []
    for scenario in test_scenarios:
        print(f"\n场景: {scenario['name']}")
        if 'detailed_desc' in scenario:
            print(f"详细说明: {scenario['detailed_desc']}")
        # run_command 现在会处理大部分的打印逻辑，我们传递原始的 description
        success = run_command(scenario['cmd'], scenario['description'])
        results.append((scenario['name'], success))
    
    # 汇总结果
    print(f"\n📋 测试结果汇总")
    print("=" * 60)
    for name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{name}: {status}")
    
    all_passed = all(success for _, success in results)
    print(f"\n🎯 总体结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
    
    return 0 if all_passed else 1


if __name__ == '__main__':
    if len(sys.argv) == 1:
        # 没有参数时运行特定测试场景
        sys.exit(run_specific_tests())
    else:
        # 有参数时使用argparse
        sys.exit(main())

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

env:
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '18'

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    name: Code Quality Check
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 black isort mypy pylint
        pip install -r backend/requirements.txt
    
    - name: Code formatting check (Black)
      run: |
        black --check backend/
    
    - name: Import sorting check (isort)
      run: |
        isort --check-only backend/
    
    - name: Linting (flake8)
      run: |
        flake8 backend/ --max-line-length=100 --ignore=E203,W503
    
    - name: Type checking (mypy)
      run: |
        mypy backend/ --ignore-missing-imports
    
    - name: Advanced linting (pylint)
      run: |
        pylint backend/ --disable=C0114,C0115,C0116 --max-line-length=100

  # 单元测试
  unit-tests:
    runs-on: ubuntu-latest
    name: Unit Tests
    needs: code-quality
    
    strategy:
      matrix:
        python-version: ['3.8', '3.9', '3.10', '3.11']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y ffmpeg redis-server
    
    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-cov pytest-asyncio
        pip install -r backend/requirements.txt
    
    - name: Start Redis
      run: |
        sudo systemctl start redis-server
    
    - name: Run unit tests
      run: |
        cd backend
        python -m pytest tests/ -v --cov=subtitle --cov-report=xml --cov-report=html
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: backend/coverage.xml
        flags: unittests
        name: codecov-umbrella

  # 集成测试
  integration-tests:
    runs-on: ubuntu-latest
    name: Integration Tests
    needs: unit-tests
    
    services:
      redis:
        image: redis:6-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y ffmpeg
        python -m pip install --upgrade pip
        pip install -r backend/requirements.txt
    
    - name: Run integration tests
      env:
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        CACHE_BACKEND: redis
        LOG_LEVEL: DEBUG
      run: |
        cd backend
        python tests/test_framework.py

  # 性能测试
  performance-tests:
    runs-on: ubuntu-latest
    name: Performance Tests
    needs: integration-tests
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/requirements.txt
        pip install locust
    
    - name: Run performance tests
      run: |
        cd backend
        # 启动服务
        python -m subtitle.main &
        sleep 10
        
        # 运行性能测试
        locust -f tests/performance_test.py --headless -u 10 -r 2 -t 30s --host=http://localhost:50051

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    name: Security Scan
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run Bandit security scan
      run: |
        bandit -r backend/ -f json -o bandit-report.json
    
    - name: Check dependencies for vulnerabilities
      run: |
        safety check --json --output safety-report.json
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json

  # Docker构建
  docker-build:
    runs-on: ubuntu-latest
    name: Docker Build
    needs: [unit-tests, integration-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Login to Docker Hub
      if: github.event_name != 'pull_request'
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: subtitle-backend
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
    
    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: backend/
        push: ${{ github.event_name != 'pull_request' }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 文档生成
  documentation:
    runs-on: ubuntu-latest
    name: Generate Documentation
    needs: code-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r backend/requirements.txt
        pip install sphinx sphinx-rtd-theme
    
    - name: Generate API documentation
      run: |
        cd backend
        python -c "
        from subtitle.api_documentation import create_default_api_docs
        doc_generator = create_default_api_docs()
        doc_generator.save_documentation('docs')
        print('✅ API文档生成完成')
        "
    
    - name: Build Sphinx documentation
      run: |
        cd backend/docs
        sphinx-build -b html . _build/html
    
    - name: Deploy to GitHub Pages
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: backend/docs/_build/html

  # 部署到开发环境
  deploy-dev:
    runs-on: ubuntu-latest
    name: Deploy to Development
    needs: [docker-build, performance-tests]
    if: github.ref == 'refs/heads/develop'
    
    environment:
      name: development
      url: https://dev-api.example.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Deploy to development
      run: |
        echo "🚀 Deploying to development environment..."
        # 这里添加实际的部署脚本
        # kubectl apply -f k8s/dev/
        echo "✅ Development deployment completed"

  # 部署到生产环境
  deploy-prod:
    runs-on: ubuntu-latest
    name: Deploy to Production
    needs: [docker-build, performance-tests, security-scan]
    if: github.event_name == 'release'
    
    environment:
      name: production
      url: https://api.example.com
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Deploy to production
      run: |
        echo "🚀 Deploying to production environment..."
        # 这里添加实际的部署脚本
        # kubectl apply -f k8s/prod/
        echo "✅ Production deployment completed"
    
    - name: Create deployment notification
      run: |
        echo "📢 Production deployment notification sent"

  # 发布通知
  notify:
    runs-on: ubuntu-latest
    name: Notify
    needs: [deploy-dev, deploy-prod]
    if: always()
    
    steps:
    - name: Notify Slack
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

# 工作流程总结
# 1. 代码质量检查 (格式化、类型检查、静态分析)
# 2. 多版本Python单元测试
# 3. 集成测试 (包含Redis服务)
# 4. 性能测试
# 5. 安全扫描
# 6. Docker镜像构建
# 7. 文档生成和部署
# 8. 环境部署 (开发/生产)
# 9. 通知

syntax = "proto3";

package monkeyfx.api.v1.subtitler;

import "google/protobuf/struct.proto"; // Added for ass_style_options
// import "common/common.proto"; // No direct usage of common.BaseResponse in current messages

option java_multiple_files = true;
option java_package = "com.monkeyfx.grpc.api.v1.subtitler";
option java_outer_classname = "SubtitlerProto";
option go_package = "github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/subtitler";

message ProgressUpdate {
  string stage_name = 1;
  int32 percentage = 2;
  string message = 3;
  bool is_error = 4;
  string error_message = 5;
  oneof final_result {
    VideoToAudioResponse video_to_audio_response = 6;
    AudioToTextResponse audio_to_text_response = 7;
    GenerateSubtitlesResponse generate_subtitles_response = 8;
    TranslateSubtitlesResponse translate_subtitles_response = 9;
    ProcessVideoToTranslatedSubtitlesResponse process_video_to_translated_subtitles_response = 10;
  }
}

service Subtitler {
  rpc VideoToAudio(VideoToAudioRequest) returns (stream ProgressUpdate);
  rpc AudioToText(AudioToTextRequest) returns (stream ProgressUpdate);
  rpc GenerateSubtitles(GenerateSubtitlesRequest) returns (stream ProgressUpdate);
  rpc TranslateSubtitles(TranslateSubtitlesRequest) returns (stream ProgressUpdate);
  rpc ProcessVideoToTranslatedSubtitles(ProcessVideoToTranslatedSubtitlesRequest) returns (stream ProgressUpdate);
  
  // 新增：字幕保存服务
  rpc SaveSubtitle(SaveSubtitleRequest) returns (SaveSubtitleResponse);
  rpc BatchSaveSubtitle(BatchSaveSubtitleRequest) returns (BatchSaveSubtitleResponse);
}

message VideoToAudioRequest {
  string video_path = 1;
}
message VideoToAudioResponse {
  string audio_path = 1;
  bytes audio_data = 2;
}
message AudioToTextRequest {
  string audio_path = 1;
  bytes audio_data = 2;
  bool request_word_timestamps = 3; // 新增字段，用于请求词级别时间戳
}

message TimestampedTextSegment {
  string text = 1;
  int64 start_time_ms = 2; // 开始时间，单位毫秒
  int64 end_time_ms = 3;   // 结束时间，单位毫秒
}

message AudioToTextResponse {
  repeated TimestampedTextSegment segments = 1; // 修改为时间戳文本段列表
}
message GenerateSubtitlesRequest {
  string text = 1;
  string audio_path = 2;
}
message GenerateSubtitlesResponse {
  string srt_content = 1;
  string ass_content = 2;
}
message TranslateSubtitlesRequest {
  string subtitle_content = 1;
  string target_language = 2;
}
message TranslateSubtitlesResponse {
  string translated_subtitle_content = 1;
}
message ProcessVideoToTranslatedSubtitlesRequest {
  string video_path = 1;
  string target_language = 2;
}
message ProcessVideoToTranslatedSubtitlesResponse {
  string translated_subtitle_content = 1;
}

// 新增：字幕保存请求
message SaveSubtitleRequest {
  string subtitle_content = 1;           // 字幕内容
  string format = 2;                     // 输出格式：srt, ass, txt, json, vtt
  string layout = 3;                     // 布局：原文在上, 译文在上, 仅原文, 仅译文, 双语并排
  string file_name = 4;                  // 文件名（不含扩展名）
  string original_content = 5;           // 原文内容（用于双语字幕）
  string translated_content = 6;         // 翻译内容（用于双语字幕）
  repeated SubtitleSegment segments = 7; // 字幕片段（带时间戳）
  bool auto_save_to_default = 8;         // 是否自动保存到默认目录
  optional google.protobuf.Struct ass_style_options = 9; // ASS字幕样式选项
}

message SaveSubtitleResponse {
  string file_path = 1;         // 保存的文件路径
  bytes file_data = 2;          // 文件内容的二进制数据（用于下载）
  string file_name = 3;         // 最终文件名
  int64 file_size = 4;          // 文件大小（字节）
  bool saved_to_default = 5;    // 是否已保存到默认目录
  string format = 6;                     // 文件格式 (e.g., "srt", "txt")
  string layout = 7;                     // 字幕布局 (e.g., "仅原文", "原文在上")
  string content_source = 8;             // 内容来源 (e.g., "原始字幕", "翻译字幕")
  string original_filename_or_title = 9; // 用于生成描述性文件名的基础标题或原始文件名
}

// 字幕片段定义
message SubtitleSegment {
  int32 start_time = 1;         // 开始时间（毫秒）
  int32 end_time = 2;           // 结束时间（毫秒）
  string original_text = 3;     // 原文
  string translated_text = 4;   // 翻译文本
}

// 批量保存字幕请求
message BatchSaveSubtitleRequest {
  repeated string formats = 1;           // 输出格式列表：srt, ass, txt, json, vtt
  repeated string layouts = 2;           // 布局列表：原文在上, 译文在上, 仅原文, 仅译文, 双语并排
  repeated string content_sources = 3;   // 内容来源列表：current-tab, transcript, segments, srt-ass, translation
  string file_name_prefix = 4;           // 文件名前缀
  string original_content = 5;           // 原文内容（用于双语字幕）
  string translated_content = 6;         // 翻译内容（用于双语字幕）
  repeated SubtitleSegment segments = 7; // 字幕片段（带时间戳）
  bool auto_save_to_default = 8;         // 是否自动保存到默认目录
  bool translation_requested = 9;        // 用户是否请求了翻译
  optional google.protobuf.Struct ass_style_options = 10; // ASS字幕样式选项
}

message BatchSaveSubtitleResponse {
  repeated SaveSubtitleResponse files = 1;  // 生成的文件列表
}
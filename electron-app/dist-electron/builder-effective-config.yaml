directories:
  output: dist-electron
  buildResources: build
appId: com.monkeyfx.subtitle-processor
productName: 字幕处理工具
files:
  - filter:
      - dist/**/*
      - main.js
      - preload.js
      - package.json
      - src/main-process/**/*
      - src/js/**/*
      - src/node-grpc-client.js
      - node_modules/**/*
      - '!**/*.proto'
      - '!**/*.py'
      - '!**/*.go'
      - '!**/*.java'
      - '!**/*.git*'
      - '!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}'
      - '!**/node_modules/*/{test,__tests__,tests,powered-test,example,examples}'
      - '!**/node_modules/*.d.ts'
      - '!**/node_modules/.bin'
      - '!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}'
      - '!**/._*'
      - '!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}'
      - '!*.env'
      - '!**/.env'
      - '!src/vue/**/*'
      - '!src/css/**/*'
      - '!dist-electron/**/*'
extraResources:
  - from: ../dist
    to: dist
    filter:
      - '**/*'
  - from: ../api-protos
    to: api-protos
    filter:
      - '**/*.proto'
  - from: ../config
    to: config
    filter:
      - '**/*'
mac:
  category: public.app-category.productivity
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
win:
  target:
    - target: nsis
      arch:
        - x64
    - target: zip
      arch:
        - x64
linux:
  target:
    - target: AppImage
      arch:
        - x64
    - target: tar.gz
      arch:
        - x64
electronVersion: 29.4.6

"use strict";
const fs_early = require("fs");
const path_early = require("path");
const util_early = require("util");
let earlyLogPath;
try {
  const { app: app_early } = require("electron");
  const userDataDir = app_early.getPath("userData");
  if (!fs_early.existsSync(userDataDir)) {
    fs_early.mkdirSync(userDataDir, { recursive: true });
  }
  earlyLogPath = path_early.join(userDataDir, "electron_main_early.log");
} catch (e) {
  const fallbackDir = path_early.join(process.env.HOME || process.cwd(), ".electron-app-logs");
  if (!fs_early.existsSync(fallbackDir)) {
    fs_early.mkdirSync(fallbackDir, { recursive: true });
  }
  earlyLogPath = path_early.join(fallbackDir, "electron_main_early.log");
}
const logFile = fs_early.createWriteStream(earlyLogPath, { flags: "a" });
const logStdout = process.stdout;
console.log = function(...args) {
  logFile.write(util_early.format.apply(null, args) + "\n");
  logStdout.write(util_early.format.apply(null, args) + "\n");
};
console.error = console.log;
console.warn = console.log;
console.log(`--- Electron Main Process Early Log Started: ${(/* @__PURE__ */ new Date()).toISOString()} ---`);
console.log(`--- Early log file path: ${earlyLogPath} ---`);
const { app } = require("electron");
const path = require("path");
const fs = require("fs");
const envPath = path.join(__dirname, ".env");
if (fs.existsSync(envPath)) {
  require("dotenv").config({ path: envPath });
} else {
  let configEnvPath;
  if (process.resourcesPath) {
    configEnvPath = path.join(process.resourcesPath, "config", ".env");
  }
  if (configEnvPath && fs.existsSync(configEnvPath)) {
    require("dotenv").config({ path: configEnvPath });
  } else {
    if (!process.env.OPENAI_API_KEY) ;
  }
}
if (!process.defaultApp) {
  const poetryBinPath = "/Users/<USER>/Library/Caches/pypoetry/virtualenvs/my-python-project-C15Z2L7S-py3.12/bin";
  const commonPaths = [
    poetryBinPath,
    // Add Poetry venv bin path to the front
    "/opt/homebrew/bin",
    "/usr/local/bin",
    "/usr/bin",
    "/bin",
    "/usr/sbin",
    "/sbin"
  ];
  const currentPath = process.env.PATH || "";
  const existingPathParts = currentPath.split(":").filter((p) => p.length > 0);
  const pathParts = [.../* @__PURE__ */ new Set([poetryBinPath, ...commonPaths, ...existingPathParts])];
  process.env.PATH = pathParts.join(":");
  console.log(`[Main] Fixed PATH for packaged app (with Poetry venv): ${process.env.PATH}`);
  console.log(`[Main] Checking if poetry python3.12 exists at ${path_early.join(poetryBinPath, "python3.12")}: ${fs_early.existsSync(path_early.join(poetryBinPath, "python3.12"))}`);
}
const appLifecycle = require(path.join(__dirname, "src", "main-process", "app-lifecycle"));
appLifecycle.initializeAppLifecycleEvents();

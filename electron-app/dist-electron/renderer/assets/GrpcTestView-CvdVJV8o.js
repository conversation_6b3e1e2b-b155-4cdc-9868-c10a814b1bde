import{d as H,_ as P,c as p,a as l,b as n,e,F as x,f as k,t as g,r as _,w as A,g as b,o as V,D as G,j as y,h as q,z as J,k as z,q as E,u as I,B as R}from"./index-CjTvhpco.js";const C=H("grpcTest",{state:()=>({selectedService:null,selectedMethod:null,requestPayload:"{}",responsePayload:null,isLoading:!1,error:null,availableServices:[],backendServices:{python:{status:"Unknown",bgColor:"bg-gray-500",clientStatus:{ready:!1,message:"Initializing..."}},go:{status:"Unknown",bgColor:"bg-gray-500",clientStatus:{ready:!1,message:"Initializing..."}},java:{status:"Unknown",bgColor:"bg-gray-500",clientStatus:{ready:!1,message:"Initializing..."},detailedStatus:"unknown"}},logs:[],maxLogs:1e3}),getters:{isRequestReady:t=>!!t.selectedService&&!!t.selectedMethod&&t.requestPayload.trim()!=="",currentMethods:t=>{if(!t.selectedService)return[];const s=t.availableServices.find(a=>a.name===t.selectedService);return s?s.methods:[]},pythonStatus:t=>t.backendServices.python,goStatus:t=>t.backendServices.go,javaStatus:t=>t.backendServices.java,recentLogs:t=>t.logs.slice(-100)},actions:{async fetchAvailableServices(){this.isLoading=!0,this.error=null;try{console.log("[GrpcTestStore] Fetching available gRPC services...");const t=await window.electronAPI.invoke("grpc:get-available-services");this.availableServices=t,console.log("[GrpcTestStore] Available gRPC services loaded:",t),this.selectedService&&!t.find(s=>s.name===this.selectedService)&&(this.selectedService=null,this.selectedMethod=null)}catch(t){this.error=`Failed to fetch available services: ${t.message}`,console.error("[GrpcTestStore] Error fetching available services:",t),this.availableServices=[]}finally{this.isLoading=!1}},setSelectedService(t){this.selectedService=t,this.selectedMethod=null,this.responsePayload=null,this.error=null},setSelectedMethod(t){this.selectedMethod=t,this.responsePayload=null,this.error=null},setRequestPayload(t){this.requestPayload=t},async sendGrpcRequest(){if(!this.isRequestReady){this.error="Service, method, and request payload must be provided.";return}this.isLoading=!0,this.responsePayload=null,this.error=null;try{let t;try{t=JSON.parse(this.requestPayload)}catch(v){this.error=`Invalid JSON payload: ${v.message}`,this.isLoading=!1;return}const s="grpc-test:invoke-method",a={service:this.selectedService,method:this.selectedMethod,payload:t};console.log(`Sending gRPC request to channel: ${s} with payload:`,a);const i=await window.electronAPI.invoke(s,a);i.error?(this.error=`gRPC Error: ${i.error.message||JSON.stringify(i.error)}`,console.error("gRPC call resulted in error:",i.error)):(this.responsePayload=JSON.stringify(i.data,null,2),console.log("gRPC call successful, response:",i.data))}catch(t){this.error=`IPC Error: ${t.message}`,console.error("Error during IPC call for gRPC request:",t)}finally{this.isLoading=!1}},resetForm(){this.requestPayload="{}",this.responsePayload=null,this.isLoading=!1,this.error=null},applyRequestTemplate(t){if(t){if(t.serviceName&&this.availableServices.some(s=>s.name===t.serviceName))this.selectedService=t.serviceName;else{const s=this.availableServices.find(a=>a.methods.includes(t.methodName)&&a.name===this.selectedService);if(s)this.selectedService=s.name;else{const a=this.availableServices.find(i=>i.methods.includes(t.methodName));a&&(this.selectedService=a.name)}}t.methodName&&this.currentMethods.includes(t.methodName)?this.selectedMethod=t.methodName:this.currentMethods.length>0&&!t.methodName?this.selectedMethod=null:this.selectedMethod=null,this.requestPayload=t.requestPayload||"{}",this.responsePayload=null,this.error=null}},updateServiceStatus(t,s,a){this.backendServices[t]&&(this.backendServices[t].status=s,this.backendServices[t].bgColor=a)},updateClientStatus(t,s){this.backendServices[t]&&(this.backendServices[t].clientStatus=s)},updateJavaDetailedStatus(t){this.backendServices.java.detailedStatus=t},addLog(t){this.logs.push({id:Date.now()+Math.random(),timestamp:new Date().toLocaleTimeString(),message:t}),this.logs.length>this.maxLogs&&(this.logs=this.logs.slice(-this.maxLogs))},clearLogs(){this.logs=[]},async refreshBackendStatus(){console.log("[GrpcTestStore] Refreshing backend status...");const t=["python","go","java"];let s=!0;for(const a of t)try{const i={name:`${a}TestUser`},v=await window.electronAPI.invoke("grpc-test:invoke-method",{service:"Greeter",method:"SayHello",payload:i});v.data?(this.updateServiceStatus(a,"Running","bg-green-500"),this.updateClientStatus(a,{ready:!0,message:"Connected"}),console.log(`[GrpcTestStore] ${a} service is running`)):(s=!1,this.updateServiceStatus(a,"Connection Failed","bg-red-500"),this.updateClientStatus(a,{ready:!1,message:"Connection Failed"}),console.log(`[GrpcTestStore] ${a} service connection failed:`,v.error))}catch(i){s=!1,this.updateServiceStatus(a,"Error","bg-red-500"),this.updateClientStatus(a,{ready:!1,message:`Error: ${i.message}`}),console.error(`[GrpcTestStore] Error testing ${a} service:`,i)}return s},initializeBackendStatusListeners(){var t,s,a,i,v,w,d,u;console.log("[GrpcTestStore] Initializing backend status listeners..."),(t=window.electronAPI)!=null&&t.onServiceStatusUpdate&&window.electronAPI.onServiceStatusUpdate(r=>{console.log("[GrpcTestStore] Received service status update:",r),this.updateServiceStatus(r.service,r.status,r.bgColor)}),this.refreshBackendStatus(),(s=window.electronAPI)!=null&&s.onPythonClientStatus&&window.electronAPI.onPythonClientStatus(r=>{this.updateClientStatus("python",r)}),(a=window.electronAPI)!=null&&a.onGoClientStatus&&window.electronAPI.onGoClientStatus(r=>{this.updateClientStatus("go",r)}),(i=window.electronAPI)!=null&&i.onJavaClientStatus&&window.electronAPI.onJavaClientStatus(r=>{this.updateClientStatus("java",r)}),(v=window.electronAPI)!=null&&v.onJavaStatus&&window.electronAPI.onJavaStatus(r=>{this.updateJavaDetailedStatus(r)}),(w=window.electronAPI)!=null&&w.onMainProcessLog&&window.electronAPI.onMainProcessLog(r=>{this.addLog(`[Main]: ${r}`)}),(d=window.electronAPI)!=null&&d.onUnifiedLog&&window.electronAPI.onUnifiedLog(r=>{this.addLog(r)}),(u=window.electronAPI)!=null&&u.onJavaLog&&window.electronAPI.onJavaLog(r=>{this.addLog(`[Java]: ${r}`)}),console.log("[GrpcTestStore] Backend status listeners initialized")}}}),F={class:"space-y-2 p-3 border rounded-md shadow-sm bg-white"},D={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},O=["value"],U=["value"],W=["value","disabled"],Q=["value"],X={__name:"ServiceSelector",setup(t){const s=C(),a=p(()=>s.selectedService),i=p(()=>s.availableServices),v=p(()=>s.selectedMethod),w=p(()=>s.currentMethods),d=r=>{s.setSelectedService(r)},u=r=>{s.setSelectedMethod(r)};return(r,c)=>(n(),l("div",F,[c[6]||(c[6]=e("h3",{class:"text-md font-medium text-gray-700"},"Service and Method",-1)),e("div",D,[e("div",null,[c[3]||(c[3]=e("label",{for:"service-select",class:"block text-sm font-medium text-gray-700 mb-1"},"Service:",-1)),e("select",{id:"service-select",value:a.value,onChange:c[0]||(c[0]=f=>d(f.target.value)),class:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"},[c[2]||(c[2]=e("option",{value:null,disabled:""},"-- Select a Service --",-1)),(n(!0),l(x,null,k(i.value,f=>(n(),l("option",{key:f.name,value:f.name},g(f.name),9,U))),128))],40,O)]),e("div",null,[c[5]||(c[5]=e("label",{for:"method-select",class:"block text-sm font-medium text-gray-700 mb-1"},"Method:",-1)),e("select",{id:"method-select",value:v.value,onChange:c[1]||(c[1]=f=>u(f.target.value)),disabled:!a.value||w.value.length===0,class:"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm disabled:bg-gray-50"},[c[4]||(c[4]=e("option",{value:null,disabled:""},"-- Select a Method --",-1)),(n(!0),l(x,null,k(w.value,f=>(n(),l("option",{key:f,value:f},g(f),9,Q))),128))],40,W)])])]))}},Y=P(X,[["__scopeId","data-v-8a203678"]]),Z={class:"space-y-2 p-3 border rounded-md shadow-sm bg-white"},K=["value"],ee={key:0,class:"text-xs text-red-600 mt-1"},te={__name:"RequestEditor",setup(t){const s=C(),a=_(null),i=p(()=>s.requestPayload),v=d=>{s.setRequestPayload(d),w(d)},w=d=>{try{JSON.parse(d),a.value=null}catch(u){d.trim()!==""?a.value=`Invalid JSON: ${u.message}`:a.value=null}};return A(i,d=>{w(d)},{immediate:!0}),(d,u)=>(n(),l("div",Z,[u[1]||(u[1]=e("h3",{class:"text-md font-medium text-gray-700"},"Request Payload (JSON)",-1)),u[2]||(u[2]=e("label",{for:"request-payload",class:"sr-only"},"Request Payload (JSON)",-1)),e("textarea",{id:"request-payload",rows:"8",class:"block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono",value:i.value,onInput:u[0]||(u[0]=r=>v(r.target.value)),placeholder:"Enter JSON payload here..."},null,40,K),a.value?(n(),l("p",ee,g(a.value),1)):b("",!0)]))}},se=P(te,[["__scopeId","data-v-ac0b7bdc"]]),oe={class:"space-y-2 p-3 border rounded-md shadow-sm bg-white"},ae={key:0,class:"text-sm text-blue-600"},re={key:1,class:"p-2 border border-red-300 bg-red-50 rounded-md"},le={class:"text-xs text-red-600 whitespace-pre-wrap break-all"},ne={key:2,class:"p-2 border border-green-300 bg-green-50 rounded-md"},ie={class:"text-xs text-green-600 whitespace-pre-wrap break-all"},de={key:3,class:"text-sm text-gray-500"},ce={__name:"ResponseDisplay",setup(t){const s=C(),a=p(()=>s.isLoading),i=p(()=>s.error),v=p(()=>s.responsePayload);return(w,d)=>(n(),l("div",oe,[d[2]||(d[2]=e("h3",{class:"text-md font-medium text-gray-700"},"Response / Error",-1)),a.value?(n(),l("div",ae," Loading... ")):b("",!0),i.value?(n(),l("div",re,[d[0]||(d[0]=e("h4",{class:"text-sm font-semibold text-red-700"},"Error:",-1)),e("pre",le,g(i.value),1)])):b("",!0),v.value&&!i.value?(n(),l("div",ne,[d[1]||(d[1]=e("h4",{class:"text-sm font-semibold text-green-700"},"Response:",-1)),e("pre",ie,g(v.value),1)])):b("",!0),!a.value&&!i.value&&!v.value?(n(),l("div",de," No response yet. Send a request to see the output. ")):b("",!0)]))}},ue=P(ce,[["__scopeId","data-v-d2956888"]]),ve={class:"grpc-test-container"},me={class:"card mb-8 animate-fade-in"},he={class:"flex items-center justify-between mb-6"},ge={class:"flex items-center space-x-3"},pe={key:0,class:"flex items-center text-sm text-gray-600"},fe={key:1,class:"flex items-center text-sm text-green-600"},we=["disabled"],be={key:0,class:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},ye={key:1,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-4 h-4 mr-2"},Se={class:"grid grid-cols-1 md:grid-cols-3 gap-6"},xe={class:"flex items-center mb-3"},ke={class:"font-semibold text-gray-800 capitalize"},_e={class:"text-sm text-gray-600 mb-2"},Pe={class:"card mb-6 animate-slide-up"},Ce={key:0,disabled:""},Me={key:1,disabled:""},Re=["label"],Le=["value"],qe={key:0,class:"p-4 mb-6 text-sm text-red-700 bg-red-50 border border-red-200 rounded-xl",role:"alert"},Ie={class:"flex items-center"},Ae={class:"space-y-6"},$e={class:"card animate-slide-up"},Ne={class:"card animate-slide-up"},Te={class:"card animate-slide-up"},je={class:"flex flex-wrap gap-4 mt-8"},Be=["disabled"],He={key:0,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 mr-2 animate-spin"},Ve={key:1,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 mr-2"},Ge={class:"card mt-8 animate-fade-in"},Je={class:"bg-gray-900 p-4 rounded-xl max-h-60 overflow-y-auto font-mono text-sm"},ze={key:0,class:"text-gray-500 text-center py-8"},Ee={class:"text-blue-400"},Fe={class:"text-gray-100"},De=3e3,Oe={__name:"GrpcTestView",setup(t){const s=C(),a=p(()=>s.isLoading),i=p(()=>s.isRequestReady),v=p(()=>s.backendServices),w=p(()=>s.recentLogs),d=p(()=>s.availableServices.filter(h=>h.templates&&h.templates.length>0)),u=_(!1),r=_(null),c=_(null),f=()=>{if(c.value){const h={...c.value};let o=null;for(const m of s.availableServices)if(m.templates&&m.templates.includes(c.value)){o=m.name;break}o&&(h.serviceName=o),s.applyRequestTemplate(h)}};A(()=>[s.selectedService,s.selectedMethod,s.requestPayload],()=>{if(c.value){const h=s.availableServices.find(o=>o.name===s.selectedService);!h||!h.templates||h.templates.some(o=>o.templateName===c.value.templateName&&o.methodName===s.selectedMethod&&o.requestPayload===s.requestPayload)}});const $=()=>{s.sendGrpcRequest()},N=()=>{s.resetForm()},T=()=>{s.clearLogs()},L=p(()=>{const h=v.value;return Object.values(h).every(o=>o.clientStatus.ready&&(o.status==="Running"||o.status.includes("Running")))}),j=()=>{r.value&&clearInterval(r.value),r.value=setInterval(async()=>{L.value?(console.log("[GrpcTestView] All services ready, stopping auto-refresh"),M()):(console.log("[GrpcTestView] Auto-refreshing backend status..."),await s.refreshBackendStatus()&&(console.log("[GrpcTestView] All services ready, stopping auto-refresh"),M()))},De)},M=()=>{r.value&&(clearInterval(r.value),r.value=null)},B=async()=>{u.value=!0;try{await s.refreshBackendStatus()}catch(h){console.error("Failed to refresh backend status:",h)}finally{u.value=!1}};return V(async()=>{s.initializeBackendStatusListeners(),await s.fetchAvailableServices(),j()}),G(()=>{M()}),(h,o)=>(n(),l("div",ve,[o[16]||(o[16]=e("div",{class:"mb-8"},[e("h1",{class:"text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2"},"gRPC 测试面板"),e("p",{class:"text-center text-gray-600 text-sm"},"多后端服务通信测试工具")],-1)),e("div",me,[e("div",he,[o[5]||(o[5]=e("h3",{class:"text-xl font-semibold text-gray-800 flex items-center"},[e("div",{class:"w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center mr-3"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 text-white"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 14.25h13.5m-13.5 0a3 3 0 01-3-3V6a3 3 0 013-3h13.5a3 3 0 013 3v5.25a3 3 0 01-3 3m-16.5 0a3 3 0 013 3v5.25a3 3 0 013 3h13.5a3 3 0 013-3v-5.25a3 3 0 01-3-3"})])]),y(" 后端服务状态 ")],-1)),e("div",ge,[r.value?(n(),l("div",pe,o[1]||(o[1]=[e("div",{class:"w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2"},null,-1),e("span",null,"自动检测中...",-1)]))):L.value?(n(),l("div",fe,o[2]||(o[2]=[e("div",{class:"w-2 h-2 bg-green-500 rounded-full mr-2"},null,-1),e("span",null,"所有服务正常",-1)]))):b("",!0),e("button",{onClick:B,disabled:u.value,class:"px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center"},[u.value?(n(),l("svg",be,o[3]||(o[3]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(n(),l("svg",ye,o[4]||(o[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"},null,-1)]))),y(" "+g(u.value?"刷新中...":"刷新状态"),1)],8,we)])]),e("div",Se,[(n(!0),l(x,null,k(v.value,(m,S)=>(n(),l("div",{key:S,class:"bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-200"},[e("div",xe,[e("div",{class:q(["w-3 h-3 rounded-full mr-3 animate-pulse",m.bgColor])},null,2),e("span",ke,g(S)+" 后端",1)]),e("p",_e,g(m.status),1),e("p",{class:q(["text-sm font-medium",m.clientStatus.ready?"text-green-600":"text-red-600"])}," 客户端: "+g(m.clientStatus.message),3)]))),128))])]),e("div",Pe,[o[7]||(o[7]=J('<h3 class="text-lg font-semibold mb-4 text-gray-800 flex items-center" data-v-09ee2433><div class="w-6 h-6 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center mr-3" data-v-09ee2433><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 text-white" data-v-09ee2433><path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c0 .621-.504 1.125-1.125 1.125H18a2.25 2.25 0 01-2.25-2.25V9.375c0-.621.504-1.125 1.125-1.125H20.25a2.25 2.25 0 012.25 2.25v11.25a2.25 2.25 0 01-2.25 2.25H21M8.25 8.25l6.75-6.75" data-v-09ee2433></path></svg></div> 快速测试模板 </h3><label for="template-selector" class="block text-sm font-medium text-gray-700 mb-2" data-v-09ee2433>选择预设模板:</label>',2)),z(e("select",{id:"template-selector","onUpdate:modelValue":o[0]||(o[0]=m=>c.value=m),onChange:f,class:"input-field"},[o[6]||(o[6]=e("option",{value:null,disabled:""},"-- 选择模板 --",-1)),a.value&&d.value.length===0?(n(),l("option",Ce,"加载模板中...")):b("",!0),!a.value&&d.value.length===0?(n(),l("option",Me,"暂无可用模板")):b("",!0),(n(!0),l(x,null,k(d.value,m=>(n(),l("optgroup",{key:m.name,label:m.name+" 模板"},[(n(!0),l(x,null,k(m.templates,S=>(n(),l("option",{key:S.templateName,value:S},g(S.templateName),9,Le))),128))],8,Re))),128))],544),[[E,c.value]])]),I(s).error?(n(),l("div",qe,[e("div",Ie,[o[8]||(o[8]=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 mr-2"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z"})],-1)),o[9]||(o[9]=e("span",{class:"font-medium"},"错误:",-1)),y(" "+g(I(s).error),1)])])):b("",!0),e("div",Ae,[e("div",$e,[R(Y)]),e("div",Ne,[R(se)]),e("div",Te,[R(ue)])]),e("div",je,[e("button",{onClick:$,disabled:!i.value||a.value,class:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center"},[a.value?(n(),l("svg",He,o[10]||(o[10]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"},null,-1)]))):(n(),l("svg",Ve,o[11]||(o[11]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.768 59.768 0 013.27 20.876L5.999 12zm0 0h7.5"},null,-1)]))),y(" "+g(a.value?"发送中...":"发送请求"),1)],8,Be),e("button",{onClick:N,class:"btn-secondary flex items-center"},o[12]||(o[12]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 mr-2"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99"})],-1),y(" 重置表单 ")]))]),e("div",Ge,[e("div",{class:"flex justify-between items-center mb-6"},[o[14]||(o[14]=e("h3",{class:"text-xl font-semibold text-gray-800 flex items-center"},[e("div",{class:"w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mr-3"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 text-white"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c0 .621-.504 1.125-1.125 1.125H18a2.25 2.25 0 01-2.25-2.25V9.375c0-.621.504-1.125 1.125-1.125H20.25a2.25 2.25 0 012.25 2.25v11.25a2.25 2.25 0 01-2.25 2.25H21M8.25 8.25l6.75-6.75"})])]),y(" 后端日志 ")],-1)),e("button",{onClick:T,class:"px-4 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center"},o[13]||(o[13]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-4 h-4 mr-2"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0"})],-1),y(" 清空日志 ")]))]),e("div",Je,[w.value.length===0?(n(),l("div",ze,o[15]||(o[15]=[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1",stroke:"currentColor",class:"w-12 h-12 mx-auto mb-2 opacity-50"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c0 .621-.504 1.125-1.125 1.125H18a2.25 2.25 0 01-2.25-2.25V9.375c0-.621.504-1.125 1.125-1.125H20.25a2.25 2.25 0 012.25 2.25v11.25a2.25 2.25 0 01-2.25 2.25H21M8.25 8.25l6.75-6.75"})],-1),y(" 暂无日志记录... ")]))):b("",!0),(n(!0),l(x,null,k(w.value,m=>(n(),l("div",{key:m.id,class:"text-xs text-gray-300 mb-2 hover:bg-gray-800 p-2 rounded transition-colors duration-200"},[e("span",Ee,"["+g(m.timestamp)+"]",1),e("span",Fe,g(m.message),1)]))),128))])])]))}},We=P(Oe,[["__scopeId","data-v-09ee2433"]]);export{We as default};

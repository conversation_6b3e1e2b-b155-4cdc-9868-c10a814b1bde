import{d as De,_ as ee,r as A,c as p,w as ce,n as ve,o as Ae,a,b as l,e,t as c,F as K,f as Q,g as f,h as H,i as pe,j as ue,k as W,v as Ve,l as Te,m as Y,p as we,u as J,q as se,s as le,x as he,y as We,z as ye,A as Ee,B as Pe,C as qe,K as He}from"./index-CjTvhpco.js";typeof window.workflowIntermediateResults>"u"&&(window.workflowIntermediateResults={transcript:null,segments:null,srt:null,translation:null});function Le(s){(typeof s!="number"||isNaN(s)||s<0)&&(s=0);const t=new Date(s),o=String(t.getUTCHours()).padStart(2,"0"),n=String(t.getUTCMinutes()).padStart(2,"0"),r=String(t.getUTCSeconds()).padStart(2,"0"),i=String(t.getUTCMilliseconds()).padStart(3,"0");return`${o}:${n}:${r},${i}`}function Ue(s){if(!Array.isArray(s)||s.length===0)return"";let t="";return s.forEach((o,n)=>{let r,i;o.startTimeMs!==void 0&&o.endTimeMs!==void 0?(r=o.startTimeMs,i=o.endTimeMs):o.start_time_ms!==void 0&&o.end_time_ms!==void 0?(r=o.start_time_ms,i=o.end_time_ms):o.start!==void 0&&o.end!==void 0?(r=Math.round(o.start*1e3),i=Math.round(o.end*1e3)):(r=0,i=0);const g=Le(r),u=Le(i),y=o.text||"";t+=`${n+1}
`,t+=`${g} --> ${u}
`,t+=`${y}

`}),t.trim()}console.log("subtitler/utils.js loaded");const Fe=()=>typeof window<"u"&&window.subtitlerClient?(console.log("[SubtitlerStore] subtitlerClient found and available"),window.subtitlerClient):(console.warn("[SubtitlerStore] subtitlerClient not available. Debugging info:"),console.warn("- window object exists:",typeof window<"u"),console.warn("- window.subtitlerClient exists:",typeof window<"u"&&!!window.subtitlerClient),console.warn("- window.electronAPI exists:",typeof window<"u"&&!!window.electronAPI),typeof window<"u"&&console.warn("- Available window properties:",Object.keys(window).filter(s=>s.includes("subtitler")||s.includes("electron")||s.includes("grpc"))),null),ae=De("subtitler",{state:()=>({uploadedFile:null,currentStep:1,isLoading:!1,progressUpdates:[],usePreviousAudioForTranscription:!0,useTranscriptionForEditing:!0,videoToAudioResult:null,videoToAudioProgress:"",videoToAudioError:null,audioToTextResult:null,audioToTextProgress:"",audioToTextError:null,generatedSubtitles:null,generateSubtitlesProgress:"",optimizedSubtitles:null,optimizeSubtitlesProgress:"",editableSegments:null,selectedSegmentId:null,editedSubtitlesSaved:!1,translationSettings:{targetLanguage:"en",quality:"balanced",style:"formal",customPrompt:""},translatedSubtitles:null,translationProgress:"",translationProgressPercent:0,exportFilename:"",exportFormat:"srt",exportLayout:"原文在上",exportLayouts:["原文在上"],exportContentSource:"editable_segments",exportContentSources:["editable_segments"],exportAutoSaveToDefault:!1,lastExportPath:null,exportResults:[],oneClickOperationInProgress:!1,oneClickOperationError:null,oneClickWorkflowType:"vid_to_srt_trans",oneClickTargetLanguage:"zh-CN",oneClickExportFormat:"srt",oneClickExportLayout:"原文在上",oneClickExportLayouts:["原文在上"],activeResultTab:"transcript"}),getters:{srtPreviewFromEditableSegments:s=>s.editableSegments&&s.editableSegments.length>0?Ue(s.editableSegments):"",srtPreviewFromOriginalSegments:s=>s.audioToTextResult&&s.audioToTextResult.segments&&s.audioToTextResult.segments.length>0?Ue(s.audioToTextResult.segments):"",getSegmentById:s=>t=>s.editableSegments&&s.editableSegments.find(o=>o.id===t)||null,isReadyForNextStep:s=>{switch(s.currentStep){case 1:return s.uploadedFile!==null;case 2:return s.videoToAudioResult!==null;case 3:return s.audioToTextResult!==null;case 4:return s.generatedSubtitles!==null&&s.generatedSubtitles.length>0;case 5:return s.optimizedSubtitles!==null||s.generatedSubtitles!==null;case 6:return s.editableSegments!==null&&s.editableSegments.length>0;case 7:return!0;case 8:return s.translatedSubtitles!==null;case 9:return!0;default:return!1}},getUploadedFileName:s=>s.uploadedFile?s.uploadedFile.name:"",getUploadedFileSize:s=>s.uploadedFile?s.uploadedFile.size:"",getUploadedFileType:s=>s.uploadedFile?s.uploadedFile.type:"",hasEditableSegments:s=>s.editableSegments&&s.editableSegments.length>0,selectedSegment:s=>!s.selectedSegmentId||!s.editableSegments?null:s.editableSegments.find(t=>t.id===s.selectedSegmentId)||null},actions:{setActiveResultTab(s){this.activeResultTab=s},setUploadedFile(s){s?(this.uploadedFile={name:s.name,path:s.path,type:s.type,size:s.size},this.currentStep=1,this.progressUpdates=[],this.videoToAudioResult=null,this.audioToTextResult=null,this.audioToTextProgress="",this.audioToTextError=null,this.editableSegments=null,this.selectedSegmentId=null,this.editedSubtitlesSaved=!1):this.uploadedFile=null},resetWorkflow(){this.uploadedFile=null,this.currentStep=1,this.isLoading=!1,this.progressUpdates=[],this.videoToAudioResult=null,this.audioToTextResult=null,this.audioToTextProgress="",this.audioToTextError=null,this.generatedSubtitles=null,this.generateSubtitlesProgress="",this.optimizedSubtitles=null,this.optimizeSubtitlesProgress="",this.editableSegments=null,this.selectedSegmentId=null,this.editedSubtitlesSaved=!1,this.translationSettings={targetLanguage:"en",quality:"balanced",style:"formal",customPrompt:""},this.translatedSubtitles=null,this.translationProgress="",this.translationProgressPercent=0,this.usePreviousAudioForTranscription=!0,this.useTranscriptionForEditing=!0,this.exportFilename="",this.exportFormat="srt",this.exportLayout="原文在上",this.exportContentSource="editable_segments",this.exportAutoSaveToDefault=!1,this.lastExportPath=null,console.log("Subtitler workflow reset.")},async processVideoToAudio(){if(!this.uploadedFile||!this.uploadedFile.path){this.videoToAudioError="No file uploaded or file path is missing.",console.error(this.videoToAudioError);return}this.isLoading=!0,this.videoToAudioError=null,this.videoToAudioProgress="Starting video to audio conversion...",this.progressUpdates=[this.videoToAudioProgress];try{const s=Fe();if(!s)if(typeof window<"u"&&window.electronAPI){console.log("[SubtitlerStore] Using electronAPI fallback for video to audio conversion");try{const i=await window.electronAPI.invoke("subtitler-full-workflow",{file_path:this.uploadedFile.path,workflow_type:"vid_to_audio",request_word_timestamps:!1});console.log("[SubtitlerStore] electronAPI result:",i);let g=null;if(i&&i.audio_path?g=i.audio_path:(i&&i.video_to_audio_response&&i.video_to_audio_response.audio_path||i&&i.final_result==="video_to_audio_response"&&i.video_to_audio_response)&&(g=i.video_to_audio_response.audio_path),g){this.videoToAudioResult=g,this.videoToAudioProgress="转换成功！",this.progressUpdates.push(`✅ 视频转音频成功：${g.split("/").pop()}`),this.currentStep=2,this.editedSubtitlesSaved=!1;return}else throw console.error("[SubtitlerStore] No audio path found in result:",i),new Error("No audio path received from conversion")}catch(i){throw console.error("[SubtitlerStore] electronAPI invoke failed:",i),new Error(`IPC call failed: ${i.message}`)}}else throw new Error("Subtitler client not available and electronAPI fallback failed. Please check backend connection.");const t={video_path:this.uploadedFile.path},o=s.videoToAudio(t);let n=null;const r=i=>{const g=i.getStageName?i.getStageName():"VideoToAudio",u=i.getPercentage?i.getPercentage():0,y=i.getMessage?i.getMessage():"Processing...",v=i.getIsError?i.getIsError():!1,m=i.getErrorMessage?i.getErrorMessage():"";if(v){this.videoToAudioError=m||"视频转音频过程中发生错误",this.videoToAudioProgress="转换失败",this.progressUpdates.push(`❌ 错误：${this.videoToAudioError}`);return}if(this.videoToAudioProgress=`${y} (${u}%)`,this.progressUpdates.push(`🔄 ${g}：${y} (${u}%)`),i.video_to_audio_response||i.getVideoToAudioResponse){const x=i.video_to_audio_response||i.getVideoToAudioResponse();if(x){const C=x.getAudioPath?x.getAudioPath():x.audio_path;C&&(n=C,this.videoToAudioResult=C,console.log("[VideoToAudio] Audio path extracted:",C))}}};await new Promise((i,g)=>{o.on("data",r),o.on("end",()=>{if(n)this.videoToAudioProgress="转换成功！",this.progressUpdates.push(`✅ 视频转音频完成：${n.split("/").pop()}`),this.currentStep=2,this.editedSubtitlesSaved=!1,i(n);else{const u=new Error("No audio path received from conversion");this.videoToAudioError=u.message,g(u)}}),o.on("error",u=>{console.error("Video to audio stream error:",u),this.videoToAudioError=u.message||"Stream error during conversion",this.videoToAudioProgress="Conversion failed.",this.progressUpdates.push(`Stream Error: ${this.videoToAudioError}`),g(u)})})}catch(s){console.error("Error in video to audio conversion:",s),this.videoToAudioError=s.message||"An unknown error occurred during video to audio conversion.",this.videoToAudioProgress="Conversion failed.",this.progressUpdates.push(`Error: ${this.videoToAudioError}`)}finally{this.isLoading=!1}},setCurrentStep(s){typeof s=="number"&&s>0?this.currentStep=s:console.warn(`Invalid step number: ${s}`)},async processAudioToText(){let s=null;if(this.usePreviousAudioForTranscription&&this.videoToAudioResult?s=this.videoToAudioResult:this.uploadedFile&&this.uploadedFile.path&&this.uploadedFile.type&&this.uploadedFile.type.startsWith("audio/")&&(s=this.uploadedFile.path),!s){this.audioToTextError='No audio source selected or available for transcription. Please ensure "Use previous audio" is checked if applicable, or upload an audio file.',console.error(this.audioToTextError);return}this.isLoading=!0,this.audioToTextError=null,this.audioToTextProgress="开始音频转文字处理...",this.currentStep!==3?this.progressUpdates=["🎤 开始音频转文字处理..."]:this.progressUpdates.unshift("🎤 开始音频转文字处理...");try{const t=Fe();if(!t)if(typeof window<"u"&&window.electronAPI){console.log("[SubtitlerStore] Using electronAPI fallback for audio to text conversion");try{const u=await window.electronAPI.invoke("subtitler-full-workflow",{file_path:s,workflow_type:"audio_to_text",request_word_timestamps:!0});console.log("[SubtitlerStore] audioToText electronAPI result:",u);let y=null,v=[];if(u&&u.transcript)y=u.transcript,v=u.segments||[];else if(u&&u.audio_to_text_response){const m=u.audio_to_text_response;y=m.transcript,v=m.segments||[]}else if(u&&u.final_result==="audio_to_text_response"&&u.audio_to_text_response){const m=u.audio_to_text_response;y=m.transcript,v=m.segments||[]}if(y||v.length>0){const m=v.map((C,b)=>({id:`segment-${b}`,text:C.text,startTimeMs:C.start_time_ms,endTimeMs:C.end_time_ms,start_time_ms:C.start_time_ms,end_time_ms:C.end_time_ms})),x={transcript:y||m.map(C=>C.text).join(" "),segments:m};this.audioToTextResult=x,this.audioToTextProgress="转录成功！",this.progressUpdates.push("✅ 音频转文字完成，识别到 "+m.length+" 个语音片段"),this.setCurrentStep(4),this.editedSubtitlesSaved=!1;return}else throw console.error("[SubtitlerStore] No transcription data found in result:",u),new Error("No transcription result received")}catch(u){throw console.error("[SubtitlerStore] electronAPI invoke failed for audioToText:",u),new Error(`IPC call failed: ${u.message}`)}}else throw new Error("Subtitler client not available and electronAPI fallback failed. Please check backend connection.");const n={audio_path:s,request_word_timestamps:!0,...{language:"auto",modelSize:"base"}},r=t.audioToText(n);let i=null;const g=u=>{const y=u.getStageName?u.getStageName():"AudioToText",v=u.getPercentage?u.getPercentage():0,m=u.getMessage?u.getMessage():"Processing...",x=u.getIsError?u.getIsError():!1,C=u.getErrorMessage?u.getErrorMessage():"";if(x){this.audioToTextError=C||"音频转文字过程中发生错误",this.audioToTextProgress="转录失败",this.progressUpdates.push(`❌ 错误：${this.audioToTextError}`);return}if(this.audioToTextProgress=`${m} (${v}%)`,this.progressUpdates.push(`🔄 ${this.getStageNameInChinese(y)}：${m} (${v}%)`),u.audio_to_text_response||u.getAudioToTextResponse){const b=u.audio_to_text_response||u.getAudioToTextResponse();if(b){const M=b.getTranscript?b.getTranscript():b.transcript,U=b.getSegmentsList?b.getSegmentsList():b.segments||[];if(M||U&&U.length>0){const w=U.map((E,q)=>({id:`segment-${q}`,text:E.getText?E.getText():E.text,startTimeMs:E.getStartTimeMs?E.getStartTimeMs():E.start_time_ms,endTimeMs:E.getEndTimeMs?E.getEndTimeMs():E.end_time_ms,start_time_ms:E.getStartTimeMs?E.getStartTimeMs():E.start_time_ms,end_time_ms:E.getEndTimeMs?E.getEndTimeMs():E.end_time_ms}));i={transcript:M||w.map(E=>E.text).join(" "),segments:w},this.audioToTextResult=i,console.log("[AudioToText] Transcription result:",i)}}}};await new Promise((u,y)=>{r.on("data",g),r.on("end",()=>{if(i)this.audioToTextProgress="转录成功！",this.progressUpdates.push(`✅ 音频转文字完成，共处理 ${i.segments.length} 个语音片段`),this.setCurrentStep(4),this.editedSubtitlesSaved=!1,u(i);else{const v=new Error("No transcription result received");this.audioToTextError=v.message,y(v)}}),r.on("error",v=>{console.error("Audio to text stream error:",v),this.audioToTextError=v.message||"转录过程中发生流错误",this.audioToTextProgress="转录失败",this.progressUpdates.push(`❌ 流错误：${this.audioToTextError}`),y(v)})})}catch(t){console.error("Error in audio to text conversion:",t),this.audioToTextError=t.message||"An unknown error occurred during audio to text conversion.",this.audioToTextProgress="Transcription failed.",this.progressUpdates.push(`Error: ${this.audioToTextError}`)}finally{this.isLoading=!1}},initializeEditableSegments(){this.useTranscriptionForEditing?this.audioToTextResult&&Array.isArray(this.audioToTextResult.segments)?(this.editableSegments=this.audioToTextResult.segments.map((s,t)=>({id:`segment-${Date.now()}-${t}`,startTimeMs:s.startTimeMs,endTimeMs:s.endTimeMs,text:s.text})),this.selectedSegmentId=null,console.log("Editable segments initialized from transcription:",this.editableSegments)):typeof this.audioToTextResult=="string"?(this.editableSegments=[{id:`segment-${Date.now()}-0`,startTimeMs:0,endTimeMs:0,text:this.audioToTextResult}],this.selectedSegmentId=null,console.log("Editable segments initialized from plain text transcription:",this.editableSegments)):(console.warn("Cannot initialize editable segments: audioToTextResult is not in expected format or is null, despite useTranscriptionForEditing being true."),this.editableSegments=[]):(this.editableSegments=[],console.log("Not using transcription for editing. Editable segments cleared.")),this.editedSubtitlesSaved=!1},setUsePreviousAudioForTranscription(s){this.usePreviousAudioForTranscription=!!s,s||(this.audioToTextResult=null,this.editableSegments=null)},setUseTranscriptionForEditing(s){this.useTranscriptionForEditing=!!s,s&&this.audioToTextResult?this.initializeEditableSegments():s||(this.editableSegments=null)},setSelectedSegmentId(s){this.selectedSegmentId=s},updateSegmentText({id:s,newText:t}){if(!this.editableSegments)return;const o=this.editableSegments.find(n=>n.id===s);o&&(o.text=t),this.editedSubtitlesSaved=!1},updateSegmentTime({id:s,newStartTimeMs:t,newEndTimeMs:o}){if(!this.editableSegments)return;const n=this.editableSegments.find(r=>r.id===s);n&&(typeof t=="number"&&t>=0&&(n.startTimeMs=t),typeof o=="number"&&o>=0&&o>=n.startTimeMs&&(n.endTimeMs=o)),this.editedSubtitlesSaved=!1},addSegment({afterId:s=null,newSegmentData:t={startTimeMs:0,endTimeMs:0,text:"New Segment"}}){this.editableSegments||(this.editableSegments=[]);const o=`segment-${Date.now()}-${this.editableSegments.length}`,n={...t,id:o};if(s===null)this.editableSegments.push(n);else{const r=this.editableSegments.findIndex(i=>i.id===s);r!==-1?this.editableSegments.splice(r+1,0,n):(this.editableSegments.push(n),console.warn(`addSegment: afterId ${s} not found. Segment added to end.`))}this.selectedSegmentId=o,this.editedSubtitlesSaved=!1},deleteSegment(s){this.editableSegments&&(this.editableSegments=this.editableSegments.filter(t=>t.id!==s),this.selectedSegmentId===s&&(this.selectedSegmentId=null),this.editedSubtitlesSaved=!1)},async saveEditedSubtitles(){if(!this.editableSegments||this.editableSegments.length===0){console.warn("No edited segments to save."),this.editedSubtitlesSaved=!1;return}this.isLoading=!0,this.editedSubtitlesSaved=!1;try{const s=this.editableSegments.map(o=>({id:o.id,text:o.text,startTimeMs:o.startTimeMs,endTimeMs:o.endTimeMs,start_time_ms:o.start_time_ms,end_time_ms:o.end_time_ms}));console.log("Saving edited subtitles:",s.length,"segments"),this.progressUpdates.push(`💾 正在保存 ${s.length} 个编辑后的字幕片段...`);const t=await window.electronAPI.invoke("subtitler:save-edited-segments",s);console.log("Save edited subtitles result:",t),this.progressUpdates.push("✅ 编辑后的字幕保存成功"),this.editedSubtitlesSaved=!0}catch(s){console.error("Error saving edited subtitles:",s),this.progressUpdates.push(`❌ 保存编辑字幕失败：${s.message}`),this.editedSubtitlesSaved=!1}finally{this.isLoading=!1}},setExportFilename(s){this.exportFilename=s},setExportFormat(s){this.exportFormat=s},setExportLayout(s){this.exportLayout=s},setExportLayouts(s){this.exportLayouts=s||[]},setExportContentSource(s){this.exportContentSource=s},setExportContentSources(s){this.exportContentSources=s||[]},setExportAutoSaveToDefault(s){this.exportAutoSaveToDefault=s},async exportSubtitles(s={}){let t,o=null;switch(this.exportContentSource){case"transcript_text":if(this.audioToTextResult&&typeof this.audioToTextResult.transcript=="string")o=this.audioToTextResult.transcript,t=[{id:"transcript",startTimeMs:0,endTimeMs:0,text:o}];else{console.warn("Transcript text selected for export, but no transcript available."),this.progressUpdates.push("❌ 错误：选择了转录文本导出，但未找到转录数据");return}break;case"transcript_segments":if(this.audioToTextResult&&Array.isArray(this.audioToTextResult.segments))t=JSON.parse(JSON.stringify(this.audioToTextResult.segments));else{console.warn("Transcript segments selected for export, but no segments available."),this.progressUpdates.push("❌ 错误：选择了转录片段导出，但未找到片段数据");return}break;case"translation_result":this.translatedSubtitles&&this.translatedSubtitles.length>0?(t=this.translatedSubtitles.map(r=>({id:r.id,text:r.originalText||r.text,translation:r.translatedText,startTimeMs:r.startTimeMs,endTimeMs:r.endTimeMs,start_time_ms:r.start_time_ms,end_time_ms:r.end_time_ms})),this.progressUpdates.push("📝 使用翻译字幕进行导出")):(console.warn("Translation result selected for export, but no translation available."),this.progressUpdates.push("⚠️ 警告：未找到翻译结果，使用当前编辑的字幕"),t=JSON.parse(JSON.stringify(this.editableSegments)));break;case"editable_segments":default:if(!this.editableSegments||this.editableSegments.length===0){console.warn("No segments to export."),this.progressUpdates.push("❌ 没有可导出的字幕");return}t=this.editableSegments.map(r=>({id:r.id,text:r.text,translation:r.translatedText||r.translation,startTimeMs:r.startTimeMs,endTimeMs:r.endTimeMs,start_time_ms:r.start_time_ms,end_time_ms:r.end_time_ms}));break}if(!t&&!o){console.warn("No content determined for export."),this.progressUpdates.push("Error: Could not determine content to export based on selection.");return}this.isLoading=!0,this.lastExportPath=null;const n={segments:t,rawContent:o,filename:s.filename||this.exportFilename||(this.uploadedFile?this.uploadedFile.name.split(".").slice(0,-1).join("."):"subtitles"),format:this.exportFormat,layout:this.exportLayout,contentSource:this.exportContentSource,autoSaveToDefault:this.exportAutoSaveToDefault};console.log("Exporting subtitles with payload:",n),this.progressUpdates.push(`Attempting to export as ${n.format.toUpperCase()}...`);try{try{this.progressUpdates.push("Using backend SaveSubtitle API...");const u={segments:t.map(v=>({start_time_ms:v.startTimeMs||v.start_time_ms||0,end_time_ms:v.endTimeMs||v.end_time_ms||0,original_text:v.text||"",translated_text:v.translation||v.translatedText||""})),format:this.exportFormat,layout:this.exportLayout,fileNamePrefix:n.filename,auto_save_to_default:this.exportAutoSaveToDefault},y=await window.electronAPI.invoke("subtitler-save-subtitle",u);if(y&&y.file_path){this.lastExportPath=y.file_path,this.progressUpdates.push(`✅ 字幕成功导出到：${y.file_path}`),console.log("Backend export successful:",y.file_path);return}else throw new Error("Backend SaveSubtitle API returned invalid response")}catch(g){console.warn("Backend SaveSubtitle failed, using fallback:",g),this.progressUpdates.push("Backend export failed, using local fallback")}const r=JSON.parse(JSON.stringify(n)),i=await window.electronAPI.invoke("subtitler:export-subtitles",r);i.filePath?(this.lastExportPath=i.filePath,this.progressUpdates.push(`✅ 字幕成功导出到：${i.filePath}`),console.log("Export successful:",i.filePath)):i.error?(console.error("Export failed:",i.error),this.progressUpdates.push(`❌ 导出失败：${i.error}`)):i.cancelled?(console.log("Export was cancelled by the user."),this.progressUpdates.push("⏹️ 用户取消了导出操作")):(console.warn("Export completed with an unknown status:",i),this.progressUpdates.push("❓ 导出状态未知"))}catch(r){console.error("Error during exportSubtitles action:",r),this.progressUpdates.push(`❌ 导出过程中发生错误：${r.message}`)}finally{this.isLoading=!1}},async performOneClickOperation(){if(!this.uploadedFile||!this.uploadedFile.path){this.oneClickOperationError="No file uploaded for one-click operation.",console.error(this.oneClickOperationError),this.progressUpdates.push("Error: No file uploaded.");return}this.oneClickOperationInProgress=!0,this.oneClickOperationError=null,this.isLoading=!0,this.lastExportPath=null,this.progressUpdates=[];try{this.progressUpdates.push("🚀 开始一键字幕生成...");const s=this.oneClickWorkflowType.includes("trans")||this.oneClickWorkflowType.includes("translated"),t=this.oneClickWorkflowType.startsWith("vid_");console.log("[OneClick] Workflow type:",this.oneClickWorkflowType,"needsTranslation:",s,"isVideoInput:",t),t&&(this.progressUpdates.push("📹 开始视频转音频..."),await this.processVideoToAudio(),this.progressUpdates.push("✅ 视频转音频完成")),this.progressUpdates.push("🎤 开始语音识别..."),await this.processAudioToText(),this.progressUpdates.push("✅ 语音识别完成"),this.progressUpdates.push("📝 开始生成字幕..."),await this.generateSubtitles(),this.progressUpdates.push("✅ 字幕生成完成"),this.progressUpdates.push("⚙️ 初始化可编辑字幕..."),this.initializeEditableSegments(),this.progressUpdates.push("✅ 可编辑字幕初始化完成"),s&&this.oneClickTargetLanguage&&(this.progressUpdates.push(`🌐 开始翻译到${this.getLanguageDisplayName(this.oneClickTargetLanguage)}...`),this.setTranslationSettings({targetLanguage:this.oneClickTargetLanguage,sourceLanguage:"en"}),await this.translateSubtitles(),this.progressUpdates.push("✅ 翻译完成")),this.progressUpdates.push("📤 开始导出字幕...");const o=this.oneClickExportLayouts&&this.oneClickExportLayouts.length>0?this.oneClickExportLayouts:[this.oneClickExportLayout];this.progressUpdates.push(`📋 将导出 ${o.length} 种布局: ${o.join(", ")}`),this.exportFormat=this.oneClickExportFormat,this.exportContentSource="editable_segments",this.exportAutoSaveToDefault=!0;const n=this.uploadedFile.name.split(".").slice(0,-1).join("."),r=s?`_${this.oneClickTargetLanguage}`:"",i=[];for(let g=0;g<o.length;g++){const u=o[g];this.progressUpdates.push(`📄 [${g+1}/${o.length}] 导出布局: ${u}`),this.exportLayout=u;const y=this._getLayoutSuffix(u);this.exportFilename=`${n}${r}_${y}`,await this.exportSubtitles(),this.lastExportPath?(i.push(this.lastExportPath),this.progressUpdates.push(`✅ [${g+1}/${o.length}] 完成: ${this.lastExportPath}`)):this.progressUpdates.push(`❌ [${g+1}/${o.length}] 失败: ${u}`)}if(i.length>0)this.progressUpdates.push(`✅ 一键操作成功完成！共导出 ${i.length} 个文件`),i.forEach((g,u)=>{this.progressUpdates.push(`📁 文件${u+1}: ${g}`)}),this.currentStep=9,this.progressUpdates.push("🎉 工作流程完成，已跳转到导出页面");else throw new Error("导出失败：所有布局都未能成功导出")}catch(s){console.error("Error in performOneClickOperation:",s),this.oneClickOperationError=s.message||"An unknown error occurred during the one-click operation.",this.progressUpdates.push(`❌ 错误：${this.oneClickOperationError}`)}finally{this.oneClickOperationInProgress=!1,this.isLoading=!1}},setOneClickWorkflowType(s){this.oneClickWorkflowType=s},setOneClickTargetLanguage(s){this.oneClickTargetLanguage=s},setOneClickExportFormat(s){this.oneClickExportFormat=s},setOneClickExportLayout(s){this.oneClickExportLayout=s},setOneClickExportLayouts(s){Array.isArray(s)&&s.length>0?(this.oneClickExportLayouts=s,this.oneClickExportLayout=s[0]):(this.oneClickExportLayouts=["原文在上"],this.oneClickExportLayout="原文在上")},async generateSubtitles(){if(!this.audioToTextResult||!this.audioToTextResult.segments)throw new Error("No transcription result available for subtitle generation");this.isLoading=!0,this.generateSubtitlesProgress="开始生成字幕...",this.progressUpdates.push("📝 开始生成字幕片段...");try{this.generateSubtitlesProgress="调用后端生成字幕...";const s=await window.electronAPI.invoke("subtitler-full-workflow",{req_text_content:this.audioToTextResult.transcript,workflow_type:"text_to_srt",request_word_timestamps:!1});s&&s.generate_subtitles_response&&s.generate_subtitles_response.segments?(this.generatedSubtitles=s.generate_subtitles_response.segments.map((t,o)=>({id:t.id||`generated-${o}`,text:t.text,startTimeMs:t.startTimeMs||t.start_time_ms,endTimeMs:t.endTimeMs||t.end_time_ms,start_time_ms:t.start_time_ms||t.startTimeMs,end_time_ms:t.end_time_ms||t.endTimeMs})),this.generateSubtitlesProgress="字幕生成完成！",this.progressUpdates.push(`✅ 成功从转录文本生成 ${this.generatedSubtitles.length} 个字幕片段`)):(this.generateSubtitlesProgress="使用备用句子生成方法...",this.generatedSubtitles=this._generateSentencesFromSegments(this.audioToTextResult.segments),this.progressUpdates.push(`🔄 使用备用方法生成 ${this.generatedSubtitles.length} 个句子`))}catch(s){console.error("Error generating subtitles:",s),this.generateSubtitlesProgress="后端失败，使用备用方法...";try{this.generatedSubtitles=this._generateSentencesFromSegments(this.audioToTextResult.segments),this.progressUpdates.push(`🔄 使用备用方法生成 ${this.generatedSubtitles.length} 个句子`)}catch(t){throw this.generateSubtitlesProgress="字幕生成失败",this.progressUpdates.push(`❌ 错误：${t.message}`),t}}finally{this.isLoading=!1}},_generateSentencesFromSegments(s){if(!s||s.length===0)return[];console.log(`[generateSentences] Processing ${s.length} segments`);const t=[];let o={words:[],startTime:null,endTime:null};const n=/[.!?。！？]/,r=12,i=5;for(let g=0;g<s.length;g++){const u=s[g],y=u.text.trim();if(!y)continue;o.words.length===0&&(o.startTime=u.startTimeMs||u.start_time_ms),o.words.push(y),o.endTime=u.endTimeMs||u.end_time_ms;const v=n.test(y),m=o.words.length>=i,x=o.words.length>=r,C=g<s.length-1&&(s[g+1].startTimeMs||s[g+1].start_time_ms)-o.endTime>1500,b=g===s.length-1;let M=!1,U="";if(v?(M=!0,U="punctuation"):x?(M=!0,U="too long"):m&&C?(M=!0,U="time gap"):b&&o.words.length>0&&(M=!0,U="last segment"),M){const w=o.words.join(" ").trim();w&&(console.log(`[generateSentences] Creating sentence ${t.length+1}: "${w.substring(0,50)}..." (${o.words.length} words, reason: ${U})`),t.push({id:`sentence-${t.length}`,text:w,startTimeMs:o.startTime,endTimeMs:o.endTime,start_time_ms:o.startTime,end_time_ms:o.endTime})),b||(o={words:[],startTime:null,endTime:null})}}return console.log(`[generateSentences] Generated ${t.length} sentences from ${s.length} segments`),t},async optimizeSubtitles(s={}){if(!this.generatedSubtitles||this.generatedSubtitles.length===0)throw new Error("No generated subtitles available for optimization");this.isLoading=!0,this.optimizeSubtitlesProgress="开始优化字幕...",this.progressUpdates.push("⚡ 开始优化字幕片段...");try{await new Promise(t=>setTimeout(t,2e3)),this.optimizedSubtitles=[...this.generatedSubtitles],this.optimizeSubtitlesProgress="字幕优化完成！",this.progressUpdates.push(`✅ 成功优化 ${this.optimizedSubtitles.length} 个字幕片段`),this.editableSegments=this.optimizedSubtitles.map(t=>({...t,id:t.id||`optimized-${Date.now()}-${Math.random()}`}))}catch(t){throw console.error("Error optimizing subtitles:",t),this.optimizeSubtitlesProgress="字幕优化失败",this.progressUpdates.push(`❌ 错误：${t.message}`),t}finally{this.isLoading=!1}},setEditableSegments(s){this.editableSegments=s},initializeEditableSegments(){this.generatedSubtitles&&this.generatedSubtitles.length>0?(this.editableSegments=this.generatedSubtitles.map(s=>({...s,id:s.id||`editable-${Date.now()}-${Math.random()}`})),console.log(`[initializeEditableSegments] Initialized ${this.editableSegments.length} editable segments from generated subtitles`)):this.audioToTextResult&&this.audioToTextResult.segments?(this.editableSegments=this.audioToTextResult.segments.map((s,t)=>({id:`editable-${t}`,text:s.text,startTimeMs:s.startTimeMs||s.start_time_ms,endTimeMs:s.endTimeMs||s.end_time_ms,start_time_ms:s.start_time_ms||s.startTimeMs,end_time_ms:s.end_time_ms||s.endTimeMs})),console.log(`[initializeEditableSegments] Initialized ${this.editableSegments.length} editable segments from transcription`)):(console.warn("[initializeEditableSegments] No subtitles or transcription available for initialization"),this.editableSegments=[])},setTranslationSettings(s){this.translationSettings={...this.translationSettings,...s}},async translateSubtitles(s=!1){if(!this.editableSegments||this.editableSegments.length===0)throw new Error("No editable segments available for translation");if(!(this.translatedSubtitles&&!s)){this.isLoading=!0,this.translationProgress="开始翻译...",this.translationProgressPercent=0,this.progressUpdates.push("🌐 开始翻译字幕到目标语言...");try{const t=this.editableSegments.length,o=[];try{this.translationProgress="调用后端翻译服务...";const n=this._segmentsToSrt(this.editableSegments);if(console.log("[TranslateSubtitles] SRT content length:",n?n.length:0),console.log("[TranslateSubtitles] SRT content preview:",n?n.substring(0,200):"null/empty"),!n||n.trim().length===0)throw new Error("无法生成SRT内容用于翻译，请检查字幕片段数据");const r=await window.electronAPI.invoke("subtitler-full-workflow",{req_text_content:n,target_language:this.translationSettings.targetLanguage,workflow_type:"text_to_translated_srt",request_word_timestamps:!1});if(console.log("[TranslateSubtitles] Backend result:",r),console.log("[TranslateSubtitles] Result keys:",r?Object.keys(r):"null"),r&&r.translate_subtitles_response&&r.translate_subtitles_response.translated_subtitle_content){const i=r.translate_subtitles_response.translated_subtitle_content,g=this._parseSrtToSegments(i,this.editableSegments);for(let u=0;u<g.length;u++)this.translationProgressPercent=Math.round((u+1)/g.length*100),this.translationProgress=`正在处理翻译片段 ${u+1}/${g.length}...`;this.translatedSubtitles=g,this.progressUpdates.push("🔄 使用后端翻译服务"),this._updateEditableSegmentsWithTranslation(g),this.translationProgress="翻译完成！",this.translationProgressPercent=100,this.progressUpdates.push(`✅ 成功翻译 ${g.length} 个片段到${this.getLanguageDisplayName(this.translationSettings.targetLanguage)}`);return}else throw new Error("Backend translation service returned invalid response")}catch(n){console.warn("Backend translation failed, using fallback:",n),this.translationProgress="后端失败，使用备用翻译...",this.progressUpdates.push("🔄 后端翻译失败，使用本地备用方法");for(let r=0;r<t;r++){const i=this.editableSegments[r];this.translationProgressPercent=Math.round(r/t*100),this.translationProgress=`正在翻译第 ${r+1} 个片段，共 ${t} 个...`,await new Promise(u=>setTimeout(u,50));let g=i.text;switch(this.translationSettings.targetLanguage){case"zh":g=this._mockTranslateToChineseSimplified(i.text);break;case"zh-TW":g=this._mockTranslateToChineseTraditional(i.text);break;case"ja":g=this._mockTranslateToJapanese(i.text);break;case"ko":g=this._mockTranslateToKorean(i.text);break;case"es":g=this._mockTranslateToSpanish(i.text);break;case"fr":g=this._mockTranslateToFrench(i.text);break;default:g=`[${this.translationSettings.targetLanguage}] ${i.text}`}o.push({...i,originalText:i.text,translatedText:g})}}this.translatedSubtitles=o,this._updateEditableSegmentsWithTranslation(o),this.translationProgress="翻译完成！",this.translationProgressPercent=100,this.progressUpdates.push(`✅ 成功翻译 ${t} 个片段到${this.getLanguageDisplayName(this.translationSettings.targetLanguage)}`)}catch(t){throw console.error("Error translating subtitles:",t),this.translationProgress="翻译失败",this.progressUpdates.push(`❌ 错误：${t.message}`),t}finally{this.isLoading=!1}}},_getLayoutSuffix(s){return{原文在上:"原文在上",译文在上:"译文在上",仅原文:"仅原文",仅译文:"仅译文",original_top:"原文在上",translation_top:"译文在上",original_only:"仅原文",translation_only:"仅译文"}[s]||s},_updateEditableSegmentsWithTranslation(s){if(!this.editableSegments||!s){console.warn("Cannot update editable segments: missing data");return}console.log("Updating editable segments with translation data..."),console.log("Original editableSegments count:",this.editableSegments.length),console.log("Translated segments count:",s.length),this.editableSegments=this.editableSegments.map((t,o)=>{let n=null;if(n=s.find(r=>r.startTimeMs===t.startTimeMs&&r.endTimeMs===t.endTimeMs),!n&&s[o]&&(n=s[o]),n){const r={...t,translatedText:n.translatedText||n.text,translation:n.translatedText||n.text,originalText:t.text};return console.log(`Updated segment ${o}:`,{original:t.text,translated:r.translatedText}),r}else return console.warn(`No translation found for segment ${o}:`,t.text),t}),console.log("Updated editableSegments with translation data"),this.progressUpdates.push("🔄 已将翻译数据合并到可编辑字幕中")},_mockTranslateToChineseSimplified(s){const t={"embeddings based retrieval":"基于嵌入的检索","very active area":"非常活跃的领域","area of research":"研究领域","looking forward to":"期待","should be aware of":"应该了解","for example":"例如","fine tune":"微调","embedding model":"嵌入模型","there's a lot of":"有很多","other techniques":"其他技术","you can":"你可以","directly using":"直接使用"},o={embeddings:"嵌入",based:"基于",retrieval:"检索",is:"是",still:"仍然",a:"一个",very:"非常",active:"活跃的",area:"领域",of:"的",research:"研究",and:"和","there's":"有",lot:"很多",other:"其他",techniques:"技术",that:"那些",you:"你",should:"应该",be:"是",aware:"了解",for:"为了",example:"例如",can:"可以",fine:"精细",tune:"调整",the:"这个",embedding:"嵌入",model:"模型",directly:"直接",using:"使用"};let n=s.toLowerCase();return Object.keys(t).forEach(r=>{const i=new RegExp(r.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");n=n.replace(i,t[r])}),n.split(" ").map(r=>{const i=r.toLowerCase().replace(/[.,!?]/g,""),g=r.match(/[.,!?]/g)||[];return(o[i]||r)+g.join("")}).join(" ")},_mockTranslateToChineseTraditional(s){const t=this._mockTranslateToChineseSimplified(s),o={嵌入:"嵌入",基于:"基於",检索:"檢索",领域:"領域",构建:"構建"};let n=t;return Object.keys(o).forEach(r=>{n=n.replace(new RegExp(r,"g"),o[r])}),n},_segmentsToSrt(s){return!s||s.length===0?(console.warn("[_segmentsToSrt] No segments provided"),""):(console.log("[_segmentsToSrt] Converting",s.length,"segments to SRT"),s.map((t,o)=>{const n=t.startTimeMs||t.start_time_ms||0,r=t.endTimeMs||t.end_time_ms||1e3,i=this._msToSrtTime(n),g=this._msToSrtTime(r),u=t.text||"";return u.trim()||console.warn("[_segmentsToSrt] Empty text for segment",o),`${o+1}
${i} --> ${g}
${u}
`}).join(`
`))},_msToSrtTime(s){const t=Math.floor(s/1e3),o=Math.floor(t/3600),n=Math.floor(t%3600/60),r=t%60,i=s%1e3;return`${o.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")},${i.toString().padStart(3,"0")}`},_parseSrtToSegments(s,t){const o=s.trim().split(`
`),n=[];let r=null;for(let i=0;i<o.length;i++){const g=o[i].trim();if(!g){r&&(n.push(r),r=null);continue}if(/^\d+$/.test(g))r={index:parseInt(g)};else if(g.includes("-->")){const[u,y]=g.split("-->").map(x=>x.trim()),v=this._srtTimeToMs(u),m=this._srtTimeToMs(y);r&&(r.startTimeMs=v,r.endTimeMs=m,r.start_time_ms=v,r.end_time_ms=m)}else if(r&&!r.text){r.text=g,r.translatedText=g;const u=r.index-1;t&&t[u]?(r.originalText=t[u].text,r.id=t[u].id||`translated-${r.index}`):(r.originalText=g,r.id=`translated-${r.index}`)}}return r&&n.push(r),n},_srtTimeToMs(s){const[t,o]=s.split(","),[n,r,i]=t.split(":").map(Number);return(n*3600+r*60+i)*1e3+parseInt(o||0)},getStageNameInChinese(s){return{audio_extraction:"音频提取",audio_processing:"音频处理",transcription:"语音识别",text_processing:"文本处理",subtitle_generation:"字幕生成",subtitle_optimization:"字幕优化",translation:"翻译处理",export:"导出处理",file_processing:"文件处理",initialization:"初始化",completion:"完成处理",VideoToAudio:"视频转音频",AudioToText:"音频转文字",GenerateSubtitles:"生成字幕",OptimizeSubtitles:"优化字幕",Translation:"翻译处理",Export:"导出处理"}[s]||s},getLanguageDisplayName(s){return{zh:"中文(简体)","zh-CN":"中文(简体)","zh-TW":"中文(繁体)",en:"英语",ja:"日语",ko:"韩语",fr:"法语",de:"德语",es:"西班牙语",it:"意大利语",pt:"葡萄牙语",ru:"俄语",ar:"阿拉伯语",th:"泰语",vi:"越南语"}[s]||s},_mockTranslateToJapanese(s){const t={embeddings:"埋め込み",based:"ベース",retrieval:"検索",is:"です",still:"まだ",a:"一つの",very:"とても",active:"アクティブな",area:"分野",looking:"楽しみに",forward:"前向きに",to:"に",seeing:"見る",what:"何を",you:"あなた",will:"でしょう",build:"構築",really:"本当に",I:"私",am:"です"};return s.split(" ").map(o=>{const n=o.toLowerCase().replace(/[.,!?]/g,"");return t[n]||o}).join(" ")},_mockTranslateToKorean(s){const t={embeddings:"임베딩",based:"기반",retrieval:"검색",is:"입니다",still:"여전히",a:"하나의",very:"매우",active:"활발한",area:"영역",looking:"기대하고",forward:"앞으로",to:"에",seeing:"보는",what:"무엇을",you:"당신",will:"할",build:"구축",really:"정말로",I:"나는"};return s.split(" ").map(o=>{const n=o.toLowerCase().replace(/[.,!?]/g,"");return t[n]||o}).join(" ")},_mockTranslateToSpanish(s){const t={embeddings:"incrustaciones",based:"basado",retrieval:"recuperación",is:"es",still:"todavía",a:"un",very:"muy",active:"activo",area:"área",looking:"esperando",forward:"adelante",to:"a",seeing:"ver",what:"qué",you:"tú",will:"vas a",build:"construir",really:"realmente",I:"yo"};return s.split(" ").map(o=>{const n=o.toLowerCase().replace(/[.,!?]/g,"");return t[n]||o}).join(" ")},_mockTranslateToFrench(s){const t={embeddings:"intégrations",based:"basé",retrieval:"récupération",is:"est",still:"encore",a:"un",very:"très",active:"actif",area:"domaine",looking:"attendant",forward:"avec impatience",to:"à",seeing:"voir",what:"ce que",you:"vous",will:"allez",build:"construire",really:"vraiment",I:"je"};return s.split(" ").map(o=>{const n=o.toLowerCase().replace(/[.,!?]/g,"");return t[n]||o}).join(" ")},async exportMultipleSubtitles(s={}){const{filename:t,contentSources:o,layouts:n,format:r,autoSave:i}=s;this.isLoading=!0,this.exportResults=[],this.progressUpdates=[];try{let g=o.length*n.length,u=0;this.progressUpdates.push(`开始批量导出：${g} 个文件`);for(const y of o)for(const v of n)try{this.exportContentSource=y,this.exportLayout=v,this.exportFormat=r,this.exportAutoSaveToDefault=i;const m=this._generateExportSuffix(y,v),x=t?`${t}_${m}`:`subtitles_${m}`;this.exportFilename=x,this.progressUpdates.push(`导出中: ${x} (${u+1}/${g})`),await this.exportSubtitles({filename:x}),this.lastExportPath&&(this.exportResults.push({contentSource:y,layout:v,format:r,filename:x,path:this.lastExportPath}),u++,this.progressUpdates.push(`✅ 完成: ${x}`))}catch(m){console.error(`Export failed for ${y} + ${v}:`,m),this.progressUpdates.push(`❌ 失败: ${y} + ${v} - ${m.message}`)}this.progressUpdates.push(`🎉 批量导出完成！成功导出 ${u}/${g} 个文件`)}catch(g){console.error("Multi-export error:",g),this.progressUpdates.push(`❌ 批量导出失败: ${g.message}`)}finally{this.isLoading=!1}},_generateExportSuffix(s,t){const o={editable_segments:"编辑版",transcript_text:"原始文本",transcript_segments:"原始片段",translation_result:"翻译版"},n={original_top:"原文在上",translation_top:"译文在上",original_only:"仅原文",translation_only:"仅译文"},r=o[s]||s,i=n[t]||t;return`${r}_${i}`}}}),Ge={class:"stepper-navigation-container"},Je={class:"workflow-status mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg"},Ke={class:"flex items-center justify-between"},Ye={class:"text-xs text-blue-600"},Xe={class:"stepper-nav bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl shadow-lg border border-blue-100 px-6 py-6"},Qe={class:"flex items-center space-x-3 sm:space-x-4 min-w-max py-4 px-4"},Ze=["onClick","disabled","title"],et={class:"text-lg sm:text-xl md:text-2xl font-bold"},tt={class:"text-xs leading-tight text-center px-1 mt-0.5"},st={key:0,class:"flex items-center mx-2 sm:mx-3"},ot={__name:"StepperNavigation",setup(s){const t=ae(),o=A(null),n=p(()=>t.currentStep),r=p(()=>{const b=i.filter(U=>g(U.id)).length,M=i.length;return`已完成 ${b}/${M} 个步骤`}),i=[{id:1,name:"上传文件"},{id:2,name:"提取音频"},{id:3,name:"语音转文字"},{id:4,name:"组成句子"},{id:5,name:"优化句子"},{id:6,name:"编辑字幕"},{id:7,name:"翻译选择"},{id:8,name:"翻译处理"},{id:9,name:"导出保存"}],g=b=>!!(b<n.value||b===2&&t.videoToAudioResult||b===3&&t.audioToTextResult||b===4&&t.editedSubtitlesSaved||b===5&&t.lastExportPath),u=b=>{if(b===n.value||g(b))return!0;const M=Math.max(n.value,y());switch(b){case 1:return!0;case 2:return t.uploadedFile!==null;case 3:return t.videoToAudioResult!==null;case 4:return t.audioToTextResult!==null;case 5:return t.audioToTextResult!==null;case 6:return t.audioToTextResult!==null;case 7:return t.editableSegments!==null;case 8:return t.editableSegments!==null;case 9:return t.editableSegments!==null;default:return b<=M}},y=()=>{var M;let b=1;return t.uploadedFile&&(b=Math.max(b,1)),t.videoToAudioResult&&(b=Math.max(b,2)),t.audioToTextResult&&(b=Math.max(b,3)),t.editableSegments&&(b=Math.max(b,6)),t.translatedSubtitles&&(b=Math.max(b,8)),(t.lastExportPath||((M=t.exportResults)==null?void 0:M.length)>0)&&(b=Math.max(b,9)),b},v=b=>{u(b)&&(t.setCurrentStep(b),ve(()=>{C()}))},m=()=>{o.value&&o.value.scrollTo({left:0,behavior:"smooth"})},x=()=>{o.value&&o.value.scrollTo({left:o.value.scrollWidth,behavior:"smooth"})},C=()=>{if(o.value){const b=o.value.querySelector(`button[title*="步骤 ${n.value}"]`);if(b){const M=o.value.getBoundingClientRect(),U=b.getBoundingClientRect(),w=U.left-M.left+o.value.scrollLeft-M.width/2+U.width/2;o.value.scrollTo({left:Math.max(0,w),behavior:"smooth"})}}};return ce(n,()=>{ve(()=>{C()})}),Ae(()=>{ve(()=>{C()})}),(b,M)=>(l(),a("div",Ge,[e("div",Je,[e("div",Ke,[M[0]||(M[0]=e("div",{class:"flex items-center"},[e("svg",{class:"h-4 w-4 text-blue-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("span",{class:"text-sm font-medium text-blue-800"},"当前工作流程")],-1)),e("span",Ye,c(r.value),1)])]),e("nav",Xe,[e("div",{class:"nav-scroll-container overflow-x-auto overflow-y-hidden",ref_key:"navContainer",ref:o},[e("ol",Qe,[(l(),a(K,null,Q(i,U=>e("li",{key:U.id,class:"flex items-center"},[e("button",{onClick:w=>v(U.id),disabled:!u(U.id),class:H(["step-button flex flex-col items-center justify-center rounded-full text-xs font-medium transition-all duration-200 ease-in-out shadow-md hover:shadow-lg","w-16 h-16 sm:w-18 sm:h-18 md:w-20 md:h-20",n.value===U.id?"bg-gradient-to-r from-blue-500 to-indigo-600 text-white ring-4 ring-blue-200 scale-110 z-10":g(U.id)?"bg-gradient-to-r from-green-400 to-emerald-500 text-white hover:from-green-500 hover:to-emerald-600":u(U.id)?"bg-white text-gray-700 hover:bg-gray-50 border-2 border-gray-200 hover:border-blue-300":"bg-gray-100 text-gray-400 cursor-not-allowed border-2 border-gray-200"]),title:`步骤 ${U.id}: ${U.name}`},[e("span",et,c(U.id),1),e("span",tt,c(U.name),1)],10,Ze),U.id<i.length?(l(),a("div",st,M[1]||(M[1]=[e("div",{class:"w-5 h-0.5 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-full"},null,-1),e("svg",{class:"h-3 w-3 text-blue-400 mx-1",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor","aria-hidden":"true"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1),e("div",{class:"w-5 h-0.5 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-full"},null,-1)]))):f("",!0)])),64))])],512),e("div",{class:"scroll-indicators flex justify-center mt-4 space-x-3"},[e("button",{onClick:m,class:"scroll-btn px-4 py-2 text-xs bg-white text-blue-600 rounded-full border border-blue-200 hover:bg-blue-50 transition-colors shadow-sm",title:"滚动到开始"}," ← 开始 "),e("button",{onClick:C,class:"scroll-btn px-4 py-2 text-xs bg-blue-100 text-blue-700 rounded-full border border-blue-300 hover:bg-blue-200 transition-colors shadow-sm",title:"滚动到当前步骤"}," 当前步骤 "),e("button",{onClick:x,class:"scroll-btn px-4 py-2 text-xs bg-white text-blue-600 rounded-full border border-blue-200 hover:bg-blue-50 transition-colors shadow-sm",title:"滚动到结束"}," 结束 → ")])])]))}},rt=ee(ot,[["__scopeId","data-v-24b9c782"]]),nt={key:0,class:"bg-white border border-gray-200 rounded-lg p-4 shadow-sm mb-4"},it={class:"flex items-center space-x-3"},lt={class:"flex-shrink-0"},at={class:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center"},ut={class:"text-xl"},dt={class:"flex-1 min-w-0"},ct={class:"flex items-center space-x-2"},gt={class:"text-sm font-medium text-gray-900 truncate"},pt={class:"flex items-center space-x-4 mt-1 text-xs text-gray-500"},mt={key:0},bt={key:0,class:"flex-shrink-0"},ft={class:"flex items-center space-x-2"},vt={key:0,class:"mt-3"},xt={class:"flex items-center justify-between text-xs text-gray-600 mb-1"},ht={class:"w-full bg-gray-200 rounded-full h-2"},yt={key:1,class:"mt-3 p-3 bg-red-50 border border-red-200 rounded-md"},wt={class:"flex items-center"},_t={class:"text-sm text-red-700"},St={__name:"FileInfoCard",props:{fileInfo:{type:Object,default:null},showCard:{type:Boolean,default:!0},processingStatus:{type:String,default:null},progress:{type:Number,default:null},progressText:{type:String,default:null},errorMessage:{type:String,default:null},showActions:{type:Boolean,default:!0},allowReselect:{type:Boolean,default:!0},allowRemove:{type:Boolean,default:!1},showProgress:{type:Boolean,default:!0}},emits:["reselect","remove"],setup(s,{emit:t}){const o=s,n=p(()=>{var x,C;if(!((x=o.fileInfo)!=null&&x.name))return"📄";const v=(C=o.fileInfo.name.split(".").pop())==null?void 0:C.toLowerCase();return{mp4:"🎬",avi:"🎬",mov:"🎬",mkv:"🎬",flv:"🎬",wmv:"🎬",mp3:"🎵",wav:"🎵",flac:"🎵",aac:"🎵",ogg:"🎵",m4a:"🎵",srt:"📝",vtt:"📝",ass:"📝",ssa:"📝",txt:"📄",doc:"📄",docx:"📄",pdf:"📄"}[v]||"📄"}),r=p(()=>({processing:"bg-blue-100 text-blue-800",completed:"bg-green-100 text-green-800",error:"bg-red-100 text-red-800",ready:"bg-gray-100 text-gray-800",waiting:"bg-yellow-100 text-yellow-800"})[o.processingStatus]||"bg-gray-100 text-gray-800"),i=p(()=>({processing:"🔄",completed:"✅",error:"❌",ready:"📋",waiting:"⏳"})[o.processingStatus]||""),g=v=>{if(!v)return"0 B";const m=["B","KB","MB","GB"],x=Math.floor(Math.log(v)/Math.log(1024));return`${(v/Math.pow(1024,x)).toFixed(1)} ${m[x]}`},u=v=>{if(!v)return"";const m=Math.floor(v/3600),x=Math.floor(v%3600/60),C=Math.floor(v%60);return m>0?`${m}:${x.toString().padStart(2,"0")}:${C.toString().padStart(2,"0")}`:`${x}:${C.toString().padStart(2,"0")}`},y=v=>{var C;if(!v)return"";const m=(C=v.split(".").pop())==null?void 0:C.toLowerCase();return{mp4:"video/mp4",avi:"video/avi",mov:"video/quicktime",mkv:"video/x-matroska",flv:"video/x-flv",wmv:"video/x-ms-wmv",mp3:"audio/mpeg",wav:"audio/wav",flac:"audio/flac",aac:"audio/aac",ogg:"audio/ogg",m4a:"audio/mp4",srt:"text/srt",vtt:"text/vtt",ass:"text/ass"}[m]||`file/${m}`};return(v,m)=>s.fileInfo&&s.showCard?(l(),a("div",nt,[e("div",it,[e("div",lt,[e("div",at,[e("span",ut,c(n.value),1)])]),e("div",dt,[e("div",ct,[e("h3",gt,c(s.fileInfo.name),1),s.processingStatus?(l(),a("span",{key:0,class:H(["inline-flex items-center px-2 py-1 rounded-full text-xs font-medium",r.value])},c(i.value)+" "+c(s.processingStatus),3)):f("",!0)]),e("div",pt,[e("span",null,c(g(s.fileInfo.size)),1),e("span",null,c(s.fileInfo.type||y(s.fileInfo.name)),1),s.fileInfo.duration?(l(),a("span",mt,c(u(s.fileInfo.duration)),1)):f("",!0)])]),s.showActions?(l(),a("div",bt,[e("div",ft,[s.allowReselect?(l(),a("button",{key:0,onClick:m[0]||(m[0]=x=>v.$emit("reselect")),class:"inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," 🔄 重新选择 ")):f("",!0),s.allowRemove?(l(),a("button",{key:1,onClick:m[1]||(m[1]=x=>v.$emit("remove")),class:"inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"}," ❌ 移除 ")):f("",!0)])])):f("",!0)]),s.showProgress&&s.progress!==null?(l(),a("div",vt,[e("div",xt,[e("span",null,c(s.progressText||"处理进度"),1),e("span",null,c(Math.round(s.progress))+"%",1)]),e("div",ht,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:pe({width:`${Math.min(100,Math.max(0,s.progress))}%`})},null,4)])])):f("",!0),s.errorMessage?(l(),a("div",yt,[e("div",wt,[m[2]||(m[2]=e("span",{class:"text-red-400 mr-2"},"⚠️",-1)),e("span",_t,c(s.errorMessage),1)])])):f("",!0)])):f("",!0)}},$e=ee(St,[["__scopeId","data-v-6b9f5912"]]),Tt={class:"flex items-start"},$t={class:"flex-shrink-0"},kt={class:"text-lg"},Ct={class:"ml-3 flex-1"},Mt={class:"flex items-center justify-between"},Et={key:0,class:"text-sm font-medium"},Pt={class:"mt-1 text-sm"},Lt={key:0,class:"mt-2 text-xs opacity-75"},Ut={key:1,class:"mt-3"},Ft={class:"flex items-center justify-between text-xs mb-1"},At={class:"w-full bg-white bg-opacity-30 rounded-full h-2"},zt={key:2,class:"mt-3 flex space-x-2"},It=["onClick","disabled"],Nt={key:0,class:"mr-1"},Rt={__name:"StatusMessage",props:{type:{type:String,default:"info",validator:s=>["success","warning","error","info","loading"].includes(s)},title:{type:String,default:null},message:{type:String,required:!0},details:{type:String,default:null},dismissible:{type:Boolean,default:!1},showProgress:{type:Boolean,default:!1},progress:{type:Number,default:null},progressText:{type:String,default:null},actions:{type:Array,default:()=>[]},autoHide:{type:Number,default:0}},emits:["dismiss","action"],setup(s,{emit:t}){const o=s,n=t,r=A(!0),i=p(()=>{const x={success:"bg-green-50 border-green-200 text-green-800",warning:"bg-yellow-50 border-yellow-200 text-yellow-800",error:"bg-red-50 border-red-200 text-red-800",info:"bg-blue-50 border-blue-200 text-blue-800",loading:"bg-gray-50 border-gray-200 text-gray-800"};return x[o.type]||x.info}),g=p(()=>{const x={success:"✅",warning:"⚠️",error:"❌",info:"ℹ️",loading:"🔄"};return x[o.type]||x.info}),u=p(()=>o.type==="loading"),y=p(()=>{const x={success:"bg-green-600",warning:"bg-yellow-600",error:"bg-red-600",info:"bg-blue-600",loading:"bg-gray-600"};return x[o.type]||x.info}),v=()=>{r.value=!1,n("dismiss")},m=x=>{x.disabled||(n("action",x),x.dismiss&&v())};return o.autoHide>0&&setTimeout(()=>{v()},o.autoHide*1e3),(x,C)=>s.message&&r.value?(l(),a("div",{key:0,class:H(["rounded-lg p-4 mb-4 border transition-all duration-300",i.value,{"animate-pulse":u.value}])},[e("div",Tt,[e("div",$t,[e("span",kt,c(g.value),1)]),e("div",Ct,[e("div",Mt,[s.title?(l(),a("h3",Et,c(s.title),1)):f("",!0),s.dismissible?(l(),a("button",{key:1,onClick:v,class:"ml-auto flex-shrink-0 text-gray-400 hover:text-gray-600 focus:outline-none"},C[0]||(C[0]=[e("span",{class:"sr-only"},"关闭",-1),e("svg",{class:"h-4 w-4",fill:"currentColor",viewBox:"0 0 20 20"},[e("path",{"fill-rule":"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z","clip-rule":"evenodd"})],-1)]))):f("",!0)]),e("div",Pt,c(s.message),1),s.details?(l(),a("div",Lt,c(s.details),1)):f("",!0),s.showProgress&&s.progress!==null?(l(),a("div",Ut,[e("div",Ft,[e("span",null,c(s.progressText||"进度"),1),e("span",null,c(Math.round(s.progress))+"%",1)]),e("div",At,[e("div",{class:H([y.value,"h-2 rounded-full transition-all duration-300"]),style:pe({width:`${Math.min(100,Math.max(0,s.progress))}%`})},null,6)])])):f("",!0),s.actions&&s.actions.length>0?(l(),a("div",zt,[(l(!0),a(K,null,Q(s.actions,b=>(l(),a("button",{key:b.key,onClick:M=>m(b),class:H(["inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2",b.primary?"text-white bg-white bg-opacity-20 hover:bg-opacity-30 focus:ring-white":"text-current bg-white bg-opacity-10 hover:bg-opacity-20 focus:ring-current"]),disabled:b.disabled},[b.icon?(l(),a("span",Nt,c(b.icon),1)):f("",!0),ue(" "+c(b.label),1)],10,It))),128))])):f("",!0)])])],2)):f("",!0)}},ge=ee(Rt,[["__scopeId","data-v-6716bcfe"]]),jt={class:"bg-white border border-gray-200 rounded-lg shadow-sm"},Ot={class:"flex items-center space-x-2"},Bt={class:"text-sm font-medium text-gray-900"},Dt={key:0,class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},Vt={class:"flex items-center space-x-2"},Wt={key:0,class:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"},qt={class:"border-t border-gray-200"},Ht={class:"p-4"},Gt={class:"flex items-center justify-between mb-3"},Jt={class:"flex items-center space-x-2"},Kt={class:"text-xs text-gray-500"},Yt={key:0,class:"text-gray-400 text-center py-4"},Xt={class:"text-gray-400 text-xs flex-shrink-0 w-16"},Qt={class:"flex-shrink-0"},Zt={key:0,class:"mt-3 flex items-center justify-between text-xs text-gray-500"},es={class:"flex items-center space-x-4"},ts={key:0,class:"text-green-600"},ss={key:1,class:"text-yellow-600"},os={key:2,class:"text-red-600"},rs={__name:"CollapsibleLog",props:{title:{type:String,default:"处理日志"},logs:{type:Array,default:()=>[]},initialExpanded:{type:Boolean,default:!1},showStats:{type:Boolean,default:!0},maxLogs:{type:Number,default:100}},emits:["clear","copy"],setup(s,{emit:t}){const o=s,n=t,r=A(o.initialExpanded),i=A(!0),g=A(!1),u=A(null),y=p(()=>o.logs.length),v=p(()=>o.logs.reduce((F,V)=>(F[V.level]=(F[V.level]||0)+1,F),{success:0,warning:0,error:0,info:0})),m=p(()=>{if(o.logs.length===0)return"";const F=o.logs[o.logs.length-1];return q(new Date(F.timestamp))}),x=()=>{r.value=!r.value,g.value=!1,r.value&&i.value&&ve(()=>{b()})},C=()=>{i.value=!i.value,i.value&&b()},b=()=>{u.value&&(u.value.scrollTop=u.value.scrollHeight)},M=()=>{n("clear"),g.value=!1},U=async()=>{const F=o.logs.map(V=>`[${R(V.timestamp)}] ${w(V.level)} ${V.message}`).join(`
`);try{await navigator.clipboard.writeText(F),n("copy",F)}catch(V){console.error("复制失败:",V)}},w=F=>({success:"✅",warning:"⚠️",error:"❌",info:"ℹ️",debug:"🔍"})[F]||"ℹ️",E=F=>({success:"text-green-400",warning:"text-yellow-400",error:"text-red-400",info:"text-blue-400",debug:"text-gray-400"})[F]||"text-gray-300",q=F=>F.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),R=F=>new Date(F).toLocaleTimeString("zh-CN",{hour12:!1,minute:"2-digit",second:"2-digit"});return ce(()=>o.logs.length,(F,V)=>{F>V&&(g.value=!r.value,r.value&&i.value&&ve(()=>{b()}))}),ce(r,F=>{F&&i.value&&ve(()=>{b()})}),(F,V)=>(l(),a("div",jt,[e("button",{onClick:x,class:"w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset rounded-t-lg"},[e("div",Ot,[V[0]||(V[0]=e("span",{class:"text-lg"},"💡",-1)),e("span",Bt,c(s.title),1),y.value>0?(l(),a("span",Dt,c(y.value),1)):f("",!0)]),e("div",Vt,[g.value?(l(),a("span",Wt)):f("",!0),(l(),a("svg",{class:H(["w-4 h-4 text-gray-500 transition-transform duration-200",{"transform rotate-180":r.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},V[1]||(V[1]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"},null,-1)]),2))])]),W(e("div",qt,[e("div",Ht,[e("div",Gt,[e("div",Jt,[e("button",{onClick:M,class:"inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," 🗑️ 清除 "),e("button",{onClick:U,class:"inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," 📋 复制 "),e("button",{onClick:C,class:H(["inline-flex items-center px-2 py-1 border shadow-sm text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",i.value?"border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100":"border-gray-300 text-gray-700 bg-white hover:bg-gray-50"])},c(i.value?"📌":"📍")+" 自动滚动 ",3)]),e("div",Kt,c(q(new Date)),1)]),e("div",{ref_key:"logContainer",ref:u,class:"bg-gray-900 rounded-lg p-3 max-h-64 overflow-y-auto font-mono text-sm"},[s.logs.length===0?(l(),a("div",Yt," 暂无日志信息 ")):f("",!0),(l(!0),a(K,null,Q(s.logs,(O,T)=>(l(),a("div",{key:T,class:H(["flex items-start space-x-2 py-1",{"border-t border-gray-700":T>0}])},[e("span",Xt,c(R(O.timestamp)),1),e("span",Qt,c(w(O.level)),1),e("span",{class:H(["flex-1 break-words",E(O.level)])},c(O.message),3)],2))),128))],512),s.showStats?(l(),a("div",Zt,[e("div",es,[e("span",null,"总计: "+c(y.value),1),v.value.success>0?(l(),a("span",ts,"✅ "+c(v.value.success),1)):f("",!0),v.value.warning>0?(l(),a("span",ss,"⚠️ "+c(v.value.warning),1)):f("",!0),v.value.error>0?(l(),a("span",os,"❌ "+c(v.value.error),1)):f("",!0)]),e("div",null," 最后更新: "+c(m.value),1)])):f("",!0)])],512),[[Ve,r.value]])]))}},be=ee(rs,[["__scopeId","data-v-4abe4d0b"]]),ns={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},is={class:"current-actions mb-6"},ls={key:0,class:"space-y-4"},as={key:1,class:"space-y-4"},us={class:"text-sm text-gray-600"},ds={class:"flex space-x-3 mt-4"},cs=["disabled"],gs=["disabled"],ps={key:0,class:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"},ms={__name:"FileUpload",setup(s){const t=ae(),o=A(!1),n=A(null),r=Te({type:"info",title:null,message:"",details:null,showProgress:!1,progress:0,progressText:"",dismissible:!1}),i=p(()=>t.uploadedFile?{name:t.getUploadedFileName,size:t.getUploadedFileSize,type:t.getUploadedFileType,duration:null}:null),g=p(()=>t.isLoading?"processing":t.uploadedFile&&!t.isLoading?"ready":null),u=p(()=>t.isLoading?50:null),y=p(()=>t.isLoading?"正在处理文件...":null),v=p(()=>null),m=p(()=>t.progressUpdates.map(O=>({timestamp:new Date().toISOString(),level:O.includes("✅")?"success":O.includes("❌")?"error":O.includes("⚠️")?"warning":"info",message:O}))),x=p(()=>t.uploadedFile&&!t.isLoading&&t.progressUpdates.some(O=>O.includes("✅"))),C=()=>{var O;(O=n.value)==null||O.click()},b=O=>{const T=O.target.files[0];T&&U(T)},M=O=>{O.preventDefault(),o.value=!1;const T=O.dataTransfer.files;T.length>0&&U(T[0])},U=O=>{if(!["video/mp4","video/avi","video/mov","video/quicktime","video/x-msvideo","audio/mp3","audio/wav","audio/mpeg","audio/x-wav"].some(G=>O.type.includes(G.split("/")[1]))){r.type="error",r.title="文件格式不支持",r.message="请选择支持的视频或音频文件格式",r.details="支持格式: MP4, AVI, MOV, MP3, WAV 等",r.dismissible=!0;return}const P=2*1024*1024*1024;if(O.size>P){r.type="error",r.title="文件过大",r.message="文件大小不能超过 2GB",r.dismissible=!0;return}t.setUploadedFile(O),r.type="success",r.title="文件选择成功",r.message=`已选择文件: ${O.name}`,r.dismissible=!0,console.log("File selected:",O)},w=()=>{t.uploadedFile&&(r.type="loading",r.title="开始处理",r.message="正在处理文件，请稍候...",r.showProgress=!0,r.progress=0,r.progressText="初始化处理",r.dismissible=!1,t.processVideoToAudio())},E=()=>{t.resetWorkflow(),n.value&&(n.value.value=""),R()},q=()=>{t.setCurrentStep(2)},R=()=>{r.message="",r.title=null,r.details=null,r.showProgress=!1,r.progress=0,r.progressText=""},F=()=>{t.progressUpdates=[]},V=O=>{r.type="success",r.title="复制成功",r.message="日志内容已复制到剪贴板",r.dismissible=!0};return(O,T)=>(l(),a(K,null,[e("div",ns,[T[8]||(T[8]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 1: 上传媒体文件"),e("p",{class:"text-sm text-gray-500 mt-1"},"选择视频或音频文件开始处理")],-1)),e("div",is,[e("div",{onDrop:M,onDragover:T[0]||(T[0]=we(()=>{},["prevent"])),onDragenter:[T[1]||(T[1]=we(()=>{},["prevent"])),T[2]||(T[2]=P=>o.value=!0)],class:H(["border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200",o.value?"border-blue-500 bg-blue-50":J(t).uploadedFile?"border-green-500 bg-green-50":"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"]),onDragleave:T[3]||(T[3]=P=>o.value=!1)},[J(t).uploadedFile?(l(),a("div",as,[T[7]||(T[7]=e("div",{class:"text-4xl"},"✅",-1)),e("div",null,[T[6]||(T[6]=e("p",{class:"text-lg font-medium text-green-700"},"文件已选择",-1)),e("p",us,c(J(t).getUploadedFileName),1)])])):(l(),a("div",ls,[T[4]||(T[4]=e("div",{class:"text-4xl"},"📁",-1)),T[5]||(T[5]=e("div",null,[e("p",{class:"text-lg font-medium text-gray-700"},"拖拽文件到此处或点击选择"),e("p",{class:"text-sm text-gray-500 mt-1"},"支持 MP4, AVI, MOV, MP3, WAV 等格式")],-1)),e("button",{onClick:C,class:"inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," 📂 选择文件 ")]))],34),e("input",{ref_key:"fileInput",ref:n,type:"file",accept:"video/*,audio/*",onChange:b,class:"hidden"},null,544),J(t).uploadedFile?(l(),Y($e,{key:0,"file-info":i.value,"processing-status":g.value,progress:u.value,"progress-text":y.value,"error-message":v.value,"show-actions":!0,"allow-reselect":!0,"allow-remove":!1,onReselect:C},null,8,["file-info","processing-status","progress","progress-text","error-message"])):f("",!0),e("div",ds,[e("button",{onClick:w,disabled:!J(t).uploadedFile||J(t).isLoading,class:"flex-1 px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🎤 "+c(J(t).uploadedFile?"开始处理":"请先选择文件"),9,cs),e("button",{onClick:E,disabled:!J(t).uploadedFile&&!J(t).isLoading,class:"px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🔄 重新选择 ",8,gs)])]),r.message?(l(),Y(ge,{key:0,type:r.type,title:r.title,message:r.message,details:r.details,"show-progress":r.showProgress,progress:r.progress,"progress-text":r.progressText,dismissible:r.dismissible,onDismiss:R},null,8,["type","title","message","details","show-progress","progress","progress-text","dismissible"])):f("",!0),J(t).progressUpdates.length>0?(l(),Y(be,{key:1,title:"处理日志",logs:m.value,"initial-expanded":!1,"show-stats":!0,onClear:F,onCopy:V},null,8,["logs"])):f("",!0)]),x.value?(l(),a("div",ps,[e("div",{class:"flex items-center justify-between"},[T[9]||(T[9]=e("div",null,[e("h4",{class:"text-sm font-medium text-green-800"},"文件处理完成"),e("p",{class:"text-sm text-green-600 mt-1"},"可以进入下一步进行语音转文字处理")],-1)),e("button",{onClick:q,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," ➡️ 进入下一步 ")])])):f("",!0)],64))}},bs=ee(ms,[["__scopeId","data-v-e5d04b13"]]),fs={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},vs={key:2,class:"current-actions mb-6"},xs={class:"flex space-x-3"},hs=["disabled"],ys={key:3,class:"mb-6"},ws={class:"bg-purple-50 border border-purple-200 rounded-lg p-4"},_s={class:"flex items-center justify-between mb-3"},Ss={class:"text-xs text-purple-600"},Ts={class:"mb-3"},$s={class:"flex items-center justify-between text-sm mb-1"},ks={class:"text-purple-700"},Cs={class:"w-full bg-purple-200 rounded-full h-2"},Ms={class:"text-sm text-purple-600"},Es={key:5,class:"mb-6"},Ps={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Ls={class:"text-sm text-green-700 mb-3"},Us={class:"bg-white rounded-md p-3 border border-green-200"},Fs={class:"text-sm text-gray-700 font-mono"},As={__name:"VideoToAudioStep",setup(s){const t=ae(),o=p(()=>t.uploadedFile),n=p(()=>t.videoToAudioResult),r=p(()=>t.videoToAudioProgress),i=p(()=>t.videoToAudioError),g=p(()=>t.isLoading),u=p(()=>o.value?{name:o.value.name||t.getUploadedFileName,size:o.value.size||t.getUploadedFileSize,type:o.value.type||t.getUploadedFileType,duration:null}:null),y=p(()=>g.value?"processing":n.value?"completed":i.value?"error":o.value?"ready":null),v=p(()=>g.value?60:n.value?100:null),m=p(()=>g.value?r.value||"正在从视频文件中提取音频...":null),x=p(()=>i.value),C=p(()=>i.value?"请检查视频文件格式是否支持，或尝试使用其他视频文件":null),b=p(()=>n.value?"音频文件已成功从视频中提取，可以进入下一步进行语音转文字处理":""),M=p(()=>n.value?n.value.split("/").pop()||n.value:""),U=p(()=>t.progressUpdates.map(T=>({timestamp:new Date().toISOString(),level:T.includes("✅")?"success":T.includes("❌")?"error":T.includes("⚠️")?"warning":"info",message:T}))),w=T=>T.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),E=async()=>{o.value?await t.processVideoToAudio():t.videoToAudioError="没有文件被上传。"},q=T=>{T.key==="goto-upload"&&t.setCurrentStep(1)},R=T=>{switch(T.key){case"retry":E();break;case"back":t.setCurrentStep(1);break}},F=()=>{t.setCurrentStep(3)},V=()=>{t.progressUpdates=[]},O=T=>{console.log("日志已复制:",T)};return(T,P)=>(l(),a("div",fs,[P[4]||(P[4]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 2: 视频转音频"),e("p",{class:"text-sm text-gray-500 mt-1"},"从视频文件中提取音频用于后续处理")],-1)),o.value?f("",!0):(l(),Y(ge,{key:0,type:"warning",title:"需要先上传文件",message:"请先在步骤1中上传视频或音频文件",actions:[{key:"goto-upload",label:"前往上传文件",icon:"📁",primary:!0}],onAction:q})),u.value?(l(),Y($e,{key:1,"file-info":u.value,"processing-status":y.value,progress:v.value,"progress-text":m.value,"error-message":x.value,"show-actions":!1,"show-progress":!0},null,8,["file-info","processing-status","progress","progress-text","error-message"])):f("",!0),o.value&&!g.value?(l(),a("div",vs,[e("div",xs,[e("button",{onClick:E,disabled:g.value||n.value,class:"flex-1 px-6 py-3 text-white bg-purple-600 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🎵 "+c(n.value?"音频已提取":"开始提取音频"),9,hs),n.value?(l(),a("button",{key:0,onClick:F,class:"px-6 py-3 text-purple-700 bg-purple-100 rounded-lg hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 font-medium transition-colors text-base"}," ➡️ 下一步 ")):f("",!0)])])):f("",!0),g.value?(l(),a("div",ys,[e("div",ws,[e("div",_s,[P[0]||(P[0]=e("h4",{class:"text-sm font-medium text-purple-800"},"🎵 正在提取音频",-1)),e("span",Ss,c(w(new Date)),1)]),e("div",Ts,[e("div",$s,[P[1]||(P[1]=e("span",{class:"text-purple-700"},"📊 提取进度",-1)),e("span",ks,c(Math.round(v.value||0))+"%",1)]),e("div",Cs,[e("div",{class:"bg-purple-600 h-2 rounded-full transition-all duration-300",style:pe({width:`${Math.min(100,Math.max(0,v.value||0))}%`})},null,4)])]),e("div",Ms," ⏱️ "+c(m.value||"正在从视频文件中提取音频..."),1)])])):f("",!0),i.value?(l(),Y(ge,{key:4,type:"error",title:"音频提取失败",message:i.value,details:C.value,actions:[{key:"retry",label:"重试",icon:"🔄",primary:!0},{key:"back",label:"返回上一步",icon:"⬅️"}],onAction:R},null,8,["message","details"])):f("",!0),n.value&&!g.value?(l(),a("div",Es,[e("div",Ps,[P[3]||(P[3]=e("div",{class:"flex items-center justify-between mb-3"},[e("h4",{class:"text-sm font-medium text-green-800"},"✅ 音频提取完成")],-1)),e("p",Ls,c(b.value),1),e("div",Us,[P[2]||(P[2]=e("div",{class:"text-xs text-green-600 mb-1"},"🎵 音频文件",-1)),e("p",Fs,c(M.value),1)])])])):f("",!0),J(t).progressUpdates.length>0?(l(),Y(be,{key:6,title:"提取日志",logs:U.value,"initial-expanded":!1,"show-stats":!0,onClear:V,onCopy:O},null,8,["logs"])):f("",!0)]))}},zs=ee(As,[["__scopeId","data-v-79a91a66"]]),Is={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},Ns={key:1,class:"current-actions mb-6"},Rs={class:"bg-gray-50 rounded-lg p-4 mb-4"},js={class:"mb-4"},Os={class:"mb-4"},Bs={class:"space-y-3"},Ds={class:"flex items-center space-x-2 text-sm text-gray-700"},Vs={class:"flex items-center space-x-2 text-sm text-gray-700"},Ws={class:"flex space-x-3"},qs=["disabled"],Hs={key:2,class:"mb-6"},Gs={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},Js={class:"flex items-center justify-between mb-3"},Ks={class:"text-xs text-blue-600"},Ys={class:"mb-3"},Xs={class:"flex items-center justify-between text-sm mb-1"},Qs={class:"text-blue-700"},Zs={class:"w-full bg-blue-200 rounded-full h-2"},eo={class:"text-sm text-blue-600"},to={key:0,class:"mt-3 p-3 bg-white rounded-md border border-blue-200"},so={class:"text-sm text-gray-700"},oo={key:4,class:"mb-6"},ro={class:"bg-green-50 border border-green-200 rounded-lg p-4"},no={class:"flex items-center justify-between mb-3"},io={class:"text-sm text-green-700 mb-3"},lo={key:0,class:"space-y-4"},ao={class:"grid grid-cols-2 md:grid-cols-4 gap-3"},uo={class:"text-center p-3 bg-white rounded-md border border-green-200"},co={class:"text-lg font-bold text-green-700"},go={class:"text-center p-3 bg-white rounded-md border border-green-200"},po={class:"text-lg font-bold text-green-700"},mo={class:"text-center p-3 bg-white rounded-md border border-green-200"},bo={class:"text-lg font-bold text-green-700"},fo={class:"text-center p-3 bg-white rounded-md border border-green-200"},vo={class:"text-lg font-bold text-green-700"},xo={class:"bg-white rounded-md p-3 border border-green-200"},ho={class:"text-sm text-gray-700 leading-relaxed"},yo={__name:"AudioToTextStep",setup(s){const t=ae(),o=A(!1),n=A("JIANYING"),r=A("zh"),i=A(!0),g=A(!1),u=A(""),y=p(()=>t.isLoading);p(()=>t.currentStep);const v=p(()=>t.videoToAudioResult),m=p(()=>t.uploadedFile),x=p(()=>t.audioToTextResult),C=p(()=>t.audioToTextProgress),b=p(()=>t.audioToTextError),M=p(()=>m.value?{name:m.value.name||t.getUploadedFileName,size:m.value.size||t.getUploadedFileSize,type:m.value.type||t.getUploadedFileType,duration:null}:null),U=p(()=>y.value?"processing":x.value?"completed":b.value?"error":m.value?"ready":null),w=p(()=>y.value?75:x.value?100:null),E=p(()=>y.value?C.value||"正在转录音频，请稍候...":null),q=p(()=>b.value),R=p(()=>b.value?"请检查音频文件质量，或尝试调整转录设置":null),F=p({get:()=>t.usePreviousAudioForTranscription,set:B=>{t.setUsePreviousAudioForTranscription(B),!B&&!(m.value&&m.value.type&&m.value.type.startsWith("audio/"))&&(t.audioToTextResult=null,t.editableSegments=null)}});p(()=>F.value&&v.value?v.value:m.value&&m.value.type&&m.value.type.startsWith("audio/")?m.value.path:null),p(()=>F.value?v.value?`将使用上一步提取的音频: ${v.value}`:m.value&&m.value.type&&m.value.type.startsWith("audio/")?`上一步音频不可用，将尝试使用已上传的音频文件: ${m.value.path}`:"未找到来自上一步的音频，也未上传独立的音频文件。请勾选“使用上一步提取的音频”并确保上一步已成功，或取消勾选并上传一个音频文件。":m.value&&m.value.type&&m.value.type.startsWith("audio/")?`将使用已上传的音频文件: ${m.value.path}`:"请上传一个音频文件，或勾选“使用上一步提取的音频”（如果适用）。");const V=p(()=>!!m.value),O=p(()=>t.progressUpdates.map(B=>({timestamp:new Date().toISOString(),level:B.includes("✅")?"success":B.includes("❌")?"error":B.includes("⚠️")?"warning":"info",message:B}))),T=p(()=>x.value?x.value.segments?x.value.segments:[]:[]),P=p(()=>T.value.length),G=p(()=>{if(!x.value)return 0;let B="";return typeof x.value=="string"?B=x.value:x.value.transcript&&(B=x.value.transcript),B.length}),Z=p(()=>{if(T.value.length===0)return null;const B=T.value[T.value.length-1],L=B.endTimeMs??B.end_time_ms??B.end;if(typeof L=="number"){const re=L<1e7?L:Math.floor(L/1e3),Se=Math.floor(re/60),fe=re%60;return`${Se}:${fe.toString().padStart(2,"0")}`}return null}),z=p(()=>{const B=P.value,L=G.value;return B===0?{label:"无数据",color:"bg-gray-400"}:B>=10&&L>=100?{label:"优秀",color:"bg-green-500"}:B>=5&&L>=50?{label:"良好",color:"bg-blue-500"}:{label:"一般",color:"bg-yellow-500"}}),h=p(()=>{if(!x.value)return"暂无转录内容";let B="";return typeof x.value=="string"?B=x.value:x.value.transcript?B=x.value.transcript:T.value.length>0&&(B=T.value.slice(0,3).map(L=>L.text).join(" ")),B.length>150?B.substring(0,150)+"...":B||"暂无转录内容"}),$=p(()=>{if(!x.value)return"";const B=P.value,L=Z.value,re=G.value;return B>0?`成功转录 ${B} 个字幕片段，共 ${re} 个字符${L?`，总时长 ${L}`:""}`:"转录完成，点击查看详细信息"}),_=B=>B.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),I=()=>{o.value=!o.value},te=()=>{const B={model:n.value,language:r.value,enableCache:i.value,needWordTimestamp:g.value};y.value&&(u.value="正在识别音频内容..."),t.processAudioToText(B)},oe=()=>{console.log("暂停转录")},ne=B=>{var L;switch(B.key){case"retry":te();break;case"settings":(L=document.querySelector(".current-actions"))==null||L.scrollIntoView({behavior:"smooth"});break}},xe=()=>{t.setCurrentStep(3)},de=()=>{t.progressUpdates=[]},_e=B=>{console.log("日志已复制:",B)};return(B,L)=>(l(),a("div",Is,[L[21]||(L[21]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 2: 语音转文字"),e("p",{class:"text-sm text-gray-500 mt-1"},"将音频内容转换为文字字幕")],-1)),M.value?(l(),Y($e,{key:0,"file-info":M.value,"processing-status":U.value,progress:w.value,"progress-text":E.value,"error-message":q.value,"show-actions":!1,"show-progress":!0},null,8,["file-info","processing-status","progress","progress-text","error-message"])):f("",!0),y.value?f("",!0):(l(),a("div",Ns,[e("div",Rs,[L[11]||(L[11]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"⚙️ 转录设置",-1)),e("div",js,[L[5]||(L[5]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 转录模型: ",-1)),W(e("select",{"onUpdate:modelValue":L[0]||(L[0]=re=>n.value=re),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},L[4]||(L[4]=[e("option",{value:"JIANYING"},"JIANYING (推荐)",-1),e("option",{value:"WHISPER"},"WHISPER",-1),e("option",{value:"AZURE"},"Azure Speech",-1)]),512),[[se,n.value]]),L[6]||(L[6]=e("p",{class:"mt-1 text-xs text-gray-500"}," JIANYING 模型对中文识别效果更好 ",-1))]),e("div",Os,[L[8]||(L[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 音频语言: ",-1)),W(e("select",{"onUpdate:modelValue":L[1]||(L[1]=re=>r.value=re),class:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},L[7]||(L[7]=[e("option",{value:"zh"},"中文",-1),e("option",{value:"en"},"English",-1),e("option",{value:"auto"},"自动检测",-1)]),512),[[se,r.value]])]),e("div",Bs,[e("label",Ds,[W(e("input",{type:"checkbox","onUpdate:modelValue":L[2]||(L[2]=re=>i.value=re),class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[le,i.value]]),L[9]||(L[9]=e("span",null,"启用缓存加速",-1))]),e("label",Vs,[W(e("input",{type:"checkbox","onUpdate:modelValue":L[3]||(L[3]=re=>g.value=re),class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[le,g.value]]),L[10]||(L[10]=e("span",null,"生成词级时间戳",-1))])])]),e("div",Ws,[e("button",{onClick:te,disabled:!V.value||y.value||x.value,class:"flex-1 px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🎤 "+c(x.value?"转录已完成":V.value?"开始转录":"请先准备音频文件"),9,qs),y.value?(l(),a("button",{key:0,onClick:oe,class:"px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 font-medium transition-colors text-base"}," ⏸️ 暂停 ")):f("",!0),x.value&&!y.value?(l(),a("button",{key:1,onClick:xe,class:"px-6 py-3 text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium transition-colors text-base"}," ➡️ 下一步 ")):f("",!0)])])),y.value?(l(),a("div",Hs,[e("div",Gs,[e("div",Js,[L[12]||(L[12]=e("h4",{class:"text-sm font-medium text-blue-800"},"🌐 中文 → 文字转录",-1)),e("span",Ks,c(_(new Date)),1)]),e("div",Ys,[e("div",Xs,[L[13]||(L[13]=e("span",{class:"text-blue-700"},"📊 转录进度",-1)),e("span",Qs,c(Math.round(w.value||0))+"%",1)]),e("div",Zs,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:pe({width:`${Math.min(100,Math.max(0,w.value||0))}%`})},null,4)])]),e("div",eo," ⏱️ "+c(E.value||"正在处理音频文件..."),1),u.value?(l(),a("div",to,[L[14]||(L[14]=e("div",{class:"text-xs text-blue-600 mb-1"},"💡 实时预览",-1)),e("p",so,c(u.value),1)])):f("",!0)])])):f("",!0),b.value?(l(),Y(ge,{key:3,type:"error",title:"转录失败",message:b.value,details:R.value,actions:[{key:"retry",label:"重试",icon:"🔄",primary:!0},{key:"settings",label:"调整设置",icon:"⚙️"}],onAction:ne},null,8,["message","details"])):f("",!0),x.value&&!y.value?(l(),a("div",oo,[e("div",ro,[e("div",no,[L[15]||(L[15]=e("h4",{class:"text-sm font-medium text-green-800"},"✅ 转录完成",-1)),e("button",{onClick:I,class:"text-xs text-green-600 hover:text-green-800 focus:outline-none"},c(o.value?"隐藏详情":"查看详情")+" "+c(o.value?"▲":"▼"),1)]),e("p",io,c($.value),1),o.value?(l(),a("div",lo,[e("div",ao,[e("div",uo,[e("div",co,c(P.value),1),L[16]||(L[16]=e("div",{class:"text-xs text-green-600"},"字幕片段",-1))]),e("div",go,[e("div",po,c(Z.value||"--"),1),L[17]||(L[17]=e("div",{class:"text-xs text-green-600"},"总时长",-1))]),e("div",mo,[e("div",bo,c(G.value),1),L[18]||(L[18]=e("div",{class:"text-xs text-green-600"},"字符数",-1))]),e("div",fo,[e("div",vo,c(z.value.label),1),L[19]||(L[19]=e("div",{class:"text-xs text-green-600"},"转录质量",-1))])]),e("div",xo,[L[20]||(L[20]=e("div",{class:"text-xs text-green-600 mb-2"},"📝 内容预览",-1)),e("p",ho,c(h.value),1)])])):f("",!0)])])):f("",!0),J(t).progressUpdates.length>0?(l(),Y(be,{key:5,title:"转录日志",logs:O.value,"initial-expanded":!1,"show-stats":!0,onClear:de,onCopy:_e},null,8,["logs"])):f("",!0)]))}},wo=ee(yo,[["__scopeId","data-v-ef9b96fd"]]),_o={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},So={key:1,class:"mb-6"},To={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},$o={class:"flex items-center justify-between mb-3"},ko={class:"text-xs text-blue-600"},Co={class:"bg-white border border-blue-200 rounded-md p-3 max-h-32 overflow-y-auto"},Mo={class:"text-gray-700 text-sm leading-relaxed"},Eo={key:2,class:"current-actions mb-6"},Po={class:"flex space-x-3"},Lo=["disabled"],Uo={key:3,class:"mb-6"},Fo={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4"},Ao={class:"flex items-center justify-between mb-3"},zo={class:"text-xs text-yellow-600"},Io={class:"mb-3"},No={class:"flex items-center justify-between text-sm mb-1"},Ro={class:"text-yellow-700"},jo={class:"w-full bg-yellow-200 rounded-full h-2"},Oo={class:"text-sm text-yellow-600"},Bo={key:4,class:"mb-6"},Do={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Vo={class:"text-sm text-green-700 mb-3"},Wo={class:"bg-white rounded-md p-3 border border-green-200"},qo={class:"grid grid-cols-2 gap-4 text-sm"},Ho={class:"text-gray-800 font-medium ml-1"},Go={class:"text-gray-800 font-medium ml-1"},Jo={__name:"GenerateSubtitlesStep",setup(s){const t=ae(),o=A(0),n=A(""),r=p(()=>t.isLoading),i=p(()=>t.audioToTextResult&&t.audioToTextResult.transcript),g=p(()=>t.generatedSubtitles&&t.generatedSubtitles.length>0),u=p(()=>t.generatedSubtitles?t.generatedSubtitles.length:0),y=p(()=>{if(!i.value)return"";const R=t.audioToTextResult.transcript;return R.length>200?R.substring(0,200)+"...":R}),v=p(()=>i.value?t.audioToTextResult.transcript.length:0),m=p(()=>g.value?`成功将转录文本组成 ${u.value} 个字幕句子，可以进入下一步进行优化`:""),x=p(()=>{if(!g.value)return 0;const R=t.generatedSubtitles.reduce((F,V)=>F+(V.text?V.text.length:0),0);return Math.round(R/u.value)}),C=p(()=>t.progressUpdates.map(R=>({timestamp:new Date().toISOString(),level:R.includes("✅")?"success":R.includes("❌")?"error":R.includes("⚠️")?"warning":"info",message:R}))),b=R=>R.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}),M=async()=>{if(i.value){o.value=0,n.value="准备组成句子...";try{await t.generateSubtitles()}catch(R){console.error("生成字幕时出错:",R)}}},U=R=>{R.key==="goto-transcription"&&t.setCurrentStep(2)},w=()=>{t.setCurrentStep(5)},E=()=>{t.progressUpdates=[]},q=R=>{console.log("日志已复制:",R)};return(R,F)=>(l(),a("div",_o,[F[7]||(F[7]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 4: 组成句子"),e("p",{class:"text-sm text-gray-500 mt-1"},"将转录文本组成带时间戳的字幕句子")],-1)),i.value?f("",!0):(l(),Y(ge,{key:0,type:"warning",title:"需要先完成音频转录",message:"请先完成语音转文字处理，获取转录文本后再进行句子组成",actions:[{key:"goto-transcription",label:"前往语音转文字",icon:"🎤",primary:!0}],onAction:U})),i.value?(l(),a("div",So,[e("div",To,[e("div",$o,[F[0]||(F[0]=e("h4",{class:"text-sm font-medium text-blue-800"},"📝 转录文本预览",-1)),e("span",ko,c(v.value)+" 字符",1)]),e("div",Co,[e("p",Mo,c(y.value),1)])])])):f("",!0),i.value&&!r.value?(l(),a("div",Eo,[e("div",Po,[e("button",{onClick:M,disabled:r.value||g.value,class:"flex-1 px-6 py-3 text-white bg-yellow-600 rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 📝 "+c(g.value?"句子已组成":"开始组成句子"),9,Lo),g.value?(l(),a("button",{key:0,onClick:w,class:"px-6 py-3 text-yellow-700 bg-yellow-100 rounded-lg hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 font-medium transition-colors text-base"}," ➡️ 下一步 ")):f("",!0)])])):f("",!0),r.value?(l(),a("div",Uo,[e("div",Fo,[e("div",Ao,[F[1]||(F[1]=e("h4",{class:"text-sm font-medium text-yellow-800"},"📝 正在组成句子",-1)),e("span",zo,c(b(new Date)),1)]),e("div",Io,[e("div",No,[F[2]||(F[2]=e("span",{class:"text-yellow-700"},"📊 组成进度",-1)),e("span",Ro,c(Math.round(o.value||0))+"%",1)]),e("div",jo,[e("div",{class:"bg-yellow-600 h-2 rounded-full transition-all duration-300",style:pe({width:`${Math.min(100,Math.max(0,o.value||0))}%`})},null,4)])]),e("div",Oo," ⏱️ "+c(n.value||"正在将转录文本组成带时间戳的字幕句子..."),1)])])):f("",!0),g.value&&!r.value?(l(),a("div",Bo,[e("div",Do,[F[6]||(F[6]=e("div",{class:"flex items-center justify-between mb-3"},[e("h4",{class:"text-sm font-medium text-green-800"},"✅ 句子组成完成")],-1)),e("p",Vo,c(m.value),1),e("div",Wo,[F[5]||(F[5]=e("div",{class:"text-xs text-green-600 mb-1"},"📊 组成统计",-1)),e("div",qo,[e("div",null,[F[3]||(F[3]=e("span",{class:"text-gray-600"},"句子数量:",-1)),e("span",Ho,c(u.value),1)]),e("div",null,[F[4]||(F[4]=e("span",{class:"text-gray-600"},"平均长度:",-1)),e("span",Go,c(x.value)+"字",1)])])])])])):f("",!0),J(t).progressUpdates.length>0?(l(),Y(be,{key:5,title:"组成日志",logs:C.value,"initial-expanded":!1,"show-stats":!0,onClear:E,onCopy:q},null,8,["logs"])):f("",!0)]))}},Ko=ee(Jo,[["__scopeId","data-v-aa183229"]]),Yo={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},Xo={key:1,class:"mb-6"},Qo={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},Zo={class:"flex items-center justify-between mb-3"},er={class:"text-xs text-blue-600"},tr={class:"bg-white border border-blue-200 rounded-md p-3 max-h-32 overflow-y-auto"},sr={class:"space-y-1"},or={class:"text-gray-500 font-mono text-xs"},rr={class:"text-gray-700 ml-2"},nr={key:2,class:"current-actions mb-6"},ir={class:"bg-gray-50 rounded-lg p-4 mb-4"},lr={class:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"},ar={class:"space-y-3 mb-4"},ur={class:"grid grid-cols-2 md:grid-cols-3 gap-3"},dr={class:"flex items-center space-x-2 text-sm text-gray-700"},cr={class:"flex items-center space-x-2 text-sm text-gray-700"},gr={class:"flex items-center space-x-2 text-sm text-gray-700"},pr={class:"flex space-x-3"},mr=["disabled"],br=["disabled"],fr={key:3,class:"mb-6"},vr={class:"bg-orange-50 border border-orange-200 rounded-lg p-4"},xr={class:"flex items-center justify-between mb-3"},hr={class:"text-xs text-orange-600"},yr={class:"mb-3"},wr={class:"flex items-center justify-between text-sm mb-1"},_r={class:"text-orange-700"},Sr={class:"w-full bg-orange-200 rounded-full h-2"},Tr={class:"text-sm text-orange-600"},$r={key:0,class:"mt-3 p-3 bg-white rounded-md border border-orange-200"},kr={class:"space-y-1"},Cr={class:"text-sm text-gray-600"},Mr={class:"text-sm text-gray-800"},Er={key:4,class:"mb-6"},Pr={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Lr={class:"flex items-center justify-between mb-3"},Ur={class:"text-sm text-green-700 mb-3"},Fr={key:0,class:"space-y-3"},Ar={class:"grid grid-cols-2 md:grid-cols-4 gap-3"},zr={class:"text-center p-3 bg-white rounded-md border border-green-200"},Ir={class:"text-lg font-bold text-green-700"},Nr={class:"text-center p-3 bg-white rounded-md border border-green-200"},Rr={class:"text-lg font-bold text-green-700"},jr={class:"text-center p-3 bg-white rounded-md border border-green-200"},Or={class:"text-lg font-bold text-green-700"},Br={class:"text-center p-3 bg-white rounded-md border border-green-200"},Dr={class:"text-lg font-bold text-green-700"},Vr={__name:"OptimizeSubtitlesStep",setup(s){const t=ae(),o=A("gemini"),n=A("medium"),r=A(""),i=A(!1),g=A(0),u=A(""),y=A(null),v=Te({grammar:!0,punctuation:!0,expression:!0}),m=p(()=>t.isLoading),x=p(()=>t.generatedSubtitles&&t.generatedSubtitles.length>0),C=p(()=>t.generatedSubtitles?t.generatedSubtitles.length:0),b=p(()=>t.optimizedSubtitles&&t.optimizedSubtitles.length>0),M=p(()=>t.optimizedSubtitles?t.optimizedSubtitles.length:0),U=p(()=>x.value?t.generatedSubtitles.slice(0,5):[]),w=p(()=>b.value?`成功优化 ${M.value} 个字幕片段，提升了语法准确性和表达流畅度`:""),E=p(()=>({grammarFixes:Math.floor(M.value*.3),punctuationFixes:Math.floor(M.value*.2),expressionImprovements:Math.floor(M.value*.4)})),q=p(()=>t.progressUpdates.map(z=>({timestamp:new Date().toISOString(),level:z.includes("✅")?"success":z.includes("❌")?"error":z.includes("⚠️")?"warning":"info",message:z}))),R=z=>{if(z==null||z===void 0)return"00:00";if(typeof z=="number"){const h=Math.floor(z/1e3),$=Math.floor(h/60),_=h%60;return`${$.toString().padStart(2,"0")}:${_.toString().padStart(2,"0")}`}else{if(z instanceof Date)return z.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"});{const h=Number(z);if(!isNaN(h)){const $=Math.floor(h/1e3),_=Math.floor($/60),I=$%60;return`${_.toString().padStart(2,"0")}:${I.toString().padStart(2,"0")}`}return"00:00"}}},F=async()=>{if(!x.value)return;const z={model:o.value,level:n.value,options:v,customPrompt:r.value};g.value=0,u.value="准备优化...",y.value={original:"这个这个技术呢就是...",optimized:"这项技术的核心原理是..."};try{await t.optimizeSubtitles(z)}catch(h){console.error("优化字幕时出错:",h)}},V=()=>{t.setEditableSegments(t.generatedSubtitles),t.setCurrentStep(5)},O=()=>{i.value=!i.value},T=z=>{z.key==="goto-edit"&&t.setCurrentStep(3)},P=()=>{t.setCurrentStep(7)},G=()=>{t.progressUpdates=[]},Z=z=>{console.log("日志已复制:",z)};return(z,h)=>(l(),a("div",Yo,[h[25]||(h[25]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 5: 优化字幕"),e("p",{class:"text-sm text-gray-500 mt-1"},"使用AI优化字幕的语法和表达")],-1)),x.value?f("",!0):(l(),Y(ge,{key:0,type:"warning",title:"需要先完成字幕生成",message:"请先完成语音转文字和字幕编辑，准备好字幕内容后再进行优化",actions:[{key:"goto-edit",label:"前往字幕编辑",icon:"📝",primary:!0}],onAction:T})),x.value?(l(),a("div",Xo,[e("div",Qo,[e("div",Zo,[h[6]||(h[6]=e("h4",{class:"text-sm font-medium text-blue-800"},"📝 字幕内容预览",-1)),e("span",er,"共 "+c(C.value)+" 个片段",1)]),e("div",tr,[e("div",sr,[(l(!0),a(K,null,Q(U.value,($,_)=>(l(),a("div",{key:_,class:"text-sm"},[e("span",or,c(R($.startTimeMs))+" → "+c(R($.endTimeMs)),1),e("span",rr,c($.text),1)]))),128))])])])])):f("",!0),x.value&&!m.value?(l(),a("div",nr,[e("div",ir,[h[16]||(h[16]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"🎯 优化设置",-1)),e("div",lr,[e("div",null,[h[8]||(h[8]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"AI模型:",-1)),W(e("select",{"onUpdate:modelValue":h[0]||(h[0]=$=>o.value=$),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"},h[7]||(h[7]=[e("option",{value:"gemini"},"Gemini-2.5-Flash",-1),e("option",{value:"gpt"},"GPT-4",-1),e("option",{value:"claude"},"Claude-3",-1)]),512),[[se,o.value]])]),e("div",null,[h[10]||(h[10]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"优化强度:",-1)),W(e("select",{"onUpdate:modelValue":h[1]||(h[1]=$=>n.value=$),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"},h[9]||(h[9]=[e("option",{value:"light"},"轻度 - 仅修正错误",-1),e("option",{value:"medium"},"标准 - 改善语法流畅度",-1),e("option",{value:"heavy"},"深度 - 全面优化润色",-1)]),512),[[se,n.value]])])]),e("div",ar,[h[14]||(h[14]=e("h5",{class:"text-sm font-medium text-gray-700"},"优化目标:",-1)),e("div",ur,[e("label",dr,[W(e("input",{type:"checkbox","onUpdate:modelValue":h[2]||(h[2]=$=>v.grammar=$),class:"rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"},null,512),[[le,v.grammar]]),h[11]||(h[11]=e("span",null,"语法修正",-1))]),e("label",cr,[W(e("input",{type:"checkbox","onUpdate:modelValue":h[3]||(h[3]=$=>v.punctuation=$),class:"rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"},null,512),[[le,v.punctuation]]),h[12]||(h[12]=e("span",null,"断句优化",-1))]),e("label",gr,[W(e("input",{type:"checkbox","onUpdate:modelValue":h[4]||(h[4]=$=>v.expression=$),class:"rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"},null,512),[[le,v.expression]]),h[13]||(h[13]=e("span",null,"用词提升",-1))])])]),e("div",null,[h[15]||(h[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"自定义提示 (可选):",-1)),W(e("textarea",{"onUpdate:modelValue":h[5]||(h[5]=$=>r.value=$),placeholder:"例如：这是技术教程，请保持专业术语准确性...",class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 resize-none",rows:"2"},"          ",512),[[he,r.value]])])]),e("div",pr,[e("button",{onClick:F,disabled:m.value||b.value,class:"flex-1 px-6 py-3 text-white bg-orange-600 rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🤖 "+c(b.value?"已优化完成":"开始AI优化"),9,mr),e("button",{onClick:V,disabled:m.value,class:"px-6 py-3 text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," ⏭️ 跳过优化 ",8,br),b.value&&!m.value?(l(),a("button",{key:0,onClick:P,class:"px-6 py-3 text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 font-medium transition-colors text-base"}," ➡️ 下一步 ")):f("",!0)])])):f("",!0),m.value?(l(),a("div",fr,[e("div",vr,[e("div",xr,[h[17]||(h[17]=e("h4",{class:"text-sm font-medium text-orange-800"},"🤖 正在AI优化",-1)),e("span",hr,c(R(new Date)),1)]),e("div",yr,[e("div",wr,[h[18]||(h[18]=e("span",{class:"text-orange-700"},"📊 优化进度",-1)),e("span",_r,c(Math.round(g.value||0))+"%",1)]),e("div",Sr,[e("div",{class:"bg-orange-600 h-2 rounded-full transition-all duration-300",style:pe({width:`${Math.min(100,Math.max(0,g.value||0))}%`})},null,4)])]),e("div",Tr," ⏱️ "+c(u.value||"正在使用AI优化字幕语法和表达..."),1),y.value?(l(),a("div",$r,[h[19]||(h[19]=e("div",{class:"text-xs text-orange-600 mb-1"},"💡 优化预览",-1)),e("div",kr,[e("div",Cr,"原文: "+c(y.value.original),1),e("div",Mr,"优化: "+c(y.value.optimized),1)])])):f("",!0)])])):f("",!0),b.value&&!m.value?(l(),a("div",Er,[e("div",Pr,[e("div",Lr,[h[20]||(h[20]=e("h4",{class:"text-sm font-medium text-green-800"},"✅ AI优化完成",-1)),e("button",{onClick:O,class:"text-xs text-green-600 hover:text-green-800 focus:outline-none"},c(i.value?"隐藏详情":"查看详情")+" "+c(i.value?"▲":"▼"),1)]),e("p",Ur,c(w.value),1),i.value?(l(),a("div",Fr,[e("div",Ar,[e("div",zr,[e("div",Ir,c(M.value),1),h[21]||(h[21]=e("div",{class:"text-xs text-green-600"},"优化片段",-1))]),e("div",Nr,[e("div",Rr,c(E.value.grammarFixes||0),1),h[22]||(h[22]=e("div",{class:"text-xs text-green-600"},"语法修正",-1))]),e("div",jr,[e("div",Or,c(E.value.punctuationFixes||0),1),h[23]||(h[23]=e("div",{class:"text-xs text-green-600"},"标点优化",-1))]),e("div",Br,[e("div",Dr,c(E.value.expressionImprovements||0),1),h[24]||(h[24]=e("div",{class:"text-xs text-green-600"},"表达提升",-1))])])])):f("",!0)])])):f("",!0),J(t).progressUpdates.length>0?(l(),Y(be,{key:5,title:"优化日志",logs:q.value,"initial-expanded":!1,"show-stats":!0,onClear:G,onCopy:Z},null,8,["logs"])):f("",!0)]))}},Wr=ee(Vr,[["__scopeId","data-v-aaff5442"]]),qr={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},Hr={key:1,class:"current-actions mb-6"},Gr={class:"bg-gray-50 rounded-lg p-4 mb-4"},Jr={for:"useTranscription",class:"flex items-center space-x-2 text-sm text-gray-700"},Kr={key:0,class:"text-xs text-yellow-600 mt-1"},Yr={class:"flex flex-col lg:flex-row gap-4"},Xr={class:"lg:w-1/2"},Qr={class:"bg-white border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto"},Zr={class:"flex items-center justify-between mb-3"},en={class:"text-sm font-medium text-gray-700"},tn={class:"space-y-2"},sn=["onClick"],on={class:"flex justify-between items-start mb-1"},rn={class:"text-xs text-gray-500 font-mono"},nn=["onClick"],ln={class:"text-sm text-gray-700 leading-relaxed"},an={class:"lg:w-1/2"},un={class:"bg-white border border-gray-200 rounded-lg p-4 min-h-[300px]"},dn={key:0},cn={class:"text-sm font-medium text-gray-700 mb-3"},gn={class:"space-y-4"},pn=["for"],mn=["id"],bn={class:"grid grid-cols-2 gap-4"},fn=["for"],vn=["id"],xn=["for"],hn=["id"],yn={class:"flex justify-end space-x-3"},wn=["disabled"],_n={key:1,class:"flex items-center justify-center h-full text-gray-500"},Sn={key:2,class:"current-actions mb-6"},Tn={class:"bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center"},$n={class:"text-yellow-700 mb-4"},kn={class:"space-y-3"},Cn={for:"useTranscription",class:"flex items-center justify-center space-x-2 text-sm text-yellow-700"},Mn={key:0,class:"mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"},En={class:"flex items-center justify-between"},Pn={class:"text-sm text-green-600 mt-1"},Ln={__name:"EditSubtitlesStep",setup(s){const t=ae(),{isLoading:o,editableSegments:n,selectedSegmentId:r,audioToTextResult:i,useTranscriptionForEditing:g}=We(t),u=p({get:()=>t.useTranscriptionForEditing,set:z=>{t.setUseTranscriptionForEditing(z),z&&i.value?t.initializeEditableSegments():z||(x.value="",C.value=0,b.value=0,M.value=!1,t.setSelectedSegmentId(null))}}),y=p(()=>!!i.value&&(!!i.value.segments||typeof i.value.transcript=="string")),v=p(()=>{const z=y.value,h=t.generatedSubtitles&&t.generatedSubtitles.length>0,$=t.optimizedSubtitles&&t.optimizedSubtitles.length>0;return!z&&!h&&!$?{title:"需要先完成语音转文字",message:"请先完成语音转文字处理，生成字幕内容后再进行编辑",actionText:"前往语音转文字"}:null}),m=p(()=>!r.value||!n.value?null:n.value.find(z=>z.id===r.value)||null),x=A(""),C=A(0),b=A(0),M=A(!1);ce(m,z=>{z?(x.value=z.text,C.value=z.startTimeMs,b.value=z.endTimeMs,M.value=!1):(x.value="",C.value=0,b.value=0,M.value=!1)},{immediate:!0});const U=()=>{M.value=!0},w=()=>{if(!(!m.value||!M.value)){if(C.value<0||b.value<0||b.value<C.value){alert("时间设置无效。结束时间必须大于等于开始时间，且两者都不能为负。"),C.value=m.value.startTimeMs,b.value=m.value.endTimeMs;return}t.updateSegmentText({id:m.value.id,newText:x.value}),t.updateSegmentTime({id:m.value.id,newStartTimeMs:C.value,newEndTimeMs:b.value}),M.value=!1}},E=()=>{m.value&&(x.value=m.value.text,C.value=m.value.startTimeMs,b.value=m.value.endTimeMs,M.value=!1)},q=z=>{M.value&&!confirm("当前片段有未应用的更改，切换将会丢失这些更改。确定要切换吗？")||t.setSelectedSegmentId(z)},R=p(()=>n.value&&n.value.length>0),F=()=>{console.log("Attempting to initialize segments from EditSubtitlesStep..."),t.initializeEditableSegments()};Ae(()=>{u.value&&y.value&&!R.value?(console.log("EditSubtitlesStep: useTranscription is true and audioToTextResult available on mount, initializing segments."),t.initializeEditableSegments()):!y.value&&u.value?console.log("EditSubtitlesStep: useTranscription is true but audioToTextResult not available on mount."):u.value||console.log("EditSubtitlesStep: useTranscription is false on mount. Segments will not be auto-initialized.")});const V=p(()=>u.value?y.value?"没有可编辑的字幕片段。点击下方按钮尝试从转录结果初始化。":"没有可编辑的字幕片段。请确保上一步“语音转文字”已成功完成，或检查“使用上一步的转录结果”选项。":"没有可编辑的字幕片段。请取消勾选“使用上一步的转录结果进行编辑”以手动添加，或确保上一步已完成并勾选该选项。"),O=z=>{if(typeof z!="number"||isNaN(z))return"00:00.000";const h=new Date(z),$=String(h.getUTCMinutes()).padStart(2,"0"),_=String(h.getUTCSeconds()).padStart(2,"0"),I=String(h.getUTCMilliseconds()).padStart(3,"0");return`${$}:${_}.${I}`},T=z=>{z.key==="goto-transcription"&&t.setCurrentStep(2)},P=()=>{if(M.value){alert("请先应用或撤销当前片段的更改，然后进入下一步。");return}t.setCurrentStep(4)},G=()=>{let z=null,h=0;if(m.value)z=m.value.id,h=m.value.endTimeMs+1;else if(n.value&&n.value.length>0){const $=n.value[n.value.length-1];z=$.id,h=$.endTimeMs+1}t.addSegment({afterId:z,newSegmentData:{startTimeMs:h,endTimeMs:h+2e3,text:"新字幕片段"}})},Z=z=>{confirm(`确定要删除片段 #${z.substring(0,8)}... 吗？此操作无法撤销。`)&&t.deleteSegment(z)};return(z,h)=>(l(),a(K,null,[e("div",qr,[h[11]||(h[11]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 3: 编辑字幕"),e("p",{class:"text-sm text-gray-500 mt-1"},"编辑和调整字幕内容、时间戳")],-1)),v.value?(l(),Y(ge,{key:0,type:"warning",title:v.value.title,message:v.value.message,actions:[{key:"goto-transcription",label:v.value.actionText,icon:"🎤",primary:!0}],onAction:T},null,8,["title","message","actions"])):f("",!0),R.value?(l(),a("div",Hr,[e("div",Gr,[h[6]||(h[6]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"编辑设置",-1)),e("label",Jr,[W(e("input",{type:"checkbox",id:"useTranscription","onUpdate:modelValue":h[0]||(h[0]=$=>u.value=$),class:"rounded border-gray-300 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"},null,512),[[le,u.value]]),h[5]||(h[5]=e("span",null,"使用上一步的转录结果进行编辑",-1))]),u.value&&!y.value?(l(),a("p",Kr," 上一步未生成转录结果，或已被清除。 ")):f("",!0)]),e("div",Yr,[e("div",Xr,[e("div",Qr,[e("div",Zr,[e("h4",en,"字幕片段 ("+c(J(n).length)+")",1),e("button",{onClick:G,class:"px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm font-medium transition-colors"}," 添加片段 ")]),e("div",tn,[(l(!0),a(K,null,Q(J(n),$=>(l(),a("div",{key:$.id,onClick:_=>q($.id),class:H(["p-3 border rounded-md cursor-pointer hover:bg-gray-50 transition-colors",{"bg-red-50 border-red-300":$.id===J(r),"border-gray-200":$.id!==J(r)}])},[e("div",on,[e("span",rn,c(O($.startTimeMs))+" - "+c(O($.endTimeMs)),1),J(r)===$.id?(l(),a("button",{key:0,onClick:we(_=>Z($.id),["stop"]),class:"text-red-500 hover:text-red-700 text-sm p-1 rounded hover:bg-red-100",title:"删除片段"}," × ",8,nn)):f("",!0)]),e("p",ln,c($.text),1)],10,sn))),128))])])]),e("div",an,[e("div",un,[m.value?(l(),a("div",dn,[e("h4",cn," 编辑片段 #"+c(m.value.id.substring(0,8))+"... ",1),e("div",gn,[e("div",null,[e("label",{for:"segment-text-"+m.value.id,class:"block text-sm font-medium text-gray-700 mb-2"}," 文本内容 ",8,pn),W(e("textarea",{id:"segment-text-"+m.value.id,"onUpdate:modelValue":h[1]||(h[1]=$=>x.value=$),onInput:U,rows:"4",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500",placeholder:"输入字幕文本..."},null,40,mn),[[he,x.value]])]),e("div",bn,[e("div",null,[e("label",{for:"start-time-"+m.value.id,class:"block text-sm font-medium text-gray-700 mb-2"}," 开始时间 (ms) ",8,fn),W(e("input",{type:"number",id:"start-time-"+m.value.id,"onUpdate:modelValue":h[2]||(h[2]=$=>C.value=$),onInput:U,min:"0",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"},null,40,vn),[[he,C.value,void 0,{number:!0}]])]),e("div",null,[e("label",{for:"end-time-"+m.value.id,class:"block text-sm font-medium text-gray-700 mb-2"}," 结束时间 (ms) ",8,xn),W(e("input",{type:"number",id:"end-time-"+m.value.id,"onUpdate:modelValue":h[3]||(h[3]=$=>b.value=$),onInput:U,min:"0",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"},null,40,hn),[[he,b.value,void 0,{number:!0}]])])]),e("div",yn,[M.value?(l(),a("button",{key:0,onClick:E,class:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"}," 撤销 ")):f("",!0),e("button",{onClick:w,disabled:!M.value,class:"px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"}," 应用更改 ",8,wn)])])])):(l(),a("div",_n,h[7]||(h[7]=[e("div",{class:"text-center"},[e("svg",{class:"h-12 w-12 text-gray-400 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("p",null,"请从左侧列表中选择一个片段进行编辑")],-1)])))])])])])):R.value?f("",!0):(l(),a("div",Sn,[e("div",Tn,[h[9]||(h[9]=e("svg",{class:"h-12 w-12 text-yellow-500 mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),h[10]||(h[10]=e("h3",{class:"text-lg font-medium text-yellow-800 mb-2"},"没有可编辑的字幕片段",-1)),e("p",$n,c(V.value),1),e("div",kn,[e("label",Cn,[W(e("input",{type:"checkbox",id:"useTranscription","onUpdate:modelValue":h[4]||(h[4]=$=>u.value=$),class:"rounded border-yellow-300 text-yellow-600 shadow-sm focus:border-yellow-300 focus:ring focus:ring-yellow-200 focus:ring-opacity-50"},null,512),[[le,u.value]]),h[8]||(h[8]=e("span",null,"使用上一步的转录结果进行编辑",-1))]),u.value&&y.value?(l(),a("button",{key:0,onClick:F,class:"px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"}," 从转录结果初始化片段 ")):f("",!0)])])]))]),R.value&&!M.value?(l(),a("div",Mn,[e("div",En,[e("div",null,[h[12]||(h[12]=e("h4",{class:"text-sm font-medium text-green-800"},"字幕编辑完成",-1)),e("p",Pn,"已编辑 "+c(J(n).length)+" 个字幕片段，可以进入下一步",1)]),e("button",{onClick:P,class:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," ➡️ 进入下一步 ")])])):f("",!0)],64))}},Un=ee(Ln,[["__scopeId","data-v-fc164d38"]]),Fn={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},An={key:0,class:"previous-results mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"},zn={class:"flex items-center justify-between"},In={class:"text-xs text-green-600"},Nn={key:1,class:"prerequisite-warning mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg"},Rn={class:"flex items-start"},jn={class:"flex-1"},On={key:2,class:"current-actions mb-6"},Bn={class:"bg-gray-50 rounded-lg p-4 mb-6"},Dn={class:"bg-white border border-gray-200 rounded-md p-3 max-h-32 overflow-y-auto"},Vn={class:"space-y-1"},Wn={class:"text-gray-500 font-mono text-xs"},qn={class:"text-gray-700 ml-2"},Hn={class:"text-sm text-gray-500 mt-2"},Gn={class:"space-y-6"},Jn={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Kn={class:"text-center"},Yn={class:"text-center"},Xn={key:0,class:"bg-indigo-50 border border-indigo-200 rounded-lg p-4 space-y-4"},Qn={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},Zn={class:"flex justify-between items-center pt-4"},ei={class:"flex space-x-3"},ti={__name:"TranslationChoiceStep",setup(s){const t=ae(),o=A(!1),n=A("zh"),r=A("balanced"),i=A("formal"),g=A(""),u=p(()=>t.editableSegments&&t.editableSegments.length>0),y=p(()=>t.editableSegments?t.editableSegments.length:0),v=p(()=>u.value?t.editableSegments.slice(0,5):[]),m=U=>{const w=Math.floor(U/1e3),E=Math.floor(w/60),q=w%60;return`${E.toString().padStart(2,"0")}:${q.toString().padStart(2,"0")}`},x=U=>{o.value=U},C=()=>{t.setTranslationSettings({targetLanguage:n.value,quality:r.value,style:i.value,customPrompt:g.value}),t.setCurrentStep(8)},b=()=>{t.setCurrentStep(6)},M=()=>{t.setCurrentStep(5)};return(U,w)=>(l(),a("div",Fn,[w[27]||(w[27]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 7: 翻译选择"),e("p",{class:"text-sm text-gray-500 mt-1"},"选择是否需要翻译字幕以及翻译设置")],-1)),u.value?(l(),a("div",An,[e("div",zn,[w[7]||(w[7]=e("div",{class:"flex items-center"},[e("svg",{class:"h-4 w-4 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})]),e("span",{class:"text-sm font-medium text-green-800"},"字幕编辑已完成")],-1)),e("span",In,"已编辑 "+c(y.value)+" 个字幕片段",1)])])):f("",!0),u.value?f("",!0):(l(),a("div",Nn,[e("div",Rn,[w[10]||(w[10]=e("svg",{class:"h-5 w-5 text-yellow-600 mr-3 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",jn,[w[8]||(w[8]=e("h4",{class:"text-sm font-medium text-yellow-800"},"需要先完成字幕编辑",-1)),w[9]||(w[9]=e("p",{class:"text-sm text-yellow-700 mt-1"},"请先在步骤6中完成字幕编辑，准备好字幕内容后再选择是否翻译",-1)),e("button",{onClick:w[0]||(w[0]=E=>U.handlePrerequisiteAction({step:6})),class:"mt-2 text-sm text-yellow-800 underline hover:text-yellow-900 transition-colors"}," 前往字幕编辑 ")])])])),u.value?(l(),a("div",On,[e("div",Bn,[w[11]||(w[11]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"当前字幕预览",-1)),e("div",Dn,[e("div",Vn,[(l(!0),a(K,null,Q(v.value,(E,q)=>(l(),a("div",{key:q,class:"text-sm"},[e("span",Wn,c(m(E.startTimeMs))+" → "+c(m(E.endTimeMs)),1),e("span",qn,c(E.text),1)]))),128))])]),e("p",Hn,"共 "+c(y.value)+" 个字幕片段",1)]),e("div",Gn,[w[26]||(w[26]=e("div",{class:"text-center mb-6"},[e("h3",{class:"text-lg font-semibold text-gray-800 mb-2"},"是否需要翻译字幕？"),e("p",{class:"text-gray-600"},"您可以选择将字幕翻译成其他语言，或直接进入导出步骤")],-1)),e("div",Jn,[e("div",{class:H(["border-2 rounded-lg p-4 cursor-pointer transition-all",{"border-green-500 bg-green-50":!o.value,"border-gray-200 hover:border-gray-300":o.value}]),onClick:w[1]||(w[1]=E=>x(!1))},[e("div",Kn,[e("div",{class:H(["w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3",{"bg-green-100":!o.value,"bg-gray-100":o.value}])},[(l(),a("svg",{class:H(["w-6 h-6",{"text-green-600":!o.value,"text-gray-500":o.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},w[12]||(w[12]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"},null,-1)]),2))],2),w[13]||(w[13]=e("h4",{class:"font-semibold text-gray-800 mb-1"},"不需要翻译",-1)),w[14]||(w[14]=e("p",{class:"text-gray-600 text-sm"},"直接使用当前语言的字幕",-1))])],2),e("div",{class:H(["border-2 rounded-lg p-4 cursor-pointer transition-all",{"border-indigo-500 bg-indigo-50":o.value,"border-gray-200 hover:border-gray-300":!o.value}]),onClick:w[2]||(w[2]=E=>x(!0))},[e("div",Yn,[e("div",{class:H(["w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3",{"bg-indigo-100":o.value,"bg-gray-100":!o.value}])},[(l(),a("svg",{class:H(["w-6 h-6",{"text-indigo-600":o.value,"text-gray-500":!o.value}]),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},w[15]||(w[15]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"},null,-1)]),2))],2),w[16]||(w[16]=e("h4",{class:"font-semibold text-gray-800 mb-1"},"需要翻译",-1)),w[17]||(w[17]=e("p",{class:"text-gray-600 text-sm"},"将字幕翻译成其他语言",-1))])],2)]),o.value?(l(),a("div",Xn,[w[25]||(w[25]=e("h4",{class:"text-sm font-medium text-indigo-800 mb-3"},"翻译设置",-1)),e("div",Qn,[e("div",null,[w[19]||(w[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"目标语言",-1)),W(e("select",{"onUpdate:modelValue":w[3]||(w[3]=E=>n.value=E),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white"},w[18]||(w[18]=[ye('<option value="zh" data-v-b63b7ec7>中文 (简体)</option><option value="zh-TW" data-v-b63b7ec7>中文 (繁体)</option><option value="en" data-v-b63b7ec7>English</option><option value="ja" data-v-b63b7ec7>日本語</option><option value="ko" data-v-b63b7ec7>한국어</option><option value="es" data-v-b63b7ec7>Español</option><option value="fr" data-v-b63b7ec7>Français</option><option value="de" data-v-b63b7ec7>Deutsch</option><option value="ru" data-v-b63b7ec7>Русский</option><option value="ar" data-v-b63b7ec7>العربية</option>',10)]),512),[[se,n.value]])]),e("div",null,[w[21]||(w[21]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"翻译质量",-1)),W(e("select",{"onUpdate:modelValue":w[4]||(w[4]=E=>r.value=E),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white"},w[20]||(w[20]=[e("option",{value:"fast"},"快速翻译 - 速度优先",-1),e("option",{value:"balanced"},"平衡模式 - 速度与质量兼顾",-1),e("option",{value:"quality"},"高质量 - 质量优先",-1)]),512),[[se,r.value]])])]),e("div",null,[w[23]||(w[23]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"翻译风格",-1)),W(e("select",{"onUpdate:modelValue":w[5]||(w[5]=E=>i.value=E),class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white"},w[22]||(w[22]=[e("option",{value:"formal"},"正式 - 适合商务、学术内容",-1),e("option",{value:"casual"},"随意 - 适合日常对话",-1),e("option",{value:"literary"},"文学 - 适合文艺作品",-1),e("option",{value:"technical"},"技术 - 适合技术文档",-1)]),512),[[se,i.value]])]),e("div",null,[w[24]||(w[24]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"自定义翻译提示 (可选)",-1)),W(e("textarea",{"onUpdate:modelValue":w[6]||(w[6]=E=>g.value=E),placeholder:"例如：这是一个烹饪教程，请保持食材和烹饪术语的准确性...",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 resize-none bg-white",rows:"3"},"            ",512),[[he,g.value]])])])):f("",!0),e("div",Zn,[e("button",{onClick:M,class:"px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium transition-colors text-base"}," ← 返回编辑 "),e("div",ei,[o.value===!1?(l(),a("button",{key:0,onClick:b,class:"px-6 py-3 text-white bg-green-600 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 font-medium transition-colors text-base"}," 📥 进入导出 ")):f("",!0),o.value===!0&&n.value?(l(),a("button",{key:1,onClick:C,class:"px-6 py-3 text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium transition-colors text-base"}," 🌐 开始翻译 ")):f("",!0)])])])])):f("",!0)]))}},si=ee(ti,[["__scopeId","data-v-b63b7ec7"]]),oi={key:0,class:"progress-indicator"},ri={class:"flex items-center justify-center py-8"},ni={class:"text-center"},ii={class:"relative"},li={class:"text-lg font-medium text-gray-700 mb-2"},ai={class:"text-sm text-gray-500 mb-4"},ui={key:0,class:"w-64 mx-auto"},di={class:"bg-gray-200 rounded-full h-2 mb-2"},ci={class:"text-xs text-gray-500"},gi={key:1,class:"mt-4 text-xs text-gray-400 max-w-sm mx-auto"},pi={__name:"ProgressIndicator",props:{isVisible:{type:Boolean,default:!1},title:{type:String,default:"处理中..."},message:{type:String,default:"请稍候"},percentage:{type:Number,default:-1},progressText:{type:String,default:"完成"},details:{type:String,default:""},theme:{type:String,default:"blue",validator:s=>["blue","green","indigo","purple","red","yellow","orange","pink","gray"].includes(s)}},setup(s){const t=s,o=p(()=>t.percentage>=0&&t.percentage<=100),n=p(()=>t.percentage<0?0:t.percentage>100?100:Math.round(t.percentage)),r=p(()=>{const g={blue:"text-blue-500",green:"text-green-500",indigo:"text-indigo-500",purple:"text-purple-500",red:"text-red-500",yellow:"text-yellow-500",orange:"text-orange-500",pink:"text-pink-500",gray:"text-gray-500"};return g[t.theme]||g.blue}),i=p(()=>{const g={blue:"bg-blue-600",green:"bg-green-600",indigo:"bg-indigo-600",purple:"bg-purple-600",red:"bg-red-600",yellow:"bg-yellow-600",orange:"bg-orange-600",pink:"bg-pink-600",gray:"bg-gray-600"};return g[t.theme]||g.blue});return(g,u)=>s.isVisible?(l(),a("div",oi,[e("div",ri,[e("div",ni,[e("div",ii,[(l(),a("svg",{class:H(["animate-spin h-10 w-10 mx-auto mb-4",r.value]),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},u[0]||(u[0]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]),2))]),e("h3",li,c(s.title),1),e("p",ai,c(s.message),1),o.value?(l(),a("div",ui,[e("div",di,[e("div",{class:H(["h-2 rounded-full transition-all duration-300 ease-out",i.value]),style:pe({width:n.value+"%"})},null,6)]),e("div",ci,c(n.value)+"% "+c(s.progressText),1)])):f("",!0),s.details?(l(),a("div",gi,c(s.details),1)):f("",!0)])])])):f("",!0)}},mi=ee(pi,[["__scopeId","data-v-3c27b7b4"]]),bi={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},fi={key:0,class:"previous-results mb-6 p-4 bg-green-50 border border-green-200 rounded-lg"},vi={class:"flex items-center justify-between"},xi={class:"text-xs text-green-600"},hi={key:1,class:"prerequisite-warning mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg"},yi={class:"flex items-start"},wi={class:"flex-1"},_i={key:2,class:"current-actions mb-6"},Si={class:"bg-gray-50 rounded-lg p-4 mb-4"},Ti={class:"grid grid-cols-2 md:grid-cols-4 gap-3 text-sm"},$i={class:"bg-white p-2 rounded border"},ki={class:"text-pink-700 font-semibold"},Ci={class:"bg-white p-2 rounded border"},Mi={class:"text-pink-700 font-semibold"},Ei={class:"bg-white p-2 rounded border"},Pi={class:"text-pink-700 font-semibold"},Li={class:"bg-white p-2 rounded border"},Ui={class:"text-pink-700 font-semibold"},Fi={class:"bg-gray-50 rounded-lg p-4 mb-4"},Ai={class:"bg-white border border-gray-200 rounded-md p-3 max-h-32 overflow-y-auto"},zi={class:"space-y-1"},Ii={class:"text-gray-500 font-mono text-xs"},Ni={class:"text-gray-700 ml-2"},Ri=["disabled"],ji={key:3,class:"translation-results mb-6"},Oi={class:"bg-gray-50 rounded-lg p-4"},Bi={class:"bg-white border border-gray-200 rounded-md p-3 max-h-48 overflow-y-auto"},Di={class:"space-y-3"},Vi={class:"text-xs text-gray-500 font-mono mb-1"},Wi={class:"text-sm text-gray-600 mb-1"},qi={class:"text-sm text-pink-800"},Hi={class:"mt-3 mb-4"},Gi={class:"text-sm text-gray-600"},Ji={class:"space-y-3"},Ki={class:"flex justify-between space-x-3"},Yi=["disabled"],Xi=["disabled"],Qi={__name:"TranslationStep",setup(s){const t=ae(),o=p(()=>t.isLoading),n=p(()=>t.editableSegments&&t.editableSegments.length>0),r=p(()=>t.editableSegments?t.editableSegments.length:0),i=p(()=>t.translatedSubtitles&&t.translatedSubtitles.length>0),g=p(()=>t.translatedSubtitles?t.translatedSubtitles.length:0),u=p(()=>t.translationProgress),y=p(()=>t.translationProgressPercent);p(()=>t.progressUpdates);const v=p(()=>t.translationSettings||{}),m=p(()=>n.value?t.editableSegments.slice(0,3):[]),x=p(()=>i.value?t.translatedSubtitles.slice(0,3):[]),C=p(()=>t.progressUpdates.map(T=>({timestamp:new Date().toISOString(),level:T.includes("✅")?"success":T.includes("❌")?"error":T.includes("⚠️")?"warning":"info",message:T}))),b=T=>{const P=Math.floor(T/1e3),G=Math.floor(P/60),Z=P%60;return`${G.toString().padStart(2,"0")}:${Z.toString().padStart(2,"0")}`},M=T=>({zh:"中文 (简体)","zh-TW":"中文 (繁体)",en:"English",ja:"日本語",ko:"한국어",es:"Español",fr:"Français",de:"Deutsch",ru:"Русский",ar:"العربية"})[T]||T,U=T=>({fast:"快速翻译",balanced:"平衡模式",quality:"高质量"})[T]||T,w=T=>({formal:"正式",casual:"随意",literary:"文学",technical:"技术"})[T]||T,E=async()=>{try{await t.translateSubtitles()}catch(T){console.error("翻译字幕时出错:",T)}},q=async()=>{try{await t.translateSubtitles(!0)}catch(T){console.error("重新翻译字幕时出错:",T)}},R=()=>{t.setCurrentStep(7)},F=()=>{t.setCurrentStep(9)},V=()=>{t.progressUpdates=[]},O=T=>{console.log("日志已复制:",T)};return(T,P)=>(l(),a("div",bi,[P[15]||(P[15]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 8: 翻译处理"),e("p",{class:"text-sm text-gray-500 mt-1"},"将字幕翻译成目标语言")],-1)),n.value?(l(),a("div",fi,[e("div",vi,[P[1]||(P[1]=e("div",{class:"flex items-center"},[e("svg",{class:"h-4 w-4 text-green-600 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})]),e("span",{class:"text-sm font-medium text-green-800"},"翻译选择已完成")],-1)),e("span",xi,"目标语言: "+c(M(v.value.targetLanguage)),1)])])):f("",!0),n.value?f("",!0):(l(),a("div",hi,[e("div",yi,[P[4]||(P[4]=e("svg",{class:"h-5 w-5 text-yellow-600 mr-3 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})],-1)),e("div",wi,[P[2]||(P[2]=e("h4",{class:"text-sm font-medium text-yellow-800"},"需要先完成翻译选择",-1)),P[3]||(P[3]=e("p",{class:"text-sm text-yellow-700 mt-1"},"请先在步骤7中完成翻译选择，设置翻译参数后再开始翻译",-1)),e("button",{onClick:P[0]||(P[0]=G=>T.handlePrerequisiteAction({step:7})),class:"mt-2 text-sm text-yellow-800 underline hover:text-yellow-900 transition-colors"}," 前往翻译选择 ")])])])),n.value&&!i.value?(l(),a("div",_i,[e("div",Si,[P[9]||(P[9]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"翻译设置",-1)),e("div",Ti,[e("div",$i,[P[5]||(P[5]=e("span",{class:"text-gray-600 font-medium"},"目标语言:",-1)),e("div",ki,c(M(v.value.targetLanguage)),1)]),e("div",Ci,[P[6]||(P[6]=e("span",{class:"text-gray-600 font-medium"},"翻译质量:",-1)),e("div",Mi,c(U(v.value.quality)),1)]),e("div",Ei,[P[7]||(P[7]=e("span",{class:"text-gray-600 font-medium"},"翻译风格:",-1)),e("div",Pi,c(w(v.value.style)),1)]),e("div",Li,[P[8]||(P[8]=e("span",{class:"text-gray-600 font-medium"},"字幕数量:",-1)),e("div",Ui,c(r.value)+" 个",1)])])]),e("div",Fi,[P[10]||(P[10]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"原文字幕预览",-1)),e("div",Ai,[e("div",zi,[(l(!0),a(K,null,Q(m.value,(G,Z)=>(l(),a("div",{key:Z,class:"text-sm"},[e("span",Ii,c(b(G.startTimeMs))+" → "+c(b(G.endTimeMs)),1),e("span",Ni,c(G.text),1)]))),128))])])]),e("button",{onClick:E,disabled:o.value,class:"w-full px-4 py-3 text-white bg-pink-600 rounded-lg hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors"},[P[11]||(P[11]=e("svg",{class:"h-4 w-4 mr-2 inline",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})],-1)),ue(" "+c(o.value?"正在翻译...":`开始翻译成${M(v.value.targetLanguage)}`),1)],8,Ri)])):f("",!0),i.value?(l(),a("div",ji,[e("div",Oi,[P[14]||(P[14]=e("h4",{class:"text-sm font-medium text-gray-700 mb-3"},"翻译结果预览",-1)),e("div",Bi,[e("div",Di,[(l(!0),a(K,null,Q(x.value,(G,Z)=>(l(),a("div",{key:Z,class:"border-b border-gray-200 pb-2 last:border-b-0"},[e("div",Vi,c(b(G.startTimeMs))+" → "+c(b(G.endTimeMs)),1),e("div",Wi,[P[12]||(P[12]=e("span",{class:"font-medium text-gray-500"},"原文:",-1)),ue(" "+c(G.originalText),1)]),e("div",qi,[P[13]||(P[13]=e("span",{class:"font-medium text-pink-600"},"译文:",-1)),ue(" "+c(G.translatedText),1)])]))),128))])]),e("div",Hi,[e("span",Gi,"共翻译 "+c(g.value)+" 个字幕片段",1)]),e("div",Ji,[e("div",{class:"flex justify-start"},[e("button",{onClick:R,class:"px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium transition-colors text-base"}," ← 返回选择 ")]),e("div",Ki,[e("button",{onClick:q,disabled:o.value,class:"flex-1 px-6 py-3 text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 🔄 重新翻译 ",8,Yi),e("button",{onClick:F,disabled:o.value,class:"flex-1 px-6 py-3 text-white bg-pink-600 rounded-lg hover:bg-pink-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," ➡️ 进入导出 ",8,Xi)])])])])):f("",!0),o.value?(l(),Y(mi,{key:4,"is-visible":!0,title:"正在翻译字幕",message:u.value||"正在处理字幕翻译，请稍候...",percentage:y.value,"progress-text":"已翻译",theme:"pink",details:n.value?`共 ${r.value} 个字幕片段`:""},null,8,["message","percentage","details"])):f("",!0),J(t).progressUpdates.length>0?(l(),Y(be,{key:5,title:"翻译日志",logs:C.value,"initial-expanded":!1,"show-stats":!0,onClear:V,onCopy:O},null,8,["logs"])):f("",!0)]))}},Zi=ee(Qi,[["__scopeId","data-v-e65ec078"]]),el={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},tl={key:0,class:"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg"},sl={class:"flex items-center justify-between"},ol={class:"text-sm text-blue-600"},rl={key:2,class:"current-actions mb-6"},nl={class:"bg-gray-50 rounded-lg p-4 mb-4"},il={class:"grid grid-cols-1 md:grid-cols-2 gap-3"},ll=["value"],al={class:"flex-1"},ul={class:"text-sm font-medium text-gray-800"},dl={class:"text-xs text-gray-600"},cl={class:"bg-gray-50 rounded-lg p-4 mb-4"},gl={class:"grid grid-cols-2 md:grid-cols-4 gap-3"},pl=["value"],ml={class:"text-center"},bl={class:"text-sm font-medium text-gray-800"},fl={class:"text-xs text-gray-600"},vl={key:0,class:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4"},xl={class:"text-sm font-medium text-blue-800 mb-3"},hl={class:"bg-white rounded-md p-3 border border-blue-200"},yl={class:"space-y-1 max-h-32 overflow-y-auto"},wl={class:"mt-3 pt-3 border-t border-blue-200 text-xs text-blue-600"},_l={key:1,class:"bg-gray-50 rounded-lg p-4 mb-4"},Sl={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Tl={class:"mt-3"},$l={class:"flex items-center space-x-2 text-sm text-gray-700"},kl={class:"bg-gray-50 rounded-lg p-4 mb-4"},Cl={class:"space-y-3"},Ml={class:"flex items-center space-x-2 text-sm text-gray-700"},El={key:0,class:"ml-6 text-xs text-gray-600"},Pl={class:"flex space-x-3"},Ll=["disabled"],Ul=["disabled"],Fl={key:3,class:"mb-6"},Al={class:"bg-blue-50 border border-blue-200 rounded-lg p-4"},zl={class:"flex items-center justify-between mb-3"},Il={class:"text-xs text-blue-600"},Nl={class:"mb-3"},Rl={class:"flex items-center justify-between text-sm mb-1"},jl={class:"text-blue-700"},Ol={class:"w-full bg-blue-200 rounded-full h-2"},Bl={class:"text-sm text-blue-600"},Dl={key:0,class:"mt-3 space-y-1"},Vl={class:"text-gray-600"},Wl={key:4,class:"mb-6"},ql={class:"bg-green-50 border border-green-200 rounded-lg p-4"},Hl={class:"flex items-center justify-between mb-3"},Gl={class:"text-sm text-green-700 mb-3"},Jl={key:0,class:"space-y-3"},Kl={class:"bg-white rounded-md p-3 border border-green-200"},Yl={class:"space-y-1 max-h-32 overflow-y-auto"},Xl={class:"text-gray-700 font-mono"},Ql={class:"text-gray-500"},Zl={class:"flex items-center justify-between text-xs text-green-600"},ea={key:0,class:"mt-8 flex justify-center"},ta={class:"p-6 border-b border-gray-200"},sa={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},oa=["value"],ra=["value"],na={class:"p-6 overflow-y-auto max-h-96"},ia={key:0,class:"bg-gray-50 rounded-lg p-4"},la={class:"flex items-center justify-between mb-3"},aa={class:"text-sm font-medium text-gray-700"},ua={class:"text-xs text-gray-500"},da={class:"text-sm text-gray-800 whitespace-pre-wrap font-mono bg-white border border-gray-200 rounded p-3 overflow-x-auto"},ca={key:1,class:"text-center text-gray-500 py-8"},ga={__name:"ExportStep",setup(s){const t=ae(),o=A(["source_only"]),n=A(["srt"]),r=A(!1),i=A(0),g=A(""),u=A([]),y=A([]),v=A(!1),m=A(!1),x=A("source_only"),C=A("srt"),b=A(""),M=Te({separator:"newline",originalFont:"16px",translationFont:"14px",addLanguageLabels:!1}),U=p(()=>{var D;const S=((D=t.translatedSubtitles)==null?void 0:D.length)>0,d=F(),N=[{value:"source_only",label:`仅原文 (${d.sourceName})`,description:"只包含原始语言内容"}];return S&&N.push({value:"target_only",label:`仅译文 (${d.targetName})`,description:"只包含翻译语言内容"},{value:"source_first",label:`原文在上 (${d.sourceName}在上，${d.targetName}在下)`,description:"原文在上，译文在下"},{value:"target_first",label:`译文在上 (${d.targetName}在上，${d.sourceName}在下)`,description:"译文在上，原文在下"}),N}),w=p(()=>[{value:"srt",label:"SRT",extension:".srt",description:"最常用的字幕格式"},{value:"vtt",label:"VTT",extension:".vtt",description:"Web视频字幕格式"},{value:"ass",label:"ASS",extension:".ass",description:"高级字幕格式"},{value:"txt",label:"TXT",extension:".txt",description:"纯文本格式"}]),E=p(()=>t.isLoading),q=p(()=>t.hasEditableSegments),R=S=>({zh:"中文 (简体)","zh-TW":"中文 (繁体)",en:"English",ja:"日本語",ko:"한국어",es:"Español",fr:"Français",de:"Deutsch",ru:"Русский",ar:"العربية"})[S]||S,F=()=>{const S=t.translationSettings;if(S&&S.targetLanguage){const d=S.sourceLanguage||"en",N=S.targetLanguage;return{source:d,target:N,sourceName:R(d),targetName:R(N)}}if(t.editableSegments&&t.editableSegments.length>0){const d=t.editableSegments[0];if(d.translatedText||d.translation){const N=d.text||"",D=d.translatedText||d.translation||"",j=ie=>/[\u4e00-\u9fff]/.test(ie)?"zh":/[a-zA-Z]/.test(ie)?"en":/[\u3040-\u309f\u30a0-\u30ff]/.test(ie)?"ja":/[\uac00-\ud7af]/.test(ie)?"ko":"en",k=j(N),X=j(D);return{source:k,target:X,sourceName:R(k),targetName:R(X)}}}return{source:"en",target:"zh",sourceName:"English",targetName:"中文 (简体)"}},V=p(()=>{var N;const S=((N=t.translatedSubtitles)==null?void 0:N.length)>0,d=F();return S?`原文: ${d.sourceName} | 译文: ${d.targetName}`:`仅${d.sourceName}`}),O=p(()=>o.value.length*n.value.length),T=p(()=>{var N,D;const S=[],d=((N=t.getUploadedFileName)==null?void 0:N.replace(/\.[^/.]+$/,""))||"video";for(const j of o.value)for(const k of n.value){const X=_(j),ie=((D=w.value.find(me=>me.value===k))==null?void 0:D.extension)||".srt";S.push(`${d}_${X}${ie}`)}return S}),P=p(()=>{var j;const D=(((j=t.editableSegments)==null?void 0:j.length)||0)*50*o.value.length*2;return D<1024?`${D}B`:D<1024*1024?`${(D/1024).toFixed(1)}KB`:`${(D/1024/1024).toFixed(1)}MB`}),G=p(()=>o.value.some(S=>S==="source_first"||S==="target_first")),Z=p(()=>{if(y.value.length===0)return"0B";const S=y.value.reduce((d,N)=>{const D=N.size.match(/(\d+(?:\.\d+)?)\s*([KMGT]?B)/i);if(D){const j=parseFloat(D[1]),k=D[2].toUpperCase(),X={B:1,KB:1024,MB:1024*1024,GB:1024*1024*1024};return d+j*(X[k]||1)}return d},0);return S<1024?`${S}B`:S<1024*1024?`${(S/1024).toFixed(1)}KB`:`${(S/1024/1024).toFixed(1)}MB`}),z=p(()=>t.progressUpdates.map(S=>({timestamp:new Date().toISOString(),level:S.includes("✅")?"success":S.includes("❌")?"error":S.includes("⚠️")?"warning":"info",message:S}))),h=p(()=>y.value.length>0&&!E.value),$=S=>{if(typeof S=="number"){const d=Math.floor(S/1e3),N=Math.floor(d/3600),D=Math.floor(d%3600/60),j=d%60;return N>0?`${N.toString().padStart(2,"0")}:${D.toString().padStart(2,"0")}:${j.toString().padStart(2,"0")}`:`${D.toString().padStart(2,"0")}:${j.toString().padStart(2,"0")}`}else return S instanceof Date?S.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"}):"00:00"},_=S=>({source_only:"仅原文",target_only:"仅译文",source_first:"原文在上",target_first:"译文在上"})[S]||S,I=S=>({completed:"text-green-600",processing:"text-blue-600",waiting:"text-gray-600",error:"text-red-600"})[S]||"text-gray-600",te=S=>({completed:"✅",processing:"🔄",waiting:"⏳",error:"❌"})[S]||"",oe=S=>{S.key==="goto-edit"&&t.setCurrentStep(3)},ne=async()=>{var N,D;if(o.value.length===0){alert("请至少选择一个内容类型");return}if(n.value.length===0){alert("请至少选择一个导出格式");return}i.value=0,g.value="准备导出...",u.value=[],y.value=[];const S=[],d=((N=t.getUploadedFileName)==null?void 0:N.replace(/\.[^/.]+$/,""))||"video";for(const j of o.value)for(const k of n.value){const X=_(j),ie=((D=w.value.find(Be=>Be.value===k))==null?void 0:D.extension)||".srt",me=`${d}_${X}${ie}`;S.push({content:j,format:k,filename:me,status:"waiting"})}u.value=S.map(j=>({file:j.filename,status:"waiting"}));try{for(let j=0;j<S.length;j++){const k=S[j];u.value[j].status="processing",g.value=`正在生成 ${k.filename}...`,i.value=j/S.length*100,await new Promise(me=>setTimeout(me,1e3)),t.setExportFormat(k.format),t.setExportFilename(k.filename.replace(/\.[^/.]+$/,""));const X={source_only:"editable_segments",target_only:"translation_result",source_first:"editable_segments",target_first:"editable_segments"};t.setExportContentSource(X[k.content]||"editable_segments");const ie={source_only:"仅原文",target_only:"仅译文",source_first:"原文在上",target_first:"译文在上"};t.setExportLayout(ie[k.content]||"仅原文");try{t.setExportAutoSaveToDefault(v.value),await t.exportSubtitles({filename:k.filename.replace(/\.[^/.]+$/,"")}),t.lastExportPath?(u.value[j].status="completed",y.value.push({filename:k.filename,path:t.lastExportPath,size:"未知",directory:t.lastExportPath.substring(0,t.lastExportPath.lastIndexOf("/"))})):u.value[j].status="error"}catch(me){console.error(`导出 ${k.filename} 失败:`,me),u.value[j].status="error"}}i.value=100,g.value="导出完成！"}catch(j){console.error("批量导出失败:",j),g.value="导出失败"}},xe=()=>{x.value=o.value[0]||"source_only",C.value=n.value[0]||"srt",b.value="",m.value=!0},de=()=>{m.value=!1,b.value=""},_e=()=>{if(!t.editableSegments||t.editableSegments.length===0){b.value="没有可预览的字幕内容";return}const S=t.editableSegments.slice(0,10);let d="";switch(C.value){case"srt":d=B(S);break;case"vtt":d=L(S);break;case"ass":d=re(S);break;case"txt":d=Se(S);break;default:d=B(S)}b.value=d},B=S=>S.map((d,N)=>{const D=ke(d.startTimeMs),j=ke(d.endTimeMs),k=fe(d);return`${N+1}
${D} --> ${j}
${k}
`}).join(`
`),L=S=>{let d=`WEBVTT

`;return d+=S.map((N,D)=>{const j=Ce(N.startTimeMs),k=Ce(N.endTimeMs),X=fe(N);return`${j} --> ${k}
${X}
`}).join(`
`),d},re=S=>{let d=`[Script Info]
Title: 字幕预览

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,20,&H00FFFFFF,&H000000FF,&H00000000,&H80000000,0,0,0,0,100,100,0,0,1,2,0,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`;return d+=S.map(N=>{const D=Me(N.startTimeMs),j=Me(N.endTimeMs),k=fe(N);return`Dialogue: 0,${D},${j},Default,,0,0,0,,${k}`}).join(`
`),d},Se=S=>S.map(d=>fe(d)).join(`
`),fe=S=>{const d=S.text||"";let N="";if(S.translatedText)N=S.translatedText;else if(S.translation)N=S.translation;else if(t.translatedSubtitles&&t.translatedSubtitles.length>0){const D=t.translatedSubtitles.find(j=>j.startTimeMs===S.startTimeMs&&j.endTimeMs===S.endTimeMs);D&&(N=D.text||D.translatedText||"")}switch(x.value){case"source_only":return d;case"target_only":return N||d;case"source_first":return N?`${d}
${N}`:d;case"target_first":return N?`${N}
${d}`:d;default:return d}},ke=S=>{const d=Math.floor(S/1e3),N=Math.floor(d/3600),D=Math.floor(d%3600/60),j=d%60,k=S%1e3;return`${N.toString().padStart(2,"0")}:${D.toString().padStart(2,"0")}:${j.toString().padStart(2,"0")},${k.toString().padStart(3,"0")}`},Ce=S=>{const d=Math.floor(S/1e3),N=Math.floor(d/3600),D=Math.floor(d%3600/60),j=d%60,k=S%1e3;return`${N.toString().padStart(2,"0")}:${D.toString().padStart(2,"0")}:${j.toString().padStart(2,"0")}.${k.toString().padStart(3,"0")}`},Me=S=>{const d=Math.floor(S/1e3),N=Math.floor(d/3600),D=Math.floor(d%3600/60),j=d%60,k=Math.floor(S%1e3/10);return`${N}:${D.toString().padStart(2,"0")}:${j.toString().padStart(2,"0")}.${k.toString().padStart(2,"0")}`},ze=()=>{r.value=!r.value},Ie=()=>{var S;if(y.value.length>0){const d=y.value[0].directory;(S=window.electronAPI)==null||S.openFolder(d)}},Ne=async()=>{const S=y.value.map(d=>d.path).join(`
`);try{await navigator.clipboard.writeText(S),console.log("路径已复制到剪贴板")}catch(d){console.error("复制失败:",d)}},Re=()=>{t.resetWorkflow(),t.setCurrentStep(1)},je=()=>{t.progressUpdates=[]},Oe=S=>{console.log("日志已复制:",S)};return(S,d)=>{var N,D,j;return l(),a(K,null,[e("div",el,[d[29]||(d[29]=e("div",{class:"step-header mb-6"},[e("h3",{class:"text-xl font-semibold text-gray-800"},"Step 6: 导出字幕"),e("p",{class:"text-sm text-gray-500 mt-1"},"选择格式和样式导出最终字幕")],-1)),q.value?(l(),a("div",tl,[e("div",sl,[d[10]||(d[10]=e("div",{class:"flex items-center"},[e("span",{class:"text-lg mr-2"},"🌍"),e("span",{class:"text-sm font-medium text-blue-800"},"当前语言设置")],-1)),e("div",ol,c(V.value),1)])])):f("",!0),q.value?f("",!0):(l(),Y(ge,{key:1,type:"warning",title:"需要先完成前面的步骤",message:"请先完成字幕编辑或翻译处理，准备好字幕内容后再进行导出",actions:[{key:"goto-edit",label:"前往字幕编辑",icon:"📝",primary:!0}],onAction:oe})),q.value&&!E.value?(l(),a("div",rl,[e("div",nl,[d[11]||(d[11]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"📋 内容选择 (可多选)",-1)),e("div",il,[(l(!0),a(K,null,Q(U.value,k=>(l(),a("label",{key:k.value,class:H(["flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200",o.value.includes(k.value)?"border-blue-500 bg-blue-50":"border-gray-200 bg-white hover:bg-gray-50"])},[W(e("input",{type:"checkbox",value:k.value,"onUpdate:modelValue":d[0]||(d[0]=X=>o.value=X),class:"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-3"},null,8,ll),[[le,o.value]]),e("div",al,[e("div",ul,c(k.label),1),e("div",dl,c(k.description),1)])],2))),128))])]),e("div",cl,[d[12]||(d[12]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"📄 格式选择 (可多选)",-1)),e("div",gl,[(l(!0),a(K,null,Q(w.value,k=>(l(),a("label",{key:k.value,class:H(["flex items-center p-3 border rounded-lg cursor-pointer transition-all duration-200",n.value.includes(k.value)?"border-green-500 bg-green-50":"border-gray-200 bg-white hover:bg-gray-50"])},[W(e("input",{type:"checkbox",value:k.value,"onUpdate:modelValue":d[1]||(d[1]=X=>n.value=X),class:"h-4 w-4 text-green-600 border-gray-300 rounded focus:ring-green-500 mr-2"},null,8,pl),[[le,n.value]]),e("div",ml,[e("div",bl,c(k.label),1),e("div",fl,c(k.extension),1)])],2))),128))])]),o.value.length>0&&n.value.length>0?(l(),a("div",vl,[e("h4",xl,"📊 导出预览 ("+c(O.value)+"个文件)",1),e("div",hl,[d[13]||(d[13]=e("div",{class:"text-xs text-blue-600 mb-2"},"📁 将生成文件:",-1)),e("div",yl,[(l(!0),a(K,null,Q(T.value,k=>(l(),a("div",{key:k,class:"text-sm text-gray-700 font-mono"},c(k),1))),128))]),e("div",wl," 📊 预计总大小: "+c(P.value),1)])])):f("",!0),G.value?(l(),a("div",_l,[d[21]||(d[21]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"🎨 双语样式设置",-1)),e("div",Sl,[e("div",null,[d[15]||(d[15]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"分隔方式:",-1)),W(e("select",{"onUpdate:modelValue":d[2]||(d[2]=k=>M.separator=k),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},d[14]||(d[14]=[e("option",{value:"newline"},"换行",-1),e("option",{value:"space"},"空格",-1),e("option",{value:"pipe"},"|",-1)]),512),[[se,M.separator]])]),e("div",null,[d[17]||(d[17]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"原文字体:",-1)),W(e("select",{"onUpdate:modelValue":d[3]||(d[3]=k=>M.originalFont=k),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},d[16]||(d[16]=[e("option",{value:"16px"},"16px",-1),e("option",{value:"18px"},"18px",-1),e("option",{value:"20px"},"20px",-1)]),512),[[se,M.originalFont]])]),e("div",null,[d[19]||(d[19]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"译文字体:",-1)),W(e("select",{"onUpdate:modelValue":d[4]||(d[4]=k=>M.translationFont=k),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},d[18]||(d[18]=[e("option",{value:"14px"},"14px",-1),e("option",{value:"16px"},"16px",-1),e("option",{value:"18px"},"18px",-1)]),512),[[se,M.translationFont]])])]),e("div",Tl,[e("label",$l,[W(e("input",{type:"checkbox","onUpdate:modelValue":d[5]||(d[5]=k=>M.addLanguageLabels=k),class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[le,M.addLanguageLabels]]),d[20]||(d[20]=e("span",null,"添加语言标识 (🇨🇳/🇺🇸)",-1))])])])):f("",!0),e("div",kl,[d[24]||(d[24]=e("h4",{class:"text-sm font-medium text-gray-700 mb-4"},"⚙️ 导出设置",-1)),e("div",Cl,[e("label",Ml,[W(e("input",{type:"checkbox","onUpdate:modelValue":d[6]||(d[6]=k=>v.value=k),class:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"},null,512),[[le,v.value]]),d[22]||(d[22]=e("span",null,"使用默认地址（不弹出确认对话框）",-1))]),v.value?(l(),a("div",El,d[23]||(d[23]=[e("span",null,"默认路径: ~/Downloads/subtitles/",-1)]))):f("",!0)])]),e("div",Pl,[e("button",{onClick:ne,disabled:E.value||!q.value||o.value.length===0||n.value.length===0,class:"flex-1 px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 📥 批量导出 ",8,Ll),e("button",{onClick:xe,disabled:E.value||!q.value||o.value.length===0||n.value.length===0,class:"px-6 py-3 text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"}," 👁️ 预览 ",8,Ul)])])):f("",!0),E.value?(l(),a("div",Fl,[e("div",Al,[e("div",zl,[d[25]||(d[25]=e("h4",{class:"text-sm font-medium text-blue-800"},"📥 正在导出...",-1)),e("span",Il,c($(new Date)),1)]),e("div",Nl,[e("div",Rl,[d[26]||(d[26]=e("span",{class:"text-blue-700"},"📊 总进度",-1)),e("span",jl,c(Math.round(i.value||0))+"%",1)]),e("div",Ol,[e("div",{class:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:pe({width:`${Math.min(100,Math.max(0,i.value||0))}%`})},null,4)])]),e("div",Bl," ⏱️ "+c(g.value||"正在生成文件..."),1),u.value.length>0?(l(),a("div",Dl,[(l(!0),a(K,null,Q(u.value,k=>(l(),a("div",{key:k.file,class:"flex items-center justify-between text-xs"},[e("span",Vl,c(k.file),1),e("span",{class:H(I(k.status))},c(te(k.status))+" "+c(k.status),3)]))),128))])):f("",!0)])])):f("",!0),y.value.length>0&&!E.value?(l(),a("div",Wl,[e("div",ql,[e("div",Hl,[d[27]||(d[27]=e("h4",{class:"text-sm font-medium text-green-800"},"✅ 批量导出成功！",-1)),e("button",{onClick:ze,class:"text-xs text-green-600 hover:text-green-800 focus:outline-none"},c(r.value?"隐藏详情":"查看详情")+" "+c(r.value?"▲":"▼"),1)]),e("p",Gl,"已生成 "+c(y.value.length)+" 个文件",1),r.value?(l(),a("div",Jl,[e("div",Kl,[d[28]||(d[28]=e("div",{class:"text-xs text-green-600 mb-2"},"📁 已生成文件:",-1)),e("div",Yl,[(l(!0),a(K,null,Q(y.value,k=>(l(),a("div",{key:k.path,class:"flex items-center justify-between text-sm"},[e("span",Xl,c(k.filename),1),e("span",Ql,"("+c(k.size)+")",1)]))),128))])]),e("div",Zl,[e("span",null,"📂 保存位置: "+c((N=y.value[0])==null?void 0:N.directory),1),e("span",null,"📊 总大小: "+c(Z.value),1)]),e("div",{class:"flex space-x-2"},[e("button",{onClick:Ie,class:"inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded-md text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," 📂 打开文件夹 "),e("button",{onClick:Ne,class:"inline-flex items-center px-3 py-1 border border-green-300 shadow-sm text-xs font-medium rounded-md text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"}," 📋 复制路径 ")])])):f("",!0)])])):f("",!0),J(t).progressUpdates.length>0?(l(),Y(be,{key:5,title:"导出日志",logs:z.value,"initial-expanded":!1,"show-stats":!0,onClear:je,onCopy:Oe},null,8,["logs"])):f("",!0)]),h.value?(l(),a("div",ea,[e("button",{onClick:Re,class:"px-8 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium transition-colors text-base shadow-lg"}," 🔄 新任务 ")])):f("",!0),m.value?(l(),a("div",{key:1,class:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:de},[e("div",{class:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[80vh] overflow-hidden",onClick:d[9]||(d[9]=we(()=>{},["stop"]))},[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-200"},[d[31]||(d[31]=e("h3",{class:"text-lg font-semibold text-gray-800"},"👁️ 字幕预览",-1)),e("button",{onClick:de,class:"text-gray-400 hover:text-gray-600 focus:outline-none"},d[30]||(d[30]=[e("svg",{class:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),e("div",ta,[e("div",sa,[e("div",null,[d[32]||(d[32]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"预览内容类型:",-1)),W(e("select",{"onUpdate:modelValue":d[7]||(d[7]=k=>x.value=k),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[(l(!0),a(K,null,Q(o.value,k=>(l(),a("option",{key:k,value:k},c(_(k)),9,oa))),128))],512),[[se,x.value]])]),e("div",null,[d[33]||(d[33]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"预览格式:",-1)),W(e("select",{"onUpdate:modelValue":d[8]||(d[8]=k=>C.value=k),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[(l(!0),a(K,null,Q(n.value,k=>{var X;return l(),a("option",{key:k,value:k},c(((X=w.value.find(ie=>ie.value===k))==null?void 0:X.label)||k),9,ra)}),128))],512),[[se,C.value]])])]),e("div",{class:"mt-4 flex justify-center"},[e("button",{onClick:_e,class:"px-4 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium transition-colors"}," 🔄 生成预览 ")])]),e("div",na,[b.value?(l(),a("div",ia,[e("div",la,[e("h4",aa," 预览: "+c(_(x.value))+" - "+c((D=w.value.find(k=>k.value===C.value))==null?void 0:D.label),1),e("span",ua,"显示前10条，总共"+c(((j=J(t).editableSegments)==null?void 0:j.length)||0)+"条",1)]),e("pre",da,c(b.value),1)])):(l(),a("div",ca,d[34]||(d[34]=[e("p",null,'请选择内容类型和格式，然后点击"生成预览"',-1)])))]),e("div",{class:"flex justify-end space-x-3 p-6 border-t border-gray-200"},[e("button",{onClick:de,class:"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 font-medium transition-colors"}," 关闭 ")])])])):f("",!0)],64)}}},pa=ee(ga,[["__scopeId","data-v-481f8de3"]]),ma={class:"p-6 bg-white shadow-xl rounded-2xl border border-gray-100"},ba={key:0,class:"flex items-center justify-center mb-4"},fa={key:1},va={class:"mb-6"},xa={class:"flex space-x-4"},ha={class:"flex items-center cursor-pointer"},ya={class:"flex items-center"},wa={key:0,class:"w-2 h-2 bg-white rounded-full m-0.5"},_a={class:"flex items-center cursor-pointer"},Sa={class:"flex items-center"},Ta={key:0,class:"w-2 h-2 bg-white rounded-full m-0.5"},$a={key:0,class:"mb-6"},ka=["disabled"],Ca={key:0,class:"text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded"},Ma={key:1,class:"mb-6"},Ea={class:"flex items-center space-x-3"},Pa=["disabled"],La={class:"flex items-center"},Ua={key:0,class:"mt-3 p-3 bg-purple-50 rounded-lg border border-purple-200"},Fa={class:"text-sm text-purple-700 font-medium mb-2"},Aa={class:"max-h-32 overflow-y-auto"},za={class:"text-xs text-purple-600 space-y-1"},Ia={key:0,class:"text-purple-500 italic"},Na={key:1,class:"mt-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200"},Ra={class:"mb-6"},ja={key:2,class:"mb-6"},Oa={class:"mb-6"},Ba={key:3,class:"mb-6"},Da={class:"space-y-2"},Va=["value"],Wa={class:"ml-2 text-sm text-gray-700"},qa=["disabled"],Ha={key:0,class:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},Ga={key:1,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 mr-2"},Ja={key:2,class:"mt-4 p-3 bg-red-700 text-red-100 rounded"},Ka={key:3,class:"mt-4 p-3 bg-green-700 text-green-100 rounded"},Ya={key:4,class:"mt-4"},Xa={class:"list-disc list-inside bg-gray-750 p-3 rounded max-h-60 overflow-y-auto text-sm"},Qa={key:5,class:"mt-4"},Za={__name:"OneClickOperation",setup(s){const t=ae(),o=A(null),n=A("single"),r=A(""),i=A([]),g=A(t.oneClickWorkflowType),u=A(t.oneClickTargetLanguage),y=A(t.oneClickExportFormat),v=A(t.oneClickExportLayout),m=A([{value:"original_top",label:"原文在上，译文在下"},{value:"translation_top",label:"译文在上，原文在下"},{value:"original_only",label:"仅显示原文"},{value:"translation_only",label:"仅显示译文"}]),x=A([t.oneClickExportLayout||"original_top"]),C=p(()=>t.isLoading),b=p(()=>t.oneClickOperationInProgress),M=p(()=>t.oneClickOperationError),U=p(()=>t.lastExportPath),w=p(()=>t.progressUpdates),E=p(()=>t.uploadedFile),q=p(()=>n.value==="single"?E.value!==null:r.value&&i.value.length>0);ce(()=>t.oneClickWorkflowType,$=>{g.value=$}),ce(()=>t.oneClickTargetLanguage,$=>{u.value=$}),ce(()=>t.oneClickExportFormat,$=>{y.value=$}),ce(()=>t.oneClickExportLayout,$=>{v.value=$});const R=()=>{t.setOneClickWorkflowType(g.value)},F=()=>{t.setOneClickTargetLanguage(u.value)},V=()=>{t.setOneClickExportFormat(y.value)},O=()=>{x.value.length===0&&(x.value=["original_top"]),t.setOneClickExportLayouts(x.value),t.setOneClickExportLayout(x.value[0])},T=$=>{const _=$.target.files[0];_?(t.setUploadedFile({name:_.name,path:_.path,type:_.type,size:_.size}),o.value=_):(t.setUploadedFile(null),o.value=null)},P=async()=>{try{const $=await window.electronAPI.selectFolder();$&&$.folderPath&&(r.value=$.folderPath,i.value=$.mediaFiles||[])}catch($){console.error("选择文件夹时出错:",$),t.$patch({oneClickOperationError:"选择文件夹时出错: "+$.message})}},G=async()=>{if(n.value==="single"){if(!t.uploadedFile){t.$patch({oneClickOperationError:"请先选择一个文件。"});return}}else if(!r.value||i.value.length===0){t.$patch({oneClickOperationError:"请先选择包含音视频文件的文件夹。"});return}t.setOneClickWorkflowType(g.value),g.value.includes("trans")&&(t.setOneClickTargetLanguage(u.value),t.setOneClickExportLayout(v.value),t.setOneClickExportLayouts(x.value)),t.setOneClickExportFormat(y.value),n.value==="single"?await t.performOneClickOperation():await Z()},Z=async()=>{try{t.$patch({oneClickOperationInProgress:!0,oneClickOperationError:null,progressUpdates:[]});const $=i.value.length;let _=0,I=0,te=0;const oe={workflowType:g.value,targetLanguage:u.value,exportFormat:y.value,exportLayout:v.value,exportLayouts:x.value};t.progressUpdates.push(`🚀 开始批量处理 ${$} 个文件...`),t.progressUpdates.push(`📋 设置: ${oe.workflowType}, ${oe.targetLanguage}, ${oe.exportFormat}, ${oe.exportLayout}`);for(const ne of i.value){const xe=`${r.value}/${ne}`;_++;try{t.progressUpdates.push(`
[${_}/${$}] 🎬 开始处理: ${ne}`),t.resetWorkflow(),t.setUploadedFile({name:ne,path:xe,type:z(ne),size:0}),t.setOneClickWorkflowType(oe.workflowType),t.setOneClickTargetLanguage(oe.targetLanguage),t.setOneClickExportFormat(oe.exportFormat),t.setOneClickExportLayout(oe.exportLayout),t.setOneClickExportLayouts(oe.exportLayouts),console.log(`[Batch] Processing file ${_}/${$}: ${ne}`),console.log("[Batch] Settings:",oe),await t.performOneClickOperation(),I++,t.progressUpdates.push(`✅ [${_}/${$}] 完成: ${ne}`),t.lastExportPath&&t.progressUpdates.push(`📁 导出到: ${t.lastExportPath}`)}catch(de){te++,console.error(`处理文件 ${ne} 时出错:`,de),t.progressUpdates.push(`❌ [${_}/${$}] 失败: ${ne}`),t.progressUpdates.push(`   错误: ${de.message}`)}}t.progressUpdates.push(`
🎉 批量处理完成!`),t.progressUpdates.push(`✅ 成功处理: ${I} 个文件`),te>0&&t.progressUpdates.push(`❌ 处理失败: ${te} 个文件`),t.$patch({oneClickOperationInProgress:!1,lastExportPath:`批量处理完成 - 成功: ${I}, 失败: ${te}`})}catch($){console.error("批量处理时出错:",$),t.$patch({oneClickOperationInProgress:!1,oneClickOperationError:`批量处理失败: ${$.message}`})}},z=$=>{const _=$.split(".").pop().toLowerCase(),I=["mp4","avi","mov","mkv","wmv","flv","webm","m4v"],te=["mp3","wav","flac","aac","ogg","wma","m4a"];return I.includes(_)?"video/"+_:te.includes(_)?"audio/"+_:"application/octet-stream"},h=()=>{t.resetWorkflow(),o.value=null,r.value="",i.value=[],n.value="single";const $=document.getElementById("oneClickFile");$&&($.value="")};return($,_)=>(l(),a("div",ma,[_[27]||(_[27]=e("h2",{class:"text-2xl font-bold mb-6 text-gray-800 flex items-center"},[e("div",{class:"w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3"},[e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"2",stroke:"currentColor",class:"w-5 h-5 text-white"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"})])]),ue(" 一键字幕生成 ")],-1)),b.value||C.value?(l(),a("div",ba,_[6]||(_[6]=[e("svg",{class:"animate-spin -ml-1 mr-3 h-5 w-5 text-blue-400",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),e("span",{class:"text-gray-300"},"Processing... Please wait.",-1)]))):f("",!0),!b.value&&!C.value?(l(),a("div",fa,[e("div",va,[_[9]||(_[9]=e("label",{class:"block text-sm font-medium text-gray-700 mb-3"},"处理模式:",-1)),e("div",xa,[e("label",ha,[W(e("input",{type:"radio","onUpdate:modelValue":_[0]||(_[0]=I=>n.value=I),value:"single",class:"sr-only"},null,512),[[Ee,n.value]]),e("div",ya,[e("div",{class:H(["w-4 h-4 rounded-full border-2 mr-2 transition-all duration-200",n.value==="single"?"border-blue-500 bg-blue-500":"border-gray-300"])},[n.value==="single"?(l(),a("div",wa)):f("",!0)],2),_[7]||(_[7]=e("span",{class:"text-sm text-gray-700"},"单文件处理",-1))])]),e("label",_a,[W(e("input",{type:"radio","onUpdate:modelValue":_[1]||(_[1]=I=>n.value=I),value:"batch",class:"sr-only"},null,512),[[Ee,n.value]]),e("div",Sa,[e("div",{class:H(["w-4 h-4 rounded-full border-2 mr-2 transition-all duration-200",n.value==="batch"?"border-purple-500 bg-purple-500":"border-gray-300"])},[n.value==="batch"?(l(),a("div",Ta)):f("",!0)],2),_[8]||(_[8]=e("span",{class:"text-sm text-gray-700"},"文件夹批量处理",-1))])])])]),n.value==="single"?(l(),a("div",$a,[_[10]||(_[10]=e("label",{for:"oneClickFile",class:"block text-sm font-medium text-gray-700 mb-2"}," 选择音视频文件: ",-1)),e("input",{type:"file",id:"oneClickFile",onChange:T,accept:"video/*,audio/*",class:"block w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-500 file:text-white hover:file:bg-blue-600 disabled:opacity-50 border border-gray-200 rounded-lg",disabled:b.value||C.value},null,40,ka),E.value?(l(),a("p",Ca," 已选择: "+c(E.value.name)+" ("+c((E.value.size/1024/1024).toFixed(2))+" MB) ",1)):f("",!0)])):f("",!0),n.value==="batch"?(l(),a("div",Ma,[_[13]||(_[13]=e("label",{for:"folderSelect",class:"block text-sm font-medium text-gray-700 mb-2"}," 选择包含音视频文件的文件夹: ",-1)),e("div",Ea,[e("button",{onClick:P,class:"flex-1 px-4 py-3 border border-gray-300 rounded-lg text-left text-gray-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors duration-200",disabled:b.value||C.value},[e("div",La,[_[11]||(_[11]=e("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-5 h-5 mr-2 text-purple-500"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25H11.69z"})],-1)),ue(" "+c(r.value||"点击选择文件夹..."),1)])],8,Pa)]),r.value&&i.value.length>0?(l(),a("div",Ua,[e("p",Fa," 检测到 "+c(i.value.length)+" 个音视频文件: ",1),e("div",Aa,[e("ul",za,[(l(!0),a(K,null,Q(i.value.slice(0,10),I=>(l(),a("li",{key:I,class:"truncate"}," 📁 "+c(I),1))),128)),i.value.length>10?(l(),a("li",Ia," ... 还有 "+c(i.value.length-10)+" 个文件 ",1)):f("",!0)])])])):r.value&&i.value.length===0?(l(),a("div",Na,_[12]||(_[12]=[e("p",{class:"text-sm text-yellow-700"}," ⚠️ 在选择的文件夹中未检测到音视频文件 ",-1)]))):f("",!0)])):f("",!0),e("div",Ra,[_[15]||(_[15]=e("label",{for:"workflowType",class:"block text-sm font-medium text-gray-700 mb-2"},"工作流类型:",-1)),W(e("select",{id:"workflowType","onUpdate:modelValue":_[2]||(_[2]=I=>g.value=I),onChange:R,class:"mt-1 block w-full pl-3 pr-10 py-3 text-base border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg"},_[14]||(_[14]=[ye('<option value="vid_to_srt" data-v-a6c5b310>视频 → SRT字幕</option><option value="vid_to_srt_trans" data-v-a6c5b310>视频 → 翻译SRT字幕</option><option value="audio_to_srt" data-v-a6c5b310>音频 → SRT字幕</option><option value="audio_to_srt_trans" data-v-a6c5b310>音频 → 翻译SRT字幕</option><option value="vid_to_text" data-v-a6c5b310>视频 → 纯文本</option><option value="audio_to_text" data-v-a6c5b310>音频 → 纯文本</option>',6)]),544),[[se,g.value]])]),g.value.includes("trans")?(l(),a("div",ja,[_[17]||(_[17]=e("label",{for:"targetLanguage",class:"block text-sm font-medium text-gray-700 mb-2"},"目标语言:",-1)),W(e("select",{id:"targetLanguage","onUpdate:modelValue":_[3]||(_[3]=I=>u.value=I),onChange:F,class:"mt-1 block w-full pl-3 pr-10 py-3 text-base border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg"},_[16]||(_[16]=[ye('<option value="zh-CN" data-v-a6c5b310>中文（简体）</option><option value="zh-TW" data-v-a6c5b310>中文（繁体）</option><option value="en" data-v-a6c5b310>英语</option><option value="ja" data-v-a6c5b310>日语</option><option value="ko" data-v-a6c5b310>韩语</option><option value="es" data-v-a6c5b310>西班牙语</option><option value="fr" data-v-a6c5b310>法语</option><option value="de" data-v-a6c5b310>德语</option><option value="it" data-v-a6c5b310>意大利语</option><option value="pt" data-v-a6c5b310>葡萄牙语</option><option value="ru" data-v-a6c5b310>俄语</option>',11)]),544),[[se,u.value]])])):f("",!0),e("div",Oa,[_[19]||(_[19]=e("label",{for:"oneClickExportFormat",class:"block text-sm font-medium text-gray-700 mb-2"},"导出格式:",-1)),W(e("select",{id:"oneClickExportFormat","onUpdate:modelValue":_[4]||(_[4]=I=>y.value=I),onChange:V,class:"mt-1 block w-full pl-3 pr-10 py-3 text-base border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg"},_[18]||(_[18]=[ye('<option value="srt" data-v-a6c5b310>SRT (.srt)</option><option value="vtt" data-v-a6c5b310>VTT (.vtt)</option><option value="ass" data-v-a6c5b310>ASS (.ass)</option><option value="txt" data-v-a6c5b310>TXT (.txt)</option><option value="json" data-v-a6c5b310>JSON (.json)</option>',5)]),544),[[se,y.value]])]),g.value.includes("trans")?(l(),a("div",Ba,[_[20]||(_[20]=e("label",{class:"block text-sm font-medium text-gray-700 mb-2"},"字幕布局 (可多选):",-1)),e("div",Da,[(l(!0),a(K,null,Q(m.value,I=>(l(),a("label",{key:I.value,class:"flex items-center"},[W(e("input",{type:"checkbox",value:I.value,"onUpdate:modelValue":_[5]||(_[5]=te=>x.value=te),onChange:O,class:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"},null,40,Va),[[le,x.value]]),e("span",Wa,c(I.label),1)]))),128))]),_[21]||(_[21]=e("p",{class:"mt-1 text-xs text-gray-500"}," 选择多个布局将生成多个文件，例如：filename_原文在上.srt, filename_仅译文.srt ",-1))])):f("",!0),e("button",{onClick:G,disabled:!q.value||b.value||C.value,class:"w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-6 rounded-xl focus:outline-none focus:ring-4 focus:ring-purple-200 transition-all duration-200 text-lg shadow-lg hover:shadow-xl disabled:shadow-none flex items-center justify-center"},[b.value||C.value?(l(),a("svg",Ha,_[22]||(_[22]=[e("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),e("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):(l(),a("svg",Ga,_[23]||(_[23]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z"},null,-1)]))),ue(" "+c(b.value||C.value?"处理中...":n.value==="batch"?"开始批量处理":"开始一键处理"),1)],8,qa)])):f("",!0),M.value&&!b.value?(l(),a("div",Ja,[_[24]||(_[24]=ue(" Error: ")),e("strong",null,c(M.value),1)])):f("",!0),U.value&&!b.value&&!M.value?(l(),a("div",Ka,[_[25]||(_[25]=ue(" ✅ 一键操作成功完成！导出到: ")),e("strong",null,c(U.value),1),e("button",{onClick:h,class:"ml-2 text-sm underline"},"清除")])):f("",!0),w.value.length>0&&(b.value||C.value||M.value||U.value)?(l(),a("div",Ya,[_[26]||(_[26]=e("h3",{class:"text-md font-semibold mb-2 text-gray-300"},"操作日志:",-1)),e("ul",Xa,[(l(!0),a(K,null,Q(w.value,(I,te)=>(l(),a("li",{key:te,class:H({"text-green-300":I.includes("✅")||I.includes("成功")||I.includes("完成"),"text-red-400":I.includes("❌")||I.includes("错误")||I.includes("失败"),"text-blue-300":I.includes("🚀")||I.includes("🔄")||I.includes("📝")||I.includes("🌐"),"text-yellow-300":I.includes("⚠️")||I.includes("警告"),"text-gray-300":!(I.includes("✅")||I.includes("❌")||I.includes("🚀")||I.includes("🔄")||I.includes("📝")||I.includes("🌐")||I.includes("⚠️"))})},c(I),3))),128))])])):f("",!0),!b.value&&(U.value||M.value)?(l(),a("div",Qa,[e("button",{onClick:h,class:"w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out"}," 🔄 开始新的一键操作 ")])):f("",!0)]))}},eu=ee(Za,[["__scopeId","data-v-a6c5b310"]]),tu={class:"subtitler-view-container"},su={class:"flex justify-center mb-8"},ou={class:"bg-white rounded-xl p-1 shadow-lg border border-gray-200"},ru={key:0},nu={class:"step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6"},iu={key:1,class:"mt-6"},lu={class:"bg-white rounded-2xl shadow-xl border border-gray-100 p-6"},au={__name:"SubtitlerView",setup(s){const t=ae(),o=p(()=>t.currentStep),n=A("stepper"),r={1:bs,2:zs,3:wo,4:Ko,5:Wr,6:Un,7:si,8:Zi,9:pa},i=p(()=>r[o.value]||null);return(g,u)=>(l(),a("div",tu,[u[2]||(u[2]=e("div",{class:"mb-8"},[e("h1",{class:"text-3xl font-bold text-center bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2"},"字幕工具"),e("p",{class:"text-center text-gray-600 text-sm"},"智能视频字幕生成与编辑工具")],-1)),e("div",su,[e("div",ou,[e("button",{onClick:u[0]||(u[0]=y=>n.value="stepper"),class:H(["px-6 py-3 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none",n.value==="stepper"?"bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md":"text-gray-600 hover:text-gray-800 hover:bg-gray-50"])}," 分步操作 ",2),e("button",{onClick:u[1]||(u[1]=y=>n.value="oneclick"),class:H(["px-6 py-3 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none",n.value==="oneclick"?"bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-md":"text-gray-600 hover:text-gray-800 hover:bg-gray-50"])}," 一键操作 ",2)])]),n.value==="stepper"?(l(),a("div",ru,[Pe(rt),e("div",nu,[(l(),Y(He,null,[(l(),Y(qe(i.value)))],1024))])])):f("",!0),n.value==="oneclick"?(l(),a("div",iu,[e("div",lu,[Pe(eu)])])):f("",!0)]))}},du=ee(au,[["__scopeId","data-v-fe916d6b"]]);export{du as default};

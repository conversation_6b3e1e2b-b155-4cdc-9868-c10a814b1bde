<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Electron gRPC Demo</title>
    <!-- 移除 CSP meta 标签以便于开发，生产环境应配置合理的 CSP -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
    <!-- CSS will be imported by Vue app, not directly here -->
  <script type="module" crossorigin src="./assets/index-CjTvhpco.js"></script>
  <link rel="stylesheet" crossorigin href="./assets/index-DEaYk4lG.css">
</head>
<body>
    <div id="vue-app">
        <!-- Vue app will be mounted here by /src/vue-main.js -->
        <!-- The entire layout including sidebar and content area will be rendered by App.vue -->
    </div>
    
    <!-- Main application code - Old renderer.js is removed -->
    <!-- <script type="module" src="./renderer.js"></script> --> <!-- New Vue app entry point -->
    
</body>
</html>
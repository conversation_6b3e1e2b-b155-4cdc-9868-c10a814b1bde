const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose Node.js APIs to the renderer process for IPC communication

contextBridge.exposeInMainWorld('electronAPI', {
    // Generic invoke method
    invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),

    // sendDebugLog is a general utility for debugging
    sendDebugLog: (message) => ipcRenderer.send('debug-log-from-renderer', message),

    // File system operations
    selectFolder: () => ipcRenderer.invoke('dialog:selectFolder'),

    // Listener methods (main sending to renderer)
    onMainProcessLog: (callback) => {
        const listener = (_event, message) => callback(message);
        ipcRenderer.on('main-process-log', listener);
        // Return a function to remove the listener
        return () => ipcRenderer.removeListener('main-process-log', listener);
    },
    onServiceStatusUpdate: (callback) => {
        const listener = (_event, data) => callback(data);
        ipcRenderer.on('service-status-update', listener);
        return () => ipcRenderer.removeListener('service-status-update', listener);
    },
    onUnifiedLog: (callback) => {
        const listener = (_event, message) => callback(message);
        ipcRenderer.on('unified-log', listener);
        return () => ipcRenderer.removeListener('unified-log', listener);
    },
    onJavaLog: (callback) => { // For specific Java logs if still used
        const listener = (_event, message) => callback(message);
        ipcRenderer.on('java-log', listener);
        return () => ipcRenderer.removeListener('java-log', listener);
    },
    onJavaStatus: (callback) => { // For specific Java status if still used
        const listener = (_event, status) => callback(status);
        ipcRenderer.on('java-status', listener);
        return () => ipcRenderer.removeListener('java-status', listener);
    },
     // Expose a way to get client status if needed, or handle it via onServiceStatusUpdate
    onPythonClientStatus: (callback) => {
        const listener = (_event, status) => callback(status);
        ipcRenderer.on('python-grpc-client-status', listener);
        return () => ipcRenderer.removeListener('python-grpc-client-status', listener);
    },
    onGoClientStatus: (callback) => {
        const listener = (_event, status) => callback(status);
        ipcRenderer.on('go-grpc-client-status', listener);
        return () => ipcRenderer.removeListener('go-grpc-client-status', listener);
    },
    onJavaClientStatus: (callback) => {
        const listener = (_event, status) => callback(status);
        ipcRenderer.on('java-grpc-client-status', listener);
        return () => ipcRenderer.removeListener('java-grpc-client-status', listener);
    },
    onSubtitlerClientStatus: (callback) => {
        const listener = (_event, status) => callback(status);
        ipcRenderer.on('subtitler-grpc-client-status', listener);
        return () => ipcRenderer.removeListener('subtitler-grpc-client-status', listener);
    },
    // Add stream event listeners for streaming gRPC calls
    onProgressUpdate: (callback) => {
        const listener = (_event, update) => {
            // Directly pass the update to the callback
            callback(update);
        };
        ipcRenderer.on('progress-update', listener);
        return () => ipcRenderer.removeListener('progress-update', listener);
    },

    // Generic event listeners
    on: (channel, callback) => {
        const listener = (_event, ...args) => callback(...args);
        ipcRenderer.on(channel, listener);
        return () => ipcRenderer.removeListener(channel, listener);
    },
    off: (channel, callback) => {
        ipcRenderer.removeListener(channel, callback);
    }
});
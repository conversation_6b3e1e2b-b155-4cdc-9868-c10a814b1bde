// electron-app/src/node-grpc-client.js

const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');
const fs = require('fs');
const { Readable } = require('stream');

// --- Mocking Utilities ---
function createMockMethod(methodName, serviceName, clientIdentifier, error) {
  return (...args) => {
    const callback = args.length > 0 && typeof args[args.length - 1] === 'function' ? args[args.length - 1] : null;
    const requestData = callback ? args.slice(0, -1) : args;

    console.error(
      `MockClient [${clientIdentifier} -> ${serviceName}]: Method ${methodName} called with:`, requestData,
      `Real client not available. Error: ${error ? error.message : 'N/A'}`
    );

    const looksLikeStreamCall = !callback && args.length <= 1;

    if (looksLikeStreamCall) {
      const stream = new Readable({ read() {} });
      stream.cancel = () => console.error(`Mock stream for ${clientIdentifier}.${methodName} cancelled.`);
      process.nextTick(() => {
        const err = new Error(`Mock stream error for ${clientIdentifier}.${methodName}: Service unavailable. Original error: ${error ? error.message : 'N/A'}`);
        err.code = grpc.status.UNAVAILABLE;
        stream.emit('error', err);
        stream.push(null);
      });
      return stream;
    } else if (callback) {
      process.nextTick(() => {
        const err = new Error(`Mock call to ${clientIdentifier}.${methodName} failed: Service unavailable. Original error: ${error ? error.message : 'N/A'}`);
        err.code = grpc.status.UNAVAILABLE;
        callback(err, null);
      });
    } else {
      return {
        then: (onfulfilled, onrejected) => {
          process.nextTick(() => {
            const err = new Error(`Mock call to ${clientIdentifier}.${methodName} failed (Promise): Service unavailable. Original error: ${error ? error.message : 'N/A'}`);
            err.code = grpc.status.UNAVAILABLE;
            if (onrejected) onrejected(err);
          });
        },
        catch: (onrejected) => {
           process.nextTick(() => {
            const err = new Error(`Mock call to ${clientIdentifier}.${methodName} failed (Promise): Service unavailable. Original error: ${error ? error.message : 'N/A'}`);
            err.code = grpc.status.UNAVAILABLE;
            if (onrejected) onrejected(err);
          });
        },
        finally: (onfinally) => {
          process.nextTick(() => {
            if (onfinally) onfinally();
          });
        }
      };
    }
  };
}

function createMockServiceClient(serviceConfig, methodNames = [], error) {
  const clientIdentifier = serviceConfig.name;
  const serviceName = serviceConfig.serviceName || 'UnknownService';
  const mockClient = {};

  if (methodNames.length > 0) {
    for (const methodName of methodNames) {
      mockClient[methodName] = createMockMethod(methodName, serviceName, clientIdentifier, error);
    }
  }

  return new Proxy(mockClient, {
    get: (target, prop) => {
      if (prop in target) {
        return target[prop];
      }
      if (typeof prop === 'symbol' || prop === 'inspect' || prop === 'then') { // Avoid mocking special properties
        return undefined;
      }
      console.warn(`MockClient [${clientIdentifier} -> ${serviceName}]: Method ${String(prop)} was not explicitly defined. Creating generic mock.`);
      return createMockMethod(String(prop), serviceName, clientIdentifier, error);
    },
  });
}


// --- GrpcClientManager Class ---
class GrpcClientManager {
  constructor() {
    this.serviceConfigs = [];
    this.clients = {};
    this.messageConstructors = {};
    this.loadedProtoDefinitions = {};
    this.grpcObjects = {};
  }

  // _getProtoPathDefault is removed as each service now has its specific proto file defined in serviceConfigs.
  // async _getProtoPathDefault(serviceNameForLog = 'default service') { ... }

  async _loadProtoDefinition(protoPath, serviceNameForLog) {
    if (this.grpcObjects[protoPath]) {
      return this.grpcObjects[protoPath];
    }
    try {
      if (!fs.existsSync(protoPath)) {
        throw new Error(`Proto file not found at ${protoPath}`);
      }
      console.log(`[GrpcClientManager] Loading proto definition from: ${protoPath} for ${serviceNameForLog}`);
      // Calculate project root based on environment
      let projectRoot;
      if (process.defaultApp) {
        // Development environment
        projectRoot = path.resolve(__dirname, '../../');
      } else {
        // Packaged environment - use resources path
        projectRoot = process.resourcesPath;
      }
      console.log(`[GrpcClientManager] Using project root for proto imports: ${projectRoot}`);
      const packageDefinition = await protoLoader.load(protoPath, {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true,
        includeDirs: [projectRoot] // Use project root for includeDirs
      });
      this.loadedProtoDefinitions[protoPath] = packageDefinition;
      const grpcObject = grpc.loadPackageDefinition(packageDefinition);
      this.grpcObjects[protoPath] = grpcObject;
      return grpcObject;
    } catch (error) {
      console.error(`[GrpcClientManager] Failed to load proto file ${protoPath} for ${serviceNameForLog}:`, error.message);
      throw error;
    }
  }

  async _initializeService(serviceConfig) {
    const { name, packageName, serviceName, serverUrl, protoPath: specificProtoPath } = serviceConfig;
    let effectiveProtoPath = specificProtoPath;
    // if (!effectiveProtoPath) {
    //     console.log(`[GrpcClientManager] No specific protoPath for ${name}, attempting to find default.`);
    //     effectiveProtoPath = this._getProtoPathDefault(name); // This line is removed
    // }

    if (!effectiveProtoPath) {
      const errMsg = `[GrpcClientManager] No protoPath provided for service ${name} in its configuration. Cannot initialize.`;
      console.error(errMsg);
      this.clients[name] = createMockServiceClient(serviceConfig, [], new Error(errMsg));
      this.messageConstructors[name] = {};
      return;
    }

    let grpcObjectRoot;
    try {
      grpcObjectRoot = await this._loadProtoDefinition(effectiveProtoPath, name);

      let packageObj;
      if (packageName && packageName.length > 0) {
        packageObj = packageName.split('.').reduce((obj, part) => obj && obj[part], grpcObjectRoot);
      } else {
        packageObj = grpcObjectRoot;
      }

      if (!packageObj) {
        throw new Error(`Package "${packageName || '(root)'}" not found in proto ${effectiveProtoPath}. Available: ${Object.keys(grpcObjectRoot)}`);
      }

      const ServiceDefinition = packageObj[serviceName];
      if (!ServiceDefinition) {
        throw new Error(`Service "${serviceName}" not found in package "${packageName || '(root)'}". Available in package: ${Object.keys(packageObj)}`);
      }

      if (typeof ServiceDefinition !== 'function' || !ServiceDefinition.service) {
        throw new Error(`"${serviceName}" in package "${packageName || '(root)'}" is not a valid gRPC service constructor.`);
      }
      
      console.log(`[GrpcClientManager] Attempting to create client for ${name} (${packageName || '(root)'}.${serviceName}) at ${serverUrl}`);
      const client = new ServiceDefinition(serverUrl, grpc.credentials.createInsecure(), {
        'grpc.keepalive_time_ms': 120000,
        'grpc.keepalive_timeout_ms': 20000,
        'grpc.keepalive_permit_without_calls': 1,
        'grpc.http2.min_time_between_pings_ms': 120000,
        'grpc.http2.max_pings_without_data': 0,
        'grpc.enable_retries': 1,
      });
      
      this.clients[name] = client;
      this.messageConstructors[name] = packageObj;
      console.log(`[GrpcClientManager] Successfully configured gRPC client for ${name} (${packageName || '(root)'}.${serviceName})`);

    } catch (error) {
      console.error(`[GrpcClientManager] Failed to initialize gRPC client for ${name} (${packageName || '(root)'}.${serviceName}):`, error.message);
      let methodNames = [];
      if (grpcObjectRoot) {
        try {
          let pkgCheck;
          if (packageName && packageName.length > 0) {
            pkgCheck = packageName.split('.').reduce((obj, part) => obj && obj[part], grpcObjectRoot);
          } else {
            pkgCheck = grpcObjectRoot;
          }
          if (pkgCheck && pkgCheck[serviceName] && pkgCheck[serviceName].service) {
            methodNames = Object.keys(pkgCheck[serviceName].service);
          }
        } catch (e) { /* ignore */ }
      }
      this.clients[name] = createMockServiceClient(serviceConfig, methodNames, error);
      
      this.messageConstructors[name] = {}; 
      if (grpcObjectRoot) {
          if (packageName && packageName.length > 0) {
              const pkg = packageName.split('.').reduce((obj, part) => obj && obj[part], grpcObjectRoot);
              if (pkg) this.messageConstructors[name] = pkg;
          } else {
              this.messageConstructors[name] = grpcObjectRoot;
          }
      }
    }
  }

  async init(serviceConfigs) {
    if (!Array.isArray(serviceConfigs) || serviceConfigs.length === 0) {
        console.warn('[GrpcClientManager] Initialization called with no service configurations.');
        this.serviceConfigs = [];
        return;
    }
    this.serviceConfigs = serviceConfigs;
    const logConfigs = serviceConfigs.map(c => ({...c, serverUrl: c.serverUrl.includes('localhost') || c.serverUrl.includes('127.0.0.1') ? c.serverUrl : 'URL_HIDDEN' }));
    console.log('[GrpcClientManager] Initializing with configurations:', JSON.stringify(logConfigs));

    await Promise.all(this.serviceConfigs.map(config => this._initializeService(config)));
    console.log('[GrpcClientManager] All services processed.');
  }

  getClient(serviceIdentifier) {
    if (!this.clients[serviceIdentifier]) {
      console.warn(`[GrpcClientManager] Client for service identifier "${serviceIdentifier}" not found or not initialized. Returning a new generic mock.`);
      const config = this.serviceConfigs.find(c => c.name === serviceIdentifier) || 
                     { name: serviceIdentifier, serviceName: 'UnknownService', packageName: 'unknown.package' };
      return createMockServiceClient(config, [], new Error(`Client "${serviceIdentifier}" was not successfully initialized or configured.`));
    }
    return this.clients[serviceIdentifier];
  }

  getMessageConstructors(serviceIdentifier) {
    const constructors = this.messageConstructors[serviceIdentifier];
    if (!constructors || Object.keys(constructors).length === 0) {
      const serviceConfig = this.serviceConfigs.find(c => c.name === serviceIdentifier);
      if (serviceConfig) {
        console.warn(`[GrpcClientManager] Message constructors for service identifier "${serviceIdentifier}" (Package: ${serviceConfig.packageName || '(root)'}) are empty or not found. This might be due to a proto loading issue.`);
      } else {
        console.warn(`[GrpcClientManager] Message constructors for unknown service identifier "${serviceIdentifier}" not found.`);
      }
      return {};
    }
    return constructors;
  }
}

// --- Module Exports ---
async function initializeGrpcServices(serviceConfigs) {
  if (global.grpcManager && typeof global.grpcManager.init === 'function') {
    console.warn('[GrpcClientManager] gRPC services may have been already initialized on global.grpcManager. Re-initializing with new configuration.');
  }
  const manager = new GrpcClientManager();
  await manager.init(serviceConfigs);
  // global.grpcManager = manager; // Removed: Manager instance will be returned and handled by the caller.
  console.log('[GrpcClientManager] GrpcClientManager instance initialized and will be returned.');
  return manager;
}

module.exports = {
  GrpcClientManager,
  initializeGrpcServices,
};
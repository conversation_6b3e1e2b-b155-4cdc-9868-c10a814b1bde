const path = require('path');
const { initializeGrpcServices: initializeNodeGrpcServices } = require('../node-grpc-client'); // Adjusted path
// mainWindow and logToRenderer will be obtained from windowManagerRef
let windowManagerRef; // To access getMainWindow() and logToRenderer()
let grpcClientManagerInstance = null; // To store the GrpcClientManager instance

// gRPC 服务配置
const serviceConfigs = [
  {
    name: 'greeterServicePython',
    packageName: 'monkeyfx.api.v1.greeter',
    serviceName: 'Greeter',
    serverUrl: 'localhost:50051',
    protoPath: getProtoPath('v1', 'greeter', 'greeter.proto'),
  },
  {
    name: 'greeterServiceGo',
    packageName: 'monkeyfx.api.v1.greeter',
    serviceName: 'Greeter',
    serverUrl: 'localhost:50052',
    protoPath: getProtoPath('v1', 'greeter', 'greeter.proto'),
  },
  {
    name: 'greeter<PERSON><PERSON><PERSON><PERSON><PERSON>',
    packageName: 'monkeyfx.api.v1.greeter',
    serviceName: 'Greeter',
    serverUrl: 'localhost:50053',
    protoPath: getProtoPath('v1', 'greeter', 'greeter.proto'),
  },
  {
    name: 'subtitlerService',
    packageName: 'monkeyfx.api.v1.subtitler',
    serviceName: 'Subtitler',
    serverUrl: 'localhost:50051', // Assuming Python backend for Subtitler
    protoPath: getProtoPath('v1', 'subtitler', 'subtitler.proto'),
  },
{
    name: 'aiConfigService', // Unique name for this client instance
    packageName: 'v1.ai_config', // Package from our .proto file
    serviceName: 'AIConfigurationService', // Service name from .proto
    serverUrl: 'localhost:50051', // Assuming Python backend hosts this
    protoPath: getProtoPath('v1', 'ai_config', 'ai_config_service.proto'),
  },
];

// Helper function to get correct proto path for both dev and packaged environments
function getProtoPath(...pathSegments) {
  if (process.defaultApp) {
    // Development environment
    return path.join(__dirname, '..', '..', '..', 'api-protos', ...pathSegments);
  } else {
    // Packaged environment - proto files are in extraResources
    return path.join(process.resourcesPath, 'api-protos', ...pathSegments);
  }
}

let subtitlerClient = null; // Local reference

function initializeGrpcManager(winMgr) {
    windowManagerRef = winMgr;
}

async function setupGrpcConnections() {
    if (!windowManagerRef) {
        console.error('[gRPC Manager] Window Manager reference not set. Cannot log.');
        // Attempt to continue, but logging will be missing.
    }
    const log = windowManagerRef ? windowManagerRef.logToRenderer : console.log;

    log('[gRPC Manager] Initializing gRPC services via GrpcClientManager...');
    try {
        grpcClientManagerInstance = await initializeNodeGrpcServices(serviceConfigs);
        log('[gRPC Manager] Node GrpcClientManager initialization complete.');
        // Initialize SubtitlerClient after GrpcClientManager is ready
        // This is typically called after the window's 'did-finish-load'
        // but can be called here if window is already loaded.
        // The actual call will be coordinated by app-lifecycle.js
    } catch (error) {
        log(`[gRPC Manager] Critical error during Node gRPC services initialization: ${error.message}`);
        // dialog.showErrorBox is an Electron UI call, should be handled where dialog is available (e.g. app-lifecycle or main.js)
        // For now, just log it. The app might show an error through other means or fail to start services.
        throw error; // Re-throw to be handled by the caller in app-lifecycle
    }
}

// This function will be called after the main window's 'did-finish-load' event.
function initializeSubtitlerClientAfterWindowLoad() {
    const log = windowManagerRef ? windowManagerRef.logToRenderer : console.log;
    log('[Subtitler Client] Initializing Subtitler client using GrpcClientManager (after window load)...');
    
    if (!grpcClientManagerInstance) {
        const errMsg = '[Subtitler Client] GrpcClientManager instance is not available. Cannot initialize Subtitler client.';
        log(errMsg);
        sendClientStatusToRenderer('subtitler-grpc-client-status', false, 'gRPC Manager not ready.');
        subtitlerClient = null; // Ensure it's null
        // global.subtitlerClient = subtitlerClient; // Removed global assignment
        return;
    }

    try {
        const client = grpcClientManagerInstance.getClient('subtitlerService');
        // Check if client is a real gRPC client (not a mock)
        const isRealClient = client && !client.toString().includes('MockClient') && typeof client.getChannel === 'function';

        if (isRealClient) {
            subtitlerClient = client;
            // global.subtitlerClient = subtitlerClient; // Removed global assignment
            const serviceConfig = serviceConfigs.find(c => c.name === 'subtitlerService');
            const serverId = serviceConfig ? `${serviceConfig.serverUrl}` : 'configured server';
            log(`[Subtitler Client] Successfully obtained SubtitlerClient for ${serverId} from GrpcClientManager.`);
            sendClientStatusToRenderer('subtitler-grpc-client-status', true, `SubtitlerClient connected via ${serverId} and ready.`);
        } else {
            subtitlerClient = client; // Assign mock if that's what getClient returned
            // global.subtitlerClient = subtitlerClient; // Removed global assignment
            const errMsg = '[Subtitler Client] Subtitler service client from GrpcClientManager seems to be a mock or not fully initialized. Operations might be simulated.';
            log(errMsg);
            sendClientStatusToRenderer('subtitler-grpc-client-status', false, 'SubtitlerClient is in mock mode or uninitialized.');
        }
    } catch (error) {
        log(`[Subtitler Client] Error obtaining SubtitlerClient from GrpcClientManager: ${error.message}`);
        sendClientStatusToRenderer('subtitler-grpc-client-status', false, `Failed to get SubtitlerClient: ${error.message}`);
        // Fallback to a mock if error occurs, GrpcClientManager.getClient should provide one.
        subtitlerClient = grpcClientManagerInstance ? grpcClientManagerInstance.getClient('subtitlerService') : null;
        // global.subtitlerClient = subtitlerClient; // Removed global assignment
    }
}

function getSubtitlerClient() {
    return subtitlerClient;
}

function getGreeterClient(type) { // type: 'python', 'go', 'java'
    if (!grpcClientManagerInstance) return null;
    if (type === 'python') return grpcClientManagerInstance.getClient('greeterServicePython');
    if (type === 'go') return grpcClientManagerInstance.getClient('greeterServiceGo');
    if (type === 'java') return grpcClientManagerInstance.getClient('greeterServiceJava');
    return null;
}
function getAIConfigServiceClient() {
    if (!grpcClientManagerInstance) {
        const log = windowManagerRef ? windowManagerRef.logToRenderer : console.log;
        log('[gRPC Manager] GrpcClientManager instance not available for AIConfigService.');
        return null;
    }
    return grpcClientManagerInstance.getClient('aiConfigService');
}

function sendClientStatusToRenderer(channel, ready, message) {
  const log = windowManagerRef ? windowManagerRef.logToRenderer : console.log;
  const mainWindow = windowManagerRef ? windowManagerRef.getMainWindow() : null;
  
  const statusPayload = { ready, message };
  log(`[gRPC Manager] Sending client status to '${channel}': ${JSON.stringify(statusPayload)}`);
  if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) {
      if (!mainWindow.webContents.isLoading()) {
          mainWindow.webContents.send(channel, statusPayload);
      } else {
          mainWindow.webContents.once('did-finish-load', () => {
              if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDestroyed()) {
                  mainWindow.webContents.send(channel, statusPayload);
              }
          });
      }
  } else {
      log(`[gRPC Manager] Cannot send client status to renderer, mainWindow or webContents not available for channel ${channel}.`);
  }
}

function getServiceConfig(serviceIdentifier) {
  if (!serviceConfigs || serviceConfigs.length === 0) {
    console.warn('[gRPC Manager] serviceConfigs is not initialized or empty.');
    return null;
  }
  const config = serviceConfigs.find(c => c.name === serviceIdentifier);
  if (!config) {
    console.warn(`[gRPC Manager] No service configuration found for identifier: ${serviceIdentifier}`);
    return null;
  }
  return { ...config }; // Return a copy to prevent external modification
}

module.exports = {
  initializeGrpcManager,
  setupGrpcConnections,
  initializeSubtitlerClientAfterWindowLoad,
  getSubtitlerClient,
  getGreeterClient,
  sendClientStatusToRenderer,
  getServiceConfig, // Export the new function
getAIConfigServiceClient,
  // serviceConfigs // No longer exporting the raw array
};
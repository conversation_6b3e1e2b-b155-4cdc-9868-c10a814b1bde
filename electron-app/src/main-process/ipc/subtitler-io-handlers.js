const { ipcMain } = require('electron');
const subtitlerUtils = require('../utils/subtitler-utils');

// Dependencies will be injected.
let grpcManager;
let windowManager;
// let utils; // appUtils will still be passed for now, but parseSrtToSegments comes from subtitlerUtils

function initializeSubtitlerIoIpcHandlers(gRPCMgr, winMgr, appUtils) {
  grpcManager = gRPCMgr;
  windowManager = winMgr;
  // utils = appUtils; // SRT utils are now directly imported

  // Helper for individual subtitler methods (videoToAudio, audioToText, etc.)
  async function handleSubtitlerStreamMethod(methodName, event, request) {
    try {
      const subtitlerClient = grpcManager.getSubtitlerClient();
      if (!subtitlerClient) {
        throw new Error('SubtitlerClient not initialized');
      }
      
      return new Promise((resolve, reject) => {
        const stream = subtitlerClient[methodName](request);
        const results = [];
        stream.on('data', (response) => {
          let normalizedResponse;
          if (typeof response.getStageName === 'function') { // Protobuf object
            normalizedResponse = {
              stageName: response.getStageName(),
              percentage: response.getPercentage(),
              message: response.getMessage(),
              isError: response.getIsError(),
              errorMessage: response.getErrorMessage()
            };
            // Dynamically add specific response part
            const responseMethodName = `get${methodName.charAt(0).toUpperCase() + methodName.slice(1)}Response`; // e.g., getVideoToAudioResponse
            if (typeof response[responseMethodName] === 'function') {
              const specificResponseProto = response[responseMethodName]();
              if (specificResponseProto) {
                if (methodName === 'videoToAudio' && typeof specificResponseProto.getAudioPath === 'function') {
                    normalizedResponse[methodName + 'Response'] = { audioPath: specificResponseProto.getAudioPath() };
                } else if (methodName === 'audioToText' && typeof specificResponseProto.getSegmentsList === 'function') {
                    const segmentsList = specificResponseProto.getSegmentsList();
                    const segments = segmentsList.map(segmentProto => ({
                        text: segmentProto.getText(),
                        start_time_ms: segmentProto.getStartTimeMs(),
                        end_time_ms: segmentProto.getEndTimeMs()
                    }));
                    let transcript = typeof specificResponseProto.getTranscript === 'function' ? specificResponseProto.getTranscript() : '';
                    if (!transcript && segments.length > 0) transcript = segments.map(s => s.text).join(' ');
                    normalizedResponse[methodName + 'Response'] = { transcript: transcript, segments: segments };
                } else if (methodName === 'generateSubtitles' && typeof specificResponseProto.getSubtitleContent === 'function') {
                    normalizedResponse[methodName + 'Response'] = { subtitleContent: specificResponseProto.getSubtitleContent() };
                } else if (methodName === 'translateSubtitles' && typeof specificResponseProto.getTranslatedContent === 'function') {
                    normalizedResponse[methodName + 'Response'] = { translatedContent: specificResponseProto.getTranslatedContent() };
                }
              }
            }
          } else { // Plain JS object (e.g., from mock client)
            normalizedResponse = { ...response };
          }
          results.push(normalizedResponse);
          if (windowManager && windowManager.getMainWindow() && windowManager.getMainWindow().webContents) {
            windowManager.getMainWindow().webContents.send('progress-update', normalizedResponse);
          }
        });
        stream.on('end', () => resolve(results));
        stream.on('error', (err) => {
          console.error(`[IPC Handler - SubtitlerIO] Error in ${methodName} stream:`, err);
          const errorResponse = {
            stageName: methodName, percentage: 0, message: 'Processing failed',
            isError: true, errorMessage: err.message || 'Unknown error'
          };
          if (windowManager && windowManager.getMainWindow() && windowManager.getMainWindow().webContents) {
            windowManager.getMainWindow().webContents.send('progress-update', errorResponse);
          }
          reject(err);
        });
      });
    } catch (error) {
      console.error(`[IPC Handler - SubtitlerIO] Error in ${methodName}:`, error);
      throw error;
    }
  }

  ipcMain.handle('subtitler-video-to-audio', (event, request) => handleSubtitlerStreamMethod('videoToAudio', event, request));
  ipcMain.handle('subtitler-audio-to-text', (event, request) => {
    const grpcRequest = {
        audio_path: request.audio_path,
        request_word_timestamps: request.request_word_timestamps || false
    };
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] subtitler-audio-to-text request: ${JSON.stringify(grpcRequest)}`);
    return handleSubtitlerStreamMethod('audioToText', event, grpcRequest);
  });
  ipcMain.handle('subtitler-generate-subtitles', (event, request) => handleSubtitlerStreamMethod('generateSubtitles', event, request));
  ipcMain.handle('subtitler-translate-subtitles', (event, request) => handleSubtitlerStreamMethod('translateSubtitles', event, request));

  // 保存字幕功能
  ipcMain.handle('subtitler-save-subtitle', async (event, saveConfig) => {
    if (windowManager) {
      const saveConfigSummary = {
        ...saveConfig,
        segments: `Count: ${saveConfig.segments?.length}, First segment (if any): ${saveConfig.segments?.[0] ? JSON.stringify(saveConfig.segments[0]) : 'N/A'}`,
        original_content: saveConfig.original_content?.substring(0,100) + (saveConfig.original_content?.length > 100 ? '...' : ''),
        translated_content: saveConfig.translated_content?.substring(0,100) + (saveConfig.translated_content?.length > 100 ? '...' : ''),
        subtitle_content: saveConfig.subtitle_content?.substring(0,100) + (saveConfig.subtitle_content?.length > 100 ? '...' : '')
      };
      windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Received save-subtitle request (saveConfig summary): ${JSON.stringify(saveConfigSummary)}`);
    }
    const subtitlerClient = grpcManager.getSubtitlerClient();
    if (!subtitlerClient) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - SubtitlerIO] SubtitlerClient not initialized for save-subtitle.');
      throw new Error('SubtitlerClient not initialized');
    }

    const grpcRequest = {
      file_name: saveConfig.fileNamePrefix || '',
      subtitle_content: saveConfig.subtitle_content || '',
      format: saveConfig.format || 'srt',
      layout: saveConfig.layout || '仅原文',
      original_content: saveConfig.original_content || '',
      translated_content: saveConfig.translated_content || '',
      auto_save_to_default: typeof saveConfig.auto_save_to_default === 'boolean' ? saveConfig.auto_save_to_default : true,
      segments: []
    };

    if (saveConfig.segments && Array.isArray(saveConfig.segments) && saveConfig.segments.length > 0) {
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Using existing segments from saveConfig: ${saveConfig.segments.length} segments`);
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Sample segment from saveConfig: ${JSON.stringify(saveConfig.segments[0])}`);
      
      grpcRequest.segments = saveConfig.segments.map((seg, index) => {
        const originalText = seg.original_text || seg.text || seg.originalText || "";
        let translatedText = "";
        if (saveConfig.translationEnabled !== false) {
          translatedText = seg.translated_text || seg.translatedText || seg.translation || "";
        }
        if (index === 0 && windowManager) {
          windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Segment field mapping - originalText: "${originalText}", translatedText: "${translatedText}"`);
          windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Raw segment fields: ${JSON.stringify(Object.keys(seg))}`);
        }
        let startTime = 0;
        let endTime = 0;
        if (seg.start_time !== undefined) startTime = parseInt(seg.start_time, 10);
        else if (seg.start_time_ms !== undefined) startTime = parseInt(seg.start_time_ms, 10);
        else if (seg.startTimeMs !== undefined) startTime = parseInt(seg.startTimeMs, 10);
        if (seg.end_time !== undefined) endTime = parseInt(seg.end_time, 10);
        else if (seg.end_time_ms !== undefined) endTime = parseInt(seg.end_time_ms, 10);
        else if (seg.endTimeMs !== undefined) endTime = parseInt(seg.endTimeMs, 10);
        if (index === 0 && windowManager) {
          windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Time field mapping - startTime: ${startTime}, endTime: ${endTime}`);
        }
        return {
          start_time: startTime,
          end_time: endTime,
          original_text: originalText,
          translated_text: translatedText
        };
      });
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Converted ${grpcRequest.segments.length} segments from saveConfig`);
      if (grpcRequest.segments.length > 0 && windowManager) {
        windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] First converted segment: ${JSON.stringify(grpcRequest.segments[0])}`);
      }
    }
    else if (subtitlerUtils && typeof subtitlerUtils.parseSrtToSegments === 'function' &&
             (grpcRequest.original_content || grpcRequest.translated_content)) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - SubtitlerIO] No segments in saveConfig, attempting to parse from SRT content');
      const originalSegments = grpcRequest.original_content ? subtitlerUtils.parseSrtToSegments(grpcRequest.original_content) : [];
      const translatedSegments = grpcRequest.translated_content ? subtitlerUtils.parseSrtToSegments(grpcRequest.translated_content) : [];
      if (windowManager) {
        windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Parsed original segments count: ${originalSegments.length}`);
        windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Parsed translated segments count: ${translatedSegments.length}`);
      }
      if (originalSegments.length > 0 || translatedSegments.length > 0) {
        const maxLength = Math.max(originalSegments.length, translatedSegments.length);
        for (let i = 0; i < maxLength; i++) {
          const oSeg = originalSegments[i];
          const tSeg = translatedSegments[i];
          const originalText = oSeg ? (oSeg.text || "") : "";
          let translatedText = "";
          if (saveConfig.translationEnabled !== false) {
              translatedText = tSeg ? (tSeg.text || "") : "";
          }
          const startTimeMs = oSeg?.start_time_ms ?? tSeg?.start_time_ms ?? 0;
          const endTimeMs = oSeg?.end_time_ms ?? tSeg?.end_time_ms ?? 0;
          grpcRequest.segments.push({
            start_time: parseInt(startTimeMs, 10),
            end_time: parseInt(endTimeMs, 10),
            original_text: originalText,
            translated_text: translatedText
          });
        }
        if (grpcRequest.segments.length > 0 && windowManager) {
          windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Successfully generated ${grpcRequest.segments.length} segments from SRT parsing`);
          windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] First parsed segment: ${JSON.stringify(grpcRequest.segments[0])}`);
        }
      } else if (windowManager) {
        windowManager.logToRenderer('[IPC Handler - SubtitlerIO] WARNING: SRT parsing returned 0 segments from both original and translated content');
      }
    } 
    else {
      if (windowManager) {
        if (!subtitlerUtils || typeof subtitlerUtils.parseSrtToSegments !== 'function') windowManager.logToRenderer('[IPC Handler - SubtitlerIO] ERROR: subtitlerUtils.parseSrtToSegments is not available');
        else windowManager.logToRenderer('[IPC Handler - SubtitlerIO] ERROR: No segments in saveConfig and no SRT content to parse');
        windowManager.logToRenderer('[IPC Handler - SubtitlerIO] WARNING: segments array will be empty, which may cause backend processing to fail');
      }
    }
    
    if (windowManager) {
      const segmentsSummary = grpcRequest.segments?.length > 0
        ? `Count: ${grpcRequest.segments.length}, First segment: ${JSON.stringify(grpcRequest.segments[0])}`
        : (grpcRequest.segments ? 'Count: 0' : 'Segments field missing or not an array');
      
      const requestSummaryForLog = {
        ...grpcRequest,
        segments: `[Segments Summary: ${segmentsSummary}]` // 用摘要替换实际的 segments 数组
      };
      
      // 对可能较长的SRT内容字段进行截断
      const maxLength = 200;
      const ellipsis = '... (truncated)';

      if (requestSummaryForLog.original_content && requestSummaryForLog.original_content.length > maxLength) {
        requestSummaryForLog.original_content = requestSummaryForLog.original_content.substring(0, maxLength) + ellipsis;
      }
      if (requestSummaryForLog.translated_content && requestSummaryForLog.translated_content.length > maxLength) {
        requestSummaryForLog.translated_content = requestSummaryForLog.translated_content.substring(0, maxLength) + ellipsis;
      }
      // subtitle_content 通常在 saveConfig 中是空的，但以防万一也处理一下
      if (requestSummaryForLog.subtitle_content && requestSummaryForLog.subtitle_content.length > maxLength) {
        requestSummaryForLog.subtitle_content = requestSummaryForLog.subtitle_content.substring(0, maxLength) + ellipsis;
      }
    
      windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Constructed gRPC SaveSubtitleRequest (summary): ${JSON.stringify(requestSummaryForLog)}`);
    }
 
    try {
      const response = await new Promise((resolve, reject) => {
        subtitlerClient.saveSubtitle(grpcRequest, {}, (error, grpcResponse) => {
          if (error) {
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Error in save-subtitle gRPC call: ${error.details || error.message}`);
            reject(error);
          } else {
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Save-subtitle gRPC call successful: ${JSON.stringify(grpcResponse)}`);
            resolve(grpcResponse);
          }
        });
      });
      return response;
    } catch (error) {
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Save-subtitle processing failed: ${error.message}`);
      throw error;
    }
  });

  // 批量保存字幕功能
  ipcMain.handle('subtitler-batch-save-subtitle', async (event, request) => {
    if (windowManager) {
      const requestSummary = {
        ...request,
        segments: `Count: ${request.segments?.length}, First segment (if any): ${request.segments?.[0] ? JSON.stringify(request.segments[0]) : 'N/A'}`,
        original_content: request.original_content?.substring(0,100) + (request.original_content?.length > 100 ? '...' : ''),
        translated_content: request.translated_content?.substring(0,100) + (request.translated_content?.length > 100 ? '...' : '')
      };
      windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Received batch-save-subtitle request (summary): ${JSON.stringify(requestSummary)}`);
    }
    let translatedTextsArray = [];
    if (request.translated_content && typeof request.translated_content === 'string') {
      const srtBlocks = request.translated_content.trim().split(/\r?\n\r?\n/);
      srtBlocks.forEach(block => {
        const lines = block.split(/\r?\n/);
        if (lines.length > 2) {
          const textContent = lines.slice(2).join(' ').trim();
          if (textContent) translatedTextsArray.push(textContent);
        }
      });
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Parsed ${translatedTextsArray.length} translated texts from SRT. First item: '${translatedTextsArray.length > 0 ? translatedTextsArray[0] : "N/A"}'`);
    } else if (windowManager) {
      windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] No translated_content found in request, or it's not a string. translated_text will be empty for all segments.`);
    }
    
    if (request.segments && Array.isArray(request.segments)) {
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Converting ${request.segments.length} segments to protobuf format`);
      request.segments = request.segments.map((seg, index) => {
        const originalText = seg.original_text || seg.text || "";
        const translatedText = (index < translatedTextsArray.length) ? translatedTextsArray[index] : "";
        const convertedSeg = {
          start_time: seg.start_time || seg.start_time_ms || 0,
          end_time: seg.end_time || seg.end_time_ms || 0,
          original_text: originalText,
          translated_text: translatedText
        };
        if (index === 0 && windowManager) {
          windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Example converted segment (index 0): ${JSON.stringify(convertedSeg)}`);
        }
        return convertedSeg;
      });
    }
    
    const subtitlerClient = grpcManager.getSubtitlerClient();
    if (!subtitlerClient) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - SubtitlerIO] SubtitlerClient not initialized for batch-save-subtitle.');
      throw new Error('SubtitlerClient not initialized');
    }

    try {
      const response = await new Promise((resolve, reject) => {
        subtitlerClient.batchSaveSubtitle(request, {}, (error, grpcResponse) => {
          if (error) {
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Error in batch-save-subtitle: ${error.message}`);
            reject(error);
          } else {
            if (windowManager) {
              const grpcResponseSummary = {
                success_count: grpcResponse.getSuccessCount ? grpcResponse.getSuccessCount() : grpcResponse.success_count, // Handle both proto and plain obj
                failure_count: grpcResponse.getFailureCount ? grpcResponse.getFailureCount() : grpcResponse.failure_count,
                responses_count: grpcResponse.getResponsesList ? grpcResponse.getResponsesList().length : (grpcResponse.responses ? grpcResponse.responses.length : 0),
                first_response_summary: null
              };
              const responsesList = grpcResponse.getResponsesList ? grpcResponse.getResponsesList() : (grpcResponse.responses || []);
              if (responsesList.length > 0) {
                const firstResp = responsesList[0];
                grpcResponseSummary.first_response_summary = {
                  file_path: firstResp.getFilePath ? firstResp.getFilePath() : firstResp.file_path,
                  file_name: firstResp.getFileName ? firstResp.getFileName() : firstResp.file_name,
                  saved_to_default: firstResp.getSavedToDefault ? firstResp.getSavedToDefault() : firstResp.saved_to_default
                };
              }
              windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Batch-save-subtitle successful (summary): ${JSON.stringify(grpcResponseSummary)}`);
            }
            resolve(grpcResponse);
          }
        });
      });
      return response;
    } catch (error) {
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - SubtitlerIO] Batch-save-subtitle failed: ${error.message}`);
      throw error;
    }
  });
}

module.exports = {
  initializeSubtitlerIoIpcHandlers,
};
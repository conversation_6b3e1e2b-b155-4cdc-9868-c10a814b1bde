const { ipcMain } = require('electron');

// Dependencies will be injected.
let grpcManager;
let windowManager;

function initializeGreeterIpcHandlers(gRPCMgr, winMgr) {
  grpcManager = gRPCMgr;
  windowManager = winMgr;

  // Greeter Handlers
  ipcMain.handle('python-say-hello', async (event, name) => {
      const client = grpcManager.getGreeterClient('python');
      if (!client || typeof client.sayHello !== 'function') {
        const errMsg = 'Python Greeter gRPC client is not available or not a valid client.';
        console.error(`[IPC Handler - Greeter] ${errMsg}`);
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - Greeter] ${errMsg}`);
        return Promise.reject(new Error(errMsg));
      }
      return new Promise((resolve, reject) => {
          client.sayHello({ name: name }, (error, response) => {
              if (error) {
                  console.error('[IPC Hand<PERSON> - Greeter] Python Greeter gRPC Error:', error);
                  if (windowManager) windowManager.logToRenderer(`[IPC Handler - Greeter] Python Greeter gRPC Error: ${error.details || error.message}`);
                  reject(error);
              } else {
                  if (windowManager) windowManager.logToRenderer(`[IPC Handler - Greeter] Python Greeter gRPC Success: ${JSON.stringify(response)}`);
                  resolve(response);
              }
          });
      });
  });

  ipcMain.handle('go-say-hello', async (event, name) => {
      const client = grpcManager.getGreeterClient('go');
      if (!client || typeof client.sayHello !== 'function') {
        const errMsg = 'Go Greeter gRPC client is not available or not a valid client.';
        console.error(`[IPC Handler - Greeter] ${errMsg}`);
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - Greeter] ${errMsg}`);
        return Promise.reject(new Error(errMsg));
      }
      return new Promise((resolve, reject) => {
          client.sayHello({ name: name }, (error, response) => {
              if (error) {
                  console.error('[IPC Handler - Greeter] Go Greeter gRPC Error:', error);
                  if (windowManager) windowManager.logToRenderer(`[IPC Handler - Greeter] Go Greeter gRPC Error: ${error.details || error.message}`);
                  reject(error);
              } else {
                  if (windowManager) windowManager.logToRenderer(`[IPC Handler - Greeter] Go Greeter gRPC Success: ${JSON.stringify(response)}`);
                  resolve(response);
              }
          });
      });
  });

  ipcMain.handle('java-say-hello', async (event, name) => {
      const client = grpcManager.getGreeterClient('java');
      if (!client || typeof client.sayHello !== 'function') {
        const errMsg = 'Java Greeter gRPC client is not available or not a valid client.';
        console.error(`[IPC Handler - Greeter] ${errMsg}`);
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - Greeter] ${errMsg}`);
        return Promise.reject(new Error(errMsg));
      }
      return new Promise((resolve, reject) => {
          client.sayHello({ name: name }, (error, response) => {
              if (error) {
                  console.error('[IPC Handler - Greeter] Java Greeter gRPC Error:', error);
                  if (windowManager) windowManager.logToRenderer(`[IPC Handler - Greeter] Java Greeter gRPC Error: ${error.details || error.message}`);
                  reject(error);
              } else {
                  if (windowManager) windowManager.logToRenderer(`[IPC Handler - Greeter] Java Greeter gRPC Success: ${JSON.stringify(response)}`);
                  resolve(response);
              }
          });
      });
  });
}

module.exports = {
  initializeGreeterIpcHandlers,
};
const { ipcMain } = require('electron');

// Dependencies will be injected.
let grpcManagerInstance;
let windowManagerInstance;

function initializeGrpcTestHandlers(grpcMgr, winMgr) {
  grpcManagerInstance = grpcMgr;
  windowManagerInstance = winMgr;

  ipcMain.handle('grpc-test:invoke-method', async (event, { service, method, payload }) => {
    if (windowManagerInstance) {
      windowManagerInstance.logToRenderer(`[IPC Handler - GrpcTest] Received grpc-test:invoke-method. Service: ${service}, Method: ${method}, Payload: ${JSON.stringify(payload)}`);
    }

    let serviceIdentifier = null;
    let client = null;

    // Map user-friendly service name to serviceIdentifier used in grpcManager
    // Determine backend based on the payload name or add explicit backend selection
    if (service === 'Greeter') {
      // Determine backend based on the user name in payload
      const userName = payload?.name || '';
      if (userName.toLowerCase().includes('python')) {
        serviceIdentifier = 'greeterServicePython';
        client = grpcManagerInstance.getGreeterClient('python');
      } else if (userName.toLowerCase().includes('go')) {
        serviceIdentifier = 'greeterServiceGo';
        client = grpcManagerInstance.getGreeterClient('go');
      } else if (userName.toLowerCase().includes('java')) {
        serviceIdentifier = 'greeterServiceJava';
        client = grpcManagerInstance.getGreeterClient('java');
      } else {
        // Default to Python if no specific backend is indicated
        serviceIdentifier = 'greeterServicePython';
        client = grpcManagerInstance.getGreeterClient('python');
      }
    } else if (service === 'Subtitler') {
      serviceIdentifier = 'subtitlerService';
      client = grpcManagerInstance.getSubtitlerClient();
    } else {
      const errorMsg = `Unsupported service: ${service}`;
      if (windowManagerInstance) windowManagerInstance.logToRenderer(`[IPC Handler - GrpcTest] Error: ${errorMsg}`);
      return { error: { message: errorMsg } };
    }

    if (!client) {
      const errorMsg = `Client for service identifier '${serviceIdentifier || service}' not available.`;
      if (windowManagerInstance) windowManagerInstance.logToRenderer(`[IPC Handler - GrpcTest] Error: ${errorMsg}`);
      return { error: { message: errorMsg } };
    }

    if (typeof client[method] !== 'function') {
      const errorMsg = `Method '${method}' not found on client for service '${service}'.`;
      if (windowManagerInstance) windowManagerInstance.logToRenderer(`[IPC Handler - GrpcTest] Error: ${errorMsg}`);
      return { error: { message: errorMsg } };
    }

    // This handler currently assumes unary calls. Streaming calls would need different handling.
    // The gRPC client methods expect a callback for unary calls.
    return new Promise((resolve) => {
      try {
        client[method](payload, (err, response) => {
          if (err) {
            if (windowManagerInstance) windowManagerInstance.logToRenderer(`[IPC Handler - GrpcTest] gRPC call to ${service}.${method} failed: ${err.message}`);
            resolve({ error: { message: err.message, details: err.details, code: err.code } });
          } else {
            if (windowManagerInstance) windowManagerInstance.logToRenderer(`[IPC Handler - GrpcTest] gRPC call to ${service}.${method} successful.`);
            resolve({ data: response });
          }
        });
      } catch (e) {
        if (windowManagerInstance) windowManagerInstance.logToRenderer(`[IPC Handler - GrpcTest] Exception during gRPC call to ${service}.${method}: ${e.message}`);
        resolve({ error: { message: `Exception during gRPC call: ${e.message}` } });
      }
    });
  });

  ipcMain.handle('grpc:get-available-services', async () => {
    if (windowManagerInstance) {
      windowManagerInstance.logToRenderer('[IPC Handler - GrpcTest] Received grpc:get-available-services');
    }
    // 模拟动态加载的服务列表
    const services = [
      {
        name: 'Greeter',
        methods: ['SayHello'],
        templates: [
          {
            templateName: 'SayHello - Python User',
            methodName: 'SayHello',
            serviceName: 'Greeter', // 明确指定服务名
            requestPayload: JSON.stringify({ name: "PythonUser" }, null, 2)
          },
          {
            templateName: 'SayHello - Go User',
            methodName: 'SayHello',
            serviceName: 'Greeter',
            requestPayload: JSON.stringify({ name: "GoUser" }, null, 2)
          },
          {
            templateName: 'SayHello - Java User',
            methodName: 'SayHello',
            serviceName: 'Greeter',
            requestPayload: JSON.stringify({ name: "JavaUser" }, null, 2)
          }
        ]
      },
      {
        name: 'Subtitler',
        methods: ['ProcessVideo', 'ProcessAudio'], // 假设这些是可用的方法
        templates: [
           {
            templateName: 'ProcessVideo - Sample',
            methodName: 'ProcessVideo',
            serviceName: 'Subtitler',
            requestPayload: JSON.stringify({ video_path: "/path/to/video.mp4", language: "en" }, null, 2)
          }
        ]
      }
      // 可以根据需要添加更多模拟服务
    ];
    if (windowManagerInstance) {
      windowManagerInstance.logToRenderer(`[IPC Handler - GrpcTest] Returning services: ${JSON.stringify(services.map(s => s.name))}`);
    }
    return services;
  });
}

module.exports = {
  initializeGrpcTestHandlers,
};
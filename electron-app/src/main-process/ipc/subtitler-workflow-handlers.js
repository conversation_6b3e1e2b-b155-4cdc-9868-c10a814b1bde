const { ipcMain, dialog, app } = require('electron'); // Added dialog, app
const fs = require('node:fs').promises; // For async file operations
const path = require('node:path'); // For path operations
const subtitlerUtils = require('../utils/subtitler-utils');

// Dependencies will be injected.
let grpcManager;
let windowManager;
// let utils; // appUtils will still be passed for now, but parseSrtToSegments comes from subtitlerUtils

// --- Top-level Data Extractors ---
const v2aDataExtractor = {
    directAccessKey: 'video_to_audio_response',
    extractorFunc: (v2aRespObject) => ({ audio_path: v2aRespObject.getAudioPath ? v2aRespObject.getAudioPath() : v2aRespObject.audio_path }),
    responseKey: 'video_to_audio_response'
};
const a2tDataExtractor = {
    directAccessKey: 'audio_to_text_response',
    extractorFunc: (a2tRespObject) => {
        const segmentsList = a2tRespObject.getSegmentsList ? a2tRespObject.getSegmentsList() : (a2tRespObject.segments || []);
        const currentSegments = segmentsList.map(s => ({ text: s.getText ? s.getText() : s.text, start_time_ms: s.getStartTimeMs ? s.getStartTimeMs() : s.start_time_ms, end_time_ms: s.getEndTimeMs ? s.getEndTimeMs() : s.end_time_ms }));
        const transcript = a2tRespObject.getTranscript ? a2tRespObject.getTranscript() : (a2tRespObject.transcript || '');
        return { transcript: transcript, segments: currentSegments };
    },
    responseKey: 'audio_to_text_response'
};
const genSubDataExtractor = {
    directAccessKey: 'generate_subtitles_response',
    extractorFunc: (genSubRespObject) => ({ srt_content: genSubRespObject.getSubtitleContent ? genSubRespObject.getSubtitleContent() : genSubRespObject.srt_content }),
    responseKey: 'generate_subtitles_response'
};
const transSubDataExtractor = {
    directAccessKey: 'translate_subtitles_response',
    extractorFunc: (transSubRespObject) => ({ translated_subtitle_content: transSubRespObject.getTranslatedContent ? transSubRespObject.getTranslatedContent() : transSubRespObject.translated_subtitle_content }),
    responseKey: 'translate_subtitles_response'
};

// --- Top-level gRPC Stream Step Function ---
const callGrpcStreamStep = (sendProgressFunction, methodName, stepRequest, dataExtractor) => {
    return new Promise((resolve, reject) => {
        const subtitlerClient = grpcManager.getSubtitlerClient();
        if (!subtitlerClient) {
            const errorMsg = `SubtitlerClient not initialized when attempting to call ${methodName}.`;
            if (windowManager) windowManager.logToRenderer(`[Workflow Step Error] ${errorMsg}`);
            if (typeof sendProgressFunction === 'function') {
                 sendProgressFunction(methodName, 0, 'gRPC Client Setup Error', true, errorMsg);
            }
            return reject(new Error(errorMsg));
        }

        if (windowManager) windowManager.logToRenderer(`[Workflow Step] Calling ${methodName} with request: ${JSON.stringify(stepRequest)}`);
        const stream = subtitlerClient[methodName](stepRequest);
        let lastMeaningfulDataFromStream = null;

        stream.on('data', (responseProto) => {
            const stageName = typeof responseProto.getStageName === 'function' ? responseProto.getStageName() : methodName;
            const percentage = typeof responseProto.getPercentage === 'function' ? responseProto.getPercentage() : 0;
            const messageText = typeof responseProto.getMessage === 'function' ? responseProto.getMessage() : 'Processing...';
            const isError = typeof responseProto.getIsError === 'function' ? responseProto.getIsError() : false;
            const errorMessageText = typeof responseProto.getErrorMessage === 'function' ? responseProto.getErrorMessage() : '';
            
            let extractedData = null;
            if (dataExtractor && dataExtractor.directAccessKey && responseProto.hasOwnProperty(dataExtractor.directAccessKey)) {
                const specificResponseObject = responseProto[dataExtractor.directAccessKey];
                if (specificResponseObject) {
                    extractedData = dataExtractor.extractorFunc(specificResponseObject);
                    let isMeaningfulUpdate = false;
                    if (extractedData) {
                        if (methodName === 'videoToAudio') {
                            if (extractedData.audio_path && extractedData.audio_path.trim() !== '') isMeaningfulUpdate = true;
                        } else if (methodName === 'audioToText') {
                            if (typeof extractedData.transcript === 'string' || (Array.isArray(extractedData.segments) && extractedData.segments.length > 0) || (extractedData.hasOwnProperty('transcript') && extractedData.hasOwnProperty('segments'))) isMeaningfulUpdate = true;
                        } else if (methodName === 'generateSubtitles') {
                            if (extractedData.srt_content && extractedData.srt_content.trim() !== '' || extractedData.hasOwnProperty('srt_content')) isMeaningfulUpdate = true;
                        } else if (methodName === 'translateSubtitles') {
                            if (extractedData.translated_subtitle_content && extractedData.translated_subtitle_content.trim() !== '' || extractedData.hasOwnProperty('translated_subtitle_content')) isMeaningfulUpdate = true;
                        } else {
                            if ((typeof extractedData === 'object' && extractedData !== null && Object.keys(extractedData).length > 0) || (typeof extractedData !== 'object' && extractedData !== null)) isMeaningfulUpdate = true;
                        }
                    }
                    if(isMeaningfulUpdate){
                        lastMeaningfulDataFromStream = extractedData;
                    }
                }
            }
            if (typeof sendProgressFunction === 'function') {
                sendProgressFunction(stageName, percentage, messageText, isError, errorMessageText, extractedData ? { [dataExtractor.responseKey || methodName + '_response']: extractedData } : null );
            }
            if (isError) {
                stream.cancel(); // Important to cancel the stream on error
                reject(new Error(errorMessageText || `Error in ${stageName}`));
            }
        });
        stream.on('end', () => {
            if (windowManager) windowManager.logToRenderer(`[Workflow Step] ${methodName} stream ended.`);
            resolve(lastMeaningfulDataFromStream);
        });
        stream.on('error', (err) => {
            if (windowManager) windowManager.logToRenderer(`[Workflow Step] Error in ${methodName} stream: ${err.details || err.message}`);
            if (typeof sendProgressFunction === 'function') {
                sendProgressFunction(methodName, 100, 'Stream error', true, err.details || err.message);
            }
            reject(err);
        });
    });
};

function initializeSubtitlerWorkflowIpcHandlers(gRPCMgr, winMgr, appUtils) {
  grpcManager = gRPCMgr;
  windowManager = winMgr;
  // utils = appUtils; // SRT utils are now directly imported

  // 完整工作流
  ipcMain.handle('subtitler-full-workflow', async (event, request) => {
    console.log('[IPC Handler - Subtitler] Received subtitler-full-workflow request:', request);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Received full-workflow request: ${JSON.stringify(request)}`);
    // const subtitlerClient = grpcManager.getSubtitlerClient(); // Moved into callGrpcStreamStep
    // if (!subtitlerClient) {
    //   if (windowManager) windowManager.logToRenderer('[IPC Handler - Workflow] SubtitlerClient not initialized for full-workflow.');
    //   throw new Error('SubtitlerClient not initialized');
    // }

    const {
      file_path,
      workflow_type,
      target_language,
      request_word_timestamps,
      req_text_content
    } = request;

    // Debug logging for text_to_translated_srt workflow
    if (workflow_type === 'text_to_translated_srt') {
      if (windowManager) {
        windowManager.logToRenderer(`[IPC Handler - Workflow] text_to_translated_srt debug:`);
        windowManager.logToRenderer(`- req_text_content exists: ${!!req_text_content}`);
        windowManager.logToRenderer(`- req_text_content length: ${req_text_content ? req_text_content.length : 0}`);
        windowManager.logToRenderer(`- req_text_content preview: ${req_text_content ? req_text_content.substring(0, 100) : 'null'}`);
        windowManager.logToRenderer(`- target_language: ${target_language}`);
      }
    }

    let activeLastMeaningfulResponseKey = null;
    let activeLastMeaningfulData = null;
    let finalSegmentsForPreview = null;
    let finalTextContentForPreview = null;
    
    const currentMainWindow = windowManager.getMainWindow();

    const sendProgress = (stageName, percentage, message, isError = false, errorMessage = '', data = null) => {
      const progress = { stageName, percentage, message, isError, errorMessage, ...data };
      if (windowManager) windowManager.logToRenderer(`[Workflow Progress] ${stageName} (${percentage}%): ${message} ${isError ? 'ERROR: ' + errorMessage : ''} Data: ${data ? JSON.stringify(data) : 'null'}`);
      if (currentMainWindow && currentMainWindow.webContents && !currentMainWindow.webContents.isDestroyed()) {
        currentMainWindow.webContents.send('progress-update', progress);
      }
    };
    
    try {
      let audioPathForCurrentWorkflow = null;
      let textContentForCurrentWorkflow = null; 
      let srtContentForCurrentWorkflow = null;

      if (workflow_type.startsWith('vid_')) {
        if (!file_path) throw new Error(`File path is required for ${workflow_type}.`);
      } else if (workflow_type.startsWith('audio_')) {
        if (!file_path) throw new Error(`File path is required for ${workflow_type}.`);
        audioPathForCurrentWorkflow = file_path;
      } else if (workflow_type.startsWith('text_')) {
        if (!req_text_content) throw new Error(`Text content (req_text_content) is required for ${workflow_type}.`);
        textContentForCurrentWorkflow = req_text_content;
        audioPathForCurrentWorkflow = file_path; // Optional audio for alignment
      } else {
        throw new Error(`Unknown or unhandled workflow_type: ${workflow_type}.`);
      }

      switch (workflow_type) {
        case 'vid_to_audio': {
          sendProgress('VideoToAudio', 0, 'Starting video to audio conversion...');
          const v2aResponse = await callGrpcStreamStep(sendProgress, 'videoToAudio', { video_path: file_path }, v2aDataExtractor);
          if (v2aResponse && v2aResponse.audio_path) {
            audioPathForCurrentWorkflow = v2aResponse.audio_path;
            activeLastMeaningfulData = v2aResponse;
            activeLastMeaningfulResponseKey = v2aDataExtractor.responseKey;
            sendProgress('VideoToAudio', 100, `Audio extracted: ${audioPathForCurrentWorkflow}`, false, '', { [activeLastMeaningfulResponseKey]: activeLastMeaningfulData });
            sendProgress('WorkflowComplete', 100, 'Video to audio conversion complete.', false, '', { final_result: activeLastMeaningfulResponseKey, [activeLastMeaningfulResponseKey]: activeLastMeaningfulData });

            // Return the result for the IPC call
            return {
              success: true,
              audio_path: audioPathForCurrentWorkflow,
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData
            };
          } else {
            throw new Error('VideoToAudio step failed: Could not extract audio path.');
          }
        }

        case 'vid_to_text':
        case 'vid_to_text_ts':
        case 'audio_to_text':
        case 'audio_to_text_ts': {
          if (workflow_type.startsWith('vid_')) {
            sendProgress('VideoToAudio', 0, 'Extracting audio from video for text conversion...');
            const v2aResponse = await callGrpcStreamStep(sendProgress, 'videoToAudio', { video_path: file_path }, v2aDataExtractor);
            if (v2aResponse && v2aResponse.audio_path) {
              audioPathForCurrentWorkflow = v2aResponse.audio_path;
              sendProgress('VideoToAudio', 100, `Audio extracted: ${audioPathForCurrentWorkflow}`, false, '', { [v2aDataExtractor.responseKey]: v2aResponse });
            } else {
              throw new Error('VideoToAudio step failed before text conversion.');
            }
          }
          if (!audioPathForCurrentWorkflow) throw new Error('Audio path is missing for AudioToText step.');
          
          sendProgress('AudioToText', 0, 'Converting audio to text...');
          const useWordTimestamps = request_word_timestamps || workflow_type.endsWith('_ts');
          const a2tParams = { audio_path: audioPathForCurrentWorkflow, request_word_timestamps: useWordTimestamps };
          const a2tResponse = await callGrpcStreamStep(sendProgress, 'audioToText', a2tParams, a2tDataExtractor);
 
          if (a2tResponse) {
            textContentForCurrentWorkflow = a2tResponse.transcript;
            finalSegmentsForPreview = a2tResponse.segments; 
            finalTextContentForPreview = textContentForCurrentWorkflow;
            if (!textContentForCurrentWorkflow && finalSegmentsForPreview && finalSegmentsForPreview.length > 0) {
              textContentForCurrentWorkflow = finalSegmentsForPreview.map(s => s.text).join(' ');
              finalTextContentForPreview = textContentForCurrentWorkflow;
            }
            activeLastMeaningfulData = { transcript: textContentForCurrentWorkflow, segments: finalSegmentsForPreview };
            activeLastMeaningfulResponseKey = a2tDataExtractor.responseKey;
            sendProgress('AudioToText', 100, `Transcription complete.`, false, '', { [activeLastMeaningfulResponseKey]: activeLastMeaningfulData });
            sendProgress('WorkflowComplete', 100, 'Workflow complete: Audio to Text.', false, '', {
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData
            });

            // Return the result for the IPC call
            return {
              success: true,
              transcript: textContentForCurrentWorkflow,
              segments: finalSegmentsForPreview,
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData
            };
          } else {
            throw new Error('AudioToText step failed.');
          }
          break;
        }

        case 'vid_to_srt':
        case 'audio_to_srt':
        case 'text_to_srt': {
          if (workflow_type.startsWith('vid_')) {
            sendProgress('VideoToAudio', 0, 'Extracting audio from video for SRT generation...');
            const v2aResponse = await callGrpcStreamStep(sendProgress, 'videoToAudio', { video_path: file_path }, v2aDataExtractor);
            if (v2aResponse && v2aResponse.audio_path) {
              audioPathForCurrentWorkflow = v2aResponse.audio_path;
              sendProgress('VideoToAudio', 100, `Audio extracted: ${audioPathForCurrentWorkflow}`, false, '', { [v2aDataExtractor.responseKey]: v2aResponse });
            } else {
              throw new Error('VideoToAudio step failed before SRT generation.');
            }
          }
          
          if (workflow_type === 'vid_to_srt' || workflow_type === 'audio_to_srt') {
            if (!audioPathForCurrentWorkflow) throw new Error('Audio path is missing for ASR before SRT generation.');
            sendProgress('AudioToText', 0, 'Converting audio to text for SRT...');
            const useWordTimestampsForASR = request_word_timestamps || false; 
            const a2tParams = { audio_path: audioPathForCurrentWorkflow, request_word_timestamps: useWordTimestampsForASR };
            const a2tResponse = await callGrpcStreamStep(sendProgress, 'audioToText', a2tParams, a2tDataExtractor);
            if (a2tResponse) {
              textContentForCurrentWorkflow = a2tResponse.transcript;
              if (!textContentForCurrentWorkflow && a2tResponse.segments && a2tResponse.segments.length > 0) {
                textContentForCurrentWorkflow = a2tResponse.segments.map(s => s.text).join(' ');
              }
              sendProgress('AudioToText', 100, 'Transcription complete for SRT generation.', false, '', { [a2tDataExtractor.responseKey]: { transcript: textContentForCurrentWorkflow, segments: a2tResponse.segments } });
            } else {
              throw new Error('AudioToText step failed before SRT generation.');
            }
          }
          if (!textContentForCurrentWorkflow) {
             throw new Error('Text content is missing for subtitle generation.');
          }

          sendProgress('GenerateSubtitles', 0, 'Generating SRT subtitles...');
          const genSubParams = { text: textContentForCurrentWorkflow, audio_path: audioPathForCurrentWorkflow };
          const genSubResponse = await callGrpcStreamStep(sendProgress, 'generateSubtitles', genSubParams, genSubDataExtractor);
 
          if (genSubResponse && genSubResponse.srt_content) {
            srtContentForCurrentWorkflow = genSubResponse.srt_content;
            activeLastMeaningfulData = genSubResponse;
            activeLastMeaningfulResponseKey = genSubDataExtractor.responseKey;
            
            if (subtitlerUtils && typeof subtitlerUtils.parseSrtToSegments === 'function') {
                finalSegmentsForPreview = subtitlerUtils.parseSrtToSegments(srtContentForCurrentWorkflow);
            } else {
                finalSegmentsForPreview = []; // Fallback or log error
                if(windowManager) windowManager.logToRenderer('[IPC Handler - Workflow] ERROR: subtitlerUtils.parseSrtToSegments is not available for SRT preview.');
            }

            if (finalSegmentsForPreview && finalSegmentsForPreview.length > 0) {
              finalTextContentForPreview = finalSegmentsForPreview.map(s => s.text).join(' ');
            } else {
              finalTextContentForPreview = textContentForCurrentWorkflow; 
              if(windowManager) windowManager.logToRenderer('[IPC Handler - Workflow] Could not parse generated SRT for preview or it was empty.');
            }
            sendProgress('GenerateSubtitles', 100, `SRT subtitles generated.`, false, '', { [activeLastMeaningfulResponseKey]: activeLastMeaningfulData });
            sendProgress('WorkflowComplete', 100, 'Workflow complete: SRT Generation.', false, '', {
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData,
              audio_to_text_response: { transcript: finalTextContentForPreview, segments: finalSegmentsForPreview }
            });

            // Return the result for the IPC call
            return {
              success: true,
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData,
              audio_to_text_response: { transcript: finalTextContentForPreview, segments: finalSegmentsForPreview }
            };
          } else {
            throw new Error('GenerateSubtitles step failed.');
          }
          break;
        }

        case 'vid_to_translated_srt':
        case 'audio_to_translated_srt':
        case 'text_to_translated_srt': {
          if (workflow_type.startsWith('vid_')) {
            sendProgress('VideoToAudio', 0, 'Extracting audio for translation workflow...');
            const v2aResponse = await callGrpcStreamStep(sendProgress, 'videoToAudio', { video_path: file_path }, v2aDataExtractor);
            if (v2aResponse && v2aResponse.audio_path) {
              audioPathForCurrentWorkflow = v2aResponse.audio_path;
              sendProgress('VideoToAudio', 100, `Audio extracted: ${audioPathForCurrentWorkflow}`, false, '', { [v2aDataExtractor.responseKey]: v2aResponse });
            } else {
              throw new Error('VideoToAudio step failed in translation workflow.');
            }
          }

          if (workflow_type === 'vid_to_translated_srt' || workflow_type === 'audio_to_translated_srt') {
            if (!audioPathForCurrentWorkflow) throw new Error('Audio path missing for ASR in translation workflow.');
            sendProgress('AudioToText', 0, 'Converting audio to text for translation...');
            const useWordTimestampsForASR = request_word_timestamps || false;
            const a2tParams = { audio_path: audioPathForCurrentWorkflow, request_word_timestamps: useWordTimestampsForASR };
            const a2tResponse = await callGrpcStreamStep(sendProgress, 'audioToText', a2tParams, a2tDataExtractor);
            if (a2tResponse) {
              textContentForCurrentWorkflow = a2tResponse.transcript;
              if (!textContentForCurrentWorkflow && a2tResponse.segments && a2tResponse.segments.length > 0) {
                textContentForCurrentWorkflow = a2tResponse.segments.map(s => s.text).join(' ');
              }
              finalSegmentsForPreview = a2tResponse.segments; 
              finalTextContentForPreview = textContentForCurrentWorkflow;
              if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Saved original ASR segments: ${finalSegmentsForPreview ? finalSegmentsForPreview.length : 0} segments`);
              sendProgress('AudioToText', 100, 'Transcription complete for translation.', false, '', { [a2tDataExtractor.responseKey]: { transcript: textContentForCurrentWorkflow, segments: finalSegmentsForPreview } });
            } else {
              throw new Error('AudioToText step failed in translation workflow.');
            }
          }
          if (!textContentForCurrentWorkflow) {
            throw new Error('Text content missing for SRT generation in translation workflow.');
          }

          sendProgress('GenerateSubtitles', 0, 'Generating original SRT for translation...');
          const genSubParams = { text: textContentForCurrentWorkflow, audio_path: audioPathForCurrentWorkflow };
          const genSubResponse = await callGrpcStreamStep(sendProgress, 'generateSubtitles', genSubParams, genSubDataExtractor);
          if (genSubResponse && genSubResponse.srt_content) {
            srtContentForCurrentWorkflow = genSubResponse.srt_content;
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Generated SRT but keeping original ASR segments: ${finalSegmentsForPreview ? finalSegmentsForPreview.length : 0} segments`);
            sendProgress('GenerateSubtitles', 100, 'Original SRT generated for translation.', false, '', { [genSubDataExtractor.responseKey]: genSubResponse, audio_to_text_response: { transcript: textContentForCurrentWorkflow, segments: finalSegmentsForPreview } });
          } else {
            throw new Error('GenerateSubtitles step failed in translation workflow.');
          }

          if (!srtContentForCurrentWorkflow) throw new Error('Original SRT content missing for translation.');
          if (!target_language) throw new Error('Target language missing for translation.');
          
          sendProgress('TranslateSubtitles', 0, `Translating SRT to ${target_language}...`);
          const transSubParams = { subtitle_content: srtContentForCurrentWorkflow, target_language: target_language };
          const transSubResponse = await callGrpcStreamStep(sendProgress, 'translateSubtitles', transSubParams, transSubDataExtractor);
 
          if (transSubResponse && transSubResponse.translated_subtitle_content) {
            const translatedSrt = transSubResponse.translated_subtitle_content;
            activeLastMeaningfulData = transSubResponse;
            activeLastMeaningfulResponseKey = transSubDataExtractor.responseKey;
            
            if (windowManager) {
                windowManager.logToRenderer(`[IPC Handler - Workflow] Keeping original ASR segments for timing: ${finalSegmentsForPreview ? finalSegmentsForPreview.length : 0} segments`);
                windowManager.logToRenderer(`[IPC Handler - Workflow] srtContentForCurrentWorkflow: ${!!srtContentForCurrentWorkflow}`);
                windowManager.logToRenderer(`[IPC Handler - Workflow] textContentForCurrentWorkflow: ${!!textContentForCurrentWorkflow}`);
                windowManager.logToRenderer(`[IPC Handler - Workflow] finalSegmentsForPreview: ${JSON.stringify(finalSegmentsForPreview ? finalSegmentsForPreview.slice(0, 1) : null)}`);
            }
            
            sendProgress('TranslateSubtitles', 100, 'SRT translation complete.', false, '', { [activeLastMeaningfulResponseKey]: activeLastMeaningfulData });
            
            const workflowCompleteData = {
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData,
              generate_subtitles_response: { srt_content: srtContentForCurrentWorkflow },
              audio_to_text_response: { 
                transcript: textContentForCurrentWorkflow, 
                segments: finalSegmentsForPreview || [] 
              }
            };
            
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Sending complete data: ${JSON.stringify({
              finalSegmentsCount: finalSegmentsForPreview ? finalSegmentsForPreview.length : 0,
              hasOriginalSRT: !!srtContentForCurrentWorkflow,
              hasTranslatedSRT: !!translatedSrt,
              hasTranscript: !!textContentForCurrentWorkflow
            })}`);
            
            sendProgress('WorkflowComplete', 100, 'Workflow complete: Translated SRT.', false, '', workflowCompleteData);
            // Add a small delay to ensure the message is likely processed by the renderer before the invoke promise resolves
            await new Promise(resolve => setTimeout(resolve, 50)); // e.g., 50ms delay

            // Return the result for the IPC call
            return {
              success: true,
              final_result: activeLastMeaningfulResponseKey,
              [activeLastMeaningfulResponseKey]: activeLastMeaningfulData,
              generate_subtitles_response: { srt_content: srtContentForCurrentWorkflow },
              audio_to_text_response: {
                transcript: textContentForCurrentWorkflow,
                segments: finalSegmentsForPreview || []
              }
            };
          } else {
            throw new Error('TranslateSubtitles step failed.');
          }
          break;
        }
        default:
          if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Unknown or unhandled workflow_type in switch: ${workflow_type}`);
          throw new Error(`Unknown or unhandled workflow_type: ${workflow_type}`);
      }
    } catch (error) {
      const errorMessage = error.message || 'An unknown error occurred in the full workflow.';
      const errorStack = error.stack || 'No stack trace available.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Workflow] Error in full-workflow (${workflow_type || 'unknown_workflow'}): ${errorMessage} \nStack: ${errorStack}`);
      sendProgress(workflow_type || 'WorkflowError', 100, 'Workflow failed.', true, errorMessage, { final_error_details: errorMessage, stack: errorStack });
      throw error;
    }
  });

  // IPC Handler for 'subtitler:video-to-audio'
  ipcMain.handle('subtitler:video-to-audio', async (event, filePath) => {
    console.log(`[IPC Handler - Subtitler] Received subtitler:video-to-audio request for path: ${filePath}`);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Received subtitler:video-to-audio request for path: ${filePath}`);
    
    const subtitlerClient = grpcManager.getSubtitlerClient();
    if (!subtitlerClient) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] SubtitlerClient not initialized for video-to-audio.');
      throw new Error('SubtitlerClient not initialized');
    }

    const currentMainWindow = windowManager.getMainWindow();
    const sendProgressToRenderer = (percentage, message, isError = false, errorMessage = '', data = null) => {
      const progress = { stageName: 'VideoToAudio', percentage, message, isError, errorMessage, ...data };
      if (windowManager) windowManager.logToRenderer(`[VideoToAudio Progress] (${percentage}%): ${message} ${isError ? 'ERROR: ' + errorMessage : ''}`);
      if (currentMainWindow && currentMainWindow.webContents && !currentMainWindow.webContents.isDestroyed()) {
        // Vue store action might not be listening to 'progress-update'.
        // It expects a promise resolution or rejection.
        // For simplicity, we'll rely on the promise result.
        // If detailed progress is needed in Vue, a separate event channel like 'subtitler:video-to-audio-progress' would be better.
        // For now, these logs are for main process visibility.
      }
    };

    try {
      sendProgressToRenderer(0, 'Starting video to audio conversion...');
      const v2aResponse = await callGrpcStreamStep(
        sendProgressToRenderer,
        'videoToAudio',
        { video_path: filePath },
        v2aDataExtractor
      );
 
      if (v2aResponse && v2aResponse.audio_path) {
        sendProgressToRenderer(100, `Audio extracted: ${v2aResponse.audio_path}`, false, '', { audio_path: v2aResponse.audio_path });
        return v2aResponse.audio_path; // Resolve the promise with the audio path
      } else {
        throw new Error('VideoToAudio step failed: Could not extract audio path from gRPC response.');
      }
    } catch (error) {
      const errorMessage = error.message || 'An unknown error occurred during video to audio conversion.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error in video-to-audio: ${errorMessage}`);
      sendProgressToRenderer(100, 'Video to audio conversion failed.', true, errorMessage);
      throw error; // Reject the promise
    }
  });

  // IPC Handler for 'subtitler:audio-to-text'
  ipcMain.handle('subtitler:audio-to-text', async (event, audioPath, additionalParams) => {
    console.log(`[IPC Handler - Subtitler] Received subtitler:audio-to-text request for path: ${audioPath}`, additionalParams);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Received subtitler:audio-to-text request for path: ${audioPath} with params: ${JSON.stringify(additionalParams)}`);

    const subtitlerClient = grpcManager.getSubtitlerClient();
    if (!subtitlerClient) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] SubtitlerClient not initialized for audio-to-text.');
      throw new Error('SubtitlerClient not initialized');
    }

    const currentMainWindow = windowManager.getMainWindow();
    const sendProgress = (stageName, percentage, message, isError = false, errorMessage = '', data = null) => {
      const progress = { stageName: `AudioToText-${stageName}`, percentage, message, isError, errorMessage, ...data };
      if (windowManager) windowManager.logToRenderer(`[AudioToText Progress - ${stageName}] (${percentage}%): ${message} ${isError ? 'ERROR: ' + errorMessage : ''}`);
      if (currentMainWindow && currentMainWindow.webContents && !currentMainWindow.webContents.isDestroyed()) {
        currentMainWindow.webContents.send('progress-update', progress);
      }
    };
 
    try {
      const requestParams = {
        audio_path: audioPath,
        request_word_timestamps: additionalParams?.request_word_timestamps || true, // Default to true or use from params
        // language: additionalParams?.language || 'auto', // Example: pass language if provided
        // model_size: additionalParams?.modelSize || 'base' // Example: pass model size if provided
      };
      // Add language and model_size to request if they exist in additionalParams
      if (additionalParams?.language) {
        requestParams.language = additionalParams.language;
      }
      if (additionalParams?.modelSize) {
        requestParams.model_size = additionalParams.modelSize;
      }

      const a2tResponse = await callGrpcStreamStep(
        sendProgress,
        'audioToText',
        requestParams,
        a2tDataExtractor // Use the top-level extractor
      );
 
      if (a2tResponse) { // a2tResponse will be { transcript, segments }
        // The store expects the result directly.
        // It could be the full object or just the transcript string depending on needs.
        // For now, returning the object.
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Audio-to-text successful. Result: ${JSON.stringify(a2tResponse)}`);
        return a2tResponse;
      } else {
        throw new Error('AudioToText step failed: No response or empty response from gRPC call.');
      }
    } catch (error) {
      const errorMessage = error.message || 'An unknown error occurred during audio to text conversion.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error in audio-to-text: ${errorMessage}`);
      throw error; // Reject the promise, Vue store action will catch this
    }
  });

  // IPC Handler for 'subtitler:save-edited-segments'
  ipcMain.handle('subtitler:save-edited-segments', async (event, segments) => {
    console.log('[IPC Handler - Subtitler] Received subtitler:save-edited-segments request.');
    if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] Received subtitler:save-edited-segments request.');

    if (!segments || !Array.isArray(segments) || segments.length === 0) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] No segments provided to save.');
      throw new Error('No segments provided to save.');
    }

    const mainWindow = windowManager.getMainWindow();
    if (!mainWindow) {
      throw new Error('Main window not available to show save dialog.');
    }

    try {
      const srtContent = subtitlerUtils.segmentsToSrt(segments);
      if (!srtContent) {
        throw new Error('Failed to convert segments to SRT format.');
      }

      const dialogResult = await dialog.showSaveDialog(mainWindow, {
        title: '保存编辑后的字幕',
        defaultPath: 'edited_subtitles.srt',
        filters: [
          { name: 'SubRip Subtitle', extensions: ['srt'] },
          { name: 'All Files', extensions: ['*'] },
        ],
      });

      if (dialogResult.canceled || !dialogResult.filePath) {
        if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] Save dialog cancelled by user.');
        return { success: false, message: '保存操作已取消。' };
      }

      const filePathToSave = dialogResult.filePath;
      await fs.writeFile(filePathToSave, srtContent, 'utf8');

      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Edited subtitles saved to: ${filePathToSave}`);
      return { success: true, message: `字幕已成功保存到 ${filePathToSave}`, filePath: filePathToSave };

    } catch (error) {
      const errorMessage = error.message || '保存编辑后的字幕时发生未知错误。';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error saving edited subtitles: ${errorMessage}`);
      console.error('[IPC Handler - Subtitler] Error saving edited subtitles:', error);
      throw new Error(errorMessage);
    }
  });

  // IPC Handler for 'subtitler:export-subtitles'
  ipcMain.handle('subtitler:export-subtitles', async (event, exportPayload) => {
    const {
      segments: inputSegments, // These are the segments based on contentSource from the store
      rawContent, // This is raw text, e.g., for transcript_text source
      filename: userSpecifiedFilename,
      format,
      layout,
      contentSource,
      autoSaveToDefault,
    } = exportPayload;

    console.log('[IPC Handler - Subtitler] Received subtitler:export-subtitles request with payload:', exportPayload);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Exporting subtitles. Format: ${format}, Layout: ${layout}, Source: ${contentSource}, AutoSave: ${autoSaveToDefault}`);

    if (!format) {
      const msg = 'No export format specified.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error: ${msg}`);
      return { error: msg };
    }

    const mainWindow = windowManager.getMainWindow();
    if (!mainWindow && !autoSaveToDefault) { // mainWindow is needed for dialog
      const msg = 'Main window not available to show save dialog.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error: ${msg}`);
      return { error: msg };
    }
    
    try {
      let segmentsForProcessing = [];
      let fileContent = '';
      const fileExtension = format.toLowerCase();

      // 1. Determine segments to process based on contentSource
      if (contentSource === 'transcript_text' && rawContent) {
        if (format.toLowerCase() === 'txt') {
          fileContent = rawContent; // Use raw content directly for TXT
        } else {
          // For other formats, create a single segment from raw text or treat as error if not TXT
          // This part might need more sophisticated handling based on desired behavior for non-TXT from raw text.
          // For now, creating a single segment.
          segmentsForProcessing = [{ text: rawContent, startTimeMs: 0, endTimeMs: 0, id: 'raw-text-segment' }];
           if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Using rawContent for ${format} as a single segment.`);
        }
      } else if (inputSegments && Array.isArray(inputSegments)) {
        segmentsForProcessing = JSON.parse(JSON.stringify(inputSegments)); // Deep copy
      } else {
         const msg = `No valid content to export for source: ${contentSource}. Segments or rawContent missing.`;
         if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error: ${msg}`);
         return { error: msg };
      }
      
      // 2. Apply layout (if not TXT from rawContent, or if TXT also needs layout on segments)
      // Layout is applied to 'segmentsForProcessing' which should contain the text to be laid out.
      // If fileContent is already set (e.g. TXT from rawContent), skip layout and conversion for that specific case.
      let laidOutSegments = segmentsForProcessing;
      if (fileContent === '') { // Only apply layout if fileContent isn't already set (e.g. for TXT from rawContent)
          if (layout && layout !== 'original_only' && segmentsForProcessing.length > 0) { // 'original_only' is default if no translation
            // The `translationField` in `applyLayoutToSegments` will look for `segment.translation`.
            // This field needs to be populated by a translation step if bilingual output is desired.
            // If not present, `applyLayoutToSegments` should gracefully handle it (e.g., original_top becomes original_only).
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Applying layout: ${layout}`);
            laidOutSegments = subtitlerUtils.applyLayoutToSegments(segmentsForProcessing, layout, 'translation'); // Assuming 'translation' field
          } else {
            if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Skipping layout or using default (original_only effectively). Layout: ${layout}`);
          }

          // 3. Convert segments to the specified format
          if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Converting to format: ${format}`);
          switch (format.toLowerCase()) {
            case 'srt':
              fileContent = subtitlerUtils.segmentsToSrt(laidOutSegments);
              break;
            case 'vtt':
              fileContent = subtitlerUtils.segmentsToVtt(laidOutSegments);
              break;
            case 'ass':
              fileContent = subtitlerUtils.segmentsToAss(laidOutSegments);
              break;
            case 'txt':
              // This case is for when 'transcript_text' was NOT the source,
              // so we convert laidOutSegments to TXT.
              fileContent = subtitlerUtils.segmentsToTxt(laidOutSegments);
              break;
            case 'json':
              fileContent = subtitlerUtils.segmentsToJson(laidOutSegments);
              break;
            default:
              throw new Error(`Unsupported export format: ${format}`);
          }
      }


      if (!fileContent && laidOutSegments.length > 0 && !(contentSource === 'transcript_text' && format.toLowerCase() === 'txt')) {
        // If there were segments to process, but fileContent is still empty (and it wasn't the special raw TXT case)
        throw new Error(`Failed to convert segments to ${format.toUpperCase()} format. Content is empty.`);
      }
      
      let filePathToSave;
      const baseFilename = userSpecifiedFilename || 'subtitles'; // Fallback filename

      if (autoSaveToDefault) {
        const downloadsPath = app.getPath('downloads');
        const defaultExportDir = path.join(downloadsPath, 'SubtitlesAppExports');
        await fs.mkdir(defaultExportDir, { recursive: true });
        filePathToSave = path.join(defaultExportDir, `${baseFilename}.${fileExtension}`);
        if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Auto-saving to: ${filePathToSave}`);
      } else {
        const dialogResult = await dialog.showSaveDialog(mainWindow, {
          title: `导出字幕为 ${format.toUpperCase()}`,
          defaultPath: `${baseFilename}.${fileExtension}`,
          filters: [
            { name: `${format.toUpperCase()} Subtitles`, extensions: [fileExtension] },
            { name: 'All Files', extensions: ['*'] },
          ],
        });

        if (dialogResult.canceled || !dialogResult.filePath) {
          if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] Export dialog cancelled by user.');
          return { cancelled: true };
        }
        filePathToSave = dialogResult.filePath;
      }

      await fs.writeFile(filePathToSave, fileContent, 'utf8');

      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Subtitles exported to: ${filePathToSave}`);
      return { success: true, message: `字幕已成功导出到 ${filePathToSave}`, filePath: filePathToSave };

    } catch (error) {
      const errorMessage = error.message || `导出字幕为 ${format.toUpperCase()} 时发生未知错误。`;
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error exporting subtitles: ${errorMessage} \n ${error.stack}`);
      console.error('[IPC Handler - Subtitler] Error exporting subtitles:', error);
      return { error: errorMessage };
    }
  });


  // IPC Handler for 'subtitler:one-click-workflow'
  ipcMain.handle('subtitler:one-click-workflow', async (event, payload) => {
    const {
      filePath,
      workflowType,
      targetLanguage, // For translation
      exportFormat,
      exportLayout,
      modelSize // Optional: for ASR model selection
    } = payload;

    console.log(`[IPC Handler - Subtitler] Received subtitler:one-click-workflow. Payload:`, payload);
    if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] One-Click Workflow started. File: ${filePath}, Workflow: ${workflowType}, Format: ${exportFormat}, Layout: ${exportLayout}, TargetLang: ${targetLanguage}`);

    const subtitlerClient = grpcManager.getSubtitlerClient();
    if (!subtitlerClient) {
      if (windowManager) windowManager.logToRenderer('[IPC Handler - Subtitler] SubtitlerClient not initialized for one-click workflow.');
      return { error: 'SubtitlerClient not initialized' };
    }
    
    const currentMainWindow = windowManager.getMainWindow();
    const sendProgress = (stage, percentage, message, isError = false, data = null) => {
        const progressUpdate = { stageName: `OneClick-${stage}`, percentage, message, isError, ...data };
        if (windowManager) windowManager.logToRenderer(`[OneClick Progress] ${stage} (${percentage}%): ${message} ${isError ? `Error: ${data?.errorDetails || ''}` : ''}`);
        if (currentMainWindow && currentMainWindow.webContents && !currentMainWindow.webContents.isDestroyed()) {
             currentMainWindow.webContents.send('progress-update', progressUpdate);
        }
    };

    try {
      let audioFilePath = filePath;
      let originalSegments = null; // Segments from ASR
      let translatedSegments = null; // Segments after translation
      let finalSegmentsToExport = null;

      // Step 1: Video to Audio (if workflowType indicates video input)
      if (workflowType.startsWith('vid_')) {
        sendProgress('VideoToAudio', 0, 'Converting video to audio...');
        const v2aResponse = await callGrpcStreamStep(sendProgress, 'videoToAudio', { video_path: filePath }, v2aDataExtractor);
        if (v2aResponse && v2aResponse.audio_path) {
          audioFilePath = v2aResponse.audio_path;
          sendProgress('VideoToAudio', 100, `Audio extracted: ${audioFilePath}`);
        } else {
          throw new Error('One-Click: Video to audio conversion failed.');
        }
      } else {
        sendProgress('VideoToAudio', 100, 'Skipping video to audio (assuming audio input or text input workflow).');
      }

      // Step 2: Audio to Text (ASR)
      // This step is always needed if the workflow isn't purely text-based for translation/formatting
      if (!workflowType.startsWith('text_to_')) { // Assuming text_to_srt or text_to_translated_srt might not need ASR if text is provided
        sendProgress('AudioToText', 0, 'Transcribing audio to text...');
        const a2tParams = {
          audio_path: audioFilePath,
          request_word_timestamps: true, // Always request segments for flexibility
          language: payload.language || 'auto', // ASR language, could be different from targetLanguage for translation
          model_size: modelSize || 'base'
        };
        const a2tResponse = await callGrpcStreamStep(sendProgress, 'audioToText', a2tParams, a2tDataExtractor);
        if (a2tResponse && a2tResponse.segments && a2tResponse.segments.length > 0) {
          originalSegments = a2tResponse.segments.map((s, i) => ({ ...s, id: `orig-${i}`})); // Ensure IDs
          sendProgress('AudioToText', 100, `Transcription complete. Segments: ${originalSegments.length}`);
        } else {
          throw new Error('One-Click: Audio to text transcription failed or returned no segments.');
        }
      } else {
         // Handle text_to_... workflows if text is directly provided in payload (not part of this task's scope yet)
         // For now, assume originalSegments would be derived from input text if that path was taken.
         // This task focuses on file-based input primarily.
         sendProgress('AudioToText', 100, 'Skipping ASR (assuming text input workflow or no ASR needed).');
         // If workflowType is e.g. text_to_translated_srt, originalSegments would need to be populated from input text.
         // This part is simplified for now.
         if (!originalSegments) originalSegments = []; // Placeholder
      }
      
      // Ensure originalSegments are in the { text, startTimeMs, endTimeMs, id } format
      // The a2tResponse.segments should already be in a good format, but let's ensure consistency.
      // The alignAndMergeSegments function expects startTimeMs and endTimeMs.
      if (originalSegments) {
        originalSegments = originalSegments.map(s => ({
          id: s.id || `orig-${Date.now()}-${Math.random()}`, // Ensure ID
          text: s.text || '',
          startTimeMs: s.start_time_ms ?? s.startTimeMs,
          endTimeMs: s.end_time_ms ?? s.endTimeMs,
        }));
      }

      // Step 3: Translation (if workflowType indicates translation)
      if (workflowType.includes('_trans') || workflowType.includes('_translated_')) {
        if (!originalSegments || originalSegments.length === 0) {
          throw new Error('One-Click: Cannot translate, no original segments available.');
        }
        if (!targetLanguage) {
          throw new Error('One-Click: Target language for translation is not specified.');
        }

        sendProgress('TranslateSubtitles', 0, `Translating to ${targetLanguage}...`);
        const srtContentForTranslation = subtitlerUtils.segmentsToSrt(originalSegments);
        if (!srtContentForTranslation) {
          throw new Error('One-Click: Failed to convert original segments to SRT for translation.');
        }

        const transSubParams = { subtitle_content: srtContentForTranslation, target_language: targetLanguage };
        const transSubResponse = await callGrpcStreamStep(sendProgress, 'translateSubtitles', transSubParams, transSubDataExtractor);

        if (transSubResponse && transSubResponse.translated_subtitle_content) {
          const rawTranslatedSegments = subtitlerUtils.parseSrtToSegments(transSubResponse.translated_subtitle_content);
          if (!rawTranslatedSegments || rawTranslatedSegments.length === 0) {
            sendProgress('TranslateSubtitles', 75, 'Translation completed, but result parsing yielded no segments. Original timing will be used.', true);
            translatedSegments = []; // Set to empty array, alignment logic will handle this
          } else {
            // Ensure translated segments also use startTimeMs, endTimeMs for consistency before alignment
            translatedSegments = rawTranslatedSegments.map((s, i) => ({
              id: `trans-${i}-${Date.now()}`,
              text: s.text || '',
              startTimeMs: s.start_time_ms ?? s.startTimeMs,
              endTimeMs: s.end_time_ms ?? s.endTimeMs,
            }));
            sendProgress('TranslateSubtitles', 100, `Translation complete. Raw Translated Segments: ${translatedSegments.length}`);
          }
        } else {
          throw new Error('One-Click: Translation failed or returned no content.');
        }
      } else {
        sendProgress('TranslateSubtitles', 100, 'Skipping translation step.');
      }

      // Step 3.5: Align and Merge Segments
      // This step will re-segment original ASR if needed, and align translations.
      sendProgress('AlignAndMerge', 0, 'Aligning and merging segments...');
      // originalSegments here are from ASR, potentially word-level
      // translatedSegments are from translated SRT, potentially sentence-level
      // workflowType helps decide if translation alignment is needed
      const alignedAndMergedSegments = subtitlerUtils.alignAndMergeSegments(
        originalSegments || [], // Ensure it's an array
        translatedSegments || [], // Ensure it's an array, even if translation was skipped/failed
        workflowType
      );

      if (!alignedAndMergedSegments || alignedAndMergedSegments.length === 0) {
        throw new Error('One-Click: Segment alignment and merging failed or resulted in no segments.');
      }
      sendProgress('AlignAndMerge', 100, `Segments aligned and merged. Count: ${alignedAndMergedSegments.length}`);
      
      // finalSegmentsToExport will be the result of alignment and merging,
      // which now contains `text` (original) and `translatedText` (if applicable) fields.
      finalSegmentsToExport = alignedAndMergedSegments;

      // Step 3.6: Apply Layout
      // applyLayoutToSegments will now use the `text` and `translatedText` fields from `finalSegmentsToExport`
      sendProgress('Layouting', 0, `Applying layout: ${exportLayout}`);
      // The `applyLayoutToSegments` function expects `segments` (with `text` and `translatedText`), `layout`,
      // and optionally the names of the original and translated text fields.
      // Our `alignAndMergeSegments` produces `text` and `translatedText`.
      const laidOutSegments = subtitlerUtils.applyLayoutToSegments(
        finalSegmentsToExport,
        exportLayout,
        'text', // Field name for original text in finalSegmentsToExport
        'translatedText' // Field name for translated text in finalSegmentsToExport
      );
      sendProgress('Layouting', 100, `Layout applied. Segments for export: ${laidOutSegments.length}`);
      finalSegmentsToExport = laidOutSegments; // These segments now have a single 'text' field with the combined layout.


      if (!finalSegmentsToExport || finalSegmentsToExport.length === 0) {
        throw new Error('One-Click: No final segments available for export after layouting.');
      }

      // Step 4: Export Subtitles
      sendProgress('ExportSubtitles', 0, `Exporting subtitles as ${exportFormat}...`);
      let exportedContent = '';
      const targetFormat = exportFormat.toLowerCase();
      let targetExtension = targetFormat;

      switch (targetFormat) {
        case 'srt':
          exportedContent = subtitlerUtils.segmentsToSrt(finalSegmentsToExport);
          break;
        case 'vtt':
          exportedContent = subtitlerUtils.segmentsToVtt(finalSegmentsToExport);
          break;
        case 'ass':
          exportedContent = subtitlerUtils.segmentsToAss(finalSegmentsToExport);
          targetExtension = 'ass'; // Ensure correct extension for ASS
          break;
        case 'txt':
          exportedContent = subtitlerUtils.segmentsToTxt(finalSegmentsToExport);
          break;
        case 'json':
          exportedContent = subtitlerUtils.segmentsToJson(finalSegmentsToExport);
          break;
        default:
          throw new Error(`One-Click: Unsupported export format "${exportFormat}".`);
      }

      if (!exportedContent && finalSegmentsToExport.length > 0) { // ASS might be empty if styling fails, but other text formats should have content
         if (targetFormat !== 'ass') { // ASS can be complex, allow empty if util returns that
            throw new Error(`One-Click: Failed to convert segments to ${targetFormat.toUpperCase()}.`);
         }
      }
      
      const originalFileName = path.basename(filePath, path.extname(filePath));
      const defaultSaveName = `${originalFileName}_${workflowType}_${targetLanguage || 'na'}.${targetExtension}`;
      
      const dialogResult = await dialog.showSaveDialog(currentMainWindow, {
        title: `One-Click Export: Save ${targetFormat.toUpperCase()}`,
        defaultPath: defaultSaveName,
        filters: [
          { name: `${targetFormat.toUpperCase()} Subtitles`, extensions: [targetExtension] },
          { name: 'All Files', extensions: ['*'] },
        ],
      });

      if (dialogResult.canceled || !dialogResult.filePath) {
        sendProgress('ExportSubtitles', 100, 'Export cancelled by user.', false, {cancelled: true}); // Not an error, but a specific status
        return { cancelled: true, message: 'Export cancelled by user.' };
      }

      const exportPath = dialogResult.filePath;
      await fs.writeFile(exportPath, exportedContent, 'utf8');
      sendProgress('ExportSubtitles', 100, `Subtitles exported to: ${exportPath}`);

      return {
        success: true,
        exportPath: exportPath,
        message: 'One-Click operation completed successfully.',
        // Optionally return some of the intermediate data if useful for UI
        transcriptionResult: { segments: originalSegments },
        translationResult: translatedSegments ? { segments: translatedSegments } : null,
      };

    } catch (error) {
      const errorMessage = error.message || 'An unknown error occurred during the one-click workflow.';
      if (windowManager) windowManager.logToRenderer(`[IPC Handler - Subtitler] Error in one-click workflow: ${errorMessage} \n ${error.stack}`);
      console.error('[IPC Handler - Subtitler] Error in one-click workflow:', error);
      sendProgress('WorkflowError', 100, 'Workflow failed', true, { errorDetails: errorMessage });
      return { error: errorMessage };
    }
  });



}

module.exports = {
  initializeSubtitlerWorkflowIpcHandlers,
};
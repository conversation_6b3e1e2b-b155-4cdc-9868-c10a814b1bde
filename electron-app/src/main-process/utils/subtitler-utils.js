// electron-app/src/main-process/utils/subtitler-utils.js

function srtTimeToMilliseconds(timeStr) {
  const parts = timeStr.split(/[:,]/);
  if (parts.length !== 4) return 0;
  const hours = parseInt(parts[0], 10);
  const minutes = parseInt(parts[1], 10);
  const seconds = parseInt(parts[2], 10);
  const milliseconds = parseInt(parts[3], 10);
  if (isNaN(hours) || isNaN(minutes) || isNaN(seconds) || isNaN(milliseconds)) return 0;
  return (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds;
}

function parseSrtToSegments(srtContent) {
  if (!srtContent || typeof srtContent !== 'string') return [];
  const segments = [];
  // Normalize line endings and then split by double newlines (or more)
  // Handles Unix (LF), Windows (CRLF), and old Mac (CR) line endings
  const blocks = srtContent.replace(/\r\n/g, '\n').replace(/\r/g, '\n').trim().split(/\n\s*\n+/);

  for (const block of blocks) {
    const lines = block.trim().split('\n');
    if (lines.length < 2) continue; 

    let timeLineIndex = -1;
    for(let i = 0; i < lines.length; i++) {
        if (lines[i].includes('-->')) {
            timeLineIndex = i;
            break;
        }
    }
    if (timeLineIndex === -1) continue; 

    const timeLine = lines[timeLineIndex];
    const textLines = lines.slice(timeLineIndex + 1);

    const timeParts = timeLine.split(' --> ');
    if (timeParts.length !== 2) continue; 

    const startTimeMs = srtTimeToMilliseconds(timeParts[0].trim());
    const endTimeMs = srtTimeToMilliseconds(timeParts[1].trim());
    
    const text = textLines.map(line => line.trim()).filter(line => line).join(' ').replace(/\s+/g, ' ').trim();

    if (text) { 
      segments.push({
        text: text,
        start_time_ms: startTimeMs,
        end_time_ms: endTimeMs,
      });
    }
  }
  return segments;
}

function formatMillisecondsToSrtTime(ms) {
  if (typeof ms !== 'number' || isNaN(ms) || ms < 0) {
    ms = 0;
  }
  const date = new Date(ms);
  const hours = String(date.getUTCHours()).padStart(2, '0');
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');
  const milliseconds = String(date.getUTCMilliseconds()).padStart(3, '0');
  return `${hours}:${minutes}:${seconds},${milliseconds}`;
}

function segmentsToSrt(segments) {
  if (!Array.isArray(segments) || segments.length === 0) {
    return '';
  }
  let srtContent = '';
  segments.forEach((segment, index) => {
    // Segments from Vue store might have startTimeMs, endTimeMs
    // Segments from parseSrtToSegments have start_time_ms, end_time_ms
    const startTime = formatMillisecondsToSrtTime(segment.startTimeMs ?? segment.start_time_ms ?? 0);
    const endTime = formatMillisecondsToSrtTime(segment.endTimeMs ?? segment.end_time_ms ?? 0);
    const text = segment.text || '';

    srtContent += `${index + 1}\n`;
    srtContent += `${startTime} --> ${endTime}\n`;
    srtContent += `${text}\n\n`;
  });
  return srtContent.trim();
}

function formatMillisecondsToVttTime(ms) {
  if (typeof ms !== 'number' || isNaN(ms) || ms < 0) {
    ms = 0;
  }
  const date = new Date(ms);
  const hours = String(date.getUTCHours()).padStart(2, '0');
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');
  const milliseconds = String(date.getUTCMilliseconds()).padStart(3, '0');
  return `${hours}:${minutes}:${seconds}.${milliseconds}`; // VTT uses dot for milliseconds
}

function segmentsToVtt(segments) {
  if (!Array.isArray(segments) || segments.length === 0) {
    return 'WEBVTT\n\n';
  }
  let vttContent = 'WEBVTT\n\n';
  segments.forEach((segment) => {
    const startTime = formatMillisecondsToVttTime(segment.startTimeMs ?? segment.start_time_ms ?? 0);
    const endTime = formatMillisecondsToVttTime(segment.endTimeMs ?? segment.end_time_ms ?? 0);
    const text = segment.text || '';

    vttContent += `${startTime} --> ${endTime}\n`;
    vttContent += `${text}\n\n`;
  });
  return vttContent.trim();
}

function formatMillisecondsToAssTime(ms) {
    if (typeof ms !== 'number' || isNaN(ms) || ms < 0) {
        ms = 0;
    }
    const date = new Date(ms);
    const hours = date.getUTCHours(); // No padding for ASS hours
    const minutes = String(date.getUTCMinutes()).padStart(2, '0');
    const seconds = String(date.getUTCSeconds()).padStart(2, '0');
    const centiseconds = String(Math.floor(date.getUTCMilliseconds() / 10)).padStart(2, '0'); // ASS uses centiseconds
    return `${hours}:${minutes}:${seconds}.${centiseconds}`;
}

function segmentsToAss(segments, style = 'Default') {
    if (!Array.isArray(segments) || segments.length === 0) {
        return `[Script Info]
Title: Exported Subtitles
ScriptType: v4.00+
WrapStyle: 0
PlayResX: 1280
PlayResY: 720

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: ${style},Arial,28,&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,1,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`; // Return header if no segments
    }

    let assContent = `[Script Info]
Title: Exported Subtitles
ScriptType: v4.00+
WrapStyle: 0
PlayResX: 1280
PlayResY: 720

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: ${style},Arial,28,&H00FFFFFF,&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,1,2,10,10,10,1

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
`;

    segments.forEach((segment) => {
        const startTime = formatMillisecondsToAssTime(segment.startTimeMs ?? segment.start_time_ms ?? 0);
        const endTime = formatMillisecondsToAssTime(segment.endTimeMs ?? segment.end_time_ms ?? 0);
        const text = (segment.text || '').replace(/\n/g, '\\N'); // ASS uses \N for newlines

        assContent += `Dialogue: 0,${startTime},${endTime},${style},,0,0,0,,${text}\n`;
    });
    return assContent.trim();
}

function segmentsToTxt(segments) {
  if (!Array.isArray(segments) || segments.length === 0) {
    return '';
  }
  return segments.map(segment => segment.text || '').join('\n');
}

function segmentsToJson(segments) {
  if (!Array.isArray(segments)) {
    return '[]'; // Return empty array string if not an array
  }
  // Standardize segment keys for JSON output if needed, e.g., always use startTimeMs
  const standardizedSegments = segments.map(s => ({
      id: s.id, // Keep id if present
      startTimeMs: s.startTimeMs ?? s.start_time_ms ?? 0,
      endTimeMs: s.endTimeMs ?? s.end_time_ms ?? 0,
      text: s.text || '',
      // Include other relevant fields if they exist, e.g., translation
      ...(s.translation && { translation: s.translation })
  }));
  return JSON.stringify(standardizedSegments, null, 2); // Pretty print JSON
}

function applyLayoutToSegments(segments, layout, translationField = 'translation') {
  if (!Array.isArray(segments)) return [];

  return segments.map(segment => {
    const originalText = segment.text || '';
    const translatedText = segment[translationField] || segment.translatedText || ''; // 支持多种翻译字段名

    let combinedText = '';

    // 支持中文和英文布局名称
    switch (layout) {
      case 'original_top':
      case '原文在上':
        combinedText = originalText;
        if (translatedText) {
          combinedText += `\n${translatedText}`;
        }
        break;
      case 'translation_top':
      case '译文在上':
        combinedText = translatedText;
        if (originalText) {
          combinedText = translatedText ? `${translatedText}\n${originalText}` : originalText;
        }
        break;
      case 'original_only':
      case '仅原文':
        combinedText = originalText;
        break;
      case 'translation_only':
      case '仅译文':
        combinedText = translatedText || originalText; // Fallback to original if translation is empty
        break;
      default: // Default to original_top if layout is unknown
        combinedText = originalText;
        if (translatedText) {
          combinedText += `\n${translatedText}`;
        }
        break;
    }
    return { ...segment, text: combinedText.trim() };
  });
}


// Helper to generate unique IDs if needed
function generateUniqueId() {
  return Math.random().toString(36).substring(2, 15);
}

/**
 * Aligns and merges ASR segments with translated segments based on timestamps.
 * Also re-segments word-level ASR segments into phrase/sentence-level segments.
 * @param {Array<Object>} originalASRSegments - Segments from ASR (may be word-level).
 *                                              Expected fields: text, start_time_ms, end_time_ms.
 * @param {Array<Object>} [translatedSRTSegments] - Segments from translated SRT (sentence-level).
 *                                                 Expected fields: text, start_time_ms, end_time_ms.
 * @param {string} workflowType - The type of workflow (e.g., 'translate', 'transcribe_only').
 * @returns {Array<Object>} Processed segments with id, startTimeMs, endTimeMs, text, and translatedText (if applicable).
 */
function alignAndMergeSegments(originalASRSegments, translatedSRTSegments, workflowType) {
  console.log(`[alignAndMergeSegments] Processing ${originalASRSegments.length} original segments, ${translatedSRTSegments ? translatedSRTSegments.length : 0} translated segments`);

  // Ensure consistent naming for time fields (use startTimeMs, endTimeMs internally)
  let processedASRSegments = originalASRSegments.map(s => ({
    ...s,
    startTimeMs: s.start_time_ms ?? s.startTimeMs,
    endTimeMs: s.end_time_ms ?? s.endTimeMs,
    id: s.id || generateUniqueId(), // Ensure each segment has an ID
  }));

  // 1. Re-segmentation of original ASR segments (if they appear to be word-level)
  // This is a simplified re-segmentation. A more robust one would consider punctuation, pauses, etc.
  // For now, let's assume ASR might give very short segments. We'll try to merge them.
  // A more sophisticated approach would be needed for true word-level to sentence conversion.
  // This example focuses on merging based on proximity and simple rules.

  const reSegmentedOriginal = [];
  if (processedASRSegments.length > 0) {
    let currentSegment = { ...processedASRSegments[0] };
    currentSegment.text = processedASRSegments[0].text; // Ensure text is a string

    for (let i = 1; i < processedASRSegments.length; i++) {
      const nextSegment = processedASRSegments[i];
      const timeGap = nextSegment.startTimeMs - currentSegment.endTimeMs;
      const endsWithPunctuation = /[.?!]$/.test(currentSegment.text.trim());

      // 更保守的合并策略，避免句子过长
      // Merge if:
      // - Gap is very small (< 300ms) AND current segment doesn't end with punctuation
      // - OR current segment is very short (< 2 words) AND doesn't end with punctuation
      // - AND combined length is reasonable (< 12 words or < 6 seconds)
      const currentWordCount = currentSegment.text.split(/\s+/).length;
      const nextWordCount = nextSegment.text.split(/\s+/).length;
      const combinedWordCount = currentWordCount + nextWordCount;
      const combinedDuration = nextSegment.endTimeMs - currentSegment.startTimeMs;

      const shouldMerge =
        (timeGap < 300 && !endsWithPunctuation && combinedWordCount < 12 && combinedDuration < 6000) ||
        (currentWordCount < 2 && !endsWithPunctuation && timeGap < 800 && combinedWordCount < 10 && combinedDuration < 5000);


      if (shouldMerge) {
        currentSegment.text += ` ${nextSegment.text}`;
        currentSegment.endTimeMs = nextSegment.endTimeMs;
      } else {
        reSegmentedOriginal.push(currentSegment);
        currentSegment = { ...nextSegment };
        currentSegment.text = nextSegment.text; // Ensure text is a string
      }
    }
    reSegmentedOriginal.push(currentSegment); // Add the last segment
  }
  
  // Ensure all segments have an ID after re-segmentation
  const finalOriginalSegments = reSegmentedOriginal.map(s => ({
    ...s,
    id: s.id || generateUniqueId(),
    translatedText: '', // Initialize for translation alignment
  }));


  // 2. Timestamp Alignment for Translation (if applicable)
  if (workflowType && workflowType.includes('translate') && translatedSRTSegments && translatedSRTSegments.length > 0) {
    const processedTranslatedSegments = translatedSRTSegments.map(s => ({
      ...s,
      startTimeMs: s.start_time_ms ?? s.startTimeMs,
      endTimeMs: s.end_time_ms ?? s.endTimeMs,
      id: s.id || generateUniqueId(),
    }));

    finalOriginalSegments.forEach(origSeg => {
      const overlappingTransSegs = processedTranslatedSegments.filter(transSeg => {
        const overlapStart = Math.max(origSeg.startTimeMs, transSeg.startTimeMs);
        const overlapEnd = Math.min(origSeg.endTimeMs, transSeg.endTimeMs);
        return overlapEnd > overlapStart; // Ensure positive overlap duration
      });

      if (overlappingTransSegs.length > 0) {
        // Sort overlapping translated segments by their start time
        overlappingTransSegs.sort((a, b) => a.startTimeMs - b.startTimeMs);
        // Concatenate their text
        const combinedText = overlappingTransSegs.map(ts => ts.text).join(' ').trim();
        origSeg.translatedText = combinedText;
      }
    });
  }

  // Ensure final segments have consistent field names
  const result = finalOriginalSegments.map(s => ({
    id: s.id,
    startTimeMs: s.startTimeMs,
    endTimeMs: s.endTimeMs,
    text: s.text.trim(),
    translatedText: s.translatedText ? s.translatedText.trim() : '',
  }));

  console.log(`[alignAndMergeSegments] Completed: ${result.length} final segments`);

  return result;
}


module.exports = {
  srtTimeToMilliseconds,
  parseSrtToSegments,
  formatMillisecondsToSrtTime,
  segmentsToSrt,
  formatMillisecondsToVttTime,
  segmentsToVtt,
  formatMillisecondsToAssTime,
  segmentsToAss,
  segmentsToTxt,
  segmentsToJson,
  applyLayoutToSegments,
  alignAndMergeSegments, // Add the new function here
};
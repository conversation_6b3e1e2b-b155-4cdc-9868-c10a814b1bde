const { BrowserWindow } = require('electron');
const path = require('path');
const { logToRenderer: logToRendererUtil } = require('./utils'); // Renamed to avoid conflict
// initializeSubtitlerClient will be called from app-lifecycle or grpc-manager after window is ready.

let mainWindow = null;

function getMainWindow() {
  return mainWindow;
}

function createWindow(onWindowClosedCallback, initializeSubtitlerClientCallback) {
  // Get correct preload path for both dev and packaged environments
  let preloadPath;
  if (process.defaultApp) {
    // Development environment
    preloadPath = path.join(__dirname, '..', '..', 'preload.js');
  } else {
    // Packaged environment - preload.js is in the app root
    preloadPath = path.join(__dirname, '..', '..', 'preload.js');
  }
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: preloadPath,
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false
    },
  });

  // VITE_DEV_SERVER_URL will be set by the vite-plugin-electron during development
  if (process.env.VITE_DEV_SERVER_URL) {
    logToRendererUtil(null, `[WindowManager] Loading dev server: ${process.env.VITE_DEV_SERVER_URL}`);
    mainWindow.loadURL(process.env.VITE_DEV_SERVER_URL);
    // Open devTools automatically if running in development
    mainWindow.webContents.openDevTools();
  } else {
    // Load the index.html of the app in production.
    // Check if we're in a packaged app or development
    let indexPath;
    if (process.defaultApp || process.argv.includes('dev')) {
      // Development mode - load from dist directory
      indexPath = path.join(__dirname, '..', '..', 'dist', 'index.html');
    } else {
      // Packaged app - files are in the asar archive, dist folder is included
      indexPath = path.join(__dirname, '..', '..', 'dist', 'index.html');
    }
    logToRendererUtil(null, `[WindowManager] Loading index.html from: ${indexPath}`);
    mainWindow.loadFile(indexPath);
  }

  // Removed: if (process.defaultApp) { mainWindow.webContents.openDevTools(); }
  // Devtools opening is now handled above based on VITE_DEV_SERVER_URL

  mainWindow.on('closed', () => {
    mainWindow = null;
    if (onWindowClosedCallback) {
      onWindowClosedCallback();
    }
  });

  // It's better to initialize gRPC clients and other services
  // after the window is ready and possibly after app 'ready' event.
  // This callback will be triggered by app-lifecycle.js
  if (mainWindow.webContents) {
    mainWindow.webContents.on('did-finish-load', () => {
        if (initializeSubtitlerClientCallback) {
            initializeSubtitlerClientCallback();
        }
        // --- 添加下面这行 ---
        if (mainWindow && mainWindow.webContents && !mainWindow.webContents.isDevToolsOpened()) {
            mainWindow.webContents.openDevTools();
            logToRendererUtil(mainWindow, '[WindowManager] DevTools opened programmatically on did-finish-load.');
        }
        // --- 添加结束 ---
    });
  }


  return mainWindow;
}

// Wrapper for logToRenderer that passes the current mainWindow
function logToRenderer(message) {
    if (mainWindow) {
        logToRendererUtil(mainWindow, message);
    } else {
        console.log(`[WindowManager Log - No Window] ${message}`);
    }
}


module.exports = {
  createWindow,
  getMainWindow,
  logToRenderer, // Export the wrapped version
};
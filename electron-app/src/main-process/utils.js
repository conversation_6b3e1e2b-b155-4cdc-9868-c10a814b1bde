const { mainWindow } = require('electron'); // This will need to be managed carefully, might need to pass mainWindow

/**
 * 辅助函数：安全地向渲染进程发送消息。
 * 会检查 mainWindow 和 webContents 是否存在且未被销毁。
 * 如果 webContents 正在加载，则等待 'did-finish-load' 事件。
 * @param {BrowserWindow} win - The BrowserWindow instance to send the message to.
 * @param {string} channel - IPC 通道名称。
 * @param  {...any} args - 要发送的参数。
 */
function sendToRendererSafe(win, channel, ...args) {
  if (win && win.webContents && !win.webContents.isDestroyed()) {
    if (!win.webContents.isLoading()) {
      win.webContents.send(channel, ...args);
    } else {
      win.webContents.once('did-finish-load', () => {
        if (win && win.webContents && !win.webContents.isDestroyed()) { // Re-check after event
            win.webContents.send(channel, ...args);
        }
      });
    }
  }
}

/**
 * 辅助函数：将日志消息发送到渲染进程，并在主进程控制台打印。
 * @param {BrowserWindow} win - The BrowserWindow instance.
 * @param {string} message - 要记录的消息。
 */
function logToRenderer(win, message) {
  try {
    console.log(message); // 始终在主进程控制台记录
  } catch (error) {
    // 忽略控制台写入错误，避免 EIO 异常
    // 这通常发生在打包环境中或标准输出流被重定向时
  }
  sendToRendererSafe(win, 'main-process-log', message);
}

// srtTimeToMilliseconds and parseSrtToSegments have been moved to utils/subtitler-utils.js

module.exports = {
  sendToRendererSafe,
  logToRenderer,
  // srtTimeToMilliseconds, // Moved
  // parseSrtToSegments, // Moved
};
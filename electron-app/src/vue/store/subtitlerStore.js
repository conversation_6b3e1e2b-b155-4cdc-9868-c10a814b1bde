import { defineStore } from 'pinia';
import { segmentsToSrt } from '@/js/renderer-modules/subtitler/utils';

// Helper function to get subtitler client (available after renderer.js initialization)
const getSubtitlerClient = () => {
  if (typeof window !== 'undefined' && window.subtitlerClient) {
    console.log('[SubtitlerStore] subtitlerClient found and available');
    return window.subtitlerClient;
  }

  // Enhanced debugging information
  console.warn('[SubtitlerStore] subtitlerClient not available. Debugging info:');
  console.warn('- window object exists:', typeof window !== 'undefined');
  console.warn('- window.subtitlerClient exists:', typeof window !== 'undefined' && !!window.subtitlerClient);
  console.warn('- window.electronAPI exists:', typeof window !== 'undefined' && !!window.electronAPI);

  if (typeof window !== 'undefined') {
    console.warn('- Available window properties:', Object.keys(window).filter(key => key.includes('subtitler') || key.includes('electron') || key.includes('grpc')));
  }

  return null;
};

export const useSubtitlerStore = defineStore('subtitler', {
  state: () => ({
    uploadedFile: null, // { name, path, type, size }
    currentStep: 1,
    isLoading: false,
    progressUpdates: [],
    
    // Control flags for using previous step results
    usePreviousAudioForTranscription: true, // Controls if AudioToText uses VideoToAudio result
    useTranscriptionForEditing: true,    // Controls if EditSubtitles uses AudioToText result

    // Video to Audio step
    videoToAudioResult: null,
    videoToAudioProgress: '', // Can be String (for messages) or Number (for percentage)
    videoToAudioError: null,
    
    // Audio to Text step
    audioToTextResult: null, // Stores the transcription result (Object or String)
    audioToTextProgress: '', // Stores transcription progress (String or Number)
    audioToTextError: null,  // Stores transcription errors (String)
    
    // Generate Subtitles step (Step 4)
    generatedSubtitles: null, // Array of generated subtitle segments
    generateSubtitlesProgress: '', // Progress message for subtitle generation

    // Optimize Subtitles step (Step 5)
    optimizedSubtitles: null, // Array of optimized subtitle segments
    optimizeSubtitlesProgress: '', // Progress message for optimization

    // Edit Subtitles step (Step 6)
    editableSegments: null, // Array of { id, startTimeMs, endTimeMs, text }
    selectedSegmentId: null, // ID of the segment currently being edited
    editedSubtitlesSaved: false, // Tracks if edited subtitles have been saved

    // Translation steps (Step 7-8)
    translationSettings: {
      targetLanguage: 'en',
      quality: 'balanced',
      style: 'formal',
      customPrompt: ''
    },
    translatedSubtitles: null, // Array of translated subtitle segments
    translationProgress: '', // Progress message for translation
    translationProgressPercent: 0, // Progress percentage for translation
    
    // Export step
    exportFilename: '', // User defined filename (without extension)
    exportFormat: 'srt', // User selected export format
    exportLayout: '原文在上', // 使用后端期望的中文布局名称
    exportLayouts: ['原文在上'], // Multi-select layouts
    exportContentSource: 'editable_segments', // e.g., 'editable_segments', 'transcript_text', 'transcript_segments', 'translation_result' (backward compatibility)
    exportContentSources: ['editable_segments'], // Multi-select content sources
    exportAutoSaveToDefault: false, // Boolean, whether to skip save dialog
    lastExportPath: null, // Last successful export path
    exportResults: [], // Store multiple export results

    // One-click operation
    oneClickOperationInProgress: false, // Tracks if one-click operation is in progress
    oneClickOperationError: null, // Stores errors from one-click operation
    oneClickWorkflowType: 'vid_to_srt_trans', // Default workflow type
    oneClickTargetLanguage: 'zh-CN', // Default target language
    oneClickExportFormat: 'srt', // Default export format for one-click
    oneClickExportLayout: '原文在上', // 使用后端期望的中文布局名称
    oneClickExportLayouts: ['原文在上'], // 多选布局支持

    // For ResultsViewer
    activeResultTab: 'transcript', // Default active tab
    // translatedSegments: [], // Example for future translation feature
  }),
  getters: {
    srtPreviewFromEditableSegments: (state) => {
      if (state.editableSegments && state.editableSegments.length > 0) {
        // Need to import segmentsToSrt from utils, or ensure it's globally available
        // For Pinia, it's better to call utility functions directly if they don't depend on store instance
        // Assuming segmentsToSrt is available in the scope where this getter is used (e.g., component)
        // Or, we can import it here if this file is a module.
        // Let's assume it will be imported in the component or passed if needed.
        // For direct use in store, we'd need to import it:
        // import { segmentsToSrt } from '@/js/renderer-modules/subtitler/utils'; // No longer needed here
        // For now, we'll structure it to be called, and the component will use the imported util.
        // This getter will just point to the data. The component will compute.
        // Actually, getters *can* perform computation.
        // const { segmentsToSrt } = require('@/js/renderer-modules/subtitler/utils'); // No longer using require
        return segmentsToSrt(state.editableSegments);
      }
      return '';
    },
    srtPreviewFromOriginalSegments: (state) => {
      if (state.audioToTextResult && state.audioToTextResult.segments && state.audioToTextResult.segments.length > 0) {
        // const { segmentsToSrt } = require('@/js/renderer-modules/subtitler/utils'); // No longer using require
        return segmentsToSrt(state.audioToTextResult.segments);
      }
      return '';
    },
    // translatedSrtPreview: (state) => { // Example for future
    //   if (state.translatedSegments && state.translatedSegments.length > 0) {
    //     // const { segmentsToSrt } = require('@/js/renderer-modules/subtitler/utils');
    //     return segmentsToSrt(state.translatedSegments);
    //   }
    //   return '';
    // },
    getSegmentById: (state) => (id) => {
      if (!state.editableSegments) return null;
      return state.editableSegments.find(segment => segment.id === id) || null;
    },
    isReadyForNextStep: (state) => {
      // Enhanced logic for step progression
      switch (state.currentStep) {
        case 1: return state.uploadedFile !== null;
        case 2: return state.videoToAudioResult !== null;
        case 3: return state.audioToTextResult !== null;
        case 4: return state.generatedSubtitles !== null && state.generatedSubtitles.length > 0;
        case 5: return state.optimizedSubtitles !== null || state.generatedSubtitles !== null; // Can skip optimization
        case 6: return state.editableSegments !== null && state.editableSegments.length > 0;
        case 7: return true; // Translation choice is always ready
        case 8: return state.translatedSubtitles !== null; // Only if translation was chosen
        case 9: return true; // Export step is always ready if we got this far
        default: return false;
      }
    },
    getUploadedFileName: (state) => {
      return state.uploadedFile ? state.uploadedFile.name : '';
    },
    getUploadedFileSize: (state) => {
      return state.uploadedFile ? state.uploadedFile.size : '';
    },
    getUploadedFileType: (state) => {
      return state.uploadedFile ? state.uploadedFile.type : '';
    },
    // Getter to check if there are editable segments
    hasEditableSegments: (state) => {
      return state.editableSegments && state.editableSegments.length > 0;
    },
    // Getter for the currently selected segment object
    selectedSegment: (state) => {
      if (!state.selectedSegmentId || !state.editableSegments) return null;
      return state.editableSegments.find(segment => segment.id === state.selectedSegmentId) || null;
    }
  },
  actions: {
    setActiveResultTab(tabName) {
      this.activeResultTab = tabName;
    },
    setUploadedFile(fileObject) {
      if (fileObject) {
        this.uploadedFile = {
          name: fileObject.name,
          path: fileObject.path, // Electron File object has 'path'
          type: fileObject.type,
          size: fileObject.size,
        };
        this.currentStep = 1; // Or advance to next step if applicable
        this.progressUpdates = []; // Reset progress for new file
        this.videoToAudioResult = null;
        this.audioToTextResult = null;
        this.audioToTextProgress = '';
        this.audioToTextError = null;
        this.editableSegments = null;
        this.selectedSegmentId = null;
        this.editedSubtitlesSaved = false; // Reset on new file
      } else {
        this.uploadedFile = null;
      }
    },
    resetWorkflow() {
      this.uploadedFile = null;
      this.currentStep = 1;
      this.isLoading = false;
      this.progressUpdates = [];
      this.videoToAudioResult = null;
      this.audioToTextResult = null;
      this.audioToTextProgress = '';
      this.audioToTextError = null;

      // Reset new workflow states
      this.generatedSubtitles = null;
      this.generateSubtitlesProgress = '';
      this.optimizedSubtitles = null;
      this.optimizeSubtitlesProgress = '';
      this.editableSegments = null;
      this.selectedSegmentId = null;
      this.editedSubtitlesSaved = false;
      this.translationSettings = {
        targetLanguage: 'en',
        quality: 'balanced',
        style: 'formal',
        customPrompt: ''
      };
      this.translatedSubtitles = null;
      this.translationProgress = '';
      this.translationProgressPercent = 0;

      // Reset control flags
      this.usePreviousAudioForTranscription = true;
      this.useTranscriptionForEditing = true;

      // Reset export options as well
      this.exportFilename = '';
      this.exportFormat = 'srt';
      this.exportLayout = '原文在上'; // 使用后端期望的中文布局名称
      this.exportContentSource = 'editable_segments';
      this.exportAutoSaveToDefault = false;
      this.lastExportPath = null;
      console.log('Subtitler workflow reset.');
    },
    async processVideoToAudio() {
      if (!this.uploadedFile || !this.uploadedFile.path) {
        this.videoToAudioError = 'No file uploaded or file path is missing.';
        console.error(this.videoToAudioError);
        return;
      }

      this.isLoading = true;
      this.videoToAudioError = null;
      this.videoToAudioProgress = 'Starting video to audio conversion...';
      this.progressUpdates = [this.videoToAudioProgress]; // Reset and add first message

      try {
        // Get the subtitler client from the global scope (set by renderer.js)
        const subtitlerClient = getSubtitlerClient();
        if (!subtitlerClient) {
          // Fallback: try to use electronAPI directly
          if (typeof window !== 'undefined' && window.electronAPI) {
            console.log('[SubtitlerStore] Using electronAPI fallback for video to audio conversion');
            try {
              const result = await window.electronAPI.invoke('subtitler-full-workflow', {
                file_path: this.uploadedFile.path,
                workflow_type: 'vid_to_audio',
                request_word_timestamps: false
              });

                console.log('[SubtitlerStore] electronAPI result:', result);

              // Check for audio path in different possible locations
              let audioPath = null;
              if (result && result.audio_path) {
                audioPath = result.audio_path;
              } else if (result && result.video_to_audio_response && result.video_to_audio_response.audio_path) {
                audioPath = result.video_to_audio_response.audio_path;
              } else if (result && result.final_result === 'video_to_audio_response' && result.video_to_audio_response) {
                audioPath = result.video_to_audio_response.audio_path;
              }

              if (audioPath) {
                this.videoToAudioResult = audioPath;
                this.videoToAudioProgress = '转换成功！';
                this.progressUpdates.push(`✅ 视频转音频成功：${audioPath.split('/').pop()}`);
                this.currentStep = 2;
                this.editedSubtitlesSaved = false;
                return;
              } else {
                console.error('[SubtitlerStore] No audio path found in result:', result);
                throw new Error('No audio path received from conversion');
              }
            } catch (ipcError) {
              console.error('[SubtitlerStore] electronAPI invoke failed:', ipcError);
              throw new Error(`IPC call failed: ${ipcError.message}`);
            }
          } else {
            throw new Error('Subtitler client not available and electronAPI fallback failed. Please check backend connection.');
          }
        }

        // Use the original API client's stream handling
        const request = { video_path: this.uploadedFile.path };
        const stream = subtitlerClient.videoToAudio(request);
        
        let lastResult = null;

        const handleStreamData = (response) => {
          const stageName = response.getStageName ? response.getStageName() : 'VideoToAudio';
          const percentage = response.getPercentage ? response.getPercentage() : 0;
          const message = response.getMessage ? response.getMessage() : 'Processing...';
          const isError = response.getIsError ? response.getIsError() : false;
          const errorMessage = response.getErrorMessage ? response.getErrorMessage() : '';
          
          if (isError) {
            this.videoToAudioError = errorMessage || '视频转音频过程中发生错误';
            this.videoToAudioProgress = '转换失败';
            this.progressUpdates.push(`❌ 错误：${this.videoToAudioError}`);
            return;
          }
          
          // Update progress
          this.videoToAudioProgress = `${message} (${percentage}%)`;
          this.progressUpdates.push(`🔄 ${stageName}：${message} (${percentage}%)`);
          
          // Extract result data
          if (response.video_to_audio_response || response.getVideoToAudioResponse) {
            const audioResponse = response.video_to_audio_response || response.getVideoToAudioResponse();
            if (audioResponse) {
              const audioPath = audioResponse.getAudioPath ? audioResponse.getAudioPath() : audioResponse.audio_path;
              if (audioPath) {
                lastResult = audioPath;
                this.videoToAudioResult = audioPath;
                console.log('[VideoToAudio] Audio path extracted:', audioPath);
              }
            }
          }
        };

        // Handle the stream
        await new Promise((resolve, reject) => {
          stream.on('data', handleStreamData);
          
          stream.on('end', () => {
            if (lastResult) {
              this.videoToAudioProgress = '转换成功！';
              this.progressUpdates.push(`✅ 视频转音频完成：${lastResult.split('/').pop()}`);
              this.currentStep = 2; // Move to next step
              this.editedSubtitlesSaved = false; // Reset save status for next steps
              resolve(lastResult);
            } else {
              const error = new Error('No audio path received from conversion');
              this.videoToAudioError = error.message;
              reject(error);
            }
          });
          
          stream.on('error', (err) => {
            console.error('Video to audio stream error:', err);
            this.videoToAudioError = err.message || 'Stream error during conversion';
            this.videoToAudioProgress = 'Conversion failed.';
            this.progressUpdates.push(`Stream Error: ${this.videoToAudioError}`);
            reject(err);
          });
        });

      } catch (error) {
        console.error('Error in video to audio conversion:', error);
        this.videoToAudioError = error.message || 'An unknown error occurred during video to audio conversion.';
        this.videoToAudioProgress = 'Conversion failed.';
        this.progressUpdates.push(`Error: ${this.videoToAudioError}`);
      } finally {
        this.isLoading = false;
      }
    },
    setCurrentStep(stepNumber) {
      if (typeof stepNumber === 'number' && stepNumber > 0) {
        // 可以添加逻辑以验证步骤是否可达
        this.currentStep = stepNumber;
      } else {
        console.warn(`Invalid step number: ${stepNumber}`);
      }
    },
    // 更多 actions 可以在后续步骤中添加
    // e.g., processAudioToText, updateSettings, etc.

    async processAudioToText() {
      let audioPath = null;
      if (this.usePreviousAudioForTranscription && this.videoToAudioResult) {
        audioPath = this.videoToAudioResult;
      } else if (this.uploadedFile && this.uploadedFile.path && this.uploadedFile.type && this.uploadedFile.type.startsWith('audio/')) {
        audioPath = this.uploadedFile.path;
      }

      if (!audioPath) {
        this.audioToTextError = 'No audio source selected or available for transcription. Please ensure "Use previous audio" is checked if applicable, or upload an audio file.';
        console.error(this.audioToTextError);
        return;
      }

      // 检查音频文件是否存在
      try {
        const fileExists = await window.electronAPI.invoke('check-file-exists', audioPath);
        if (!fileExists) {
          this.audioToTextError = `音频文件不存在: ${audioPath}。请重新执行视频转音频步骤，或上传新的音频文件。`;
          console.error(this.audioToTextError);
          this.progressUpdates.push(`❌ 错误: 音频文件不存在，请重新生成音频文件`);
          return;
        }
      } catch (checkError) {
        console.warn('无法检查文件存在性:', checkError);
        // 继续执行，让后端处理文件不存在的情况
      }

      this.isLoading = true;
      this.audioToTextError = null;
      this.audioToTextProgress = '开始音频转文字处理...';
      // Prepend to progressUpdates, or create if it's the first message for this step
      if (this.currentStep !== 3) { // Assuming step 3 is AudioToText
        this.progressUpdates = ['🎤 开始音频转文字处理...'];
      } else {
        this.progressUpdates.unshift('🎤 开始音频转文字处理...');
      }

      try {
        // Get the subtitler client from the global scope (set by renderer.js)
        const subtitlerClient = getSubtitlerClient();
        if (!subtitlerClient) {
          // Fallback: try to use electronAPI directly
          if (typeof window !== 'undefined' && window.electronAPI) {
            console.log('[SubtitlerStore] Using electronAPI fallback for audio to text conversion');
            try {
              const result = await window.electronAPI.invoke('subtitler-full-workflow', {
                file_path: audioPath,
                workflow_type: 'audio_to_text',
                request_word_timestamps: true
              });

                console.log('[SubtitlerStore] audioToText electronAPI result:', result);

              // Check for transcription data in different possible locations
              let transcript = null;
              let segments = [];

              if (result && result.transcript) {
                transcript = result.transcript;
                segments = result.segments || [];
              } else if (result && result.audio_to_text_response) {
                const a2tResponse = result.audio_to_text_response;
                transcript = a2tResponse.transcript;
                segments = a2tResponse.segments || [];
              } else if (result && result.final_result === 'audio_to_text_response' && result.audio_to_text_response) {
                const a2tResponse = result.audio_to_text_response;
                transcript = a2tResponse.transcript;
                segments = a2tResponse.segments || [];
              }

              if (transcript || segments.length > 0) {
                const processedSegments = segments.map((seg, index) => ({
                  id: `segment-${index}`,
                  text: seg.text,
                  startTimeMs: seg.start_time_ms,
                  endTimeMs: seg.end_time_ms,
                  start_time_ms: seg.start_time_ms,
                  end_time_ms: seg.end_time_ms
                }));

                const transcriptionResult = {
                  transcript: transcript || processedSegments.map(s => s.text).join(' '),
                  segments: processedSegments
                };

                this.audioToTextResult = transcriptionResult;
                this.audioToTextProgress = '转录成功！';
                this.progressUpdates.push('✅ 音频转文字完成，识别到 ' + processedSegments.length + ' 个语音片段');
                this.setCurrentStep(4); // 进入组成句子步骤
                this.editedSubtitlesSaved = false;
                return;
              } else {
                console.error('[SubtitlerStore] No transcription data found in result:', result);
                throw new Error('No transcription result received');
              }
            } catch (ipcError) {
              console.error('[SubtitlerStore] electronAPI invoke failed for audioToText:', ipcError);
              throw new Error(`IPC call failed: ${ipcError.message}`);
            }
          } else {
            throw new Error('Subtitler client not available and electronAPI fallback failed. Please check backend connection.');
          }
        }

        // TODO: Define actual additionalParams, possibly from store or UI
        const additionalParams = {
          language: 'auto', // Example: 'en', 'zh', 'auto'
          modelSize: 'base' // Example: 'tiny', 'base', 'small', 'medium', 'large'
        };

        // Use the original API client's stream handling
        const request = { 
          audio_path: audioPath,
          request_word_timestamps: true, // Enable word timestamps
          ...additionalParams
        };
        const stream = subtitlerClient.audioToText(request);
        
        let lastResult = null;

        const handleStreamData = (response) => {
          const stageName = response.getStageName ? response.getStageName() : 'AudioToText';
          const percentage = response.getPercentage ? response.getPercentage() : 0;
          const message = response.getMessage ? response.getMessage() : 'Processing...';
          const isError = response.getIsError ? response.getIsError() : false;
          const errorMessage = response.getErrorMessage ? response.getErrorMessage() : '';
          
          if (isError) {
            this.audioToTextError = errorMessage || '音频转文字过程中发生错误';
            this.audioToTextProgress = '转录失败';
            this.progressUpdates.push(`❌ 错误：${this.audioToTextError}`);
            return;
          }
          
          // Update progress
          this.audioToTextProgress = `${message} (${percentage}%)`;
          this.progressUpdates.push(`🔄 ${this.getStageNameInChinese(stageName)}：${message} (${percentage}%)`);
          
          // Extract result data
          if (response.audio_to_text_response || response.getAudioToTextResponse) {
            const textResponse = response.audio_to_text_response || response.getAudioToTextResponse();
            if (textResponse) {
              const transcript = textResponse.getTranscript ? textResponse.getTranscript() : textResponse.transcript;
              const segmentsList = textResponse.getSegmentsList ? textResponse.getSegmentsList() : textResponse.segments || [];
              
              if (transcript || (segmentsList && segmentsList.length > 0)) {
                const segments = segmentsList.map((seg, index) => ({
                  id: `segment-${index}`,
                  text: seg.getText ? seg.getText() : seg.text,
                  startTimeMs: seg.getStartTimeMs ? seg.getStartTimeMs() : seg.start_time_ms,
                  endTimeMs: seg.getEndTimeMs ? seg.getEndTimeMs() : seg.end_time_ms,
                  start_time_ms: seg.getStartTimeMs ? seg.getStartTimeMs() : seg.start_time_ms,
                  end_time_ms: seg.getEndTimeMs ? seg.getEndTimeMs() : seg.end_time_ms
                }));
                
                lastResult = {
                  transcript: transcript || segments.map(s => s.text).join(' '),
                  segments: segments
                };
                
                // Store results in the store
                this.audioToTextResult = lastResult;
                
                console.log('[AudioToText] Transcription result:', lastResult);
              }
            }
          }
        };

        // Handle the stream
        await new Promise((resolve, reject) => {
          stream.on('data', handleStreamData);
          
          stream.on('end', () => {
            if (lastResult) {
              this.audioToTextProgress = '转录成功！';
              this.progressUpdates.push(`✅ 音频转文字完成，共处理 ${lastResult.segments.length} 个语音片段`);
              this.setCurrentStep(4); // Move to generate subtitles step
              this.editedSubtitlesSaved = false; // Reset save status as new segments are loaded
              resolve(lastResult);
            } else {
              const error = new Error('No transcription result received');
              this.audioToTextError = error.message;
              reject(error);
            }
          });
          
          stream.on('error', (err) => {
            console.error('Audio to text stream error:', err);
            this.audioToTextError = err.message || '转录过程中发生流错误';
            this.audioToTextProgress = '转录失败';
            this.progressUpdates.push(`❌ 流错误：${this.audioToTextError}`);
            reject(err);
          });
        });

      } catch (error) {
        console.error('Error in audio to text conversion:', error);
        this.audioToTextError = error.message || 'An unknown error occurred during audio to text conversion.';
        this.audioToTextProgress = 'Transcription failed.';
        this.progressUpdates.push(`Error: ${this.audioToTextError}`);
      } finally {
        this.isLoading = false;
      }
    },

    initializeEditableSegments() {
      if (this.useTranscriptionForEditing) {
        if (this.audioToTextResult && Array.isArray(this.audioToTextResult.segments)) {
          this.editableSegments = this.audioToTextResult.segments.map((segment, index) => ({
            id: `segment-${Date.now()}-${index}`, // Simple unique ID
            startTimeMs: segment.startTimeMs, // Assuming this structure from ASR
            endTimeMs: segment.endTimeMs,   // Assuming this structure from ASR
            text: segment.text,
          }));
          this.selectedSegmentId = null; // Reset selected segment
          console.log('Editable segments initialized from transcription:', this.editableSegments);
        } else if (typeof this.audioToTextResult === 'string') {
          // Handle plain text result - create a single segment
          this.editableSegments = [{
            id: `segment-${Date.now()}-0`,
            startTimeMs: 0, // Default start time
            endTimeMs: 0,   // Default end time, user might need to adjust
            text: this.audioToTextResult,
          }];
          this.selectedSegmentId = null;
          console.log('Editable segments initialized from plain text transcription:', this.editableSegments);
        } else {
          console.warn('Cannot initialize editable segments: audioToTextResult is not in expected format or is null, despite useTranscriptionForEditing being true.');
          this.editableSegments = []; // Initialize as empty array to prevent errors
        }
      } else {
        // If not using transcription for editing, clear segments or allow manual input (handled by component)
        this.editableSegments = []; // Or null, depending on desired behavior for manual input
        console.log('Not using transcription for editing. Editable segments cleared.');
      }
      this.editedSubtitlesSaved = false; // Segments initialized or cleared, not saved yet
    },

    setUsePreviousAudioForTranscription(value) {
      this.usePreviousAudioForTranscription = !!value;
      // Potentially reset audioToTextResult if this changes, or let component handle it
      if (!value) {
        this.audioToTextResult = null; // Clear transcription if not using previous audio and no other audio is loaded
        this.editableSegments = null; // Also clear segments
      }
    },

    setUseTranscriptionForEditing(value) {
      this.useTranscriptionForEditing = !!value;
      if (value && this.audioToTextResult) {
        this.initializeEditableSegments(); // Re-initialize if toggled to true and result exists
      } else if (!value) {
        this.editableSegments = null; // Clear segments if not using transcription
      }
    },

    setSelectedSegmentId(id) {
      this.selectedSegmentId = id;
    },

    updateSegmentText({ id, newText }) {
      if (!this.editableSegments) return;
      const segment = this.editableSegments.find(s => s.id === id);
      if (segment) {
        segment.text = newText;
      }
      this.editedSubtitlesSaved = false; // Text updated, not saved yet
    },

    updateSegmentTime({ id, newStartTimeMs, newEndTimeMs }) {
      if (!this.editableSegments) return;
      const segment = this.editableSegments.find(s => s.id === id);
      if (segment) {
        // Basic validation, can be expanded
        if (typeof newStartTimeMs === 'number' && newStartTimeMs >= 0) {
          segment.startTimeMs = newStartTimeMs;
        }
        if (typeof newEndTimeMs === 'number' && newEndTimeMs >= 0 && newEndTimeMs >= segment.startTimeMs) {
          segment.endTimeMs = newEndTimeMs;
        }
      }
      this.editedSubtitlesSaved = false; // Time updated, not saved yet
    },

    addSegment({ afterId = null, newSegmentData = { startTimeMs: 0, endTimeMs: 0, text: 'New Segment' } }) {
      if (!this.editableSegments) this.editableSegments = [];

      const newId = `segment-${Date.now()}-${this.editableSegments.length}`;
      const segmentToAdd = { ...newSegmentData, id: newId };

      if (afterId === null) { // Add to the end
        this.editableSegments.push(segmentToAdd);
      } else {
        const index = this.editableSegments.findIndex(s => s.id === afterId);
        if (index !== -1) {
          this.editableSegments.splice(index + 1, 0, segmentToAdd);
        } else { // If afterId not found, add to end
          this.editableSegments.push(segmentToAdd);
          console.warn(`addSegment: afterId ${afterId} not found. Segment added to end.`);
        }
      }
      this.selectedSegmentId = newId; // Optionally select the new segment
      this.editedSubtitlesSaved = false; // Segment added, not saved yet
    },

    deleteSegment(id) {
      if (!this.editableSegments) return;
      this.editableSegments = this.editableSegments.filter(s => s.id !== id);
      if (this.selectedSegmentId === id) {
        this.selectedSegmentId = null; // Clear selection if deleted segment was selected
      }
      this.editedSubtitlesSaved = false; // Segment deleted, potentially needs saving
    },

    async saveEditedSubtitles() {
      if (!this.editableSegments || this.editableSegments.length === 0) {
        console.warn('No edited segments to save.');
        // Optionally, inform the user via UI store
        this.editedSubtitlesSaved = false; // Explicitly false if nothing to save or already saved
        return;
      }
      this.isLoading = true;
      this.editedSubtitlesSaved = false; // Set to false initially for the save attempt
      try {
        // Create a clean, serializable copy of the segments
        const cleanSegments = this.editableSegments.map(segment => ({
          id: segment.id,
          text: segment.text,
          startTimeMs: segment.startTimeMs,
          endTimeMs: segment.endTimeMs,
          start_time_ms: segment.start_time_ms,
          end_time_ms: segment.end_time_ms
        }));

        console.log('Saving edited subtitles:', cleanSegments.length, 'segments');
        this.progressUpdates.push(`💾 正在保存 ${cleanSegments.length} 个编辑后的字幕片段...`);
        // IPC call to main process to handle saving
        // The main process might save to a file, send to backend, etc.
        const result = await window.electronAPI.invoke('subtitler:save-edited-segments', cleanSegments);
        console.log('Save edited subtitles result:', result);
        this.progressUpdates.push(`✅ 编辑后的字幕保存成功`);
        this.editedSubtitlesSaved = true; // Mark as saved on success
        // Handle success - e.g., show notification, move to next step
        // this.setCurrentStep(5); // Example: move to an "Export" or "Done" step
      } catch (error) {
        console.error('Error saving edited subtitles:', error);
        this.progressUpdates.push(`❌ 保存编辑字幕失败：${error.message}`);
        this.editedSubtitlesSaved = false; // Mark as not saved on error
        // Handle error - e.g., show error message to user
      } finally {
        this.isLoading = false;
      }
    },
    setExportFilename(filename) {
      this.exportFilename = filename;
    },
    setExportFormat(format) {
      this.exportFormat = format;
    },
    setExportLayout(layout) {
      this.exportLayout = layout;
    },
    setExportLayouts(layouts) {
      this.exportLayouts = layouts || [];
    },
    setExportContentSource(source) {
      this.exportContentSource = source;
    },
    setExportContentSources(sources) {
      this.exportContentSources = sources || [];
    },
    setExportAutoSaveToDefault(autoSave) {
      this.exportAutoSaveToDefault = autoSave;
    },

    async exportSubtitles(options = {}) {
      // Determine content to export based on exportContentSource
      let segmentsToExport;
      let rawContentToExport = null; // For text or raw segment data

      switch (this.exportContentSource) {
        case 'transcript_text':
          if (this.audioToTextResult && typeof this.audioToTextResult.transcript === 'string') {
            rawContentToExport = this.audioToTextResult.transcript;
            // For TXT format, we can pass raw text. For others, we might need to create a dummy segment.
            // Or, the main process handler for TXT can directly use this.
            // For simplicity here, we'll assume main process handles rawContent for TXT.
            // If format is not TXT, we might need to convert this text to a segment structure or show an error.
            // For now, we'll pass it and let main process decide.
            segmentsToExport = [{ id: 'transcript', startTimeMs: 0, endTimeMs: 0, text: rawContentToExport }]; // Simplified for now
          } else {
            console.warn('Transcript text selected for export, but no transcript available.');
            this.progressUpdates.push('❌ 错误：选择了转录文本导出，但未找到转录数据');
            return;
          }
          break;
        case 'transcript_segments':
          if (this.audioToTextResult && Array.isArray(this.audioToTextResult.segments)) {
            segmentsToExport = JSON.parse(JSON.stringify(this.audioToTextResult.segments));
          } else {
            console.warn('Transcript segments selected for export, but no segments available.');
            this.progressUpdates.push('❌ 错误：选择了转录片段导出，但未找到片段数据');
            return;
          }
          break;
        case 'translation_result':
          if (this.translatedSubtitles && this.translatedSubtitles.length > 0) {
            // Convert translatedSubtitles format to export format
            segmentsToExport = this.translatedSubtitles.map(segment => ({
              id: segment.id,
              text: segment.originalText || segment.text, // Original text
              translation: segment.translatedText, // Translated text
              startTimeMs: segment.startTimeMs,
              endTimeMs: segment.endTimeMs,
              start_time_ms: segment.start_time_ms,
              end_time_ms: segment.end_time_ms
            }));
            this.progressUpdates.push('📝 使用翻译字幕进行导出');
          } else {
            console.warn('Translation result selected for export, but no translation available.');
            this.progressUpdates.push('⚠️ 警告：未找到翻译结果，使用当前编辑的字幕');
            segmentsToExport = JSON.parse(JSON.stringify(this.editableSegments));
          }
          break;
        case 'editable_segments':
        default:
          if (!this.editableSegments || this.editableSegments.length === 0) {
            console.warn('No segments to export.');
            this.progressUpdates.push('❌ 没有可导出的字幕');
            return;
          }
          // Check if editable segments have translation data
          segmentsToExport = this.editableSegments.map(segment => ({
            id: segment.id,
            text: segment.text,
            translation: segment.translatedText || segment.translation, // Support both field names
            startTimeMs: segment.startTimeMs,
            endTimeMs: segment.endTimeMs,
            start_time_ms: segment.start_time_ms,
            end_time_ms: segment.end_time_ms
          }));
          break;
      }
      
      if (!segmentsToExport && !rawContentToExport) {
        console.warn('No content determined for export.');
        this.progressUpdates.push('Error: Could not determine content to export based on selection.');
        return;
      }

      this.isLoading = true;
      this.lastExportPath = null;
      
      const exportPayload = {
        segments: segmentsToExport, // This might be null if rawContentToExport is used for TXT
        rawContent: rawContentToExport, // Specifically for TXT or direct content
        filename: options.filename || this.exportFilename || (this.uploadedFile ? this.uploadedFile.name.split('.').slice(0, -1).join('.') : 'subtitles'),
        format: this.exportFormat,
        layout: this.exportLayout,
        contentSource: this.exportContentSource,
        autoSaveToDefault: this.exportAutoSaveToDefault,
      };

      console.log('Exporting subtitles with payload:', exportPayload);
      this.progressUpdates.push(`Attempting to export as ${exportPayload.format.toUpperCase()}...`);

      try {
        // Try to use backend SaveSubtitle API first
        try {
          this.progressUpdates.push('Using backend SaveSubtitle API...');

          // Prepare segments for backend API (compatible with existing handler)
          const backendSegments = segmentsToExport.map(segment => ({
            start_time_ms: segment.startTimeMs || segment.start_time_ms || 0,
            end_time_ms: segment.endTimeMs || segment.end_time_ms || 0,
            original_text: segment.text || '',
            translated_text: segment.translation || segment.translatedText || ''
          }));

          const backendPayload = {
            segments: backendSegments,
            format: this.exportFormat,
            layout: this.exportLayout,
            fileNamePrefix: exportPayload.filename,
            auto_save_to_default: this.exportAutoSaveToDefault
          };

          const result = await window.electronAPI.invoke('subtitler-save-subtitle', backendPayload);

          if (result && result.file_path) {
            this.lastExportPath = result.file_path;
            this.progressUpdates.push(`✅ 字幕成功导出到：${result.file_path}`);
            console.log('Backend export successful:', result.file_path);
            return; // Success, exit early
          } else {
            throw new Error('Backend SaveSubtitle API returned invalid response');
          }
        } catch (backendError) {
          console.warn('Backend SaveSubtitle failed, using fallback:', backendError);
          this.progressUpdates.push('Backend export failed, using local fallback');
        }

        // Fallback to local export
        // Ensure the payload is serializable by creating a clean copy
        const cleanPayload = JSON.parse(JSON.stringify(exportPayload));
        const result = await window.electronAPI.invoke('subtitler:export-subtitles', cleanPayload);
        
        if (result.filePath) {
          this.lastExportPath = result.filePath;
          this.progressUpdates.push(`✅ 字幕成功导出到：${result.filePath}`);
          console.log('Export successful:', result.filePath);
        } else if (result.error) {
          console.error('Export failed:', result.error);
          this.progressUpdates.push(`❌ 导出失败：${result.error}`);
        } else if (result.cancelled) {
          console.log('Export was cancelled by the user.');
          this.progressUpdates.push('⏹️ 用户取消了导出操作');
        } else {
          console.warn('Export completed with an unknown status:', result);
          this.progressUpdates.push('❓ 导出状态未知');
        }
      } catch (error) {
        console.error('Error during exportSubtitles action:', error);
        this.progressUpdates.push(`❌ 导出过程中发生错误：${error.message}`);
      } finally {
        this.isLoading = false;
      }
    },
 
    async performOneClickOperation() {
      if (!this.uploadedFile || !this.uploadedFile.path) {
        this.oneClickOperationError = 'No file uploaded for one-click operation.';
        console.error(this.oneClickOperationError);
        this.progressUpdates.push('Error: No file uploaded.');
        return;
      }

      this.oneClickOperationInProgress = true;
      this.oneClickOperationError = null;
      this.isLoading = true;
      this.lastExportPath = null;
      this.progressUpdates = [];

      try {
        this.progressUpdates.push('🚀 开始一键字幕生成...');

        // 确定工作流程类型
        const needsTranslation = this.oneClickWorkflowType.includes('trans') || this.oneClickWorkflowType.includes('translated');
        const isVideoInput = this.oneClickWorkflowType.startsWith('vid_');

        console.log('[OneClick] Workflow type:', this.oneClickWorkflowType, 'needsTranslation:', needsTranslation, 'isVideoInput:', isVideoInput);

        // Step 1: 视频转音频 (如果需要)
        if (isVideoInput) {
          this.progressUpdates.push('📹 开始视频转音频...');
          await this.processVideoToAudio();
          this.progressUpdates.push('✅ 视频转音频完成');
        }

        // Step 2: 音频转文字
        this.progressUpdates.push('🎤 开始语音识别...');
        await this.processAudioToText();
        this.progressUpdates.push('✅ 语音识别完成');

        // Step 3: 生成字幕
        this.progressUpdates.push('📝 开始生成字幕...');
        await this.generateSubtitles();
        this.progressUpdates.push('✅ 字幕生成完成');

        // Step 4: 初始化可编辑字幕
        this.progressUpdates.push('⚙️ 初始化可编辑字幕...');
        this.initializeEditableSegments();
        this.progressUpdates.push('✅ 可编辑字幕初始化完成');

        // Step 5: 翻译 (如果需要)
        if (needsTranslation && this.oneClickTargetLanguage) {
          this.progressUpdates.push(`🌐 开始翻译到${this.getLanguageDisplayName(this.oneClickTargetLanguage)}...`);

          // 设置翻译参数
          this.setTranslationSettings({
            targetLanguage: this.oneClickTargetLanguage,
            sourceLanguage: 'en' // 假设原文是英文
          });

          // 调用单步操作的翻译方法
          await this.translateSubtitles();
          this.progressUpdates.push('✅ 翻译完成');
        }

        // Step 6: 设置导出参数并导出多个布局
        this.progressUpdates.push('📤 开始导出字幕...');

        // 获取要导出的布局列表
        const layoutsToExport = this.oneClickExportLayouts && this.oneClickExportLayouts.length > 0
          ? this.oneClickExportLayouts
          : [this.oneClickExportLayout];

        this.progressUpdates.push(`📋 将导出 ${layoutsToExport.length} 种布局: ${layoutsToExport.join(', ')}`);

        // 设置基本导出参数
        this.exportFormat = this.oneClickExportFormat;
        this.exportContentSource = 'editable_segments';
        this.exportAutoSaveToDefault = true; // 一键操作自动保存

        // 生成基础文件名
        const originalFileName = this.uploadedFile.name.split('.').slice(0, -1).join('.');
        const languageSuffix = needsTranslation ? `_${this.oneClickTargetLanguage}` : '';

        const exportedPaths = [];

        // 为每个布局导出文件
        for (let i = 0; i < layoutsToExport.length; i++) {
          const layout = layoutsToExport[i];
          this.progressUpdates.push(`📄 [${i + 1}/${layoutsToExport.length}] 导出布局: ${layout}`);

          // 设置当前布局
          this.exportLayout = layout;

          // 生成带布局后缀的文件名
          const layoutSuffix = this._getLayoutSuffix(layout);
          this.exportFilename = `${originalFileName}${languageSuffix}_${layoutSuffix}`;

          // 调用单步操作的导出方法
          await this.exportSubtitles();

          if (this.lastExportPath) {
            exportedPaths.push(this.lastExportPath);
            this.progressUpdates.push(`✅ [${i + 1}/${layoutsToExport.length}] 完成: ${this.lastExportPath}`);
          } else {
            this.progressUpdates.push(`❌ [${i + 1}/${layoutsToExport.length}] 失败: ${layout}`);
          }
        }

        if (exportedPaths.length > 0) {
          this.progressUpdates.push(`✅ 一键操作成功完成！共导出 ${exportedPaths.length} 个文件`);
          exportedPaths.forEach((path, index) => {
            this.progressUpdates.push(`📁 文件${index + 1}: ${path}`);
          });

          // 更新当前步骤到导出完成
          this.currentStep = 9;
          this.progressUpdates.push(`🎉 工作流程完成，已跳转到导出页面`);
        } else {
          throw new Error('导出失败：所有布局都未能成功导出');
        }

      } catch (error) {
        console.error('Error in performOneClickOperation:', error);
        this.oneClickOperationError = error.message || 'An unknown error occurred during the one-click operation.';
        this.progressUpdates.push(`❌ 错误：${this.oneClickOperationError}`);
      } finally {
        this.oneClickOperationInProgress = false;
        this.isLoading = false;
      }
    },

    // Set workflow type for one-click operation
    setOneClickWorkflowType(workflowType) {
      this.oneClickWorkflowType = workflowType;
    },

    // Set target language for one-click operation
    setOneClickTargetLanguage(language) {
      this.oneClickTargetLanguage = language;
    },

    // Set export format for one-click operation
    setOneClickExportFormat(format) {
      this.oneClickExportFormat = format;
    },

    // Set export layout for one-click operation
    setOneClickExportLayout(layout) {
      this.oneClickExportLayout = layout;
    },

    // Set multiple export layouts for one-click operation
    setOneClickExportLayouts(layouts) {
      if (Array.isArray(layouts) && layouts.length > 0) {
        this.oneClickExportLayouts = layouts;
        // 同时更新单选布局（用于兼容性）
        this.oneClickExportLayout = layouts[0];
      } else {
        // 如果传入空数组或无效值，使用默认值
        this.oneClickExportLayouts = ['原文在上'];
        this.oneClickExportLayout = '原文在上';
      }
    },

    // New workflow methods
    async generateSubtitles() {
      if (!this.audioToTextResult || !this.audioToTextResult.segments) {
        throw new Error('No transcription result available for subtitle generation');
      }

      this.isLoading = true;
      this.generateSubtitlesProgress = '开始生成字幕...';
      this.progressUpdates.push('📝 开始生成字幕片段...');

      try {
        // Call backend to generate subtitles using text_to_srt workflow
        this.generateSubtitlesProgress = '调用后端生成字幕...';

        const result = await window.electronAPI.invoke('subtitler-full-workflow', {
          req_text_content: this.audioToTextResult.transcript,
          workflow_type: 'text_to_srt',
          request_word_timestamps: false
        });

        if (result && result.generate_subtitles_response && result.generate_subtitles_response.segments) {
          // Use the segments from the backend response
          this.generatedSubtitles = result.generate_subtitles_response.segments.map((segment, index) => ({
            id: segment.id || `generated-${index}`,
            text: segment.text,
            startTimeMs: segment.startTimeMs || segment.start_time_ms,
            endTimeMs: segment.endTimeMs || segment.end_time_ms,
            start_time_ms: segment.start_time_ms || segment.startTimeMs,
            end_time_ms: segment.end_time_ms || segment.endTimeMs
          }));

          this.generateSubtitlesProgress = '字幕生成完成！';
          this.progressUpdates.push(`✅ 成功从转录文本生成 ${this.generatedSubtitles.length} 个字幕片段`);
        } else {
          // Fallback: use simple sentence splitting if backend doesn't support text_to_srt
          this.generateSubtitlesProgress = '使用备用句子生成方法...';
          this.generatedSubtitles = this._generateSentencesFromSegments(this.audioToTextResult.segments);
          this.progressUpdates.push(`🔄 使用备用方法生成 ${this.generatedSubtitles.length} 个句子`);
        }
      } catch (error) {
        console.error('Error generating subtitles:', error);
        this.generateSubtitlesProgress = '后端失败，使用备用方法...';

        // Fallback to local sentence generation
        try {
          this.generatedSubtitles = this._generateSentencesFromSegments(this.audioToTextResult.segments);
          this.progressUpdates.push(`🔄 使用备用方法生成 ${this.generatedSubtitles.length} 个句子`);
        } catch (fallbackError) {
          this.generateSubtitlesProgress = '字幕生成失败';
          this.progressUpdates.push(`❌ 错误：${fallbackError.message}`);
          throw fallbackError;
        }
      } finally {
        this.isLoading = false;
      }
    },

    // Helper method to generate sentences from word-level segments
    _generateSentencesFromSegments(segments) {
      if (!segments || segments.length === 0) {
        return [];
      }

      console.log(`[generateSentences] Processing ${segments.length} segments`);

      const sentences = [];
      let currentSentence = {
        words: [],
        startTime: null,
        endTime: null
      };

      // Sentence ending punctuation
      const sentenceEnders = /[.!?。！？]/;

      // More aggressive sentence splitting - split every 8-12 words or on punctuation
      const maxWordsPerSentence = 12;
      const minWordsPerSentence = 5;

      for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        const text = segment.text.trim();

        if (!text) continue;

        // Initialize start time for new sentence
        if (currentSentence.words.length === 0) {
          currentSentence.startTime = segment.startTimeMs || segment.start_time_ms;
        }

        currentSentence.words.push(text);
        currentSentence.endTime = segment.endTimeMs || segment.end_time_ms;

        // Check if this segment ends a sentence
        const endsWithPunctuation = sentenceEnders.test(text);
        const isLongEnough = currentSentence.words.length >= minWordsPerSentence;
        const isTooLong = currentSentence.words.length >= maxWordsPerSentence;
        const hasTimeGap = i < segments.length - 1 &&
          ((segments[i + 1].startTimeMs || segments[i + 1].start_time_ms) - currentSentence.endTime) > 1500; // 1.5 second gap
        const isLastSegment = i === segments.length - 1;

        // Decision logic for ending sentence
        let shouldEndSentence = false;
        let reason = '';

        if (endsWithPunctuation) {
          shouldEndSentence = true;
          reason = 'punctuation';
        } else if (isTooLong) {
          shouldEndSentence = true;
          reason = 'too long';
        } else if (isLongEnough && hasTimeGap) {
          shouldEndSentence = true;
          reason = 'time gap';
        } else if (isLastSegment && currentSentence.words.length > 0) {
          shouldEndSentence = true;
          reason = 'last segment';
        }

        if (shouldEndSentence) {
          // Create sentence
          const sentenceText = currentSentence.words.join(' ').trim();
          if (sentenceText) {
            console.log(`[generateSentences] Creating sentence ${sentences.length + 1}: "${sentenceText.substring(0, 50)}..." (${currentSentence.words.length} words, reason: ${reason})`);

            sentences.push({
              id: `sentence-${sentences.length}`,
              text: sentenceText,
              startTimeMs: currentSentence.startTime,
              endTimeMs: currentSentence.endTime,
              start_time_ms: currentSentence.startTime,
              end_time_ms: currentSentence.endTime
            });
          }

          // Reset for next sentence (only if not the last segment)
          if (!isLastSegment) {
            currentSentence = {
              words: [],
              startTime: null,
              endTime: null
            };
          }
        }
      }

      console.log(`[generateSentences] Generated ${sentences.length} sentences from ${segments.length} segments`);
      return sentences;
    },

    async optimizeSubtitles(options = {}) {
      if (!this.generatedSubtitles || this.generatedSubtitles.length === 0) {
        throw new Error('No generated subtitles available for optimization');
      }

      this.isLoading = true;
      this.optimizeSubtitlesProgress = '开始优化字幕...';
      this.progressUpdates.push('⚡ 开始优化字幕片段...');

      try {
        // TODO: Implement subtitle optimization API call
        // For now, simulate optimization by copying generated subtitles
        await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate processing

        this.optimizedSubtitles = [...this.generatedSubtitles];
        this.optimizeSubtitlesProgress = '字幕优化完成！';
        this.progressUpdates.push(`✅ 成功优化 ${this.optimizedSubtitles.length} 个字幕片段`);

        // Set optimized subtitles as editable segments
        this.editableSegments = this.optimizedSubtitles.map(segment => ({
          ...segment,
          id: segment.id || `optimized-${Date.now()}-${Math.random()}`
        }));
      } catch (error) {
        console.error('Error optimizing subtitles:', error);
        this.optimizeSubtitlesProgress = '字幕优化失败';
        this.progressUpdates.push(`❌ 错误：${error.message}`);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    setEditableSegments(segments) {
      this.editableSegments = segments;
    },

    // 初始化可编辑字幕片段
    initializeEditableSegments() {
      if (this.generatedSubtitles && this.generatedSubtitles.length > 0) {
        // 使用生成的字幕作为可编辑字幕
        this.editableSegments = this.generatedSubtitles.map(segment => ({
          ...segment,
          id: segment.id || `editable-${Date.now()}-${Math.random()}`
        }));
        console.log(`[initializeEditableSegments] Initialized ${this.editableSegments.length} editable segments from generated subtitles`);
      } else if (this.audioToTextResult && this.audioToTextResult.segments) {
        // 如果没有生成的字幕，使用转录结果
        this.editableSegments = this.audioToTextResult.segments.map((segment, index) => ({
          id: `editable-${index}`,
          text: segment.text,
          startTimeMs: segment.startTimeMs || segment.start_time_ms,
          endTimeMs: segment.endTimeMs || segment.end_time_ms,
          start_time_ms: segment.start_time_ms || segment.startTimeMs,
          end_time_ms: segment.end_time_ms || segment.endTimeMs
        }));
        console.log(`[initializeEditableSegments] Initialized ${this.editableSegments.length} editable segments from transcription`);
      } else {
        console.warn('[initializeEditableSegments] No subtitles or transcription available for initialization');
        this.editableSegments = [];
      }
    },

    setTranslationSettings(settings) {
      this.translationSettings = { ...this.translationSettings, ...settings };
    },

    async translateSubtitles(forceRetranslate = false) {
      if (!this.editableSegments || this.editableSegments.length === 0) {
        throw new Error('No editable segments available for translation');
      }

      if (this.translatedSubtitles && !forceRetranslate) {
        return; // Already translated
      }

      this.isLoading = true;
      this.translationProgress = '开始翻译...';
      this.translationProgressPercent = 0;
      this.progressUpdates.push('🌐 开始翻译字幕到目标语言...');

      try {
        const totalSegments = this.editableSegments.length;
        const translatedSegments = [];

        // Try to use backend translation API first
        try {
          this.translationProgress = '调用后端翻译服务...';

          // Convert segments to SRT format for backend translation
          const srtContent = this._segmentsToSrt(this.editableSegments);

          console.log('[TranslateSubtitles] SRT content length:', srtContent ? srtContent.length : 0);
          console.log('[TranslateSubtitles] SRT content preview:', srtContent ? srtContent.substring(0, 200) : 'null/empty');

          if (!srtContent || srtContent.trim().length === 0) {
            throw new Error('无法生成SRT内容用于翻译，请检查字幕片段数据');
          }

          const result = await window.electronAPI.invoke('subtitler-full-workflow', {
            req_text_content: srtContent,
            target_language: this.translationSettings.targetLanguage,
            workflow_type: 'text_to_translated_srt',
            request_word_timestamps: false
          });

          console.log('[TranslateSubtitles] Backend result:', result);
          console.log('[TranslateSubtitles] Result keys:', result ? Object.keys(result) : 'null');

          if (result && result.translate_subtitles_response && result.translate_subtitles_response.translated_subtitle_content) {
            // Parse translated SRT back to segments
            const translatedSrt = result.translate_subtitles_response.translated_subtitle_content;
            const translatedSegments = this._parseSrtToSegments(translatedSrt, this.editableSegments);

            for (let i = 0; i < translatedSegments.length; i++) {
              this.translationProgressPercent = Math.round(((i + 1) / translatedSegments.length) * 100);
              this.translationProgress = `正在处理翻译片段 ${i + 1}/${translatedSegments.length}...`;
            }

            this.translatedSubtitles = translatedSegments;
            this.progressUpdates.push('🔄 使用后端翻译服务');

            // 更新editableSegments以包含翻译数据
            this._updateEditableSegmentsWithTranslation(translatedSegments);

            this.translationProgress = '翻译完成！';
            this.translationProgressPercent = 100;
            this.progressUpdates.push(`✅ 成功翻译 ${translatedSegments.length} 个片段到${this.getLanguageDisplayName(this.translationSettings.targetLanguage)}`);
            return; // Success, exit early
          } else {
            throw new Error('Backend translation service returned invalid response');
          }
        } catch (backendError) {
          console.warn('Backend translation failed, using fallback:', backendError);
          this.translationProgress = '后端失败，使用备用翻译...';
          this.progressUpdates.push('🔄 后端翻译失败，使用本地备用方法');

          // Fallback to local mock translation
          for (let i = 0; i < totalSegments; i++) {
            const segment = this.editableSegments[i];

            // Simulate translation progress
            this.translationProgressPercent = Math.round((i / totalSegments) * 100);
            this.translationProgress = `正在翻译第 ${i + 1} 个片段，共 ${totalSegments} 个...`;

            await new Promise(resolve => setTimeout(resolve, 50)); // Faster simulation

            // Mock translation based on target language
            let translatedText = segment.text;
            switch (this.translationSettings.targetLanguage) {
              case 'zh':
                translatedText = this._mockTranslateToChineseSimplified(segment.text);
                break;
              case 'zh-TW':
                translatedText = this._mockTranslateToChineseTraditional(segment.text);
                break;
              case 'ja':
                translatedText = this._mockTranslateToJapanese(segment.text);
                break;
              case 'ko':
                translatedText = this._mockTranslateToKorean(segment.text);
                break;
              case 'es':
                translatedText = this._mockTranslateToSpanish(segment.text);
                break;
              case 'fr':
                translatedText = this._mockTranslateToFrench(segment.text);
                break;
              default:
                translatedText = `[${this.translationSettings.targetLanguage}] ${segment.text}`;
            }

            translatedSegments.push({
              ...segment,
              originalText: segment.text,
              translatedText: translatedText
            });
          }
        }

        this.translatedSubtitles = translatedSegments;

        // 更新editableSegments以包含翻译数据
        this._updateEditableSegmentsWithTranslation(translatedSegments);

        this.translationProgress = '翻译完成！';
        this.translationProgressPercent = 100;
        this.progressUpdates.push(`✅ 成功翻译 ${totalSegments} 个片段到${this.getLanguageDisplayName(this.translationSettings.targetLanguage)}`);
      } catch (error) {
        console.error('Error translating subtitles:', error);
        this.translationProgress = '翻译失败';
        this.progressUpdates.push(`❌ 错误：${error.message}`);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 获取布局后缀用于文件命名
    _getLayoutSuffix(layout) {
      const layoutSuffixMap = {
        '原文在上': '原文在上',
        '译文在上': '译文在上',
        '仅原文': '仅原文',
        '仅译文': '仅译文',
        'original_top': '原文在上',
        'translation_top': '译文在上',
        'original_only': '仅原文',
        'translation_only': '仅译文'
      };
      return layoutSuffixMap[layout] || layout;
    },

    // 更新editableSegments以包含翻译数据
    _updateEditableSegmentsWithTranslation(translatedSegments) {
      if (!this.editableSegments || !translatedSegments) {
        console.warn('Cannot update editable segments: missing data');
        return;
      }

      console.log('Updating editable segments with translation data...');
      console.log('Original editableSegments count:', this.editableSegments.length);
      console.log('Translated segments count:', translatedSegments.length);

      // 更新editableSegments，为每个segment添加翻译数据
      this.editableSegments = this.editableSegments.map((segment, index) => {
        // 查找对应的翻译片段
        let translatedSegment = null;

        // 首先尝试通过时间戳匹配
        translatedSegment = translatedSegments.find(ts =>
          ts.startTimeMs === segment.startTimeMs && ts.endTimeMs === segment.endTimeMs
        );

        // 如果时间戳匹配失败，尝试通过索引匹配
        if (!translatedSegment && translatedSegments[index]) {
          translatedSegment = translatedSegments[index];
        }

        // 如果找到了翻译片段，添加翻译数据
        if (translatedSegment) {
          const updatedSegment = {
            ...segment,
            translatedText: translatedSegment.translatedText || translatedSegment.text,
            translation: translatedSegment.translatedText || translatedSegment.text,
            originalText: segment.text
          };

          console.log(`Updated segment ${index}:`, {
            original: segment.text,
            translated: updatedSegment.translatedText
          });

          return updatedSegment;
        } else {
          console.warn(`No translation found for segment ${index}:`, segment.text);
          return segment;
        }
      });

      console.log('Updated editableSegments with translation data');
      this.progressUpdates.push('🔄 已将翻译数据合并到可编辑字幕中');
    },

    // Mock translation methods for fallback
    _mockTranslateToChineseSimplified(text) {
      // Improved mock translation with better phrase handling
      const phraseTranslations = {
        'embeddings based retrieval': '基于嵌入的检索',
        'very active area': '非常活跃的领域',
        'area of research': '研究领域',
        'looking forward to': '期待',
        'should be aware of': '应该了解',
        'for example': '例如',
        'fine tune': '微调',
        'embedding model': '嵌入模型',
        'there\'s a lot of': '有很多',
        'other techniques': '其他技术',
        'you can': '你可以',
        'directly using': '直接使用'
      };

      const wordTranslations = {
        'embeddings': '嵌入',
        'based': '基于',
        'retrieval': '检索',
        'is': '是',
        'still': '仍然',
        'a': '一个',
        'very': '非常',
        'active': '活跃的',
        'area': '领域',
        'of': '的',
        'research': '研究',
        'and': '和',
        'there\'s': '有',
        'lot': '很多',
        'other': '其他',
        'techniques': '技术',
        'that': '那些',
        'you': '你',
        'should': '应该',
        'be': '是',
        'aware': '了解',
        'for': '为了',
        'example': '例如',
        'can': '可以',
        'fine': '精细',
        'tune': '调整',
        'the': '这个',
        'embedding': '嵌入',
        'model': '模型',
        'directly': '直接',
        'using': '使用'
      };

      // First try phrase translations
      let result = text.toLowerCase();
      Object.keys(phraseTranslations).forEach(phrase => {
        const regex = new RegExp(phrase.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        result = result.replace(regex, phraseTranslations[phrase]);
      });

      // Then translate remaining words
      return result.split(' ').map(word => {
        const cleanWord = word.toLowerCase().replace(/[.,!?]/g, '');
        const punctuation = word.match(/[.,!?]/g) || [];
        const translated = wordTranslations[cleanWord] || word;
        return translated + punctuation.join('');
      }).join(' ');
    },

    _mockTranslateToChineseTraditional(text) {
      // Convert simplified to traditional (basic mapping)
      const simplified = this._mockTranslateToChineseSimplified(text);
      const traditionalMap = {
        '嵌入': '嵌入',
        '基于': '基於',
        '检索': '檢索',
        '领域': '領域',
        '构建': '構建'
      };

      let traditional = simplified;
      Object.keys(traditionalMap).forEach(key => {
        traditional = traditional.replace(new RegExp(key, 'g'), traditionalMap[key]);
      });

      return traditional;
    },

    // Helper methods for SRT conversion
    _segmentsToSrt(segments) {
      if (!segments || segments.length === 0) {
        console.warn('[_segmentsToSrt] No segments provided');
        return '';
      }

      console.log('[_segmentsToSrt] Converting', segments.length, 'segments to SRT');

      return segments.map((segment, index) => {
        const startTimeMs = segment.startTimeMs || segment.start_time_ms || 0;
        const endTimeMs = segment.endTimeMs || segment.end_time_ms || 1000;

        const startTime = this._msToSrtTime(startTimeMs);
        const endTime = this._msToSrtTime(endTimeMs);
        const text = segment.text || '';

        if (!text.trim()) {
          console.warn('[_segmentsToSrt] Empty text for segment', index);
        }

        return `${index + 1}\n${startTime} --> ${endTime}\n${text}\n`;
      }).join('\n');
    },

    _msToSrtTime(ms) {
      const totalSeconds = Math.floor(ms / 1000);
      const hours = Math.floor(totalSeconds / 3600);
      const minutes = Math.floor((totalSeconds % 3600) / 60);
      const seconds = totalSeconds % 60;
      const milliseconds = ms % 1000;

      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
    },

    _parseSrtToSegments(srtContent, originalSegments) {
      const lines = srtContent.trim().split('\n');
      const segments = [];
      let currentSegment = null;

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();

        if (!line) {
          if (currentSegment) {
            segments.push(currentSegment);
            currentSegment = null;
          }
          continue;
        }

        // Check if line is a number (subtitle index)
        if (/^\d+$/.test(line)) {
          currentSegment = { index: parseInt(line) };
        }
        // Check if line is a timestamp
        else if (line.includes('-->')) {
          const [startStr, endStr] = line.split('-->').map(s => s.trim());
          const startMs = this._srtTimeToMs(startStr);
          const endMs = this._srtTimeToMs(endStr);

          if (currentSegment) {
            currentSegment.startTimeMs = startMs;
            currentSegment.endTimeMs = endMs;
            currentSegment.start_time_ms = startMs;
            currentSegment.end_time_ms = endMs;
          }
        }
        // Text content
        else if (currentSegment && !currentSegment.text) {
          currentSegment.text = line;
          currentSegment.translatedText = line;

          // Find corresponding original segment
          const originalIndex = currentSegment.index - 1;
          if (originalSegments && originalSegments[originalIndex]) {
            currentSegment.originalText = originalSegments[originalIndex].text;
            currentSegment.id = originalSegments[originalIndex].id || `translated-${currentSegment.index}`;
          } else {
            currentSegment.originalText = line; // Fallback
            currentSegment.id = `translated-${currentSegment.index}`;
          }
        }
      }

      // Add last segment if exists
      if (currentSegment) {
        segments.push(currentSegment);
      }

      return segments;
    },

    _srtTimeToMs(timeStr) {
      const [time, ms] = timeStr.split(',');
      const [hours, minutes, seconds] = time.split(':').map(Number);

      return (hours * 3600 + minutes * 60 + seconds) * 1000 + parseInt(ms || 0);
    },

    // 将英文阶段名称转换为中文
    getStageNameInChinese(stageName) {
      const stageMap = {
        'audio_extraction': '音频提取',
        'audio_processing': '音频处理',
        'transcription': '语音识别',
        'text_processing': '文本处理',
        'subtitle_generation': '字幕生成',
        'subtitle_optimization': '字幕优化',
        'translation': '翻译处理',
        'export': '导出处理',
        'file_processing': '文件处理',
        'initialization': '初始化',
        'completion': '完成处理',
        'VideoToAudio': '视频转音频',
        'AudioToText': '音频转文字',
        'GenerateSubtitles': '生成字幕',
        'OptimizeSubtitles': '优化字幕',
        'Translation': '翻译处理',
        'Export': '导出处理'
      };
      return stageMap[stageName] || stageName;
    },

    // 获取语言的显示名称
    getLanguageDisplayName(languageCode) {
      const languageMap = {
        'zh': '中文(简体)',
        'zh-CN': '中文(简体)',
        'zh-TW': '中文(繁体)',
        'en': '英语',
        'ja': '日语',
        'ko': '韩语',
        'fr': '法语',
        'de': '德语',
        'es': '西班牙语',
        'it': '意大利语',
        'pt': '葡萄牙语',
        'ru': '俄语',
        'ar': '阿拉伯语',
        'th': '泰语',
        'vi': '越南语'
      };
      return languageMap[languageCode] || languageCode;
    },

    _mockTranslateToJapanese(text) {
      const translations = {
        'embeddings': '埋め込み',
        'based': 'ベース',
        'retrieval': '検索',
        'is': 'です',
        'still': 'まだ',
        'a': '一つの',
        'very': 'とても',
        'active': 'アクティブな',
        'area': '分野',
        'looking': '楽しみに',
        'forward': '前向きに',
        'to': 'に',
        'seeing': '見る',
        'what': '何を',
        'you': 'あなた',
        'will': 'でしょう',
        'build': '構築',
        'really': '本当に',
        'I': '私',
        'am': 'です'
      };

      return text.split(' ').map(word => {
        const cleanWord = word.toLowerCase().replace(/[.,!?]/g, '');
        return translations[cleanWord] || word;
      }).join(' ');
    },

    _mockTranslateToKorean(text) {
      const translations = {
        'embeddings': '임베딩',
        'based': '기반',
        'retrieval': '검색',
        'is': '입니다',
        'still': '여전히',
        'a': '하나의',
        'very': '매우',
        'active': '활발한',
        'area': '영역',
        'looking': '기대하고',
        'forward': '앞으로',
        'to': '에',
        'seeing': '보는',
        'what': '무엇을',
        'you': '당신',
        'will': '할',
        'build': '구축',
        'really': '정말로',
        'I': '나는'
      };

      return text.split(' ').map(word => {
        const cleanWord = word.toLowerCase().replace(/[.,!?]/g, '');
        return translations[cleanWord] || word;
      }).join(' ');
    },

    _mockTranslateToSpanish(text) {
      const translations = {
        'embeddings': 'incrustaciones',
        'based': 'basado',
        'retrieval': 'recuperación',
        'is': 'es',
        'still': 'todavía',
        'a': 'un',
        'very': 'muy',
        'active': 'activo',
        'area': 'área',
        'looking': 'esperando',
        'forward': 'adelante',
        'to': 'a',
        'seeing': 'ver',
        'what': 'qué',
        'you': 'tú',
        'will': 'vas a',
        'build': 'construir',
        'really': 'realmente',
        'I': 'yo'
      };

      return text.split(' ').map(word => {
        const cleanWord = word.toLowerCase().replace(/[.,!?]/g, '');
        return translations[cleanWord] || word;
      }).join(' ');
    },

    _mockTranslateToFrench(text) {
      const translations = {
        'embeddings': 'intégrations',
        'based': 'basé',
        'retrieval': 'récupération',
        'is': 'est',
        'still': 'encore',
        'a': 'un',
        'very': 'très',
        'active': 'actif',
        'area': 'domaine',
        'looking': 'attendant',
        'forward': 'avec impatience',
        'to': 'à',
        'seeing': 'voir',
        'what': 'ce que',
        'you': 'vous',
        'will': 'allez',
        'build': 'construire',
        'really': 'vraiment',
        'I': 'je'
      };

      return text.split(' ').map(word => {
        const cleanWord = word.toLowerCase().replace(/[.,!?]/g, '');
        return translations[cleanWord] || word;
      }).join(' ');
    },

    // Multi-select export method
    async exportMultipleSubtitles(options = {}) {
      const { filename, contentSources, layouts, format, autoSave } = options;

      this.isLoading = true;
      this.exportResults = [];
      this.progressUpdates = [];

      try {
        let totalExports = contentSources.length * layouts.length;
        let completedExports = 0;

        this.progressUpdates.push(`开始批量导出：${totalExports} 个文件`);

        for (const contentSource of contentSources) {
          for (const layout of layouts) {
            try {
              // Set current export settings
              this.exportContentSource = contentSource;
              this.exportLayout = layout;
              this.exportFormat = format;
              this.exportAutoSaveToDefault = autoSave;

              // Generate filename with suffix
              const suffix = this._generateExportSuffix(contentSource, layout);
              const currentFilename = filename ? `${filename}_${suffix}` : `subtitles_${suffix}`;
              this.exportFilename = currentFilename;

              this.progressUpdates.push(`导出中: ${currentFilename} (${completedExports + 1}/${totalExports})`);

              // Call single export
              await this.exportSubtitles({ filename: currentFilename });

              if (this.lastExportPath) {
                this.exportResults.push({
                  contentSource,
                  layout,
                  format,
                  filename: currentFilename,
                  path: this.lastExportPath
                });
                completedExports++;
                this.progressUpdates.push(`✅ 完成: ${currentFilename}`);
              }

            } catch (error) {
              console.error(`Export failed for ${contentSource} + ${layout}:`, error);
              this.progressUpdates.push(`❌ 失败: ${contentSource} + ${layout} - ${error.message}`);
            }
          }
        }

        this.progressUpdates.push(`🎉 批量导出完成！成功导出 ${completedExports}/${totalExports} 个文件`);

      } catch (error) {
        console.error('Multi-export error:', error);
        this.progressUpdates.push(`❌ 批量导出失败: ${error.message}`);
      } finally {
        this.isLoading = false;
      }
    },

    // Generate export filename suffix
    _generateExportSuffix(contentSource, layout) {
      const sourceMap = {
        'editable_segments': '编辑版',
        'transcript_text': '原始文本',
        'transcript_segments': '原始片段',
        'translation_result': '翻译版'
      };

      const layoutMap = {
        'original_top': '原文在上',
        'translation_top': '译文在上',
        'original_only': '仅原文',
        'translation_only': '仅译文'
      };

      const sourceLabel = sourceMap[contentSource] || contentSource;
      const layoutLabel = layoutMap[layout] || layout;

      return `${sourceLabel}_${layoutLabel}`;
    },
  },
});
import { defineStore } from 'pinia';

export const useUiStore = defineStore('ui', {
  state: () => ({
    activeView: 'SubtitlerView', // 默认视图
    availableViews: [
      { name: 'SubtitlerView', displayName: '字幕工具', icon: 'icon-subtitles' }, // 示例图标类名
      { name: 'GrpcTestView', displayName: 'gRPC 测试', icon: 'icon-grpc' },    // 示例图标类名
      { name: 'AISettingsView', displayName: 'AI 服务配置', icon: 'icon-ai-settings' } // 新增 AI 服务配置视图
    ],
    isSidebarCollapsed: false,
  }),
  getters: {
    // getCurrentViewComponent: (state) => state.activeView, // App.vue 将直接使用 activeView 名称来动态导入组件
    getActiveViewName: (state) => state.activeView,
    getAvailableViews: (state) => state.availableViews,
    getIsSidebarCollapsed: (state) => state.isSidebarCollapsed,
  },
  actions: {
    setActiveView(viewName) {
      if (this.availableViews.some(view => view.name === viewName)) {
        this.activeView = viewName;
      } else {
        console.warn(`View "${viewName}" is not available.`);
      }
    },
    toggleSidebar() {
      this.isSidebarCollapsed = !this.isSidebarCollapsed;
    },
  },
});
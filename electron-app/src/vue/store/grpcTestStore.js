import { defineStore } from 'pinia';

export const useGrpcTestStore = defineStore('grpcTest', {
  state: () => ({
    selectedService: null, // 例如: 'Greeter', 'Subtitler'
    selectedMethod: null,
    requestPayload: '{}', // 默认为空JSON对象
    responsePayload: null,
    isLoading: false,
    error: null,
    availableServices: [], // 初始化为空数组，将通过 action 动态获取
    
    // Backend status management
    backendServices: {
      python: {
        status: 'Unknown',
        bgColor: 'bg-gray-500',
        clientStatus: { ready: false, message: 'Initializing...' }
      },
      go: {
        status: 'Unknown', 
        bgColor: 'bg-gray-500',
        clientStatus: { ready: false, message: 'Initializing...' }
      },
      java: {
        status: 'Unknown',
        bgColor: 'bg-gray-500', 
        clientStatus: { ready: false, message: 'Initializing...' },
        detailedStatus: 'unknown'
      }
    },
    
    // Log management
    logs: [],
    maxLogs: 1000 // Limit to prevent memory issues
  }),
  getters: {
    isRequestReady: (state) => {
      return !!state.selectedService && !!state.selectedMethod && state.requestPayload.trim() !== '';
    },
    currentMethods: (state) => {
      if (!state.selectedService) {
        return [];
      }
      const service = state.availableServices.find(s => s.name === state.selectedService);
      return service ? service.methods : [];
    },
    
    // Backend status getters
    pythonStatus: (state) => state.backendServices.python,
    goStatus: (state) => state.backendServices.go,
    javaStatus: (state) => state.backendServices.java,
    
    // Recent logs getter
    recentLogs: (state) => state.logs.slice(-100) // Show last 100 logs
  },
  actions: {
    async fetchAvailableServices() {
      this.isLoading = true;
      this.error = null;
      try {
        console.log('[GrpcTestStore] Fetching available gRPC services...');
        const services = await window.electronAPI.invoke('grpc:get-available-services');
        this.availableServices = services;
        console.log('[GrpcTestStore] Available gRPC services loaded:', services);
        // 如果之前有选中的服务，并且新的服务列表中不再存在，则清空选择
        if (this.selectedService && !services.find(s => s.name === this.selectedService)) {
          this.selectedService = null;
          this.selectedMethod = null;
        }
      } catch (e) {
        this.error = `Failed to fetch available services: ${e.message}`;
        console.error('[GrpcTestStore] Error fetching available services:', e);
        this.availableServices = []; // 出错时设置为空数组
      } finally {
        this.isLoading = false;
      }
    },
    setSelectedService(serviceName) {
      this.selectedService = serviceName;
      this.selectedMethod = null; // 服务更改时重置方法
      this.responsePayload = null;
      this.error = null;
    },
    setSelectedMethod(methodName) {
      this.selectedMethod = methodName;
      this.responsePayload = null;
      this.error = null;
    },
    setRequestPayload(payloadString) {
      this.requestPayload = payloadString;
    },
    async sendGrpcRequest() {
      if (!this.isRequestReady) {
        this.error = 'Service, method, and request payload must be provided.';
        return;
      }

      this.isLoading = true;
      this.responsePayload = null;
      this.error = null;

      try {
        let parsedPayload;
        try {
          parsedPayload = JSON.parse(this.requestPayload);
        } catch (e) {
          this.error = `Invalid JSON payload: ${e.message}`;
          this.isLoading = false;
          return;
        }

        // Use a dedicated IPC channel for gRPC test requests
        const channel = 'grpc-test:invoke-method';
        const requestData = {
          service: this.selectedService,
          method: this.selectedMethod,
          payload: parsedPayload
        };
        console.log(`Sending gRPC request to channel: ${channel} with payload:`, requestData);

        const result = await window.electronAPI.invoke(channel, requestData);

        if (result.error) {
          this.error = `gRPC Error: ${result.error.message || JSON.stringify(result.error)}`;
          console.error('gRPC call resulted in error:', result.error);
        } else {
          this.responsePayload = JSON.stringify(result.data, null, 2);
          console.log('gRPC call successful, response:', result.data);
        }
      } catch (e) {
        this.error = `IPC Error: ${e.message}`;
        console.error('Error during IPC call for gRPC request:', e);
      } finally {
        this.isLoading = false;
      }
    },
    resetForm() {
      // this.selectedService = null; // 通常不重置服务和方法选择，除非有特定需求
      // this.selectedMethod = null;
      this.requestPayload = '{}';
      this.responsePayload = null;
      this.isLoading = false;
      this.error = null;
    },
    applyRequestTemplate(template) {
      if (!template) return;

      // 查找服务以确保它是已知的
      // 确保 template.serviceName 存在并且在 availableServices 中
      if (template.serviceName && this.availableServices.some(s => s.name === template.serviceName)) {
        this.selectedService = template.serviceName;
      } else {
         // 如果模板没有直接指定 serviceName，或者指定的服务名无效，尝试通过 methodName 查找
         // 这部分逻辑可能需要根据实际模板数据结构调整
        const serviceForMethod = this.availableServices.find(s => s.methods.includes(template.methodName) && s.name === this.selectedService); // 优先在当前选中服务中找
        if (serviceForMethod) {
            this.selectedService = serviceForMethod.name;
        } else {
            // 如果当前选中服务没有此方法，则在所有服务中查找第一个包含此方法的服务
            const anyServiceWithMethod = this.availableServices.find(s => s.methods.includes(template.methodName));
            if (anyServiceWithMethod) {
                this.selectedService = anyServiceWithMethod.name;
            } else {
                // 如果找不到服务，则不改变当前选中的服务，或者可以清空
                // this.selectedService = null;
            }
        }
      }


      if (template.methodName && this.currentMethods.includes(template.methodName)) {
        this.selectedMethod = template.methodName;
      } else if (this.currentMethods.length > 0 && !template.methodName) {
         this.selectedMethod = null;
      } else {
        this.selectedMethod = null;
      }
      
      this.requestPayload = template.requestPayload || '{}';
      this.responsePayload = null; // 清除旧的响应
      this.error = null; // 清除错误
    },
    
    // Backend status management actions
    updateServiceStatus(serviceKey, statusText, bgColor) {
      if (this.backendServices[serviceKey]) {
        this.backendServices[serviceKey].status = statusText;
        this.backendServices[serviceKey].bgColor = bgColor;
      }
    },
    
    updateClientStatus(serviceKey, clientStatus) {
      if (this.backendServices[serviceKey]) {
        this.backendServices[serviceKey].clientStatus = clientStatus;
      }
    },
    
    updateJavaDetailedStatus(detailedStatus) {
      this.backendServices.java.detailedStatus = detailedStatus;
    },
    
    // Log management actions
    addLog(logEntry) {
      this.logs.push({
        id: Date.now() + Math.random(),
        timestamp: new Date().toLocaleTimeString(),
        message: logEntry
      });
      
      // Keep only recent logs to prevent memory issues
      if (this.logs.length > this.maxLogs) {
        this.logs = this.logs.slice(-this.maxLogs);
      }
    },
    
    clearLogs() {
      this.logs = [];
    },

    // 主动刷新后端状态
    async refreshBackendStatus() {
      console.log('[GrpcTestStore] Refreshing backend status...');

      // 测试每个服务的连接状态
      const services = ['python', 'go', 'java'];
      let allServicesReady = true;

      for (const service of services) {
        try {
          // 尝试调用一个简单的gRPC方法来测试连接
          const testPayload = { name: `${service}TestUser` };
          const result = await window.electronAPI.invoke('grpc-test:invoke-method', {
            service: 'Greeter',
            method: 'SayHello',
            payload: testPayload
          });

          if (result.data) {
            // 连接成功，更新状态
            this.updateServiceStatus(service, 'Running', 'bg-green-500');
            this.updateClientStatus(service, { ready: true, message: 'Connected' });
            console.log(`[GrpcTestStore] ${service} service is running`);
          } else {
            // 连接失败
            allServicesReady = false;
            this.updateServiceStatus(service, 'Connection Failed', 'bg-red-500');
            this.updateClientStatus(service, { ready: false, message: 'Connection Failed' });
            console.log(`[GrpcTestStore] ${service} service connection failed:`, result.error);
          }
        } catch (error) {
          // 异常情况
          allServicesReady = false;
          this.updateServiceStatus(service, 'Error', 'bg-red-500');
          this.updateClientStatus(service, { ready: false, message: `Error: ${error.message}` });
          console.error(`[GrpcTestStore] Error testing ${service} service:`, error);
        }
      }

      // 返回是否所有服务都正常
      return allServicesReady;
    },
    
    // Initialize backend status listeners
    initializeBackendStatusListeners() {
      console.log('[GrpcTestStore] Initializing backend status listeners...');

      // Service status updates
      if (window.electronAPI?.onServiceStatusUpdate) {
        window.electronAPI.onServiceStatusUpdate((data) => {
          console.log('[GrpcTestStore] Received service status update:', data);
          this.updateServiceStatus(data.service, data.status, data.bgColor);
        });
      }

      // 主动查询当前状态（解决时机问题）
      this.refreshBackendStatus();
      
      // Client status updates
      if (window.electronAPI?.onPythonClientStatus) {
        window.electronAPI.onPythonClientStatus((status) => {
          this.updateClientStatus('python', status);
        });
      }
      
      if (window.electronAPI?.onGoClientStatus) {
        window.electronAPI.onGoClientStatus((status) => {
          this.updateClientStatus('go', status);
        });
      }
      
      if (window.electronAPI?.onJavaClientStatus) {
        window.electronAPI.onJavaClientStatus((status) => {
          this.updateClientStatus('java', status);
        });
      }
      
      // Java detailed status
      if (window.electronAPI?.onJavaStatus) {
        window.electronAPI.onJavaStatus((status) => {
          this.updateJavaDetailedStatus(status);
        });
      }
      
      // Log listeners
      if (window.electronAPI?.onMainProcessLog) {
        window.electronAPI.onMainProcessLog((message) => {
          this.addLog(`[Main]: ${message}`);
        });
      }
      
      if (window.electronAPI?.onUnifiedLog) {
        window.electronAPI.onUnifiedLog((message) => {
          this.addLog(message);
        });
      }
      
      if (window.electronAPI?.onJavaLog) {
        window.electronAPI.onJavaLog((message) => {
          this.addLog(`[Java]: ${message}`);
        });
      }
      
      console.log('[GrpcTestStore] Backend status listeners initialized');
    }
  }
});
<template>
  <div id="app-container" class="flex h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <SidebarNav />
    <main class="flex-1 p-6 overflow-y-auto">
      <keep-alive>
        <component :is="activeViewComponent" />
      </keep-alive>
    </main>
  </div>
</template>

<script setup>
import { computed, shallowRef, defineAsyncComponent, onMounted, onUnmounted } from 'vue';
import { useUiStore } from './store/uiStore';
import SidebarNav from './components/layout/SidebarNav.vue';

// 异步加载视图组件以优化初始加载时间
const SubtitlerView = defineAsyncComponent(() => import('./views/SubtitlerView.vue'));
const GrpcTestView = defineAsyncComponent(() => import('./views/GrpcTestView.vue'));
const AISettingsView = defineAsyncComponent(() => import('./views/AISettingsView.vue'));

const uiStore = useUiStore();

const activeViewName = computed(() => uiStore.getActiveViewName);

const viewComponents = shallowRef({
  SubtitlerView,
  GrpcTestView,
  AISettingsView,
});

const activeViewComponent = computed(() => {
  return viewComponents.value[activeViewName.value] || null;
});

// 监听来自主进程的导航消息
const handleNavigateToView = (viewName) => {
  console.log('[App] 收到导航请求:', viewName);
  uiStore.setActiveView(viewName);
};

let removeNavigationListener = null;

onMounted(() => {
  // 监听主进程的导航消息
  if (window.electronAPI && window.electronAPI.on) {
    removeNavigationListener = window.electronAPI.on('navigate-to-view', handleNavigateToView);
  }
});

onUnmounted(() => {
  // 清理监听器
  if (removeNavigationListener) {
    removeNavigationListener();
  }
});

</script>

<style scoped>
/* 组件样式已移至Tailwind CSS类中 */
</style>
<template>
  <div class="space-y-2 p-3 border rounded-md shadow-sm bg-white">
    <h3 class="text-md font-medium text-gray-700">Response / Error</h3>
    <div v-if="isLoading" class="text-sm text-blue-600">
      Loading...
    </div>
    <div v-if="error" class="p-2 border border-red-300 bg-red-50 rounded-md">
      <h4 class="text-sm font-semibold text-red-700">Error:</h4>
      <pre class="text-xs text-red-600 whitespace-pre-wrap break-all">{{ error }}</pre>
    </div>
    <div v-if="responsePayload && !error" class="p-2 border border-green-300 bg-green-50 rounded-md">
      <h4 class="text-sm font-semibold text-green-700">Response:</h4>
      <pre class="text-xs text-green-600 whitespace-pre-wrap break-all">{{ responsePayload }}</pre>
    </div>
    <div v-if="!isLoading && !error && !responsePayload" class="text-sm text-gray-500">
      No response yet. Send a request to see the output.
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useGrpcTestStore } from '../../store/grpcTestStore';

const store = useGrpcTestStore();

const isLoading = computed(() => store.isLoading);
const error = computed(() => store.error);
const responsePayload = computed(() => store.responsePayload);
</script>

<style scoped>
pre {
  max-height: 300px; /* Or any other max-height you prefer */
  overflow-y: auto;
}
</style>
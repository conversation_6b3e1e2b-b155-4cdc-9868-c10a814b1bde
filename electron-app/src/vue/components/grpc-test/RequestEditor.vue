<template>
  <div class="space-y-2 p-3 border rounded-md shadow-sm bg-white">
    <h3 class="text-md font-medium text-gray-700">Request Payload (JSON)</h3>
    <label for="request-payload" class="sr-only">Request Payload (JSON)</label>
    <textarea
      id="request-payload"
      rows="8"
      class="block w-full border border-gray-300 rounded-md shadow-sm p-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono"
      :value="requestPayload"
      @input="updateRequestPayload($event.target.value)"
      placeholder="Enter JSON payload here..."
    ></textarea>
    <p v-if="payloadError" class="text-xs text-red-600 mt-1">{{ payloadError }}</p>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { useGrpcTestStore } from '../../store/grpcTestStore';

const store = useGrpcTestStore();
const payloadError = ref(null);

const requestPayload = computed(() => store.requestPayload);

const updateRequestPayload = (value) => {
  store.setRequestPayload(value);
  validateJson(value);
};

const validateJson = (value) => {
  try {
    JSON.parse(value);
    payloadError.value = null;
  } catch (e) {
    if (value.trim() !== '') { // Only show error if not empty
        payloadError.value = `Invalid JSON: ${e.message}`;
    } else {
        payloadError.value = null; // Clear error if input is empty
    }
  }
};

// Initial validation and on subsequent changes from store (e.g. reset)
watch(requestPayload, (newValue) => {
  validateJson(newValue);
}, { immediate: true });

</script>

<style scoped>
textarea {
  min-height: 100px;
  resize: vertical;
}
</style>
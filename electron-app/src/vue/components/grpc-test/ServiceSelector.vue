<template>
  <div class="space-y-2 p-3 border rounded-md shadow-sm bg-white">
    <h3 class="text-md font-medium text-gray-700">Service and Method</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <div>
        <label for="service-select" class="block text-sm font-medium text-gray-700 mb-1">Service:</label>
        <select
          id="service-select"
          :value="selectedService"
          @change="handleServiceChange($event.target.value)"
          class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm"
        >
          <option :value="null" disabled>-- Select a Service --</option>
          <option v-for="service in availableServices" :key="service.name" :value="service.name">
            {{ service.name }}
          </option>
        </select>
      </div>
      <div>
        <label for="method-select" class="block text-sm font-medium text-gray-700 mb-1">Method:</label>
        <select
          id="method-select"
          :value="selectedMethod"
          @change="handleMethodChange($event.target.value)"
          :disabled="!selectedService || currentMethods.length === 0"
          class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md shadow-sm disabled:bg-gray-50"
        >
          <option :value="null" disabled>-- Select a Method --</option>
          <option v-for="method in currentMethods" :key="method" :value="method">
            {{ method }}
          </option>
        </select>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useGrpcTestStore } from '../../store/grpcTestStore';

const store = useGrpcTestStore();

const selectedService = computed(() => store.selectedService);
const availableServices = computed(() => store.availableServices);
const selectedMethod = computed(() => store.selectedMethod);
const currentMethods = computed(() => store.currentMethods);

const handleServiceChange = (serviceName) => {
  store.setSelectedService(serviceName);
};

const handleMethodChange = (methodName) => {
  store.setSelectedMethod(methodName);
};
</script>

<style scoped>
/* Add component-specific styles here if needed */
</style>
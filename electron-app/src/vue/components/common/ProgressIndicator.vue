<template>
  <div v-if="isVisible" class="progress-indicator">
    <div class="flex items-center justify-center py-8">
      <div class="text-center">
        <!-- 统一的加载动画 -->
        <div class="relative">
          <svg 
            class="animate-spin h-10 w-10 mx-auto mb-4"
            :class="spinnerColorClass"
            xmlns="http://www.w3.org/2000/svg" 
            fill="none" 
            viewBox="0 0 24 24">
            <circle 
              class="opacity-25" 
              cx="12" 
              cy="12" 
              r="10" 
              stroke="currentColor" 
              stroke-width="4">
            </circle>
            <path 
              class="opacity-75" 
              fill="currentColor" 
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
        </div>
        
        <!-- 标题和消息 -->
        <h3 class="text-lg font-medium text-gray-700 mb-2">{{ title }}</h3>
        <p class="text-sm text-gray-500 mb-4">{{ message }}</p>
        
        <!-- 进度条 (如果有百分比) -->
        <div v-if="showProgressBar" class="w-64 mx-auto">
          <div class="bg-gray-200 rounded-full h-2 mb-2">
            <div 
              class="h-2 rounded-full transition-all duration-300 ease-out"
              :class="progressBarColorClass"
              :style="{ width: progressPercentage + '%' }">
            </div>
          </div>
          <div class="text-xs text-gray-500">
            {{ progressPercentage }}% {{ progressText }}
          </div>
        </div>
        
        <!-- 详细状态信息 -->
        <div v-if="details" class="mt-4 text-xs text-gray-400 max-w-sm mx-auto">
          {{ details }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 是否显示进度指示器
  isVisible: {
    type: Boolean,
    default: false
  },
  // 主标题
  title: {
    type: String,
    default: '处理中...'
  },
  // 详细消息
  message: {
    type: String,
    default: '请稍候'
  },
  // 进度百分比 (0-100)
  percentage: {
    type: Number,
    default: -1
  },
  // 进度文本
  progressText: {
    type: String,
    default: '完成'
  },
  // 详细信息
  details: {
    type: String,
    default: ''
  },
  // 主题色彩 (blue, green, indigo, purple, red, yellow, orange, pink, gray)
  theme: {
    type: String,
    default: 'blue',
    validator: (value) => ['blue', 'green', 'indigo', 'purple', 'red', 'yellow', 'orange', 'pink', 'gray'].includes(value)
  }
});

// 计算属性
const showProgressBar = computed(() => props.percentage >= 0 && props.percentage <= 100);

const progressPercentage = computed(() => {
  if (props.percentage < 0) return 0;
  if (props.percentage > 100) return 100;
  return Math.round(props.percentage);
});

const spinnerColorClass = computed(() => {
  const colorMap = {
    blue: 'text-blue-500',
    green: 'text-green-500',
    indigo: 'text-indigo-500',
    purple: 'text-purple-500',
    red: 'text-red-500',
    yellow: 'text-yellow-500',
    orange: 'text-orange-500',
    pink: 'text-pink-500',
    gray: 'text-gray-500'
  };
  return colorMap[props.theme] || colorMap.blue;
});

const progressBarColorClass = computed(() => {
  const colorMap = {
    blue: 'bg-blue-600',
    green: 'bg-green-600',
    indigo: 'bg-indigo-600',
    purple: 'bg-purple-600',
    red: 'bg-red-600',
    yellow: 'bg-yellow-600',
    orange: 'bg-orange-600',
    pink: 'bg-pink-600',
    gray: 'bg-gray-600'
  };
  return colorMap[props.theme] || colorMap.blue;
});
</script>

<style scoped>
.progress-indicator {
  @apply bg-white border border-gray-200 rounded-lg shadow-sm;
}

/* 确保动画流畅 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>

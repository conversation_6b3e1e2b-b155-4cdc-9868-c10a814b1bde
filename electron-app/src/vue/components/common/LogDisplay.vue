<template>
  <div v-if="shouldShow" class="log-display">
    <!-- 日志头部 -->
    <div class="flex items-center justify-between mb-3">
      <div class="flex items-center">
        <svg class="h-4 w-4 text-gray-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
          </path>
        </svg>
        <h3 class="text-md font-semibold text-gray-700">{{ title }}</h3>
        <span v-if="logs.length > 0" class="ml-2 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
          {{ logs.length }}
        </span>
      </div>
      
      <div class="flex items-center space-x-2">
        <!-- 折叠/展开按钮 -->
        <button 
          v-if="collapsible"
          @click="toggleCollapsed"
          class="text-xs text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100 transition-colors">
          <svg class="h-3 w-3 transform transition-transform" :class="{ 'rotate-180': !isCollapsed }" 
               fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>
        
        <!-- 清除日志按钮 -->
        <button 
          v-if="clearable && logs.length > 0"
          @click="clearLogs" 
          class="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100 transition-colors">
          清除
        </button>
      </div>
    </div>
    
    <!-- 日志内容 -->
    <div 
      v-show="!isCollapsed"
      class="log-content bg-gray-50 border border-gray-200 rounded-lg overflow-hidden">
      
      <!-- 空状态 -->
      <div v-if="logs.length === 0" class="p-4 text-center text-gray-500 text-sm">
        <svg class="h-8 w-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
          </path>
        </svg>
        {{ emptyMessage }}
      </div>
      
      <!-- 日志列表 -->
      <div v-else class="p-4 max-h-40 overflow-y-auto">
        <ul class="space-y-1">
          <li 
            v-for="(log, index) in displayLogs" 
            :key="index" 
            :class="getLogClass(log)"
            class="text-sm flex items-start">
            
            <!-- 日志图标 -->
            <div class="flex-shrink-0 mr-2 mt-0.5">
              <div class="w-1.5 h-1.5 rounded-full" :class="getLogDotClass(log)"></div>
            </div>
            
            <!-- 时间戳 -->
            <span v-if="showTimestamp" class="text-xs text-gray-400 mr-2 font-mono flex-shrink-0">
              {{ formatTime(log.timestamp || Date.now()) }}
            </span>
            
            <!-- 日志内容 -->
            <span class="flex-1">{{ getLogMessage(log) }}</span>
          </li>
        </ul>
        
        <!-- 显示更多按钮 -->
        <div v-if="logs.length > maxDisplayLogs" class="mt-2 text-center">
          <button 
            @click="showAllLogs = !showAllLogs"
            class="text-xs text-blue-600 hover:text-blue-800 underline">
            {{ showAllLogs ? '显示较少' : `显示全部 (${logs.length - maxDisplayLogs} 条更多)` }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  // 日志数组
  logs: {
    type: Array,
    default: () => []
  },
  // 标题
  title: {
    type: String,
    default: '处理日志'
  },
  // 空状态消息
  emptyMessage: {
    type: String,
    default: '暂无日志'
  },
  // 是否可折叠
  collapsible: {
    type: Boolean,
    default: true
  },
  // 是否可清除
  clearable: {
    type: Boolean,
    default: true
  },
  // 是否显示时间戳
  showTimestamp: {
    type: Boolean,
    default: false
  },
  // 最大显示日志数量
  maxDisplayLogs: {
    type: Number,
    default: 10
  },
  // 是否自动显示 (有日志时才显示)
  autoShow: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['clear']);

// 响应式状态
const isCollapsed = ref(false);
const showAllLogs = ref(false);

// 计算属性
const shouldShow = computed(() => {
  return props.autoShow ? props.logs.length > 0 : true;
});

const displayLogs = computed(() => {
  if (showAllLogs.value || props.logs.length <= props.maxDisplayLogs) {
    return props.logs;
  }
  return props.logs.slice(-props.maxDisplayLogs);
});

// 方法
const toggleCollapsed = () => {
  isCollapsed.value = !isCollapsed.value;
};

const clearLogs = () => {
  emit('clear');
};

const getLogMessage = (log) => {
  if (typeof log === 'string') {
    return log;
  }
  return log.message || log.text || String(log);
};

const getLogClass = (log) => {
  const message = getLogMessage(log);

  // 检查emoji图标
  if (message.includes('✅') || message.includes('成功') || message.includes('完成') || message.includes('success')) {
    return 'text-green-600';
  }
  if (message.includes('❌') || message.includes('错误') || message.includes('失败') || message.includes('error') || message.includes('failed')) {
    return 'text-red-600';
  }
  if (message.includes('⚠️') || message.includes('警告') || message.includes('warning')) {
    return 'text-yellow-600';
  }
  if (message.includes('🔄') || message.includes('正在') || message.includes('processing') || message.includes('开始')) {
    return 'text-blue-600';
  }
  if (message.includes('📝') || message.includes('💾') || message.includes('🎤') || message.includes('🌐') || message.includes('🚀')) {
    return 'text-indigo-600';
  }
  if (message.includes('⏹️') || message.includes('取消') || message.includes('cancelled')) {
    return 'text-orange-600';
  }
  return 'text-gray-600';
};

const getLogDotClass = (log) => {
  const message = getLogMessage(log);

  // 检查emoji图标
  if (message.includes('✅') || message.includes('成功') || message.includes('完成') || message.includes('success')) {
    return 'bg-green-500';
  }
  if (message.includes('❌') || message.includes('错误') || message.includes('失败') || message.includes('error') || message.includes('failed')) {
    return 'bg-red-500';
  }
  if (message.includes('⚠️') || message.includes('警告') || message.includes('warning')) {
    return 'bg-yellow-500';
  }
  if (message.includes('🔄') || message.includes('正在') || message.includes('processing') || message.includes('开始')) {
    return 'bg-blue-500';
  }
  if (message.includes('📝') || message.includes('💾') || message.includes('🎤') || message.includes('🌐') || message.includes('🚀')) {
    return 'bg-indigo-500';
  }
  if (message.includes('⏹️') || message.includes('取消') || message.includes('cancelled')) {
    return 'bg-orange-500';
  }
  return 'bg-gray-400';
};

const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  });
};
</script>

<style scoped>
.log-display {
  @apply mt-6;
}

.log-content {
  transition: all 0.2s ease-in-out;
}

/* 滚动条样式 */
.log-content::-webkit-scrollbar {
  width: 4px;
}

.log-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.log-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.log-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>

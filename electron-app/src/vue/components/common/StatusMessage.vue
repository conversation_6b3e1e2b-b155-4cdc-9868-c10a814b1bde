<template>
  <div 
    v-if="message && showMessage"
    :class="[
      'rounded-lg p-4 mb-4 border transition-all duration-300',
      statusClasses,
      { 'animate-pulse': isLoading }
    ]"
  >
    <div class="flex items-start">
      <!-- 状态图标 -->
      <div class="flex-shrink-0">
        <span class="text-lg">{{ statusIcon }}</span>
      </div>
      
      <!-- 消息内容 -->
      <div class="ml-3 flex-1">
        <div class="flex items-center justify-between">
          <h3 v-if="title" class="text-sm font-medium">
            {{ title }}
          </h3>
          <button
            v-if="dismissible"
            @click="dismiss"
            class="ml-auto flex-shrink-0 text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <span class="sr-only">关闭</span>
            <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div class="mt-1 text-sm">
          {{ message }}
        </div>
        
        <!-- 详细信息 -->
        <div v-if="details" class="mt-2 text-xs opacity-75">
          {{ details }}
        </div>
        
        <!-- 进度条 -->
        <div v-if="showProgress && progress !== null" class="mt-3">
          <div class="flex items-center justify-between text-xs mb-1">
            <span>{{ progressText || '进度' }}</span>
            <span>{{ Math.round(progress) }}%</span>
          </div>
          <div class="w-full bg-white bg-opacity-30 rounded-full h-2">
            <div 
              :class="progressBarClass"
              class="h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min(100, Math.max(0, progress))}%` }"
            />
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div v-if="actions && actions.length > 0" class="mt-3 flex space-x-2">
          <button
            v-for="action in actions"
            :key="action.key"
            @click="handleAction(action)"
            :class="[
              'inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2',
              action.primary 
                ? 'text-white bg-white bg-opacity-20 hover:bg-opacity-30 focus:ring-white'
                : 'text-current bg-white bg-opacity-10 hover:bg-opacity-20 focus:ring-current'
            ]"
            :disabled="action.disabled"
          >
            <span v-if="action.icon" class="mr-1">{{ action.icon }}</span>
            {{ action.label }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'warning', 'error', 'info', 'loading'].includes(value)
  },
  title: {
    type: String,
    default: null
  },
  message: {
    type: String,
    required: true
  },
  details: {
    type: String,
    default: null
  },
  dismissible: {
    type: Boolean,
    default: false
  },
  showProgress: {
    type: Boolean,
    default: false
  },
  progress: {
    type: Number,
    default: null
  },
  progressText: {
    type: String,
    default: null
  },
  actions: {
    type: Array,
    default: () => []
  },
  autoHide: {
    type: Number,
    default: 0 // 0 表示不自动隐藏，其他数字表示秒数
  }
});

const emit = defineEmits(['dismiss', 'action']);

const showMessage = ref(true);

// 计算状态样式
const statusClasses = computed(() => {
  const classMap = {
    success: 'bg-green-50 border-green-200 text-green-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
    error: 'bg-red-50 border-red-200 text-red-800',
    info: 'bg-blue-50 border-blue-200 text-blue-800',
    loading: 'bg-gray-50 border-gray-200 text-gray-800'
  };
  
  return classMap[props.type] || classMap.info;
});

// 计算状态图标
const statusIcon = computed(() => {
  const iconMap = {
    success: '✅',
    warning: '⚠️',
    error: '❌',
    info: 'ℹ️',
    loading: '🔄'
  };
  
  return iconMap[props.type] || iconMap.info;
});

// 计算是否为加载状态
const isLoading = computed(() => props.type === 'loading');

// 计算进度条样式
const progressBarClass = computed(() => {
  const classMap = {
    success: 'bg-green-600',
    warning: 'bg-yellow-600',
    error: 'bg-red-600',
    info: 'bg-blue-600',
    loading: 'bg-gray-600'
  };
  
  return classMap[props.type] || classMap.info;
});

// 处理关闭
const dismiss = () => {
  showMessage.value = false;
  emit('dismiss');
};

// 处理操作按钮点击
const handleAction = (action) => {
  if (action.disabled) return;
  
  emit('action', action);
  
  if (action.dismiss) {
    dismiss();
  }
};

// 自动隐藏
if (props.autoHide > 0) {
  setTimeout(() => {
    dismiss();
  }, props.autoHide * 1000);
}
</script>

<style scoped>
/* 动画效果 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .flex {
    @apply flex-col space-y-2;
  }
  
  .ml-3 {
    @apply ml-0;
  }
  
  .space-x-2 > * + * {
    @apply ml-0 mt-2;
  }
}

/* 按钮悬停效果 */
button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

button:not(:disabled):hover {
  transform: translateY(-1px);
}
</style>

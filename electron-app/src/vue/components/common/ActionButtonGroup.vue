<template>
  <div v-if="actions.length > 0" class="action-button-group mt-6 p-4 bg-gray-50 rounded-xl border border-gray-200">
    <div class="flex items-center justify-between mb-3">
      <h4 class="text-sm font-medium text-gray-700">{{ title }}</h4>
      <span v-if="subtitle" class="text-xs text-gray-500">{{ subtitle }}</span>
    </div>
    
    <div class="flex flex-wrap items-center gap-3">
      <button
        v-for="action in actions"
        :key="action.key"
        @click="handleAction(action)"
        :disabled="action.disabled"
        class="px-4 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ease-in-out"
        :class="getActionClasses(action)">
        
        <!-- 图标 (如果有) -->
        <svg v-if="action.icon" class="h-4 w-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="getIconPath(action.icon)"></path>
        </svg>
        
        {{ action.label }}
      </button>
    </div>
    
    <!-- 提示信息 -->
    <div v-if="hint" class="mt-3 text-xs text-gray-500 bg-white rounded-lg p-2 border border-gray-200">
      {{ hint }}
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 操作按钮数组
  actions: {
    type: Array,
    default: () => []
  },
  // 标题
  title: {
    type: String,
    default: '操作'
  },
  // 副标题
  subtitle: {
    type: String,
    default: ''
  },
  // 提示信息
  hint: {
    type: String,
    default: ''
  },
  // 布局方式
  layout: {
    type: String,
    default: 'horizontal', // horizontal, vertical, grid
    validator: (value) => ['horizontal', 'vertical', 'grid'].includes(value)
  }
});

const emit = defineEmits(['action']);

// 方法
const handleAction = (action) => {
  if (!action.disabled) {
    emit('action', action);
  }
};

const getActionClasses = (action) => {
  const variant = action.variant || 'default';
  const disabled = action.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md transform hover:-translate-y-0.5';
  
  const variants = {
    primary: 'bg-blue-600 text-white border-2 border-blue-600 hover:bg-blue-700 hover:border-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-sm',
    secondary: 'bg-white text-gray-700 border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-sm',
    success: 'bg-green-600 text-white border-2 border-green-600 hover:bg-green-700 hover:border-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 shadow-sm',
    warning: 'bg-yellow-500 text-white border-2 border-yellow-500 hover:bg-yellow-600 hover:border-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 shadow-sm',
    danger: 'bg-red-600 text-white border-2 border-red-600 hover:bg-red-700 hover:border-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 shadow-sm',
    default: 'bg-white text-gray-700 border-2 border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-sm'
  };
  
  return `${variants[variant]} ${disabled}`;
};

const getIconPath = (iconName) => {
  const icons = {
    'next': 'M9 5l7 7-7 7',
    'edit': 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z',
    'view': 'M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z',
    'stats': 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
    'download': 'M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4',
    'upload': 'M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12',
    'play': 'M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15',
    'stop': 'M6 18L18 6M6 6l12 12',
    'refresh': 'M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15',
    'check': 'M5 13l4 4L19 7',
    'settings': 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z'
  };
  
  return icons[iconName] || icons['next'];
};
</script>

<style scoped>
.action-button-group {
  transition: all 0.2s ease-in-out;
}

.action-button-group:hover {
  background-color: rgb(249 250 251);
}

/* 垂直布局 */
.layout-vertical .flex {
  flex-direction: column;
  align-items: stretch;
}

.layout-vertical button {
  width: 100%;
  justify-content: center;
}

/* 网格布局 */
.layout-grid .flex {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}
</style>

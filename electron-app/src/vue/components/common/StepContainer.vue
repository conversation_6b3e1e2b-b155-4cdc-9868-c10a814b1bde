<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <div class="flex items-center mb-2">
        <div class="step-icon w-8 h-8 rounded-lg flex items-center justify-center mr-3" :class="stepIconClass">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="stepIconPath"></path>
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-gray-800">{{ title }}</h3>
      </div>
      <p v-if="description" class="text-sm text-gray-500 ml-11">{{ description }}</p>
    </div>

    <!-- 前置结果摘要 (如果有) -->
    <div v-if="previousResult" class="previous-results mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <svg class="h-4 w-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span class="text-sm font-medium text-green-800">{{ previousResult.title }}</span>
        </div>
        <span class="text-xs text-green-600">{{ previousResult.summary }}</span>
      </div>
    </div>

    <!-- 前置条件警告 (如果需要) -->
    <div v-if="prerequisiteWarning" class="prerequisite-warning mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div class="flex items-start">
        <svg class="h-5 w-5 text-yellow-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <div>
          <h4 class="text-sm font-medium text-yellow-800 mb-1">{{ prerequisiteWarning.title }}</h4>
          <p class="text-sm text-yellow-700">{{ prerequisiteWarning.message }}</p>
          <button 
            v-if="prerequisiteWarning.action"
            @click="$emit('prerequisite-action', prerequisiteWarning.action)"
            class="mt-2 text-sm text-yellow-800 underline hover:text-yellow-900 transition-colors">
            {{ prerequisiteWarning.actionText }}
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容插槽 -->
    <slot></slot>

    <!-- 当前结果显示 (如果有) -->
    <div v-if="currentResult" class="current-results mt-6 pt-6 border-t border-gray-200">
      <slot name="result" :result="currentResult">
        <!-- 默认结果显示 -->
        <StatusCard
          :status="currentResult.status"
          :title="currentResult.title"
          :description="currentResult.description"
          :show-timestamp="currentResult.showTimestamp"
          :actions="currentResult.actions"
          @action="$emit('result-action', $event)"
        />
      </slot>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import StatusCard from './StatusCard.vue';

const props = defineProps({
  // 步骤基本信息
  stepNumber: {
    type: Number,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  
  // 前置结果
  previousResult: {
    type: Object,
    default: null
    // { title: string, summary: string }
  },
  
  // 前置条件警告
  prerequisiteWarning: {
    type: Object,
    default: null
    // { title: string, message: string, action: any, actionText: string }
  },
  
  // 当前结果
  currentResult: {
    type: Object,
    default: null
    // { status: string, title: string, description: string, showTimestamp: boolean, actions: array }
  }
});

const emit = defineEmits(['prerequisite-action', 'result-action']);

// 步骤主题配置
const stepThemes = {
  1: { 
    icon: 'bg-blue-500', 
    path: 'M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12'
  },
  2: { 
    icon: 'bg-purple-500', 
    path: 'M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3'
  },
  3: { 
    icon: 'bg-green-500', 
    path: 'M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z'
  },
  4: { 
    icon: 'bg-yellow-500', 
    path: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  },
  5: { 
    icon: 'bg-orange-500', 
    path: 'M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z'
  },
  6: { 
    icon: 'bg-red-500', 
    path: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z'
  },
  7: { 
    icon: 'bg-indigo-500', 
    path: 'M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z'
  },
  8: { 
    icon: 'bg-pink-500', 
    path: 'M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129'
  },
  9: { 
    icon: 'bg-gray-500', 
    path: 'M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
  }
};

// 计算步骤图标样式
const stepIconClass = computed(() => {
  const theme = stepThemes[props.stepNumber];
  return theme ? theme.icon : 'bg-gray-500';
});

const stepIconPath = computed(() => {
  const theme = stepThemes[props.stepNumber];
  return theme ? theme.path : 'M12 6v6m0 0v6m0-6h6m-6 0H6';
});
</script>

<style scoped>
.step-content-container {
  transition: all 0.3s ease;
}

.step-content-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.step-icon {
  transition: all 0.3s ease;
}

.step-icon:hover {
  transform: scale(1.1);
}
</style>

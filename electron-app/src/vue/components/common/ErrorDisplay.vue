<template>
  <div v-if="error" class="error-display">
    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
      <div class="flex items-start">
        <!-- 错误图标 -->
        <div class="flex-shrink-0 mr-3">
          <svg class="h-5 w-5 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
            </path>
          </svg>
        </div>
        
        <div class="flex-1 min-w-0">
          <!-- 错误标题 -->
          <h4 class="text-red-800 font-medium mb-1">
            {{ title }}
          </h4>
          
          <!-- 错误消息 -->
          <div class="text-sm text-red-600 mb-3">
            <p>{{ errorMessage }}</p>
            
            <!-- 详细错误信息 (可折叠) -->
            <div v-if="hasDetails" class="mt-2">
              <button 
                @click="showDetails = !showDetails"
                class="text-xs text-red-700 hover:text-red-900 underline flex items-center">
                <span>{{ showDetails ? '隐藏' : '显示' }}详细信息</span>
                <svg class="h-3 w-3 ml-1 transform transition-transform" 
                     :class="{ 'rotate-180': showDetails }" 
                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </button>
              
              <div v-show="showDetails" class="mt-2 p-2 bg-red-100 rounded text-xs font-mono text-red-800 overflow-x-auto">
                {{ errorDetails }}
              </div>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="flex items-center space-x-3">
            <!-- 重试按钮 -->
            <button 
              v-if="canRetry"
              @click="handleRetry"
              :disabled="isRetrying"
              class="inline-flex items-center px-3 py-1.5 text-sm font-medium text-red-700 bg-red-100 border border-red-300 rounded hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors">
              <svg v-if="isRetrying" class="animate-spin -ml-1 mr-2 h-3 w-3 text-red-700" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <svg v-else class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15">
                </path>
              </svg>
              {{ isRetrying ? '重试中...' : '重试' }}
            </button>
            
            <!-- 忽略按钮 -->
            <button 
              v-if="canIgnore"
              @click="handleIgnore"
              class="text-sm text-red-700 hover:text-red-900 underline">
              忽略错误
            </button>
            
            <!-- 关闭按钮 -->
            <button 
              v-if="canClose"
              @click="handleClose"
              class="text-sm text-red-700 hover:text-red-900 underline">
              关闭
            </button>
          </div>
          
          <!-- 建议操作 -->
          <div v-if="suggestions.length > 0" class="mt-3 pt-3 border-t border-red-200">
            <p class="text-xs text-red-700 font-medium mb-2">建议操作:</p>
            <ul class="text-xs text-red-600 space-y-1">
              <li v-for="(suggestion, index) in suggestions" :key="index" class="flex items-start">
                <span class="text-red-400 mr-1">•</span>
                <span>{{ suggestion }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  // 错误对象或错误消息
  error: {
    type: [String, Object, Error],
    default: null
  },
  // 错误标题
  title: {
    type: String,
    default: '操作失败'
  },
  // 是否可以重试
  canRetry: {
    type: Boolean,
    default: false
  },
  // 是否可以忽略
  canIgnore: {
    type: Boolean,
    default: false
  },
  // 是否可以关闭
  canClose: {
    type: Boolean,
    default: true
  },
  // 建议操作列表
  suggestions: {
    type: Array,
    default: () => []
  },
  // 是否正在重试
  isRetrying: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['retry', 'ignore', 'close']);

// 响应式状态
const showDetails = ref(false);

// 计算属性
const errorMessage = computed(() => {
  if (!props.error) return '';
  
  if (typeof props.error === 'string') {
    return props.error;
  }
  
  if (props.error instanceof Error) {
    return props.error.message;
  }
  
  if (typeof props.error === 'object') {
    return props.error.message || props.error.error || String(props.error);
  }
  
  return String(props.error);
});

const errorDetails = computed(() => {
  if (!props.error) return '';
  
  if (typeof props.error === 'string') {
    return props.error;
  }
  
  if (props.error instanceof Error) {
    return props.error.stack || props.error.message;
  }
  
  if (typeof props.error === 'object') {
    return JSON.stringify(props.error, null, 2);
  }
  
  return String(props.error);
});

const hasDetails = computed(() => {
  return errorDetails.value && errorDetails.value !== errorMessage.value;
});

// 方法
const handleRetry = () => {
  emit('retry');
};

const handleIgnore = () => {
  emit('ignore');
};

const handleClose = () => {
  emit('close');
};
</script>

<style scoped>
.error-display {
  /* 可以添加额外的样式 */
}

/* 确保代码块可以水平滚动 */
.overflow-x-auto {
  scrollbar-width: thin;
  scrollbar-color: #ef4444 #fecaca;
}

.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #fecaca;
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #ef4444;
  border-radius: 2px;
}
</style>

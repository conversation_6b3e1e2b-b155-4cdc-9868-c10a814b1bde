<template>
  <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
    <!-- 折叠头部 -->
    <button
      @click="toggleExpanded"
      class="w-full px-4 py-3 text-left flex items-center justify-between hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset rounded-t-lg"
    >
      <div class="flex items-center space-x-2">
        <span class="text-lg">💡</span>
        <span class="text-sm font-medium text-gray-900">{{ title }}</span>
        <span 
          v-if="logCount > 0"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
        >
          {{ logCount }}
        </span>
      </div>
      <div class="flex items-center space-x-2">
        <span v-if="hasNewLogs" class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
        <svg 
          :class="[
            'w-4 h-4 text-gray-500 transition-transform duration-200',
            { 'transform rotate-180': isExpanded }
          ]"
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>
    </button>
    
    <!-- 日志内容 -->
    <div 
      v-show="isExpanded"
      class="border-t border-gray-200"
    >
      <div class="p-4">
        <!-- 日志控制栏 -->
        <div class="flex items-center justify-between mb-3">
          <div class="flex items-center space-x-2">
            <button
              @click="clearLogs"
              class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              🗑️ 清除
            </button>
            <button
              @click="copyLogs"
              class="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              📋 复制
            </button>
            <button
              @click="toggleAutoScroll"
              :class="[
                'inline-flex items-center px-2 py-1 border shadow-sm text-xs font-medium rounded focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
                autoScroll 
                  ? 'border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100'
                  : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
              ]"
            >
              {{ autoScroll ? '📌' : '📍' }} 自动滚动
            </button>
          </div>
          <div class="text-xs text-gray-500">
            {{ formatTime(new Date()) }}
          </div>
        </div>
        
        <!-- 日志列表 -->
        <div 
          ref="logContainer"
          class="bg-gray-900 rounded-lg p-3 max-h-64 overflow-y-auto font-mono text-sm"
        >
          <div v-if="logs.length === 0" class="text-gray-400 text-center py-4">
            暂无日志信息
          </div>
          <div
            v-for="(log, index) in logs"
            :key="index"
            :class="[
              'flex items-start space-x-2 py-1',
              { 'border-t border-gray-700': index > 0 }
            ]"
          >
            <!-- 时间戳 -->
            <span class="text-gray-400 text-xs flex-shrink-0 w-16">
              {{ formatLogTime(log.timestamp) }}
            </span>
            
            <!-- 日志级别图标 -->
            <span class="flex-shrink-0">{{ getLogIcon(log.level) }}</span>
            
            <!-- 日志内容 -->
            <span 
              :class="[
                'flex-1 break-words',
                getLogTextClass(log.level)
              ]"
            >
              {{ log.message }}
            </span>
          </div>
        </div>
        
        <!-- 日志统计 -->
        <div v-if="showStats" class="mt-3 flex items-center justify-between text-xs text-gray-500">
          <div class="flex items-center space-x-4">
            <span>总计: {{ logCount }}</span>
            <span v-if="logStats.success > 0" class="text-green-600">✅ {{ logStats.success }}</span>
            <span v-if="logStats.warning > 0" class="text-yellow-600">⚠️ {{ logStats.warning }}</span>
            <span v-if="logStats.error > 0" class="text-red-600">❌ {{ logStats.error }}</span>
          </div>
          <div>
            最后更新: {{ lastUpdateTime }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '处理日志'
  },
  logs: {
    type: Array,
    default: () => []
  },
  initialExpanded: {
    type: Boolean,
    default: false
  },
  showStats: {
    type: Boolean,
    default: true
  },
  maxLogs: {
    type: Number,
    default: 100
  }
});

const emit = defineEmits(['clear', 'copy']);

const isExpanded = ref(props.initialExpanded);
const autoScroll = ref(true);
const hasNewLogs = ref(false);
const logContainer = ref(null);

// 计算属性
const logCount = computed(() => props.logs.length);

const logStats = computed(() => {
  return props.logs.reduce((stats, log) => {
    stats[log.level] = (stats[log.level] || 0) + 1;
    return stats;
  }, { success: 0, warning: 0, error: 0, info: 0 });
});

const lastUpdateTime = computed(() => {
  if (props.logs.length === 0) return '';
  const lastLog = props.logs[props.logs.length - 1];
  return formatTime(new Date(lastLog.timestamp));
});

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value;
  hasNewLogs.value = false;
  
  if (isExpanded.value && autoScroll.value) {
    nextTick(() => {
      scrollToBottom();
    });
  }
};

const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value;
  if (autoScroll.value) {
    scrollToBottom();
  }
};

const scrollToBottom = () => {
  if (logContainer.value) {
    logContainer.value.scrollTop = logContainer.value.scrollHeight;
  }
};

const clearLogs = () => {
  emit('clear');
  hasNewLogs.value = false;
};

const copyLogs = async () => {
  const logText = props.logs.map(log => 
    `[${formatLogTime(log.timestamp)}] ${getLogIcon(log.level)} ${log.message}`
  ).join('\n');
  
  try {
    await navigator.clipboard.writeText(logText);
    emit('copy', logText);
  } catch (err) {
    console.error('复制失败:', err);
  }
};

const getLogIcon = (level) => {
  const iconMap = {
    success: '✅',
    warning: '⚠️',
    error: '❌',
    info: 'ℹ️',
    debug: '🔍'
  };
  return iconMap[level] || 'ℹ️';
};

const getLogTextClass = (level) => {
  const classMap = {
    success: 'text-green-400',
    warning: 'text-yellow-400',
    error: 'text-red-400',
    info: 'text-blue-400',
    debug: 'text-gray-400'
  };
  return classMap[level] || 'text-gray-300';
};

const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN', { 
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const formatLogTime = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', { 
    hour12: false,
    minute: '2-digit',
    second: '2-digit'
  });
};

// 监听日志变化
watch(() => props.logs.length, (newCount, oldCount) => {
  if (newCount > oldCount) {
    hasNewLogs.value = !isExpanded.value;
    
    if (isExpanded.value && autoScroll.value) {
      nextTick(() => {
        scrollToBottom();
      });
    }
  }
});

// 监听展开状态变化
watch(isExpanded, (expanded) => {
  if (expanded && autoScroll.value) {
    nextTick(() => {
      scrollToBottom();
    });
  }
});
</script>

<style scoped>
/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 动画效果 */
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* 响应式设计 */
@media (max-width: 640px) {
  .space-x-4 > * + * {
    @apply ml-2;
  }
  
  .space-x-2 > * + * {
    @apply ml-1;
  }
  
  .text-xs {
    @apply text-[10px];
  }
  
  .max-h-64 {
    @apply max-h-48;
  }
}
</style>

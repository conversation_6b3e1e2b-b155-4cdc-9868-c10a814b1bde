<template>
  <div class="w-full bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200 px-6 py-4 shadow-sm">
    <div class="max-w-6xl mx-auto">
      <!-- 进度条 -->
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center space-x-2">
          <span class="text-lg font-semibold text-gray-800">Step {{ currentStep }}</span>
          <div class="flex items-center space-x-1">
            <div
              v-for="i in totalSteps"
              :key="i"
              :class="[
                'w-3 h-3 rounded-full transition-all duration-200',
                i === currentStep
                  ? 'bg-blue-600 scale-110'
                  : i < currentStep
                  ? 'bg-green-500'
                  : 'bg-gray-300'
              ]"
            />
          </div>
          <span class="text-lg font-semibold text-gray-800">Step {{ totalSteps }}</span>
        </div>
        <div class="text-sm text-gray-600">
          {{ steps[currentStep - 1]?.name }} → 导出字幕
        </div>
      </div>

      <!-- 步骤详情 -->
      <div class="flex items-center justify-between">
        <template v-for="(step, index) in steps" :key="step.id">
          <div class="flex flex-col items-center">
            <div 
              :class="[
                'flex items-center justify-center w-10 h-10 rounded-full text-sm font-medium transition-all duration-200',
                step.id === currentStep 
                  ? 'bg-blue-600 text-white shadow-lg scale-110' 
                  : step.id < currentStep 
                    ? 'bg-green-500 text-white' 
                    : 'bg-gray-200 text-gray-600'
              ]"
            >
              {{ step.id < currentStep ? '✓' : step.icon }}
            </div>
            <span 
              :class="[
                'mt-2 text-xs font-medium text-center',
                step.id === currentStep ? 'text-blue-600' : 'text-gray-500'
              ]"
            >
              {{ step.name }}
            </span>
          </div>
          <div 
            v-if="index < steps.length - 1"
            :class="[
              'flex-1 h-0.5 mx-2 transition-all duration-300',
              step.id < currentStep ? 'bg-green-500' : 'bg-gray-300'
            ]"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  currentStep: {
    type: Number,
    required: true,
    validator: (value) => value >= 1 && value <= 6
  },
  totalSteps: {
    type: Number,
    default: 6
  }
});

const steps = computed(() => [
  { id: 1, name: '上传文件', icon: '📁' },
  { id: 2, name: '语音转文字', icon: '🎤' },
  { id: 3, name: '编辑字幕', icon: '📝' },
  { id: 4, name: '优化字幕', icon: '✨' },
  { id: 5, name: '翻译设置', icon: '🌍' },
  { id: 6, name: '导出字幕', icon: '📥' }
]);
</script>

<style scoped>
/* 响应式设计 */
@media (max-width: 768px) {
  .max-w-6xl {
    @apply max-w-full;
  }
  
  .flex-col {
    @apply items-center;
  }
  
  .text-xs {
    @apply text-[10px];
  }
  
  .w-10 {
    @apply w-8;
  }
  
  .h-10 {
    @apply h-8;
  }
}
</style>

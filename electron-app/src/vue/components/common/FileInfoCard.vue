<template>
  <div 
    v-if="fileInfo && showCard"
    class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm mb-4"
  >
    <div class="flex items-center space-x-3">
      <!-- 文件图标 -->
      <div class="flex-shrink-0">
        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <span class="text-xl">{{ fileIcon }}</span>
        </div>
      </div>
      
      <!-- 文件信息 -->
      <div class="flex-1 min-w-0">
        <div class="flex items-center space-x-2">
          <h3 class="text-sm font-medium text-gray-900 truncate">
            {{ fileInfo.name }}
          </h3>
          <span 
            v-if="processingStatus"
            :class="[
              'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
              statusClasses
            ]"
          >
            {{ statusIcon }} {{ processingStatus }}
          </span>
        </div>
        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
          <span>{{ formatFileSize(fileInfo.size) }}</span>
          <span>{{ fileInfo.type || getFileType(fileInfo.name) }}</span>
          <span v-if="fileInfo.duration">{{ formatDuration(fileInfo.duration) }}</span>
        </div>
      </div>
      
      <!-- 操作按钮 -->
      <div v-if="showActions" class="flex-shrink-0">
        <div class="flex items-center space-x-2">
          <button
            v-if="allowReselect"
            @click="$emit('reselect')"
            class="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            🔄 重新选择
          </button>
          <button
            v-if="allowRemove"
            @click="$emit('remove')"
            class="inline-flex items-center px-3 py-1 border border-red-300 shadow-sm text-xs font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            ❌ 移除
          </button>
        </div>
      </div>
    </div>
    
    <!-- 处理进度 -->
    <div v-if="showProgress && progress !== null" class="mt-3">
      <div class="flex items-center justify-between text-xs text-gray-600 mb-1">
        <span>{{ progressText || '处理进度' }}</span>
        <span>{{ Math.round(progress) }}%</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${Math.min(100, Math.max(0, progress))}%` }"
        />
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="errorMessage" class="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
      <div class="flex items-center">
        <span class="text-red-400 mr-2">⚠️</span>
        <span class="text-sm text-red-700">{{ errorMessage }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  fileInfo: {
    type: Object,
    default: null
  },
  showCard: {
    type: Boolean,
    default: true
  },
  processingStatus: {
    type: String,
    default: null // 'processing', 'completed', 'error', 'ready'
  },
  progress: {
    type: Number,
    default: null
  },
  progressText: {
    type: String,
    default: null
  },
  errorMessage: {
    type: String,
    default: null
  },
  showActions: {
    type: Boolean,
    default: true
  },
  allowReselect: {
    type: Boolean,
    default: true
  },
  allowRemove: {
    type: Boolean,
    default: false
  },
  showProgress: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['reselect', 'remove']);

// 计算文件图标
const fileIcon = computed(() => {
  if (!props.fileInfo?.name) return '📄';
  
  const extension = props.fileInfo.name.split('.').pop()?.toLowerCase();
  const iconMap = {
    // 视频文件
    'mp4': '🎬', 'avi': '🎬', 'mov': '🎬', 'mkv': '🎬', 'flv': '🎬', 'wmv': '🎬',
    // 音频文件
    'mp3': '🎵', 'wav': '🎵', 'flac': '🎵', 'aac': '🎵', 'ogg': '🎵', 'm4a': '🎵',
    // 字幕文件
    'srt': '📝', 'vtt': '📝', 'ass': '📝', 'ssa': '📝',
    // 其他
    'txt': '📄', 'doc': '📄', 'docx': '📄', 'pdf': '📄'
  };
  
  return iconMap[extension] || '📄';
});

// 计算状态样式
const statusClasses = computed(() => {
  const statusMap = {
    'processing': 'bg-blue-100 text-blue-800',
    'completed': 'bg-green-100 text-green-800',
    'error': 'bg-red-100 text-red-800',
    'ready': 'bg-gray-100 text-gray-800',
    'waiting': 'bg-yellow-100 text-yellow-800'
  };
  
  return statusMap[props.processingStatus] || 'bg-gray-100 text-gray-800';
});

// 计算状态图标
const statusIcon = computed(() => {
  const iconMap = {
    'processing': '🔄',
    'completed': '✅',
    'error': '❌',
    'ready': '📋',
    'waiting': '⏳'
  };
  
  return iconMap[props.processingStatus] || '';
});

// 工具函数
const formatFileSize = (bytes) => {
  if (!bytes) return '0 B';
  
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  
  return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
};

const formatDuration = (seconds) => {
  if (!seconds) return '';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

const getFileType = (filename) => {
  if (!filename) return '';
  
  const extension = filename.split('.').pop()?.toLowerCase();
  const typeMap = {
    // 视频
    'mp4': 'video/mp4', 'avi': 'video/avi', 'mov': 'video/quicktime', 
    'mkv': 'video/x-matroska', 'flv': 'video/x-flv', 'wmv': 'video/x-ms-wmv',
    // 音频
    'mp3': 'audio/mpeg', 'wav': 'audio/wav', 'flac': 'audio/flac', 
    'aac': 'audio/aac', 'ogg': 'audio/ogg', 'm4a': 'audio/mp4',
    // 字幕
    'srt': 'text/srt', 'vtt': 'text/vtt', 'ass': 'text/ass'
  };
  
  return typeMap[extension] || `file/${extension}`;
};
</script>

<style scoped>
/* 响应式设计 */
@media (max-width: 640px) {
  .flex-col {
    @apply space-y-2;
  }
  
  .space-x-4 > * + * {
    @apply ml-2;
  }
  
  .text-xs {
    @apply text-[10px];
  }
}

/* 动画效果 */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}

/* 悬停效果 */
.hover\:bg-gray-50:hover {
  background-color: rgb(249 250 251);
}

.hover\:bg-red-50:hover {
  background-color: rgb(254 242 242);
}
</style>

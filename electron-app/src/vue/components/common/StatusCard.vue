<template>
  <div v-if="shouldShow" class="status-card" :class="cardClasses">
    <div class="flex items-start">
      <!-- 状态图标 -->
      <div class="flex-shrink-0 mr-3">
        <div class="w-8 h-8 rounded-full flex items-center justify-center" :class="iconBgClasses">
          <!-- 成功图标 -->
          <svg v-if="status === 'success'" class="h-4 w-4" :class="iconClasses" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          
          <!-- 进行中图标 -->
          <svg v-else-if="status === 'processing'" class="h-4 w-4 animate-spin" :class="iconClasses" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          
          <!-- 警告图标 -->
          <svg v-else-if="status === 'warning'" class="h-4 w-4" :class="iconClasses" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
          </svg>
          
          <!-- 错误图标 -->
          <svg v-else-if="status === 'error'" class="h-4 w-4" :class="iconClasses" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          
          <!-- 信息图标 -->
          <svg v-else class="h-4 w-4" :class="iconClasses" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      </div>
      
      <div class="flex-1 min-w-0">
        <!-- 标题和时间 -->
        <div class="flex items-center justify-between mb-1">
          <h4 class="font-medium" :class="titleClasses">
            {{ title }}
          </h4>
          <span v-if="showTimestamp" class="text-xs text-gray-500 font-mono">
            {{ formattedTime }}
          </span>
        </div>
        
        <!-- 描述 -->
        <p v-if="description" class="text-sm" :class="descriptionClasses">
          {{ description }}
        </p>
        
        <!-- 详细信息 -->
        <div v-if="details" class="mt-2 text-xs" :class="detailsClasses">
          {{ details }}
        </div>
        
        <!-- 进度信息 -->
        <div v-if="showProgress" class="mt-2">
          <div class="flex items-center justify-between text-xs mb-1" :class="progressTextClasses">
            <span>{{ progressText }}</span>
            <span>{{ progressPercentage }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-1.5">
            <div 
              class="h-1.5 rounded-full transition-all duration-300"
              :class="progressBarClasses"
              :style="{ width: progressPercentage + '%' }">
            </div>
          </div>
        </div>
        
        <!-- 操作按钮 -->
        <div v-if="actions.length > 0" class="mt-4 flex items-center space-x-3">
          <button
            v-for="action in actions"
            :key="action.key"
            @click="handleAction(action)"
            :disabled="action.disabled"
            class="px-4 py-2 text-sm font-medium rounded-lg border transition-colors"
            :class="getActionClasses(action)">
            {{ action.label }}
          </button>
        </div>
      </div>
      
      <!-- 关闭按钮 -->
      <button 
        v-if="closable"
        @click="handleClose"
        class="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 transition-colors">
        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  // 状态类型
  status: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'processing', 'warning', 'error', 'info'].includes(value)
  },
  // 标题
  title: {
    type: String,
    required: true
  },
  // 描述
  description: {
    type: String,
    default: ''
  },
  // 详细信息
  details: {
    type: String,
    default: ''
  },
  // 进度百分比
  progress: {
    type: Number,
    default: -1
  },
  // 进度文本
  progressText: {
    type: String,
    default: '进度'
  },
  // 是否显示时间戳
  showTimestamp: {
    type: Boolean,
    default: false
  },
  // 时间戳
  timestamp: {
    type: [Number, Date],
    default: () => Date.now()
  },
  // 是否可关闭
  closable: {
    type: Boolean,
    default: false
  },
  // 操作按钮
  actions: {
    type: Array,
    default: () => []
  },
  // 是否自动显示
  autoShow: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['close', 'action']);

// 计算属性
const shouldShow = computed(() => props.autoShow);

const showProgress = computed(() => props.progress >= 0 && props.progress <= 100);

const progressPercentage = computed(() => {
  if (props.progress < 0) return 0;
  if (props.progress > 100) return 100;
  return Math.round(props.progress);
});

const formattedTime = computed(() => {
  const date = new Date(props.timestamp);
  return date.toLocaleTimeString('zh-CN', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  });
});

// 样式类
const cardClasses = computed(() => {
  const baseClasses = 'p-4 rounded-lg border mb-4';
  const statusClasses = {
    success: 'bg-green-50 border-green-200',
    processing: 'bg-blue-50 border-blue-200',
    warning: 'bg-yellow-50 border-yellow-200',
    error: 'bg-red-50 border-red-200',
    info: 'bg-gray-50 border-gray-200'
  };
  return `${baseClasses} ${statusClasses[props.status]}`;
});

const iconBgClasses = computed(() => {
  const classes = {
    success: 'bg-green-100',
    processing: 'bg-blue-100',
    warning: 'bg-yellow-100',
    error: 'bg-red-100',
    info: 'bg-gray-100'
  };
  return classes[props.status];
});

const iconClasses = computed(() => {
  const classes = {
    success: 'text-green-600',
    processing: 'text-blue-600',
    warning: 'text-yellow-600',
    error: 'text-red-600',
    info: 'text-gray-600'
  };
  return classes[props.status];
});

const titleClasses = computed(() => {
  const classes = {
    success: 'text-green-800',
    processing: 'text-blue-800',
    warning: 'text-yellow-800',
    error: 'text-red-800',
    info: 'text-gray-800'
  };
  return classes[props.status];
});

const descriptionClasses = computed(() => {
  const classes = {
    success: 'text-green-600',
    processing: 'text-blue-600',
    warning: 'text-yellow-600',
    error: 'text-red-600',
    info: 'text-gray-600'
  };
  return classes[props.status];
});

const detailsClasses = computed(() => {
  const classes = {
    success: 'text-green-500',
    processing: 'text-blue-500',
    warning: 'text-yellow-500',
    error: 'text-red-500',
    info: 'text-gray-500'
  };
  return classes[props.status];
});

const progressTextClasses = computed(() => {
  const classes = {
    success: 'text-green-600',
    processing: 'text-blue-600',
    warning: 'text-yellow-600',
    error: 'text-red-600',
    info: 'text-gray-600'
  };
  return classes[props.status];
});

const progressBarClasses = computed(() => {
  const classes = {
    success: 'bg-green-500',
    processing: 'bg-blue-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
    info: 'bg-gray-500'
  };
  return classes[props.status];
});

// 方法
const handleClose = () => {
  emit('close');
};

const handleAction = (action) => {
  emit('action', action);
};

const getActionClasses = (action) => {
  const variant = action.variant || 'default';
  const disabled = action.disabled ? 'opacity-50 cursor-not-allowed' : '';

  const variants = {
    primary: 'bg-blue-600 text-white border-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-sm',
    secondary: 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-sm',
    danger: 'bg-red-600 text-white border-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 shadow-sm',
    success: 'bg-green-600 text-white border-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 shadow-sm',
    default: 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-sm'
  };

  return `${variants[variant]} ${disabled}`;
};
</script>

<style scoped>
.status-card {
  transition: all 0.2s ease-in-out;
}

.status-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>

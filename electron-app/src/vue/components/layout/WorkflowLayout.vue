<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 步骤进度指示器 -->
    <StepProgressIndicator :current-step="currentStep" />
    
    <!-- 主要内容区域 -->
    <div class="max-w-6xl mx-auto px-4 py-6">
      <!-- 工作流内容 -->
      <div class="workflow-content">
        <slot />
      </div>
      
      <!-- 快速操作栏 -->
      <div 
        v-if="showQuickActions"
        class="fixed bottom-6 right-6 z-50"
      >
        <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-4">
          <h4 class="text-sm font-medium text-gray-900 mb-3">⚡ 快速操作</h4>
          <div class="space-y-2">
            <button
              v-for="action in quickActions"
              :key="action.key"
              @click="handleQuickAction(action)"
              :disabled="action.disabled"
              :class="[
                'w-full text-left px-3 py-2 text-sm rounded-md transition-colors',
                action.disabled 
                  ? 'text-gray-400 cursor-not-allowed'
                  : 'text-gray-700 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500'
              ]"
            >
              <span class="mr-2">{{ action.icon }}</span>
              {{ action.label }}
            </button>
          </div>
        </div>
      </div>
      
      <!-- 帮助按钮 -->
      <button
        v-if="showHelpButton"
        @click="toggleHelp"
        class="fixed bottom-6 left-6 z-50 w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
      >
        <span class="text-lg">❓</span>
      </button>
    </div>
    
    <!-- 帮助面板 -->
    <div 
      v-if="showHelp"
      class="fixed inset-0 z-50 overflow-y-auto"
      @click="closeHelp"
    >
      <div class="flex items-center justify-center min-h-screen px-4">
        <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity"></div>
        
        <div 
          class="relative bg-white rounded-lg max-w-2xl w-full p-6 shadow-xl"
          @click.stop
        >
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              📖 Step {{ currentStep }} 帮助
            </h3>
            <button
              @click="closeHelp"
              class="text-gray-400 hover:text-gray-600 focus:outline-none"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div class="prose prose-sm max-w-none">
            <slot name="help">
              <div class="text-gray-600">
                <h4>{{ stepHelp.title }}</h4>
                <p>{{ stepHelp.description }}</p>
                <ul>
                  <li v-for="tip in stepHelp.tips" :key="tip">{{ tip }}</li>
                </ul>
              </div>
            </slot>
          </div>
          
          <div class="mt-6 flex justify-end">
            <button
              @click="closeHelp"
              class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              知道了
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import StepProgressIndicator from '@/vue/components/common/StepProgressIndicator.vue';

const props = defineProps({
  currentStep: {
    type: Number,
    required: true
  },
  showQuickActions: {
    type: Boolean,
    default: true
  },
  showHelpButton: {
    type: Boolean,
    default: true
  },
  quickActions: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['quick-action', 'help-toggle']);

const showHelp = ref(false);

// 计算步骤帮助内容
const stepHelp = computed(() => {
  const helpContent = {
    1: {
      title: '文件上传帮助',
      description: '选择您要处理的视频或音频文件',
      tips: [
        '支持拖拽文件到上传区域',
        '支持的格式：MP4, AVI, MOV, MP3, WAV 等',
        '文件大小限制：2GB 以内',
        '建议使用高质量的音频文件以获得更好的转录效果'
      ]
    },
    2: {
      title: '语音转文字帮助',
      description: '将音频内容转换为文字字幕',
      tips: [
        '选择合适的转录模型以获得最佳效果',
        '确保音频语言设置正确',
        '启用缓存可以加速重复处理',
        '处理时间取决于音频长度和质量'
      ]
    },
    3: {
      title: '字幕编辑帮助',
      description: '编辑和调整转录生成的字幕',
      tips: [
        '可以手动编辑字幕文本内容',
        '调整时间轴以匹配音频',
        '使用分割和合并功能优化字幕段落',
        '预览功能可以实时查看效果'
      ]
    },
    4: {
      title: '字幕优化帮助',
      description: '使用AI优化字幕的语法和表达',
      tips: [
        '选择合适的优化级别',
        '语法修正可以改善文本质量',
        '断句优化让字幕更易阅读',
        '可以逐条审核优化结果'
      ]
    },
    5: {
      title: '翻译设置帮助',
      description: '将字幕翻译为其他语言',
      tips: [
        '目前支持中英文互译',
        '选择合适的翻译风格',
        '术语一致性确保专业词汇准确',
        '可以跳过翻译直接导出'
      ]
    },
    6: {
      title: '导出字幕帮助',
      description: '选择格式和样式导出最终字幕',
      tips: [
        '支持多种内容组合：仅原文、仅译文、双语等',
        '支持多种格式：SRT、VTT、ASS、TXT',
        '可以同时导出多个版本',
        '双语字幕支持不同的排列方式'
      ]
    }
  };
  
  return helpContent[props.currentStep] || {
    title: '帮助',
    description: '当前步骤的帮助信息',
    tips: []
  };
});

// 方法
const handleQuickAction = (action) => {
  emit('quick-action', action);
};

const toggleHelp = () => {
  showHelp.value = !showHelp.value;
  emit('help-toggle', showHelp.value);
};

const closeHelp = () => {
  showHelp.value = false;
  emit('help-toggle', false);
};
</script>

<style scoped>
/* 工作流布局样式 */
.workflow-content {
  min-height: calc(100vh - 200px);
}

/* 快速操作栏动画 */
.fixed {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 帮助面板动画 */
.fixed.inset-0 {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fixed.bottom-6.right-6 {
    @apply bottom-4 right-4;
  }
  
  .fixed.bottom-6.left-6 {
    @apply bottom-4 left-4;
  }
  
  .max-w-2xl {
    @apply max-w-sm;
  }
  
  .px-4.py-6 {
    @apply px-2 py-4;
  }
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
</style>

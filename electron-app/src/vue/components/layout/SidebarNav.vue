<template>
  <div
    class="flex flex-col bg-gradient-to-b from-indigo-900 via-purple-900 to-pink-900 text-white transition-all duration-300 ease-in-out shadow-lg"
    :class="isSidebarCollapsed ? 'w-16' : 'w-56'"
  >
    <!-- Header / Logo -->
    <div class="flex items-center justify-between p-4 h-16 border-b border-white/20 backdrop-blur-sm">
      <div v-if="!isSidebarCollapsed" class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09z" />
          </svg>
        </div>
        <span class="text-lg font-bold bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent">应用导航</span>
      </div>
      <button @click="toggleSidebarHandler" class="p-2 rounded-lg hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20 transition-all duration-200">
        <svg v-if="!isSidebarCollapsed" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5L8.25 12l7.5-7.5" />
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5">
          <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
        </svg>
      </button>
    </div>

    <!-- Navigation Links -->
    <nav class="flex-grow pt-6 px-3">
      <ul class="space-y-2">
        <li v-for="view in availableViews" :key="view.name">
          <a
            href="#"
            @click.prevent="setActiveViewHandler(view.name)"
            class="group flex items-center transition-all duration-300 hover:bg-white/10 hover:backdrop-blur-sm hover:scale-105"
            :class="[
              isSidebarCollapsed ? 'px-2 py-2 rounded-lg justify-center' : 'px-3 py-3 rounded-xl',
              activeViewName === view.name
                ? (isSidebarCollapsed
                    ? 'bg-gradient-to-r from-cyan-400 to-blue-500 shadow-lg shadow-cyan-500/50 scale-110'
                    : 'bg-gradient-to-r from-cyan-500/20 to-blue-500/20 bg-white/10 backdrop-blur-sm border border-white/20 shadow-lg shadow-cyan-500/20')
                : ''
            ]"
          >
            <!-- Icon -->
            <div class="flex-shrink-0" :class="{ 'mr-3': !isSidebarCollapsed }">
              <div class="w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-200"
                   :class="[
                     activeViewName === view.name
                       ? (isSidebarCollapsed ? 'bg-white/20' : 'bg-gradient-to-r from-cyan-400 to-blue-500')
                       : 'bg-white/10 group-hover:bg-white/20'
                   ]">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5">
                  <path v-if="view.icon === 'icon-subtitles'" stroke-linecap="round" stroke-linejoin="round" d="M7.5 8.25h9m-9 3H12m-9.75 1.51c0 1.6 1.123 2.994 2.707 3.227 1.129.166 2.27.293 3.423.379.35.026.67.21.865.501L12 21l2.755-4.133a1.14 1.14 0 01.865-.501 48.172 48.172 0 003.423-.379c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
                  <path v-if="view.icon === 'icon-grpc'" stroke-linecap="round" stroke-linejoin="round" d="M6.75 7.5l3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0021 18V6a2.25 2.25 0 00-2.25-2.25H5.25A2.25 2.25 0 003 6v12a2.25 2.25 0 002.25 2.25z" />
                  <path v-if="view.icon === 'icon-ai-settings'" stroke-linecap="round" stroke-linejoin="round" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065zM15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
            </div>
            <span v-if="!isSidebarCollapsed" class="font-medium text-white/90 group-hover:text-white transition-colors duration-200">{{ view.displayName }}</span>
          </a>
        </li>
      </ul>
    </nav>

    <!-- Footer (optional) -->
    <div class="p-4 border-t border-white/10">
      <div v-if="!isSidebarCollapsed" class="flex items-center space-x-2">
        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <p class="text-xs text-white/60">版本 1.0.0 - 运行中</p>
      </div>
      <div v-else class="flex justify-center">
        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useUiStore } from '../../store/uiStore';

const uiStore = useUiStore();

const availableViews = computed(() => uiStore.getAvailableViews);
const activeViewName = computed(() => uiStore.getActiveViewName);
const isSidebarCollapsed = computed(() => uiStore.getIsSidebarCollapsed);

const setActiveViewHandler = (viewName) => {
  uiStore.setActiveView(viewName);
};

const toggleSidebarHandler = () => {
  uiStore.toggleSidebar();
};
</script>

<style scoped>
/* Scoped styles can be added here if needed, beyond Tailwind utility classes */
/* For example, custom icon styles if not using SVG paths directly or a library */
.icon-subtitles { /* Example if you were using background images or font icons */ }
.icon-grpc { /* Example */ }
</style>
<template>
  <div class="ai-service-config-form p-4 border border-gray-200 rounded-lg shadow-sm">
    <h3 class="text-lg font-semibold mb-6">{{ formTitle }}</h3>
    <form @submit.prevent="submitForm">
      <!-- Provider Type (Select for new, readonly for existing) -->
      <div class="mb-4">
        <label for="provider_type" class="block text-sm font-medium text-gray-700 mb-1">服务类型</label>
        <select
          id="provider_type"
          v-model="editableConfig.provider_type"
          @change="handleProviderTypeChange"
          :disabled="!isNew"
          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          required
        >
          <option value="" disabled>请选择服务类型</option>
          <option v-for="type in availableProviderTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </option>
        </select>
      </div>

      <!-- Display Name -->
      <div class="mb-4">
        <label for="display_name" class="block text-sm font-medium text-gray-700 mb-1">显示名称</label>
        <input
          type="text"
          id="display_name"
          v-model="editableConfig.display_name"
          class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          required
          placeholder="例如：我的主力 OpenAI"
        />
      </div>

      <!-- Provider ID -->
      <div class="mb-4">
        <label for="provider_id" class="block text-sm font-medium text-gray-700 mb-1">服务 ID</label>
        <input
          type="text"
          id="provider_id"
          v-model="editableConfig.provider_id"
          @blur="validateProviderId"
          class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          required
          placeholder="例如：openai_default_01 (需唯一)"
        />
        <p v-if="providerIdError" class="text-xs text-red-500 mt-1">{{ providerIdError }}</p>
        <p v-else class="text-xs text-gray-500 mt-1">唯一标识符，创建后若无必要请勿修改。</p>
      </div>
      
      <!-- Is Enabled -->
      <div class="mb-6">
        <label for="is_enabled" class="flex items-center cursor-pointer">
          <div class="relative">
            <input type="checkbox" id="is_enabled" class="sr-only" v-model="editableConfig.is_enabled">
            <div class="block bg-gray-600 w-10 h-6 rounded-full"></div>
            <div :class="{'translate-x-full': editableConfig.is_enabled}" class="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
          </div>
          <span class="ml-3 text-sm font-medium text-gray-700">启用此服务</span>
        </label>
      </div>

      <!-- Dynamically generated fields for credentials and attributes -->
      <div v-if="editableConfig.provider_type && currentProviderSchema">
        <!-- Credentials -->
        <div v-if="currentProviderSchema.credentials && Object.keys(currentProviderSchema.credentials).length > 0" class="mb-6 p-4 border border-yellow-300 bg-yellow-50 rounded-md">
          <h4 class="text-md font-semibold mb-3 text-yellow-800">凭证 (Credentials)</h4>
          <div v-for="(field, key) in currentProviderSchema.credentials" :key="key" class="mb-4">
            <label :for="`cred-${key}`" class="block text-sm font-medium text-gray-700 mb-1">
              {{ field.label }}
              <span v-if="field.required" class="text-red-500">*</span>
            </label>
            <div class="relative">
              <input
                :type="field.type === 'password' && !showPasswords[key] ? 'password' : 'text'"
                :id="`cred-${key}`"
                v-model="editableConfig.credentials[key]"
                class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                :placeholder="field.placeholder || ''"
                :required="field.required"
              />
              <button
                v-if="field.type === 'password'"
                type="button"
                @click="togglePasswordVisibility(key)"
                class="absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5 text-gray-500 hover:text-gray-700"
              >
                <svg v-if="!showPasswords[key]" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7 .946-3.118 3.703-5.434 6.932-6.018M15 12a3 3 0 11-6 0 3 3 0 016 0zm6.042-1.042c.14-.392.268-.801.372-1.228M3.958 13.042c-.14.392-.268.801-.372 1.228" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3l18 18" /></svg>
              </button>
            </div>
            <p v-if="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
          </div>
        </div>

        <!-- Attributes -->
        <div v-if="currentProviderSchema.attributes && Object.keys(currentProviderSchema.attributes).length > 0" class="mb-6 p-4 border border-gray-200 bg-gray-50 rounded-md">
          <h4 class="text-md font-semibold mb-3 text-gray-700">属性 (Attributes)</h4>
          <div v-for="(field, key) in currentProviderSchema.attributes" :key="key" class="mb-4">
            <label :for="`attr-${key}`" class="block text-sm font-medium text-gray-700 mb-1">
              {{ field.label }}
              <span v-if="field.required" class="text-red-500">*</span>
            </label>
            <!-- Model Select Field -->
            <div v-if="field.type === 'model_select'" class="space-y-2">
              <div class="flex items-center space-x-2">
                <select
                  :id="`attr-${key}`"
                  v-model="editableConfig.attributes[key]"
                  class="flex-1 py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  :required="field.required"
                >
                  <option value="">请选择模型</option>
                  <optgroup v-if="availableModels[editableConfig.provider_type] && availableModels[editableConfig.provider_type].length > 0" label="可用模型">
                    <option
                      v-for="model in availableModels[editableConfig.provider_type]"
                      :key="model.id"
                      :value="model.id"
                    >
                      {{ model.displayName || model.id }} {{ model.owned_by ? `(${model.owned_by})` : '' }}
                    </option>
                  </optgroup>
                  <optgroup v-if="field.fallback_models" label="常用模型">
                    <option
                      v-for="model in field.fallback_models"
                      :key="model"
                      :value="model"
                    >
                      {{ model }}
                    </option>
                  </optgroup>
                </select>
                <button
                  type="button"
                  @click="loadModels(editableConfig.provider_type, key)"
                  :disabled="!hasRequiredCredentials || isLoadingModels[editableConfig.provider_type]"
                  class="px-3 py-2 bg-green-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="获取最新模型列表"
                >
                  <span v-if="isLoadingModels[editableConfig.provider_type]" class="flex items-center">
                    <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </span>
                  <span v-else>🔄</span>
                </button>
              </div>

              <!-- Custom model input -->
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">或输入自定义模型:</span>
                <input
                  type="text"
                  v-model="editableConfig.attributes[key]"
                  class="flex-1 py-1 px-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  :placeholder="field.placeholder || ''"
                />
              </div>

              <!-- Model loading status -->
              <div v-if="modelLoadErrors[editableConfig.provider_type]" class="text-xs text-red-600">
                获取模型列表失败: {{ modelLoadErrors[editableConfig.provider_type] }}
              </div>
              <div v-else-if="availableModels[editableConfig.provider_type] && availableModels[editableConfig.provider_type].length > 0" class="text-xs text-green-600">
                已加载 {{ availableModels[editableConfig.provider_type].length }} 个可用模型
              </div>
            </div>

            <!-- Regular input fields -->
            <input
              v-else-if="field.type === 'text' || field.type === 'number' || field.type === 'url'"
              :type="field.type === 'number' ? 'number' : 'text'"
              :id="`attr-${key}`"
              v-model="editableConfig.attributes[key]"
              class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              :placeholder="field.placeholder || ''"
              :required="field.required"
              :step="field.type === 'number' && field.step ? field.step : undefined"
              :min="field.type === 'number' && field.min !== undefined ? field.min : undefined"
              :max="field.type === 'number' && field.max !== undefined ? field.max : undefined"
            />
            <!-- Add other input types like select, checkbox if needed based on schema -->
            <p v-if="field.description" class="text-xs text-gray-500 mt-1">{{ field.description }}</p>
          </div>
        </div>
      </div>
      <div v-else-if="editableConfig.provider_type && !currentProviderSchema" class="text-center text-gray-500 py-4">
        未找到针对 "{{ editableConfig.provider_type }}" 类型的配置定义。
      </div>


      <!-- Test Connection Section -->
      <div v-if="editableConfig.provider_type && currentProviderSchema && hasRequiredCredentials" class="mt-6 p-4 border border-blue-200 bg-blue-50 rounded-md">
        <h4 class="text-md font-semibold mb-3 text-blue-800">测试连接</h4>
        <p class="text-sm text-blue-700 mb-3">在保存配置前，您可以测试 API 连接是否正常工作。</p>

        <!-- Test Results Display -->
        <div v-if="testResult" class="mb-3 p-3 rounded-md" :class="testResult.success ? 'bg-green-100 border border-green-300' : 'bg-red-100 border border-red-300'">
          <div class="flex items-start">
            <svg v-if="testResult.success" class="h-5 w-5 text-green-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <svg v-else class="h-5 w-5 text-red-500 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
            <div class="flex-1">
              <p class="text-sm font-medium" :class="testResult.success ? 'text-green-800' : 'text-red-800'">
                {{ testResult.success ? '✅ 连接测试成功！' : '❌ 连接测试失败' }}
              </p>
              <p class="text-sm mt-1" :class="testResult.success ? 'text-green-700' : 'text-red-700'">
                {{ testResult.message }}
              </p>
              <div v-if="testResult.details" class="mt-2 text-xs" :class="testResult.success ? 'text-green-600' : 'text-red-600'">
                <details>
                  <summary class="cursor-pointer hover:underline">查看详细信息</summary>
                  <pre class="mt-1 whitespace-pre-wrap">{{ testResult.details }}</pre>
                </details>
              </div>
            </div>
          </div>
        </div>

        <button
          type="button"
          @click="testConnection"
          :disabled="isTestingConnection || !hasRequiredCredentials"
          class="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span v-if="isTestingConnection" class="flex items-center">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            测试中...
          </span>
          <span v-else>测试连接</span>
        </button>
      </div>

      <!-- Form Actions -->
      <div class="mt-8 flex justify-end space-x-3">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          取消
        </button>
        <button
          type="submit"
          class="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          :disabled="!!providerIdError"
        >
          {{ isNew ? '添加服务' : '保存更改' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue';

const props = defineProps({
  serviceConfig: {
    type: Object,
    default: () => null
  },
  isNew: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['save', 'cancel']);

const editableConfig = ref({});
const originalProviderId = ref(''); // To track if provider_id changes for existing items
const providerIdError = ref('');

// Test connection related state
const isTestingConnection = ref(false);
const testResult = ref(null);

// Model list related state
const availableModels = ref({});
const isLoadingModels = ref({});
const modelLoadErrors = ref({});

// Define available provider types and their schemas
// This should ideally come from a shared configuration or be more dynamic
const providerSchemas = {
  OpenAI: {
    credentials: {
      api_key: { label: 'API Key', type: 'password', required: true, placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: '您的 OpenAI API 密钥。' }
    },
    attributes: {
      api_base_url: { label: 'API Base URL', type: 'url', required: false, placeholder: 'https://api.openai.com/v1', description: 'OpenAI API 的基础地址，留空使用官方默认。' },
      default_model: {
        label: '默认模型',
        type: 'model_select',
        required: true,
        placeholder: 'gpt-4o',
        description: '选择或输入模型名称',
        fallback_models: ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-3.5-turbo-16k']
      },
      timeout_seconds: { label: '超时时间 (秒)', type: 'number', required: false, placeholder: '60', min: 1 },
      max_tokens: { label: '最大 Tokens', type: 'number', required: false, placeholder: '4096', min: 1 },
      temperature: { label: 'Temperature', type: 'number', required: false, placeholder: '0.7', min:0, max:2, step: 0.1 },
    }
  },
  DeepSeek: {
    credentials: {
      api_key: { label: 'API Key', type: 'password', required: true, placeholder: 'sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx', description: '您的 DeepSeek API 密钥。' }
    },
    attributes: {
      api_base_url: { label: 'API Base URL', type: 'url', required: false, placeholder: 'https://api.deepseek.com', description: 'DeepSeek API 的基础地址，留空使用官方默认。' },
      default_model: {
        label: '默认模型',
        type: 'model_select',
        required: true,
        placeholder: 'deepseek-chat',
        description: '选择或输入模型名称',
        fallback_models: ['deepseek-chat', 'deepseek-coder', 'deepseek-math']
      }
    }
  },
  Gemini: {
    credentials: {
      api_key: { label: 'API Key', type: 'password', required: true, placeholder: 'AIzaSyXXXXXXXXXXXXXXXXXXXXXXXXX', description: '您的 Google AI Gemini API 密钥。' }
    },
    attributes: {
      // Gemini API usually doesn't use a base URL in the same way, but model name is key.
      // api_base_url: { label: 'API Base URL', type: 'url', required: false, placeholder: 'https://generativelanguage.googleapis.com' },
      default_model: {
        label: '默认模型',
        type: 'model_select',
        required: true,
        placeholder: 'gemini-2.0-flash',
        description: '选择或输入模型名称',
        fallback_models: [
          'gemini-2.0-flash',
          'gemini-2.0-flash-001',
          'gemini-1.5-pro-latest',
          'gemini-1.5-flash-latest',
          'gemini-1.5-flash-8b-latest',
          'gemini-1.0-pro'
        ]
      }
    }
  },
  // CustomLLM might be a future addition, requiring more generic fields or user-defined fields
};

const availableProviderTypes = ref([
  { value: 'OpenAI', label: 'OpenAI' },
  { value: 'DeepSeek', label: 'DeepSeek' },
  { value: 'Gemini', label: 'Gemini' },
  // { value: 'CustomLLM', label: '自定义 LLM' }
]);

const currentProviderSchema = computed(() => {
  return editableConfig.value.provider_type ? providerSchemas[editableConfig.value.provider_type] : null;
});

// Check if all required credentials are filled
const hasRequiredCredentials = computed(() => {
  if (!currentProviderSchema.value || !currentProviderSchema.value.credentials) {
    return false;
  }

  for (const key in currentProviderSchema.value.credentials) {
    const field = currentProviderSchema.value.credentials[key];
    if (field.required && (!editableConfig.value.credentials[key] || editableConfig.value.credentials[key].trim() === '')) {
      return false;
    }
  }
  return true;
});

const showPasswords = ref({}); // To toggle password visibility for each field

const formTitle = computed(() => {
  if (props.isNew) {
    return '添加新的 AI 服务';
  }
  return `编辑 ${editableConfig.value.display_name || '服务'} 配置`;
});

watch(() => props.serviceConfig, (newConfig) => {
  if (newConfig) {
    // Deep clone to prevent modifying the original object in the parent list directly
    editableConfig.value = JSON.parse(JSON.stringify(newConfig));
    if (!editableConfig.value.credentials) editableConfig.value.credentials = {};
    if (!editableConfig.value.attributes) editableConfig.value.attributes = {};
    if (!editableConfig.value.metadata) editableConfig.value.metadata = {};

    if (!props.isNew) {
      originalProviderId.value = newConfig.provider_id;
    } else {
      originalProviderId.value = ''; // No original ID for new services
      // Suggest a unique ID for new services if provider_type is selected
      if (editableConfig.value.provider_type) {
         editableConfig.value.provider_id = `${editableConfig.value.provider_type.toLowerCase()}_${Date.now().toString().slice(-5)}`;
      }
    }
    // Initialize showPasswords for any password fields
    showPasswords.value = {};
    if (currentProviderSchema.value && currentProviderSchema.value.credentials) {
      Object.keys(currentProviderSchema.value.credentials).forEach(key => {
        if (currentProviderSchema.value.credentials[key].type === 'password') {
          showPasswords.value[key] = false;
        }
      });
    }
  } else {
    editableConfig.value = {}; // Clear form if no service is selected (e.g. after cancel)
    originalProviderId.value = '';
  }
  providerIdError.value = ''; // Reset error on config change
  testResult.value = null; // Clear test results when config changes
}, { immediate: true, deep: true });

// Watch for changes in credentials to clear test results and auto-load models
watch(() => editableConfig.value.credentials, (newCredentials, oldCredentials) => {
  if (testResult.value) {
    testResult.value = null; // Clear test results when credentials change
  }

  // Auto-load models when API key is provided and valid
  if (newCredentials?.api_key &&
      newCredentials.api_key !== oldCredentials?.api_key &&
      editableConfig.value.provider_type &&
      hasRequiredCredentials.value) {

    // Debounce the auto-load to avoid too many requests
    setTimeout(() => {
      if (hasRequiredCredentials.value) {
        console.log('Auto-loading models due to API key change');
        loadModels(editableConfig.value.provider_type, 'default_model');
      }
    }, 1000);
  }
}, { deep: true });

// Watch for changes in attributes to clear test results
watch(() => editableConfig.value.attributes, () => {
  if (testResult.value) {
    testResult.value = null; // Clear test results when attributes change
  }
}, { deep: true });


const handleProviderTypeChange = () => {
  // Reset credentials and attributes when type changes for a new service
  if (props.isNew) {
    editableConfig.value.credentials = {};
    editableConfig.value.attributes = {};
    // Suggest a new provider_id based on type
    if (editableConfig.value.provider_type) {
        editableConfig.value.provider_id = `${editableConfig.value.provider_type.toLowerCase()}_${Date.now().toString().slice(-5)}`;
    } else {
        editableConfig.value.provider_id = `new_service_${Date.now().toString().slice(-5)}`;
    }
  }
  // Initialize showPasswords for the new schema
  showPasswords.value = {};
  if (currentProviderSchema.value && currentProviderSchema.value.credentials) {
    Object.keys(currentProviderSchema.value.credentials).forEach(key => {
      if (currentProviderSchema.value.credentials[key].type === 'password') {
        showPasswords.value[key] = false;
      }
    });
  }
   providerIdError.value = ''; // Reset error on type change
   testResult.value = null; // Clear test results when provider type changes
};

const validateProviderId = () => {
  if (!editableConfig.value.provider_id || editableConfig.value.provider_id.trim() === '') {
    providerIdError.value = '服务 ID 不能为空。';
    return;
  }
  // Basic validation for provider_id format (e.g., no spaces, etc.)
  if (/\s/.test(editableConfig.value.provider_id)) {
    providerIdError.value = '服务 ID 不能包含空格。';
    return;
  }
  // Uniqueness check will be handled in the parent component (AISettingsView) upon save,
  // as this form doesn't have access to the full list of services.
  // However, we clear local error if format is okay.
  providerIdError.value = '';
};


const togglePasswordVisibility = (key) => {
  showPasswords.value[key] = !showPasswords.value[key];
};

// Load models method
const loadModels = async (providerType, fieldKey) => {
  if (!providerType || !hasRequiredCredentials.value) {
    return;
  }

  isLoadingModels.value[providerType] = true;
  modelLoadErrors.value[providerType] = null;

  try {
    // Create a configuration object for model loading
    const loadConfig = {
      provider_type: providerType,
      credentials: { ...editableConfig.value.credentials },
      attributes: { ...editableConfig.value.attributes }
    };

    console.log('Loading models for provider:', providerType);

    // Call the load models API through IPC
    const result = await window.electronAPI.invoke('load-ai-models', loadConfig);

    if (result.success && result.models) {
      availableModels.value[providerType] = result.models;
      console.log(`Loaded ${result.models.length} models for ${providerType}`);
    } else {
      modelLoadErrors.value[providerType] = result.message || '获取模型列表失败';
      console.error('Failed to load models:', result.message);
    }
  } catch (error) {
    console.error('Error loading models:', error);
    modelLoadErrors.value[providerType] = error.message || '网络错误';
  } finally {
    isLoadingModels.value[providerType] = false;
  }
};

// Test connection method
const testConnection = async () => {
  if (!hasRequiredCredentials.value) {
    testResult.value = {
      success: false,
      message: '请先填写所有必需的凭证信息',
      details: null
    };
    return;
  }

  isTestingConnection.value = true;
  testResult.value = null;

  try {
    // Create a test configuration object
    const testConfig = {
      provider_id: editableConfig.value.provider_id || 'test_connection',
      provider_type: editableConfig.value.provider_type,
      display_name: editableConfig.value.display_name || 'Test Connection',
      is_enabled: true,
      credentials: { ...editableConfig.value.credentials },
      attributes: { ...editableConfig.value.attributes },
      metadata: { test_mode: true }
    };

    console.log('Testing AI service connection:', testConfig.provider_type);

    // Call the test API through IPC
    const result = await window.electronAPI.invoke('test-ai-service-connection', testConfig);

    if (result.success) {
      testResult.value = {
        success: true,
        message: result.message || `${testConfig.provider_type} API 连接成功！`,
        details: result.details || null
      };
    } else {
      testResult.value = {
        success: false,
        message: result.message || '连接测试失败',
        details: result.details || result.error || null
      };
    }
  } catch (error) {
    console.error('Error testing AI service connection:', error);
    testResult.value = {
      success: false,
      message: '测试连接时发生错误',
      details: error.message || error.toString()
    };
  } finally {
    isTestingConnection.value = false;
  }
};

const submitForm = () => {
  validateProviderId();
  if (providerIdError.value) {
    // alert('请修正服务 ID 的错误。'); // Or more sophisticated error display
    return;
  }

  // Ensure all required fields for the selected provider type are filled
  if (currentProviderSchema.value) {
    for (const section of ['credentials', 'attributes']) {
      if (currentProviderSchema.value[section]) {
        for (const key in currentProviderSchema.value[section]) {
          const fieldSchema = currentProviderSchema.value[section][key];
          if (fieldSchema.required && (editableConfig.value[section][key] === undefined || editableConfig.value[section][key] === '')) {
            alert(`错误：字段 "${fieldSchema.label}" 是必填项。`);
            return;
          }
        }
      }
    }
  }


  const configToSave = JSON.parse(JSON.stringify(editableConfig.value));
  if (!props.isNew) {
    configToSave.provider_id_original = originalProviderId.value;
  }

  emit('save', configToSave);
};

onMounted(() => {
  // If it's a new service and a type is pre-selected (e.g. from parent), initialize
  if (props.isNew && editableConfig.value.provider_type) {
    handleProviderTypeChange(); // To set up schema and default ID
  }
});

</script>

<style scoped>
.dot {
  transition: transform .3s ease-in-out;
}
/* Add any additional scoped styles here */
</style>
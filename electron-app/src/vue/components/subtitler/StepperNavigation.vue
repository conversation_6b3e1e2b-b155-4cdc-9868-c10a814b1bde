<template>
  <div class="stepper-navigation-container">
    <!-- 工作流程状态指示 -->
    <div class="workflow-status mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <svg class="h-4 w-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <span class="text-sm font-medium text-blue-800">当前工作流程</span>
        </div>
        <span class="text-xs text-blue-600">{{ workflowDescription }}</span>
      </div>
    </div>

    <!-- 导航栏 -->
    <nav class="stepper-nav bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl shadow-lg border border-blue-100 px-6 py-6">
      <!-- 滚动容器 -->
      <div class="nav-scroll-container overflow-x-auto overflow-y-hidden" ref="navContainer">
        <ol class="flex items-center space-x-3 sm:space-x-4 min-w-max py-4 px-4">
          <li v-for="step in steps" :key="step.id" class="flex items-center">
            <button
              @click="navigateToStep(step.id)"
              :disabled="!isStepReachable(step.id)"
              :class="[
                'step-button flex flex-col items-center justify-center rounded-full text-xs font-medium transition-all duration-200 ease-in-out shadow-md hover:shadow-lg',
                'w-16 h-16 sm:w-18 sm:h-18 md:w-20 md:h-20',
                currentStep === step.id
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white ring-4 ring-blue-200 scale-110 z-10'
                  : isStepCompleted(step.id)
                  ? 'bg-gradient-to-r from-green-400 to-emerald-500 text-white hover:from-green-500 hover:to-emerald-600'
                  : isStepReachable(step.id)
                  ? 'bg-white text-gray-700 hover:bg-gray-50 border-2 border-gray-200 hover:border-blue-300'
                  : 'bg-gray-100 text-gray-400 cursor-not-allowed border-2 border-gray-200',
              ]"
              :title="`步骤 ${step.id}: ${step.name}`"
            >
              <span class="text-lg sm:text-xl md:text-2xl font-bold">{{ step.id }}</span>
              <span class="text-xs leading-tight text-center px-1 mt-0.5">{{ step.name }}</span>
            </button>
            <!-- Separator, not shown for the last item -->
            <div v-if="step.id < steps.length" class="flex items-center mx-2 sm:mx-3">
              <div class="w-5 h-0.5 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-full"></div>
              <svg
                class="h-3 w-3 text-blue-400 mx-1"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                />
              </svg>
              <div class="w-5 h-0.5 bg-gradient-to-r from-blue-200 to-indigo-200 rounded-full"></div>
            </div>
          </li>
        </ol>
      </div>

      <!-- 滚动指示器 -->
      <div class="scroll-indicators flex justify-center mt-4 space-x-3">
        <button
          @click="scrollToStart"
          class="scroll-btn px-4 py-2 text-xs bg-white text-blue-600 rounded-full border border-blue-200 hover:bg-blue-50 transition-colors shadow-sm"
          title="滚动到开始">
          ← 开始
        </button>
        <button
          @click="scrollToCurrent"
          class="scroll-btn px-4 py-2 text-xs bg-blue-100 text-blue-700 rounded-full border border-blue-300 hover:bg-blue-200 transition-colors shadow-sm"
          title="滚动到当前步骤">
          当前步骤
        </button>
        <button
          @click="scrollToEnd"
          class="scroll-btn px-4 py-2 text-xs bg-white text-blue-600 rounded-full border border-blue-200 hover:bg-blue-50 transition-colors shadow-sm"
          title="滚动到结束">
          结束 →
        </button>
      </div>
    </nav>
  </div>
</template>

<script setup>
import { computed, ref, nextTick, onMounted, watch } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore'; // 确保路径正确

const subtitlerStore = useSubtitlerStore();
const navContainer = ref(null);

const currentStep = computed(() => subtitlerStore.currentStep);

// 工作流程描述
const workflowDescription = computed(() => {
  const completedSteps = steps.filter(step => isStepCompleted(step.id)).length;
  const totalSteps = steps.length;
  return `已完成 ${completedSteps}/${totalSteps} 个步骤`;
});

// 定义完整的分布操作步骤
const steps = [
  { id: 1, name: '上传文件' }, // Upload
  { id: 2, name: '提取音频' }, // Video to Audio
  { id: 3, name: '语音转文字' }, // Audio to Text
  { id: 4, name: '组成句子' }, // Generate Subtitles
  { id: 5, name: '优化句子' }, // Optimize Subtitles
  { id: 6, name: '编辑字幕' }, // Edit Subtitles
  { id: 7, name: '翻译选择' }, // Translation Choice
  { id: 8, name: '翻译处理' }, // Translation (conditional)
  { id: 9, name: '导出保存' }, // Export/Save
];

// 判断步骤是否已完成的逻辑 (示例)
// 实际应用中，可能需要更复杂的状态来跟踪每个步骤的完成情况
const isStepCompleted = (stepId) => {
  if (stepId < currentStep.value) return true;
  // 例如，如果视频转音频完成，则步骤2完成
  if (stepId === 2 && subtitlerStore.videoToAudioResult) return true;
  if (stepId === 3 && subtitlerStore.audioToTextResult) return true;
  // 假设 store 中会有一个状态 `editedSubtitlesSaved` 来标记编辑步骤是否已保存/完成
  if (stepId === 4 && subtitlerStore.editedSubtitlesSaved) return true; // Assuming save in edit step marks it complete for navigation
  // For step 5 (Export), completion could be marked by a successful export
  if (stepId === 5 && subtitlerStore.lastExportPath) return true;
  // ... 其他步骤的完成条件
  return false;
};

// 判断步骤是否可达 (例如，用户是否可以点击跳转)
const isStepReachable = (stepId) => {
  // 更灵活的步骤跳转逻辑

  // 总是允许跳转到当前步骤
  if (stepId === currentStep.value) return true;

  // 允许跳转到任何已完成的步骤
  if (isStepCompleted(stepId)) return true;

  // 允许在已达到的最高步骤范围内自由跳转
  const maxReachedStep = Math.max(currentStep.value, getMaxCompletedStep());

  // 基本的前置条件检查
  switch (stepId) {
    case 1: return true; // 总是可以回到上传步骤
    case 2: return subtitlerStore.uploadedFile !== null;
    case 3: return subtitlerStore.videoToAudioResult !== null;
    case 4: return subtitlerStore.audioToTextResult !== null;
    case 5: return subtitlerStore.audioToTextResult !== null; // 可以跳过优化
    case 6: return subtitlerStore.audioToTextResult !== null; // 编辑步骤
    case 7: return subtitlerStore.editableSegments !== null; // 翻译选择
    case 8: return subtitlerStore.editableSegments !== null; // 翻译处理
    case 9: return subtitlerStore.editableSegments !== null; // 导出
    default: return stepId <= maxReachedStep;
  }
};

// 获取已完成的最高步骤
const getMaxCompletedStep = () => {
  let maxStep = 1;
  if (subtitlerStore.uploadedFile) maxStep = Math.max(maxStep, 1);
  if (subtitlerStore.videoToAudioResult) maxStep = Math.max(maxStep, 2);
  if (subtitlerStore.audioToTextResult) maxStep = Math.max(maxStep, 3);
  if (subtitlerStore.editableSegments) maxStep = Math.max(maxStep, 6);
  if (subtitlerStore.translatedSubtitles) maxStep = Math.max(maxStep, 8);
  if (subtitlerStore.lastExportPath || subtitlerStore.exportResults?.length > 0) maxStep = Math.max(maxStep, 9);
  return maxStep;
};

const navigateToStep = (stepId) => {
  if (isStepReachable(stepId)) {
    subtitlerStore.setCurrentStep(stepId);
    // 导航后滚动到当前步骤
    nextTick(() => {
      scrollToCurrent();
    });
  }
};

// 滚动控制方法
const scrollToStart = () => {
  if (navContainer.value) {
    navContainer.value.scrollTo({
      left: 0,
      behavior: 'smooth'
    });
  }
};

const scrollToEnd = () => {
  if (navContainer.value) {
    navContainer.value.scrollTo({
      left: navContainer.value.scrollWidth,
      behavior: 'smooth'
    });
  }
};

const scrollToCurrent = () => {
  if (navContainer.value) {
    const currentStepButton = navContainer.value.querySelector(`button[title*="步骤 ${currentStep.value}"]`);
    if (currentStepButton) {
      const containerRect = navContainer.value.getBoundingClientRect();
      const buttonRect = currentStepButton.getBoundingClientRect();
      const scrollLeft = buttonRect.left - containerRect.left + navContainer.value.scrollLeft - (containerRect.width / 2) + (buttonRect.width / 2);

      navContainer.value.scrollTo({
        left: Math.max(0, scrollLeft),
        behavior: 'smooth'
      });
    }
  }
};

// 监听当前步骤变化，自动滚动到当前步骤
watch(currentStep, () => {
  nextTick(() => {
    scrollToCurrent();
  });
});

// 组件挂载后滚动到当前步骤
onMounted(() => {
  nextTick(() => {
    scrollToCurrent();
  });
});
</script>

<style scoped>
/* 导航容器样式 */
.stepper-navigation-container {
  width: 100%;
  max-width: 100%;
}

/* 滚动容器样式 */
.nav-scroll-container {
  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
  /* 确保有足够的高度，防止垂直滚动 */
  min-height: 120px;
  /* 隐藏垂直滚动条 */
  overflow-y: hidden;
}

.nav-scroll-container::-webkit-scrollbar {
  height: 6px;
}

.nav-scroll-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.nav-scroll-container::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.nav-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 步骤按钮样式 */
.step-button {
  min-width: 4rem; /* 确保最小宽度 */
  min-height: 4rem;
  flex-shrink: 0; /* 防止收缩 */
  /* 确保按钮内容不会溢出 */
  overflow: hidden;
}

.step-button:disabled {
  opacity: 0.7;
}

/* 滚动指示器按钮样式 */
.scroll-btn {
  flex-shrink: 0;
  min-width: fit-content;
  white-space: nowrap;
}

/* 导航栏样式 */
.stepper-nav {
  /* 确保导航栏有足够的高度 */
  min-height: 180px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stepper-nav {
    padding: 1rem;
    min-height: 160px;
  }

  .nav-scroll-container {
    min-height: 100px;
  }

  .step-button {
    min-width: 3.5rem;
    min-height: 3.5rem;
  }

  .scroll-indicators {
    margin-top: 0.75rem;
  }

  .scroll-btn {
    padding: 0.375rem 0.75rem;
    font-size: 0.625rem;
  }
}

@media (max-width: 640px) {
  .stepper-nav {
    padding: 0.75rem;
    min-height: 140px;
  }

  .nav-scroll-container {
    min-height: 80px;
  }

  .step-button {
    min-width: 3rem;
    min-height: 3rem;
  }

  .scroll-indicators {
    margin-top: 0.5rem;
  }

  .scroll-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.625rem;
  }
}

/* 工作流程状态样式 */
.workflow-status {
  backdrop-filter: blur(10px);
  background: rgba(239, 246, 255, 0.8);
}

/* 导航栏样式 */
.stepper-nav {
  backdrop-filter: blur(10px);
  background: rgba(239, 246, 255, 0.9);
}

/* 动画效果 */
.step-button {
  transform-origin: center;
}

.step-button:hover:not(:disabled) {
  transform: scale(1.05);
}

.step-button.scale-110 {
  transform: scale(1.1);
  z-index: 10;
  position: relative;
}
</style>
<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 4: 组成句子</h3>
      <p class="text-sm text-gray-500 mt-1">将转录文本组成带时间戳的字幕句子</p>
    </div>

    <!-- 前置条件警告 -->
    <StatusMessage
      v-if="!hasAudioToTextResult"
      type="warning"
      title="需要先完成音频转录"
      message="请先完成语音转文字处理，获取转录文本后再进行句子组成"
      :actions="[
        { key: 'goto-transcription', label: '前往语音转文字', icon: '🎤', primary: true }
      ]"
      @action="handlePrerequisiteAction"
    />

    <!-- 转录文本预览 -->
    <div v-if="hasAudioToTextResult" class="mb-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-blue-800">📝 转录文本预览</h4>
          <span class="text-xs text-blue-600">{{ transcriptLength }} 字符</span>
        </div>

        <div class="bg-white border border-blue-200 rounded-md p-3 max-h-32 overflow-y-auto">
          <p class="text-gray-700 text-sm leading-relaxed">{{ transcriptPreview }}</p>
        </div>
      </div>
    </div>

    <!-- 句子组成操作 -->
    <div v-if="hasAudioToTextResult && !isLoading" class="current-actions mb-6">
      <div class="flex space-x-3">
        <button
          @click="generateSubtitles"
          :disabled="isLoading || hasGeneratedSubtitles"
          class="flex-1 px-6 py-3 text-white bg-yellow-600 rounded-lg hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          📝 {{ hasGeneratedSubtitles ? '句子已组成' : '开始组成句子' }}
        </button>

        <button
          v-if="hasGeneratedSubtitles"
          @click="goToNextStep"
          class="px-6 py-3 text-yellow-700 bg-yellow-100 rounded-lg hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 font-medium transition-colors text-base"
        >
          ➡️ 下一步
        </button>
      </div>
    </div>

    <!-- 组成进度显示 -->
    <div v-if="isLoading" class="mb-6">
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-yellow-800">📝 正在组成句子</h4>
          <span class="text-xs text-yellow-600">{{ formatTime(new Date()) }}</span>
        </div>

        <div class="mb-3">
          <div class="flex items-center justify-between text-sm mb-1">
            <span class="text-yellow-700">📊 组成进度</span>
            <span class="text-yellow-700">{{ Math.round(generationProgress || 0) }}%</span>
          </div>
          <div class="w-full bg-yellow-200 rounded-full h-2">
            <div
              class="bg-yellow-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min(100, Math.max(0, generationProgress || 0))}%` }"
            />
          </div>
        </div>

        <div class="text-sm text-yellow-600">
          ⏱️ {{ progressText || '正在将转录文本组成带时间戳的字幕句子...' }}
        </div>
      </div>
    </div>

    <!-- 组成完成显示 -->
    <div v-if="hasGeneratedSubtitles && !isLoading" class="mb-6">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-green-800">✅ 句子组成完成</h4>
        </div>

        <p class="text-sm text-green-700 mb-3">{{ generationSummary }}</p>

        <!-- 组成统计 -->
        <div class="bg-white rounded-md p-3 border border-green-200">
          <div class="text-xs text-green-600 mb-1">📊 组成统计</div>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-gray-600">句子数量:</span>
              <span class="text-gray-800 font-medium ml-1">{{ generatedSubtitlesCount }}</span>
            </div>
            <div>
              <span class="text-gray-600">平均长度:</span>
              <span class="text-gray-800 font-medium ml-1">{{ averageLength }}字</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 可折叠的组成日志 -->
    <CollapsibleLog
      v-if="subtitlerStore.progressUpdates.length > 0"
      title="组成日志"
      :logs="formattedLogs"
      :initial-expanded="false"
      :show-stats="true"
      @clear="clearLogs"
      @copy="handleLogCopy"
    />

  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import StatusMessage from '@/vue/components/common/StatusMessage.vue';
import CollapsibleLog from '@/vue/components/common/CollapsibleLog.vue';

const subtitlerStore = useSubtitlerStore();

// 响应式数据
const generationProgress = ref(0);
const progressText = ref('');

// 计算属性
const isLoading = computed(() => subtitlerStore.isLoading);
const hasAudioToTextResult = computed(() => subtitlerStore.audioToTextResult && subtitlerStore.audioToTextResult.transcript);
const hasGeneratedSubtitles = computed(() => subtitlerStore.generatedSubtitles && subtitlerStore.generatedSubtitles.length > 0);
const generatedSubtitlesCount = computed(() => subtitlerStore.generatedSubtitles ? subtitlerStore.generatedSubtitles.length : 0);

const transcriptPreview = computed(() => {
  if (!hasAudioToTextResult.value) return '';
  const transcript = subtitlerStore.audioToTextResult.transcript;
  return transcript.length > 200 ? transcript.substring(0, 200) + '...' : transcript;
});

const transcriptLength = computed(() => {
  if (!hasAudioToTextResult.value) return 0;
  return subtitlerStore.audioToTextResult.transcript.length;
});

const generationSummary = computed(() => {
  if (!hasGeneratedSubtitles.value) return '';

  return `成功将转录文本组成 ${generatedSubtitlesCount.value} 个字幕句子，可以进入下一步进行优化`;
});

const averageLength = computed(() => {
  if (!hasGeneratedSubtitles.value) return 0;

  const totalLength = subtitlerStore.generatedSubtitles.reduce((sum, subtitle) => {
    return sum + (subtitle.text ? subtitle.text.length : 0);
  }, 0);

  return Math.round(totalLength / generatedSubtitlesCount.value);
});

const formattedLogs = computed(() => {
  return subtitlerStore.progressUpdates.map(update => ({
    timestamp: new Date().toISOString(),
    level: update.includes('✅') ? 'success' :
           update.includes('❌') ? 'error' :
           update.includes('⚠️') ? 'warning' : 'info',
    message: update
  }));
});



// 方法
const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const generateSubtitles = async () => {
  if (!hasAudioToTextResult.value) return;

  // 设置进度
  generationProgress.value = 0;
  progressText.value = '准备组成句子...';

  try {
    await subtitlerStore.generateSubtitles();
  } catch (error) {
    console.error('生成字幕时出错:', error);
  }
};

const handlePrerequisiteAction = (action) => {
  if (action.key === 'goto-transcription') {
    subtitlerStore.setCurrentStep(2); // 前往语音转文字
  }
};

const goToNextStep = () => {
  subtitlerStore.setCurrentStep(5); // 进入字幕优化
};

const clearLogs = () => {
  subtitlerStore.progressUpdates = [];
};

const handleLogCopy = (logText) => {
  console.log('日志已复制:', logText);
};
</script>

<style scoped>
/* 组件特定样式 */
</style>

<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 3: 编辑字幕</h3>
      <p class="text-sm text-gray-500 mt-1">编辑和调整字幕内容、时间戳</p>
    </div>

    <!-- 前置条件警告 -->
    <StatusMessage
      v-if="prerequisiteWarning"
      type="warning"
      :title="prerequisiteWarning.title"
      :message="prerequisiteWarning.message"
      :actions="[
        { key: 'goto-transcription', label: prerequisiteWarning.actionText, icon: '🎤', primary: true }
      ]"
      @action="handlePrerequisiteAction"
    />

    <!-- 当前操作区域 -->
    <div v-if="hasSegments" class="current-actions mb-6">
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-3">编辑设置</h4>

        <label for="useTranscription" class="flex items-center space-x-2 text-sm text-gray-700">
          <input
            type="checkbox"
            id="useTranscription"
            v-model="useTranscription"
            class="rounded border-gray-300 text-red-600 shadow-sm focus:border-red-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
          />
          <span>使用上一步的转录结果进行编辑</span>
        </label>
        <p v-if="useTranscription && !audioToTextResultAvailable" class="text-xs text-yellow-600 mt-1">
          上一步未生成转录结果，或已被清除。
        </p>
      </div>

      <!-- 字幕编辑界面 -->
      <div class="flex flex-col lg:flex-row gap-4">
        <!-- 片段列表 -->
        <div class="lg:w-1/2">
          <div class="bg-white border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-700">字幕片段 ({{ editableSegments.length }})</h4>
              <button
                @click="addNewSegment"
                class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 text-sm font-medium transition-colors">
                添加片段
              </button>
            </div>

            <div class="space-y-2">
              <div
                v-for="segment in editableSegments"
                :key="segment.id"
                @click="selectSegmentForEdit(segment.id)"
                class="p-3 border rounded-md cursor-pointer hover:bg-gray-50 transition-colors"
                :class="{ 'bg-red-50 border-red-300': segment.id === selectedSegmentId, 'border-gray-200': segment.id !== selectedSegmentId }"
              >
                <div class="flex justify-between items-start mb-1">
                  <span class="text-xs text-gray-500 font-mono">
                    {{ formatTime(segment.startTimeMs) }} - {{ formatTime(segment.endTimeMs) }}
                  </span>
                  <button
                    v-if="selectedSegmentId === segment.id"
                    @click.stop="confirmDeleteSegment(segment.id)"
                    class="text-red-500 hover:text-red-700 text-sm p-1 rounded hover:bg-red-100"
                    title="删除片段">
                    ×
                  </button>
                </div>
                <p class="text-sm text-gray-700 leading-relaxed">{{ segment.text }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 编辑区域 -->
        <div class="lg:w-1/2">
          <div class="bg-white border border-gray-200 rounded-lg p-4 min-h-[300px]">
            <div v-if="currentEditingSegment">
              <h4 class="text-sm font-medium text-gray-700 mb-3">
                编辑片段 #{{ currentEditingSegment.id.substring(0,8) }}...
              </h4>

              <div class="space-y-4">
                <div>
                  <label :for="'segment-text-' + currentEditingSegment.id" class="block text-sm font-medium text-gray-700 mb-2">
                    文本内容
                  </label>
                  <textarea
                    :id="'segment-text-' + currentEditingSegment.id"
                    v-model="editableText"
                    @input="markChanges"
                    rows="4"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    placeholder="输入字幕文本..."
                  ></textarea>
                </div>

                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label :for="'start-time-' + currentEditingSegment.id" class="block text-sm font-medium text-gray-700 mb-2">
                      开始时间 (ms)
                    </label>
                    <input
                      type="number"
                      :id="'start-time-' + currentEditingSegment.id"
                      v-model.number="editableStartTimeMs"
                      @input="markChanges"
                      min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    />
                  </div>
                  <div>
                    <label :for="'end-time-' + currentEditingSegment.id" class="block text-sm font-medium text-gray-700 mb-2">
                      结束时间 (ms)
                    </label>
                    <input
                      type="number"
                      :id="'end-time-' + currentEditingSegment.id"
                      v-model.number="editableEndTimeMs"
                      @input="markChanges"
                      min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500"
                    />
                  </div>
                </div>

                <div class="flex justify-end space-x-3">
                  <button
                    v-if="hasPendingChanges"
                    @click="revertSegmentChanges"
                    class="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    撤销
                  </button>
                  <button
                    @click="applySegmentChanges"
                    :disabled="!hasPendingChanges"
                    class="px-4 py-2 text-white bg-red-600 rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                    应用更改
                  </button>
                </div>
              </div>
            </div>
            <div v-else class="flex items-center justify-center h-full text-gray-500">
              <div class="text-center">
                <svg class="h-12 w-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                <p>请从左侧列表中选择一个片段进行编辑</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无片段时的提示 -->
    <div v-else-if="!hasSegments" class="current-actions mb-6">
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
        <svg class="h-12 w-12 text-yellow-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <h3 class="text-lg font-medium text-yellow-800 mb-2">没有可编辑的字幕片段</h3>
        <p class="text-yellow-700 mb-4">{{ noSegmentsMessage }}</p>

        <div class="space-y-3">
          <label for="useTranscription" class="flex items-center justify-center space-x-2 text-sm text-yellow-700">
            <input
              type="checkbox"
              id="useTranscription"
              v-model="useTranscription"
              class="rounded border-yellow-300 text-yellow-600 shadow-sm focus:border-yellow-300 focus:ring focus:ring-yellow-200 focus:ring-opacity-50"
            />
            <span>使用上一步的转录结果进行编辑</span>
          </label>

          <button
            v-if="useTranscription && audioToTextResultAvailable"
            @click="initializeSegments"
            class="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2">
            从转录结果初始化片段
          </button>
        </div>
      </div>
    </div>

  </div>

  <!-- 下一步操作 (仅在编辑完成且无未保存更改时显示) -->
  <div
    v-if="hasSegments && !hasPendingChanges"
    class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"
  >
    <div class="flex items-center justify-between">
      <div>
        <h4 class="text-sm font-medium text-green-800">字幕编辑完成</h4>
        <p class="text-sm text-green-600 mt-1">已编辑 {{ editableSegments.length }} 个字幕片段，可以进入下一步</p>
      </div>
      <button
        @click="goToNextStep"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
      >
        ➡️ 进入下一步
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import { storeToRefs } from 'pinia';
import StatusMessage from '@/vue/components/common/StatusMessage.vue';

const subtitlerStore = useSubtitlerStore();
const { 
  isLoading, 
  editableSegments,
  selectedSegmentId,
  audioToTextResult, // 用于初始化
  useTranscriptionForEditing // 新增的 store state
} = storeToRefs(subtitlerStore);

const useTranscription = computed({
  get: () => subtitlerStore.useTranscriptionForEditing,
  set: (value) => {
    subtitlerStore.setUseTranscriptionForEditing(value);
    // 如果切换为 true 且有转录结果，则尝试初始化
    // 如果切换为 false，store action 会处理清空 editableSegments
    if (value && audioToTextResult.value) {
      subtitlerStore.initializeEditableSegments();
    } else if (!value) {
      // editableSegments 会在 store action 中被清空
      // 如果需要，可以在这里重置编辑区
      editableText.value = '';
      editableStartTimeMs.value = 0;
      editableEndTimeMs.value = 0;
      hasPendingChanges.value = false;
      subtitlerStore.setSelectedSegmentId(null);
    }
  }
});

const audioToTextResultAvailable = computed(() => {
  return !!audioToTextResult.value && (!!audioToTextResult.value.segments || typeof audioToTextResult.value.transcript === 'string');
});

// 前置条件警告
const prerequisiteWarning = computed(() => {
  const hasAudioToTextResult = audioToTextResultAvailable.value;
  const hasGeneratedSubtitles = subtitlerStore.generatedSubtitles && subtitlerStore.generatedSubtitles.length > 0;
  const hasOptimizedSubtitles = subtitlerStore.optimizedSubtitles && subtitlerStore.optimizedSubtitles.length > 0;

  if (!hasAudioToTextResult && !hasGeneratedSubtitles && !hasOptimizedSubtitles) {
    return {
      title: '需要先完成语音转文字',
      message: '请先完成语音转文字处理，生成字幕内容后再进行编辑',
      actionText: '前往语音转文字'
    };
  }
  return null;
});


const currentEditingSegment = computed(() => {
  if (!selectedSegmentId.value || !editableSegments.value) return null;
  return editableSegments.value.find(s => s.id === selectedSegmentId.value) || null;
});

// 本地编辑状态，避免直接修改 store 中的 segment 直到用户确认
const editableText = ref('');
const editableStartTimeMs = ref(0);
const editableEndTimeMs = ref(0);
const hasPendingChanges = ref(false);

watch(currentEditingSegment, (newSegment) => {
  if (newSegment) {
    editableText.value = newSegment.text;
    editableStartTimeMs.value = newSegment.startTimeMs;
    editableEndTimeMs.value = newSegment.endTimeMs;
    hasPendingChanges.value = false; // Reset pending changes when segment changes
  } else {
    // Clear fields if no segment is selected
    editableText.value = '';
    editableStartTimeMs.value = 0;
    editableEndTimeMs.value = 0;
    hasPendingChanges.value = false;
  }
}, { immediate: true });

const markChanges = () => {
  hasPendingChanges.value = true;
};

const applySegmentChanges = () => {
  if (!currentEditingSegment.value || !hasPendingChanges.value) return;
  
  // Basic validation for time
  if (editableStartTimeMs.value < 0 || editableEndTimeMs.value < 0 || editableEndTimeMs.value < editableStartTimeMs.value) {
    alert('时间设置无效。结束时间必须大于等于开始时间，且两者都不能为负。');
    // Revert to original values from store to avoid inconsistent state
    editableStartTimeMs.value = currentEditingSegment.value.startTimeMs;
    editableEndTimeMs.value = currentEditingSegment.value.endTimeMs;
    return;
  }

  subtitlerStore.updateSegmentText({
    id: currentEditingSegment.value.id,
    newText: editableText.value,
  });
  subtitlerStore.updateSegmentTime({
    id: currentEditingSegment.value.id,
    newStartTimeMs: editableStartTimeMs.value,
    newEndTimeMs: editableEndTimeMs.value,
  });
  hasPendingChanges.value = false; // Reset after applying
  // Optionally, provide user feedback (e.g., a toast notification)
};

const revertSegmentChanges = () => {
  if (currentEditingSegment.value) {
    editableText.value = currentEditingSegment.value.text;
    editableStartTimeMs.value = currentEditingSegment.value.startTimeMs;
    editableEndTimeMs.value = currentEditingSegment.value.endTimeMs;
    hasPendingChanges.value = false;
  }
};

const selectSegmentForEdit = (id) => {
  if (hasPendingChanges.value) {
    if (!confirm('当前片段有未应用的更改，切换将会丢失这些更改。确定要切换吗？')) {
      return;
    }
  }
  subtitlerStore.setSelectedSegmentId(id);
};

const hasSegments = computed(() => editableSegments.value && editableSegments.value.length > 0);

const initializeSegments = () => {
  console.log('Attempting to initialize segments from EditSubtitlesStep...');
  subtitlerStore.initializeEditableSegments();
};

onMounted(() => {
  // 尝试在组件挂载时初始化，如果设置了使用转录结果且结果存在
  if (useTranscription.value && audioToTextResultAvailable.value && !hasSegments.value) {
    console.log('EditSubtitlesStep: useTranscription is true and audioToTextResult available on mount, initializing segments.');
    subtitlerStore.initializeEditableSegments();
  } else if (!audioToTextResultAvailable.value && useTranscription.value) {
     console.log('EditSubtitlesStep: useTranscription is true but audioToTextResult not available on mount.');
  } else if (!useTranscription.value) {
     console.log('EditSubtitlesStep: useTranscription is false on mount. Segments will not be auto-initialized.');
  }
});

const noSegmentsMessage = computed(() => {
  if (useTranscription.value) {
    if (audioToTextResultAvailable.value) {
      return '没有可编辑的字幕片段。点击下方按钮尝试从转录结果初始化。';
    }
    return '没有可编辑的字幕片段。请确保上一步“语音转文字”已成功完成，或检查“使用上一步的转录结果”选项。';
  }
  return '没有可编辑的字幕片段。请取消勾选“使用上一步的转录结果进行编辑”以手动添加，或确保上一步已完成并勾选该选项。';
});


const formatTime = (ms) => {
  if (typeof ms !== 'number' || isNaN(ms)) return '00:00.000';
  const date = new Date(ms);
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');
  const milliseconds = String(date.getUTCMilliseconds()).padStart(3, '0');
  return `${minutes}:${seconds}.${milliseconds}`;
};

const handlePrerequisiteAction = (action) => {
  if (action.key === 'goto-transcription') {
    subtitlerStore.setCurrentStep(2); // 前往语音转文字
  }
};

const handleResultAction = (action) => {
  if (action.key === 'next' && !hasPendingChanges.value) {
    subtitlerStore.setCurrentStep(7); // 进入翻译选择步骤
  }
};

const goToNextStep = () => {
  if (hasPendingChanges.value) {
    alert('请先应用或撤销当前片段的更改，然后进入下一步。');
    return;
  }

  // 进入字幕优化步骤
  subtitlerStore.setCurrentStep(4);
};

const addNewSegment = () => {
  let afterId = null;
  let newStartTime = 0;

  if (currentEditingSegment.value) {
    afterId = currentEditingSegment.value.id;
    newStartTime = currentEditingSegment.value.endTimeMs + 1; // Start after the current one
  } else if (editableSegments.value && editableSegments.value.length > 0) {
    const lastSegment = editableSegments.value[editableSegments.value.length - 1];
    afterId = lastSegment.id;
    newStartTime = lastSegment.endTimeMs + 1;
  }

  subtitlerStore.addSegment({ 
    afterId: afterId, 
    newSegmentData: { 
      startTimeMs: newStartTime, 
      endTimeMs: newStartTime + 2000, // Default duration 2s
      text: '新字幕片段' 
    }
  });
};

const confirmDeleteSegment = (id) => {
  if (confirm(`确定要删除片段 #${id.substring(0,8)}... 吗？此操作无法撤销。`)) {
    subtitlerStore.deleteSegment(id);
  }
};

</script>

<style scoped>
.segment-list-container {
  /* Tailwind max-h-96 is 24rem. Adjust if needed */
}
.editor-area {
  min-height: 200px; /* Ensure editor area has some height even if empty */
}
/* Additional styling can be added here */
</style>
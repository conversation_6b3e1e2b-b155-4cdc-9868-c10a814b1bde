<template>
  <div class="p-6 bg-white shadow-xl rounded-2xl border border-gray-100">
    <h2 class="text-2xl font-bold mb-6 text-gray-800 flex items-center">
      <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 text-white">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
        </svg>
      </div>
      一键字幕生成
    </h2>

    <div v-if="oneClickOperationInProgress || isLoading" class="flex items-center justify-center mb-4">
      <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="text-gray-300">Processing... Please wait.</span>
    </div>

    <div v-if="!oneClickOperationInProgress && !isLoading">
      <!-- Processing Mode Selection -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-3">处理模式:</label>
        <div class="flex space-x-4">
          <label class="flex items-center cursor-pointer">
            <input
              type="radio"
              v-model="processingMode"
              value="single"
              class="sr-only"
            />
            <div class="flex items-center">
              <div :class="[
                'w-4 h-4 rounded-full border-2 mr-2 transition-all duration-200',
                processingMode === 'single'
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              ]">
                <div v-if="processingMode === 'single'" class="w-2 h-2 bg-white rounded-full m-0.5"></div>
              </div>
              <span class="text-sm text-gray-700">单文件处理</span>
            </div>
          </label>
          <label class="flex items-center cursor-pointer">
            <input
              type="radio"
              v-model="processingMode"
              value="batch"
              class="sr-only"
            />
            <div class="flex items-center">
              <div :class="[
                'w-4 h-4 rounded-full border-2 mr-2 transition-all duration-200',
                processingMode === 'batch'
                  ? 'border-purple-500 bg-purple-500'
                  : 'border-gray-300'
              ]">
                <div v-if="processingMode === 'batch'" class="w-2 h-2 bg-white rounded-full m-0.5"></div>
              </div>
              <span class="text-sm text-gray-700">文件夹批量处理</span>
            </div>
          </label>
        </div>
      </div>

      <!-- Single File Upload -->
      <div v-if="processingMode === 'single'" class="mb-6">
        <label for="oneClickFile" class="block text-sm font-medium text-gray-700 mb-2">
          选择音视频文件:
        </label>
        <input
          type="file"
          id="oneClickFile"
          @change="handleFileSelect"
          accept="video/*,audio/*"
          class="block w-full text-sm text-gray-600 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-500 file:text-white hover:file:bg-blue-600 disabled:opacity-50 border border-gray-200 rounded-lg"
          :disabled="oneClickOperationInProgress || isLoading"
        />
        <p v-if="uploadedFile" class="text-xs text-gray-500 mt-2 p-2 bg-gray-50 rounded">
          已选择: {{ uploadedFile.name }} ({{ (uploadedFile.size / 1024 / 1024).toFixed(2) }} MB)
        </p>
      </div>

      <!-- Folder Selection -->
      <div v-if="processingMode === 'batch'" class="mb-6">
        <label for="folderSelect" class="block text-sm font-medium text-gray-700 mb-2">
          选择包含音视频文件的文件夹:
        </label>
        <div class="flex items-center space-x-3">
          <button
            @click="selectFolder"
            class="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-left text-gray-600 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors duration-200"
            :disabled="oneClickOperationInProgress || isLoading"
          >
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 mr-2 text-purple-500">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25H11.69z" />
              </svg>
              {{ selectedFolder || '点击选择文件夹...' }}
            </div>
          </button>
        </div>
        <div v-if="selectedFolder && detectedFiles.length > 0" class="mt-3 p-3 bg-purple-50 rounded-lg border border-purple-200">
          <p class="text-sm text-purple-700 font-medium mb-2">
            检测到 {{ detectedFiles.length }} 个音视频文件:
          </p>
          <div class="max-h-32 overflow-y-auto">
            <ul class="text-xs text-purple-600 space-y-1">
              <li v-for="file in detectedFiles.slice(0, 10)" :key="file" class="truncate">
                📁 {{ file }}
              </li>
              <li v-if="detectedFiles.length > 10" class="text-purple-500 italic">
                ... 还有 {{ detectedFiles.length - 10 }} 个文件
              </li>
            </ul>
          </div>
        </div>
        <div v-else-if="selectedFolder && detectedFiles.length === 0" class="mt-3 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
          <p class="text-sm text-yellow-700">
            ⚠️ 在选择的文件夹中未检测到音视频文件
          </p>
        </div>
      </div>

      <!-- Workflow Type Selection -->
      <div class="mb-6">
        <label for="workflowType" class="block text-sm font-medium text-gray-700 mb-2">工作流类型:</label>
        <select
          id="workflowType"
          v-model="selectedWorkflowType"
          @change="updateWorkflowType"
          class="mt-1 block w-full pl-3 pr-10 py-3 text-base border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg">
          <option value="vid_to_srt">视频 → SRT字幕</option>
          <option value="vid_to_srt_trans">视频 → 翻译SRT字幕</option>
          <option value="audio_to_srt">音频 → SRT字幕</option>
          <option value="audio_to_srt_trans">音频 → 翻译SRT字幕</option>
          <option value="vid_to_text">视频 → 纯文本</option>
          <option value="audio_to_text">音频 → 纯文本</option>
        </select>
      </div>

      <!-- Target Language Selection (for translation workflows) -->
      <div v-if="selectedWorkflowType.includes('trans')" class="mb-6">
        <label for="targetLanguage" class="block text-sm font-medium text-gray-700 mb-2">目标语言:</label>
        <select
          id="targetLanguage"
          v-model="selectedTargetLanguage"
          @change="updateTargetLanguage"
          class="mt-1 block w-full pl-3 pr-10 py-3 text-base border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg">
          <option value="zh-CN">中文（简体）</option>
          <option value="zh-TW">中文（繁体）</option>
          <option value="en">英语</option>
          <option value="ja">日语</option>
          <option value="ko">韩语</option>
          <option value="es">西班牙语</option>
          <option value="fr">法语</option>
          <option value="de">德语</option>
          <option value="it">意大利语</option>
          <option value="pt">葡萄牙语</option>
          <option value="ru">俄语</option>
        </select>
      </div>

      <!-- Export Format Selection -->
      <div class="mb-6">
        <label for="oneClickExportFormat" class="block text-sm font-medium text-gray-700 mb-2">导出格式:</label>
        <select
          id="oneClickExportFormat"
          v-model="selectedOneClickExportFormat"
          @change="updateStoreOneClickExportFormat"
          class="mt-1 block w-full pl-3 pr-10 py-3 text-base border border-gray-300 bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-lg">
          <option value="srt">SRT (.srt)</option>
          <option value="vtt">VTT (.vtt)</option>
          <option value="ass">ASS (.ass)</option>
          <option value="txt">TXT (.txt)</option>
          <option value="json">JSON (.json)</option>
        </select>
      </div>

      <!-- Subtitle Layout Selection (if applicable) -->
      <div v-if="selectedWorkflowType.includes('trans')" class="mb-6">
        <label class="block text-sm font-medium text-gray-700 mb-2">字幕布局 (可多选):</label>
        <div class="space-y-2">
          <label v-for="layout in availableLayouts" :key="layout.value" class="flex items-center">
            <input
              type="checkbox"
              :value="layout.value"
              v-model="selectedOneClickExportLayouts"
              @change="updateStoreOneClickExportLayouts"
              class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <span class="ml-2 text-sm text-gray-700">{{ layout.label }}</span>
          </label>
        </div>
        <p class="mt-1 text-xs text-gray-500">
          选择多个布局将生成多个文件，例如：filename_原文在上.srt, filename_仅译文.srt
        </p>
      </div>

      <button
        @click="startOneClickProcess"
        :disabled="!canStartProcessing || oneClickOperationInProgress || isLoading"
        class="w-full bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-bold py-4 px-6 rounded-xl focus:outline-none focus:ring-4 focus:ring-purple-200 transition-all duration-200 text-lg shadow-lg hover:shadow-xl disabled:shadow-none flex items-center justify-center">
        <svg v-if="oneClickOperationInProgress || isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 mr-2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
        </svg>
        {{ oneClickOperationInProgress || isLoading ? '处理中...' : (processingMode === 'batch' ? '开始批量处理' : '开始一键处理') }}
      </button>
    </div>

    <div v-if="oneClickOperationError && !oneClickOperationInProgress" class="mt-4 p-3 bg-red-700 text-red-100 rounded">
      Error: <strong>{{ oneClickOperationError }}</strong>
    </div>

    <div v-if="lastExportPath && !oneClickOperationInProgress && !oneClickOperationError" class="mt-4 p-3 bg-green-700 text-green-100 rounded">
      ✅ 一键操作成功完成！导出到: <strong>{{ lastExportPath }}</strong>
       <button @click="resetAndClear" class="ml-2 text-sm underline">清除</button>
    </div>

    <!-- Display progress updates -->
    <div v-if="progressUpdates.length > 0 && (oneClickOperationInProgress || isLoading || oneClickOperationError || lastExportPath)" class="mt-4">
      <h3 class="text-md font-semibold mb-2 text-gray-300">操作日志:</h3>
      <ul class="list-disc list-inside bg-gray-750 p-3 rounded max-h-60 overflow-y-auto text-sm">
        <li v-for="(update, index) in progressUpdates" :key="index"
            :class="{
              'text-green-300': update.includes('✅') || update.includes('成功') || update.includes('完成'),
              'text-red-400': update.includes('❌') || update.includes('错误') || update.includes('失败'),
              'text-blue-300': update.includes('🚀') || update.includes('🔄') || update.includes('📝') || update.includes('🌐'),
              'text-yellow-300': update.includes('⚠️') || update.includes('警告'),
              'text-gray-300': !(update.includes('✅') || update.includes('❌') || update.includes('🚀') || update.includes('🔄') || update.includes('📝') || update.includes('🌐') || update.includes('⚠️'))
            }">
          {{ update }}
        </li>
      </ul>
    </div>
    
    <div v-if="!oneClickOperationInProgress && (lastExportPath || oneClickOperationError)" class="mt-4">
        <button
            @click="resetAndClear"
            class="w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline transition duration-150 ease-in-out">
            🔄 开始新的一键操作
        </button>
    </div>

  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';

const subtitlerStore = useSubtitlerStore();

// Local refs for form inputs
const localFile = ref(null);
const processingMode = ref('single'); // 'single' or 'batch'
const selectedFolder = ref('');
const detectedFiles = ref([]);
const selectedWorkflowType = ref(subtitlerStore.oneClickWorkflowType);
const selectedTargetLanguage = ref(subtitlerStore.oneClickTargetLanguage);
const selectedOneClickExportFormat = ref(subtitlerStore.oneClickExportFormat);
const selectedOneClickExportLayout = ref(subtitlerStore.oneClickExportLayout);

// 多选布局支持
const availableLayouts = ref([
  { value: 'original_top', label: '原文在上，译文在下' },
  { value: 'translation_top', label: '译文在上，原文在下' },
  { value: 'original_only', label: '仅显示原文' },
  { value: 'translation_only', label: '仅显示译文' }
]);

const selectedOneClickExportLayouts = ref([subtitlerStore.oneClickExportLayout || 'original_top']);

// Computed properties from store
const isLoading = computed(() => subtitlerStore.isLoading);
const oneClickOperationInProgress = computed(() => subtitlerStore.oneClickOperationInProgress);
const oneClickOperationError = computed(() => subtitlerStore.oneClickOperationError);
const lastExportPath = computed(() => subtitlerStore.lastExportPath);
const progressUpdates = computed(() => subtitlerStore.progressUpdates);
const uploadedFile = computed(() => subtitlerStore.uploadedFile);

// Computed property for button state
const canStartProcessing = computed(() => {
  if (processingMode.value === 'single') {
    return uploadedFile.value !== null;
  } else {
    return selectedFolder.value && detectedFiles.value.length > 0;
  }
});

// Watch for store changes and update local refs
// watch(() => subtitlerStore.exportFormat, (newFormat) => { // Replaced by oneClickExportFormat
//   selectedExportFormat.value = newFormat;
// });

watch(() => subtitlerStore.oneClickWorkflowType, (newType) => {
  selectedWorkflowType.value = newType;
});

watch(() => subtitlerStore.oneClickTargetLanguage, (newLang) => {
  selectedTargetLanguage.value = newLang;
});

watch(() => subtitlerStore.oneClickExportFormat, (newFormat) => {
  selectedOneClickExportFormat.value = newFormat;
});

watch(() => subtitlerStore.oneClickExportLayout, (newLayout) => {
  selectedOneClickExportLayout.value = newLayout;
});


// Update store methods
// const updateStoreExportFormat = () => { // Replaced by updateStoreOneClickExportFormat
//   subtitlerStore.setExportFormat(selectedExportFormat.value);
// };

const updateWorkflowType = () => {
  subtitlerStore.setOneClickWorkflowType(selectedWorkflowType.value);
};

const updateTargetLanguage = () => {
  subtitlerStore.setOneClickTargetLanguage(selectedTargetLanguage.value);
};

const updateStoreOneClickExportFormat = () => {
  subtitlerStore.setOneClickExportFormat(selectedOneClickExportFormat.value);
};

const updateStoreOneClickExportLayout = () => {
  subtitlerStore.setOneClickExportLayout(selectedOneClickExportLayout.value);
};

// 多选布局更新方法
const updateStoreOneClickExportLayouts = () => {
  // 确保至少选择一个布局
  if (selectedOneClickExportLayouts.value.length === 0) {
    selectedOneClickExportLayouts.value = ['original_top'];
  }

  // 将多选布局存储到store中
  subtitlerStore.setOneClickExportLayouts(selectedOneClickExportLayouts.value);

  // 同时更新单选布局（用于兼容性）
  subtitlerStore.setOneClickExportLayout(selectedOneClickExportLayouts.value[0]);
};

const handleFileSelect = (event) => {
  const file = event.target.files[0];
  if (file) {
    subtitlerStore.setUploadedFile({
        name: file.name,
        path: file.path, // Electron provides this
        type: file.type,
        size: file.size,
    });
    localFile.value = file;
  } else {
    subtitlerStore.setUploadedFile(null);
    localFile.value = null;
  }
};

// 文件夹选择方法
const selectFolder = async () => {
  try {
    // 使用Electron的IPC通信选择文件夹
    const result = await window.electronAPI.selectFolder();

    if (result && result.folderPath) {
      selectedFolder.value = result.folderPath;
      detectedFiles.value = result.mediaFiles || [];
    }
  } catch (error) {
    console.error('选择文件夹时出错:', error);
    subtitlerStore.$patch({ oneClickOperationError: "选择文件夹时出错: " + error.message });
  }
};

const startOneClickProcess = async () => {
  // 验证输入
  if (processingMode.value === 'single') {
    if (!subtitlerStore.uploadedFile) {
      subtitlerStore.$patch({ oneClickOperationError: "请先选择一个文件。" });
      return;
    }
  } else {
    if (!selectedFolder.value || detectedFiles.value.length === 0) {
      subtitlerStore.$patch({ oneClickOperationError: "请先选择包含音视频文件的文件夹。" });
      return;
    }
  }

  // 确保所有设置都是最新的
  subtitlerStore.setOneClickWorkflowType(selectedWorkflowType.value);
  if (selectedWorkflowType.value.includes('trans')) {
    subtitlerStore.setOneClickTargetLanguage(selectedTargetLanguage.value);
    subtitlerStore.setOneClickExportLayout(selectedOneClickExportLayout.value);
    subtitlerStore.setOneClickExportLayouts(selectedOneClickExportLayouts.value);
  }
  subtitlerStore.setOneClickExportFormat(selectedOneClickExportFormat.value);

  if (processingMode.value === 'single') {
    // 单文件处理
    await subtitlerStore.performOneClickOperation();
  } else {
    // 批量处理
    await performBatchOperation();
  }
};

// 批量处理方法
const performBatchOperation = async () => {
  try {
    subtitlerStore.$patch({
      oneClickOperationInProgress: true,
      oneClickOperationError: null,
      progressUpdates: []
    });

    const totalFiles = detectedFiles.value.length;
    let processedFiles = 0;
    let successCount = 0;
    let errorCount = 0;

    // 保存批量处理的设置，避免被单个文件处理覆盖
    const batchSettings = {
      workflowType: selectedWorkflowType.value,
      targetLanguage: selectedTargetLanguage.value,
      exportFormat: selectedOneClickExportFormat.value,
      exportLayout: selectedOneClickExportLayout.value, // 兼容性
      exportLayouts: selectedOneClickExportLayouts.value // 多选布局
    };

    subtitlerStore.progressUpdates.push(`🚀 开始批量处理 ${totalFiles} 个文件...`);
    subtitlerStore.progressUpdates.push(`📋 设置: ${batchSettings.workflowType}, ${batchSettings.targetLanguage}, ${batchSettings.exportFormat}, ${batchSettings.exportLayout}`);

    for (const fileName of detectedFiles.value) {
      const filePath = `${selectedFolder.value}/${fileName}`;
      processedFiles++;

      try {
        subtitlerStore.progressUpdates.push(`\n[${processedFiles}/${totalFiles}] 🎬 开始处理: ${fileName}`);

        // 重置store状态，避免前一个文件的数据污染
        subtitlerStore.resetWorkflow();

        // 设置当前文件到store
        subtitlerStore.setUploadedFile({
          name: fileName,
          path: filePath,
          type: getFileType(fileName),
          size: 0 // 批量处理时不需要精确大小
        });

        // 重新设置批量处理的参数，确保每个文件都使用正确的设置
        subtitlerStore.setOneClickWorkflowType(batchSettings.workflowType);
        subtitlerStore.setOneClickTargetLanguage(batchSettings.targetLanguage);
        subtitlerStore.setOneClickExportFormat(batchSettings.exportFormat);
        subtitlerStore.setOneClickExportLayout(batchSettings.exportLayout);
        subtitlerStore.setOneClickExportLayouts(batchSettings.exportLayouts);

        console.log(`[Batch] Processing file ${processedFiles}/${totalFiles}: ${fileName}`);
        console.log(`[Batch] Settings:`, batchSettings);

        // 执行单文件处理
        await subtitlerStore.performOneClickOperation();

        successCount++;
        subtitlerStore.progressUpdates.push(`✅ [${processedFiles}/${totalFiles}] 完成: ${fileName}`);

        if (subtitlerStore.lastExportPath) {
          subtitlerStore.progressUpdates.push(`📁 导出到: ${subtitlerStore.lastExportPath}`);
        }

      } catch (error) {
        errorCount++;
        console.error(`处理文件 ${fileName} 时出错:`, error);
        subtitlerStore.progressUpdates.push(`❌ [${processedFiles}/${totalFiles}] 失败: ${fileName}`);
        subtitlerStore.progressUpdates.push(`   错误: ${error.message}`);
      }
    }

    // 批量处理完成
    subtitlerStore.progressUpdates.push(`\n🎉 批量处理完成!`);
    subtitlerStore.progressUpdates.push(`✅ 成功处理: ${successCount} 个文件`);
    if (errorCount > 0) {
      subtitlerStore.progressUpdates.push(`❌ 处理失败: ${errorCount} 个文件`);
    }

    subtitlerStore.$patch({
      oneClickOperationInProgress: false,
      lastExportPath: `批量处理完成 - 成功: ${successCount}, 失败: ${errorCount}`
    });

  } catch (error) {
    console.error('批量处理时出错:', error);
    subtitlerStore.$patch({
      oneClickOperationInProgress: false,
      oneClickOperationError: `批量处理失败: ${error.message}`
    });
  }
};

// 根据文件扩展名获取文件类型
const getFileType = (fileName) => {
  const ext = fileName.split('.').pop().toLowerCase();
  const videoExts = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm', 'm4v'];
  const audioExts = ['mp3', 'wav', 'flac', 'aac', 'ogg', 'wma', 'm4a'];

  if (videoExts.includes(ext)) {
    return 'video/' + ext;
  } else if (audioExts.includes(ext)) {
    return 'audio/' + ext;
  }
  return 'application/octet-stream';
};

const resetAndClear = () => {
    subtitlerStore.resetWorkflow();
    localFile.value = null;
    selectedFolder.value = '';
    detectedFiles.value = [];
    processingMode.value = 'single';
    const fileInput = document.getElementById('oneClickFile');
    if (fileInput) {
        fileInput.value = '';
    }
};

</script>

<style scoped>
/* Scoped styles for OneClickOperation.vue */
.list-disc li::marker {
  color: #60a5fa; /* blue-400 for markers */
}
.bg-gray-750 {
    background-color: #374151;
}
</style>
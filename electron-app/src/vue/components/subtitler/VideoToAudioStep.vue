<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 2: 视频转音频</h3>
      <p class="text-sm text-gray-500 mt-1">从视频文件中提取音频用于后续处理</p>
    </div>

    <!-- 前置条件警告 -->
    <StatusMessage
      v-if="!uploadedFile"
      type="warning"
      title="需要先上传文件"
      message="请先在步骤1中上传视频或音频文件"
      :actions="[
        { key: 'goto-upload', label: '前往上传文件', icon: '📁', primary: true }
      ]"
      @action="handlePrerequisiteAction"
    />

    <!-- 文件信息卡片 (统一显示) -->
    <FileInfoCard
      v-if="fileInfo"
      :file-info="fileInfo"
      :processing-status="processingStatus"
      :progress="processingProgress"
      :progress-text="progressText"
      :error-message="errorMessage"
      :show-actions="false"
      :show-progress="true"
    />

    <!-- 音频提取操作 -->
    <div v-if="uploadedFile && !isLoading" class="current-actions mb-6">
      <div class="flex space-x-3">
        <button
          @click="handleProcessVideoToAudio"
          :disabled="isLoading || videoToAudioResult"
          class="flex-1 px-6 py-3 text-white bg-purple-600 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          🎵 {{ videoToAudioResult ? '音频已提取' : '开始提取音频' }}
        </button>

        <button
          v-if="videoToAudioResult"
          @click="goToNextStep"
          class="px-6 py-3 text-purple-700 bg-purple-100 rounded-lg hover:bg-purple-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 font-medium transition-colors text-base"
        >
          ➡️ 下一步
        </button>
      </div>
    </div>

    <!-- 提取进度显示 -->
    <div v-if="isLoading" class="mb-6">
      <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-purple-800">🎵 正在提取音频</h4>
          <span class="text-xs text-purple-600">{{ formatTime(new Date()) }}</span>
        </div>

        <div class="mb-3">
          <div class="flex items-center justify-between text-sm mb-1">
            <span class="text-purple-700">📊 提取进度</span>
            <span class="text-purple-700">{{ Math.round(processingProgress || 0) }}%</span>
          </div>
          <div class="w-full bg-purple-200 rounded-full h-2">
            <div
              class="bg-purple-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min(100, Math.max(0, processingProgress || 0))}%` }"
            />
          </div>
        </div>

        <div class="text-sm text-purple-600">
          ⏱️ {{ progressText || '正在从视频文件中提取音频...' }}
        </div>
      </div>
    </div>

    <!-- 错误信息显示 -->
    <StatusMessage
      v-if="videoToAudioError"
      type="error"
      title="音频提取失败"
      :message="videoToAudioError"
      :details="errorDetails"
      :actions="[
        { key: 'retry', label: '重试', icon: '🔄', primary: true },
        { key: 'back', label: '返回上一步', icon: '⬅️' }
      ]"
      @action="handleErrorAction"
    />

    <!-- 提取完成显示 -->
    <div v-if="videoToAudioResult && !isLoading" class="mb-6">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-green-800">✅ 音频提取完成</h4>
        </div>

        <p class="text-sm text-green-700 mb-3">{{ extractionSummary }}</p>

        <!-- 音频文件信息 -->
        <div class="bg-white rounded-md p-3 border border-green-200">
          <div class="text-xs text-green-600 mb-1">🎵 音频文件</div>
          <p class="text-sm text-gray-700 font-mono">{{ audioFileName }}</p>
        </div>
      </div>
    </div>

    <!-- 可折叠的提取日志 -->
    <CollapsibleLog
      v-if="subtitlerStore.progressUpdates.length > 0"
      title="提取日志"
      :logs="formattedLogs"
      :initial-expanded="false"
      :show-stats="true"
      @clear="clearLogs"
      @copy="handleLogCopy"
    />

  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import FileInfoCard from '@/vue/components/common/FileInfoCard.vue';
import StatusMessage from '@/vue/components/common/StatusMessage.vue';
import CollapsibleLog from '@/vue/components/common/CollapsibleLog.vue';

const subtitlerStore = useSubtitlerStore();

// 计算属性
const uploadedFile = computed(() => subtitlerStore.uploadedFile);
const videoToAudioResult = computed(() => subtitlerStore.videoToAudioResult);
const videoToAudioProgress = computed(() => subtitlerStore.videoToAudioProgress);
const videoToAudioError = computed(() => subtitlerStore.videoToAudioError);
const isLoading = computed(() => subtitlerStore.isLoading);

const fileInfo = computed(() => {
  if (!uploadedFile.value) return null;

  return {
    name: uploadedFile.value.name || subtitlerStore.getUploadedFileName,
    size: uploadedFile.value.size || subtitlerStore.getUploadedFileSize,
    type: uploadedFile.value.type || subtitlerStore.getUploadedFileType,
    duration: null
  };
});

const processingStatus = computed(() => {
  if (isLoading.value) return 'processing';
  if (videoToAudioResult.value) return 'completed';
  if (videoToAudioError.value) return 'error';
  if (uploadedFile.value) return 'ready';
  return null;
});

const processingProgress = computed(() => {
  if (isLoading.value) {
    // 模拟进度，实际应该从store获取
    return 60;
  }
  return videoToAudioResult.value ? 100 : null;
});

const progressText = computed(() => {
  if (isLoading.value) {
    return videoToAudioProgress.value || '正在从视频文件中提取音频...';
  }
  return null;
});

const errorMessage = computed(() => {
  return videoToAudioError.value;
});

const errorDetails = computed(() => {
  if (videoToAudioError.value) {
    return '请检查视频文件格式是否支持，或尝试使用其他视频文件';
  }
  return null;
});

const extractionSummary = computed(() => {
  if (!videoToAudioResult.value) return '';

  return '音频文件已成功从视频中提取，可以进入下一步进行语音转文字处理';
});

const audioFileName = computed(() => {
  if (!videoToAudioResult.value) return '';

  // 从路径中提取文件名
  const fileName = videoToAudioResult.value.split('/').pop() || videoToAudioResult.value;
  return fileName;
});

const formattedLogs = computed(() => {
  return subtitlerStore.progressUpdates.map(update => ({
    timestamp: new Date().toISOString(),
    level: update.includes('✅') ? 'success' :
           update.includes('❌') ? 'error' :
           update.includes('⚠️') ? 'warning' : 'info',
    message: update
  }));
});

// 方法
const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const handleProcessVideoToAudio = async () => {
  if (uploadedFile.value) {
    await subtitlerStore.processVideoToAudio();
  } else {
    subtitlerStore.videoToAudioError = "没有文件被上传。";
  }
};

const handlePrerequisiteAction = (action) => {
  if (action.key === 'goto-upload') {
    subtitlerStore.setCurrentStep(1);
  }
};

const handleErrorAction = (action) => {
  switch (action.key) {
    case 'retry':
      handleProcessVideoToAudio();
      break;
    case 'back':
      subtitlerStore.setCurrentStep(1);
      break;
  }
};

const goToNextStep = () => {
  subtitlerStore.setCurrentStep(3);
};

const clearLogs = () => {
  subtitlerStore.progressUpdates = [];
};

const handleLogCopy = (logText) => {
  console.log('日志已复制:', logText);
};
</script>

<style scoped>
/* 可以根据需要添加特定样式 */
code {
  word-break: break-all;
}
</style>
<template>
  <div class="results-viewer">
    <!-- 主要状态卡片 -->
    <StatusCard
      :status="transcriptionStatus"
      :title="statusTitle"
      :description="statusDescription"
      :show-timestamp="true"
      :timestamp="transcriptionTimestamp"
      :actions="primaryActions"
      @action="handleAction"
    >
      <!-- 智能摘要预览 (可选显示) -->
      <template v-if="showQuickPreview">
        <div class="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-center mb-3">
            <svg class="h-4 w-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h4 class="text-sm font-semibold text-blue-800">转录摘要</h4>
          </div>

          <!-- 统计信息 -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-3 mb-4">
            <div class="text-center">
              <div class="text-lg font-bold text-blue-700">{{ totalSegments }}</div>
              <div class="text-xs text-blue-600">字幕片段</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-blue-700">{{ calculateTotalDuration() || '--' }}</div>
              <div class="text-xs text-blue-600">总时长</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-blue-700">{{ averageSegmentLength }}</div>
              <div class="text-xs text-blue-600">平均长度</div>
            </div>
            <div class="text-center">
              <div class="text-lg font-bold text-blue-700">{{ totalWords }}</div>
              <div class="text-xs text-blue-600">总字数</div>
            </div>
          </div>

          <!-- 内容摘要 -->
          <div class="bg-white rounded-md p-3 border border-blue-200">
            <div class="text-xs text-blue-600 mb-1">内容摘要</div>
            <p class="text-sm text-gray-700 leading-relaxed">{{ contentSummary }}</p>
          </div>

          <!-- 质量指标 -->
          <div class="mt-3 flex items-center justify-between text-xs">
            <div class="flex items-center">
              <div class="w-2 h-2 rounded-full mr-2" :class="qualityIndicator.color"></div>
              <span class="text-gray-600">转录质量: {{ qualityIndicator.label }}</span>
            </div>
            <span class="text-gray-500">{{ formatTimestamp(transcriptionTimestamp) }}</span>
          </div>
        </div>
      </template>
    </StatusCard>

    <!-- 详细信息 (可折叠) -->
    <div v-if="showDetailedView" class="detailed-view mt-4 border border-gray-200 rounded-lg bg-white">
      <div class="border-b border-gray-200 p-4">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-800">详细信息</h3>
          <button
            @click="showDetailedView = false"
            class="text-gray-400 hover:text-gray-600 transition-colors">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- 详细信息标签页 -->
      <div class="p-4">
        <div class="flex space-x-2 mb-4 border-b border-gray-200 pb-2">
          <button
            @click="detailTab = 'segments'"
            :class="getDetailTabClass('segments')"
          >
            字幕片段
          </button>
          <button
            @click="detailTab = 'srt'"
            :class="getDetailTabClass('srt')"
          >
            SRT格式
          </button>
          <button
            @click="detailTab = 'transcript'"
            :class="getDetailTabClass('transcript')"
          >
            纯文本
          </button>
        </div>

        <div class="detail-content max-h-80 overflow-y-auto">
          <!-- 字幕片段详细视图 -->
          <div v-if="detailTab === 'segments'">
            <div v-if="currentSegments && currentSegments.length" class="space-y-3">
              <div v-for="(segment, index) in currentSegments" :key="segment.id || index"
                   class="p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div class="flex items-center justify-between mb-2">
                  <span class="text-xs text-gray-500 font-medium">片段 {{ index + 1 }}</span>
                  <span class="text-xs text-blue-600 font-mono">{{ formatTime(segment) }}</span>
                </div>
                <p class="text-sm text-gray-800">{{ segment.text }}</p>
              </div>
            </div>
            <p v-else class="text-sm text-gray-500 text-center py-8">暂无字幕片段</p>
          </div>

          <!-- SRT格式详细视图 -->
          <div v-if="detailTab === 'srt'">
            <pre class="whitespace-pre-wrap text-sm text-gray-700 bg-gray-50 p-4 rounded-lg border border-gray-200 font-mono">{{ srtPreviewContent || '暂无 SRT 内容' }}</pre>
          </div>

          <!-- 纯文本详细视图 -->
          <div v-if="detailTab === 'transcript'">
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <p class="text-sm text-gray-700 whitespace-pre-wrap">{{ transcript || '暂无转录文本' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import { segmentsToSrt, formatMillisecondsToSrtTime } from '@/js/renderer-modules/subtitler/utils';
import StatusCard from '@/vue/components/common/StatusCard.vue';

const subtitlerStore = useSubtitlerStore();

// 响应式状态
const showQuickPreview = ref(false);
const showDetailedView = ref(false);
const detailTab = ref('segments');

// 基础数据
const transcript = computed(() => subtitlerStore.audioToTextResult?.transcript);
const originalSegments = computed(() => subtitlerStore.audioToTextResult?.segments);
const editableSegments = computed(() => subtitlerStore.editableSegments);

// 当前显示的片段
const currentSegments = computed(() => {
  // 调试信息
  console.log('ResultsViewer - Debug data:', {
    editableSegments: editableSegments.value,
    originalSegments: originalSegments.value,
    audioToTextResult: subtitlerStore.audioToTextResult
  });

  if (editableSegments.value && editableSegments.value.length > 0) {
    console.log('Using editableSegments:', editableSegments.value.length);
    return editableSegments.value;
  }
  if (originalSegments.value && originalSegments.value.length > 0) {
    console.log('Using originalSegments:', originalSegments.value.length);
    return originalSegments.value;
  }
  console.log('No segments found');
  return [];
});

// 总片段数
const totalSegments = computed(() => currentSegments.value.length);

// 智能摘要相关计算属性
const averageSegmentLength = computed(() => {
  if (totalSegments.value === 0) return '--';
  const totalDuration = calculateTotalDurationInSeconds();
  if (!totalDuration) return '--';
  return `${Math.round(totalDuration / totalSegments.value)}秒`;
});

const totalWords = computed(() => {
  if (!currentSegments.value) return 0;
  return currentSegments.value.reduce((total, segment) => {
    return total + (segment.text ? segment.text.length : 0);
  }, 0);
});

const contentSummary = computed(() => {
  if (!currentSegments.value || currentSegments.value.length === 0) {
    return '暂无转录内容';
  }

  // 取前3个片段的文本作为摘要
  const firstThreeSegments = currentSegments.value.slice(0, 3);
  const summaryText = firstThreeSegments.map(segment => segment.text).join(' ');

  // 如果文本太长，截取前100个字符
  if (summaryText.length > 100) {
    return summaryText.substring(0, 100) + '...';
  }

  return summaryText;
});

const qualityIndicator = computed(() => {
  const segmentCount = totalSegments.value;
  const avgLength = parseFloat(averageSegmentLength.value);

  if (segmentCount === 0) {
    return { label: '无数据', color: 'bg-gray-400' };
  }

  if (segmentCount >= 10 && avgLength >= 3 && avgLength <= 15) {
    return { label: '优秀', color: 'bg-green-500' };
  } else if (segmentCount >= 5 && avgLength >= 2) {
    return { label: '良好', color: 'bg-blue-500' };
  } else {
    return { label: '一般', color: 'bg-yellow-500' };
  }
});

// SRT预览内容
const srtPreviewContent = computed(() => {
  if (editableSegments.value && editableSegments.value.length > 0) {
    return subtitlerStore.srtPreviewFromEditableSegments;
  }
  return subtitlerStore.srtPreviewFromOriginalSegments;
});

// 状态卡片相关
const transcriptionStatus = computed(() => {
  if (totalSegments.value > 0) {
    return 'success';
  }
  return 'info';
});

const statusTitle = computed(() => {
  if (totalSegments.value > 0) {
    return '音频转录完成';
  }
  return '等待转录结果';
});

const statusDescription = computed(() => {
  if (totalSegments.value > 0) {
    const duration = calculateTotalDuration();
    return `成功转录 ${totalSegments.value} 个字幕片段${duration ? `，总时长 ${duration}` : ''}`;
  }
  return '暂无转录结果';
});

const transcriptionTimestamp = computed(() => {
  return Date.now(); // 可以从store中获取实际的转录完成时间
});

// 主要操作按钮
const primaryActions = computed(() => {
  const actions = [];

  if (totalSegments.value > 0) {
    actions.push(
      {
        key: 'summary',
        label: showQuickPreview.value ? '隐藏摘要' : '查看摘要',
        variant: 'secondary'
      },
      {
        key: 'details',
        label: '详细内容',
        variant: 'secondary'
      },
      {
        key: 'edit',
        label: '立即编辑',
        variant: 'primary'
      }
    );
  }

  return actions;
});

// 方法
const handleAction = (action) => {
  switch (action.key) {
    case 'summary':
      showQuickPreview.value = !showQuickPreview.value;
      break;
    case 'details':
      showDetailedView.value = true;
      detailTab.value = 'segments';
      break;
    case 'edit':
      // 智能编辑：自动准备编辑环境
      prepareEditingEnvironment();
      break;
  }
};

const prepareEditingEnvironment = () => {
  // 1. 确保editableSegments已准备好
  if (!subtitlerStore.editableSegments || subtitlerStore.editableSegments.length === 0) {
    // 如果没有可编辑片段，从原始片段创建
    if (subtitlerStore.audioToTextResult?.segments) {
      subtitlerStore.editableSegments = [...subtitlerStore.audioToTextResult.segments];
    }
  }

  // 2. 设置编辑提示信息
  subtitlerStore.addProgressUpdate('准备编辑环境...');

  // 3. 跳转到编辑步骤
  subtitlerStore.setCurrentStep(6);

  // 4. 添加编辑指导信息
  setTimeout(() => {
    subtitlerStore.addProgressUpdate(`已准备 ${totalSegments.value} 个字幕片段供编辑`);
    subtitlerStore.addProgressUpdate('提示：双击片段可快速编辑文本内容');
  }, 100);
};

const getDetailTabClass = (tabName) => {
  const baseClass = 'px-3 py-1.5 text-sm font-medium rounded-md transition-colors';
  const activeClass = 'bg-blue-500 text-white';
  const inactiveClass = 'bg-gray-100 text-gray-700 hover:bg-gray-200';

  return `${baseClass} ${detailTab.value === tabName ? activeClass : inactiveClass}`;
};

const formatTime = (segment) => {
  const startTime = segment.startTimeMs ?? segment.start_time_ms ?? segment.start;
  const endTime = segment.endTimeMs ?? segment.end_time_ms ?? segment.end;

  const formatSingleTime = (time) => {
    if (typeof time === 'number') {
      // 如果是小数字，假设是秒，转换为毫秒
      if (time < 10000000) {
        return formatMillisecondsToSrtTime(Math.round(time * 1000)).split(',')[0];
      }
      return formatMillisecondsToSrtTime(time).split(',')[0];
    }
    return time || 'N/A';
  };

  return `${formatSingleTime(startTime)} - ${formatSingleTime(endTime)}`;
};

const calculateTotalDuration = () => {
  if (!currentSegments.value || currentSegments.value.length === 0) {
    return null;
  }

  const lastSegment = currentSegments.value[currentSegments.value.length - 1];
  const endTime = lastSegment.endTimeMs ?? lastSegment.end_time_ms ?? lastSegment.end;

  if (typeof endTime === 'number') {
    const totalSeconds = endTime < 10000000 ? endTime : Math.floor(endTime / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  return null;
};

const calculateTotalDurationInSeconds = () => {
  if (!currentSegments.value || currentSegments.value.length === 0) {
    return null;
  }

  const lastSegment = currentSegments.value[currentSegments.value.length - 1];
  const endTime = lastSegment.endTimeMs ?? lastSegment.end_time_ms ?? lastSegment.end;

  if (typeof endTime === 'number') {
    return endTime < 10000000 ? endTime : Math.floor(endTime / 1000);
  }

  return null;
};

const formatTimestamp = (timestamp) => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

</script>

<style scoped>
.results-viewer {
  /* 主容器样式 */
}

.detailed-view {
  /* 详细视图动画 */
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detail-content {
  /* 详细内容滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.detail-content::-webkit-scrollbar {
  width: 6px;
}

.detail-content::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 3px;
}

.detail-content::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.detail-content::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 快速预览动画 */
.quick-preview-enter-active,
.quick-preview-leave-active {
  transition: all 0.3s ease;
}

.quick-preview-enter-from,
.quick-preview-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 2: 语音转文字</h3>
      <p class="text-sm text-gray-500 mt-1">将音频内容转换为文字字幕</p>
    </div>

    <!-- 文件信息卡片 (统一显示) -->
    <FileInfoCard
      v-if="fileInfo"
      :file-info="fileInfo"
      :processing-status="processingStatus"
      :progress="processingProgress"
      :progress-text="progressText"
      :error-message="errorMessage"
      :show-actions="false"
      :show-progress="true"
    />

    <!-- 转录设置 -->
    <div v-if="!isLoading" class="current-actions mb-6">
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-4">⚙️ 转录设置</h4>

        <!-- 转录模型选择 -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            转录模型:
          </label>
          <select
            v-model="selectedModel"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="JIANYING">J 方案 - 剪映 (推荐)</option>
            <option value="BIJIAN">B 方案 - 必剪</option>
          </select>
          <p class="mt-1 text-xs text-gray-500">
            J 方案（剪映）对中文识别效果更好，B 方案（必剪）速度较快。未来将支持本地 Whisper 模型。
          </p>
        </div>

        <!-- 音频语言选择 -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            音频语言:
          </label>
          <select
            v-model="selectedLanguage"
            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="zh">中文</option>
            <option value="en">English</option>
            <option value="auto">自动检测</option>
          </select>
        </div>

        <!-- 高级选项 -->
        <div class="space-y-3">
          <label class="flex items-center space-x-2 text-sm text-gray-700">
            <input
              type="checkbox"
              v-model="enableCache"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <span>启用缓存加速</span>
          </label>

          <label class="flex items-center space-x-2 text-sm text-gray-700">
            <input
              type="checkbox"
              v-model="needWordTimestamp"
              class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            />
            <span>生成词级时间戳</span>
          </label>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-3">
        <button
          @click="handleProcessAudioToText"
          :disabled="!canStartTranscription || isLoading || audioToTextResult"
          class="flex-1 px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          🎤 {{ audioToTextResult ? '转录已完成' : canStartTranscription ? '开始转录' : '请先准备音频文件' }}
        </button>

        <button
          @click="pauseTranscription"
          v-if="isLoading"
          class="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 font-medium transition-colors text-base"
        >
          ⏸️ 暂停
        </button>

        <button
          @click="goToNextStep"
          v-if="audioToTextResult && !isLoading"
          class="px-6 py-3 text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 font-medium transition-colors text-base"
        >
          ➡️ 下一步
        </button>
      </div>
    </div>

    <!-- 转录进度显示 -->
    <div v-if="isLoading" class="mb-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-blue-800">🌐 中文 → 文字转录</h4>
          <span class="text-xs text-blue-600">{{ formatTime(new Date()) }}</span>
        </div>

        <div class="mb-3">
          <div class="flex items-center justify-between text-sm mb-1">
            <span class="text-blue-700">📊 转录进度</span>
            <span class="text-blue-700">{{ Math.round(processingProgress || 0) }}%</span>
          </div>
          <div class="w-full bg-blue-200 rounded-full h-2">
            <div
              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min(100, Math.max(0, processingProgress || 0))}%` }"
            />
          </div>
        </div>

        <div class="text-sm text-blue-600">
          ⏱️ {{ progressText || '正在处理音频文件...' }}
        </div>

        <!-- 实时预览 -->
        <div v-if="realtimePreview" class="mt-3 p-3 bg-white rounded-md border border-blue-200">
          <div class="text-xs text-blue-600 mb-1">💡 实时预览</div>
          <p class="text-sm text-gray-700">{{ realtimePreview }}</p>
        </div>
      </div>
    </div>

    <!-- 错误信息显示 -->
    <StatusMessage
      v-if="audioToTextError"
      type="error"
      title="转录失败"
      :message="audioToTextError"
      :details="errorDetails"
      :actions="[
        { key: 'retry', label: '重试', icon: '🔄', primary: true },
        { key: 'settings', label: '调整设置', icon: '⚙️' }
      ]"
      @action="handleErrorAction"
    />

    <!-- 转录结果展示 -->
    <div v-if="audioToTextResult && !isLoading" class="mb-6">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-green-800">✅ 转录完成</h4>
          <button
            @click="toggleStats"
            class="text-xs text-green-600 hover:text-green-800 focus:outline-none"
          >
            {{ showStats ? '隐藏详情' : '查看详情' }} {{ showStats ? '▲' : '▼' }}
          </button>
        </div>

        <p class="text-sm text-green-700 mb-3">{{ transcriptionSummary }}</p>

        <!-- 转录统计 -->
        <div v-if="showStats" class="space-y-4">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div class="text-center p-3 bg-white rounded-md border border-green-200">
              <div class="text-lg font-bold text-green-700">{{ transcriptionSegmentCount }}</div>
              <div class="text-xs text-green-600">字幕片段</div>
            </div>
            <div class="text-center p-3 bg-white rounded-md border border-green-200">
              <div class="text-lg font-bold text-green-700">{{ transcriptionDuration || '--' }}</div>
              <div class="text-xs text-green-600">总时长</div>
            </div>
            <div class="text-center p-3 bg-white rounded-md border border-green-200">
              <div class="text-lg font-bold text-green-700">{{ transcriptionWordCount }}</div>
              <div class="text-xs text-green-600">字符数</div>
            </div>
            <div class="text-center p-3 bg-white rounded-md border border-green-200">
              <div class="text-lg font-bold text-green-700">{{ transcriptionQuality.label }}</div>
              <div class="text-xs text-green-600">转录质量</div>
            </div>
          </div>

          <!-- 内容预览 -->
          <div class="bg-white rounded-md p-3 border border-green-200">
            <div class="text-xs text-green-600 mb-2">📝 内容预览</div>
            <p class="text-sm text-gray-700 leading-relaxed">{{ transcriptionPreview }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 可折叠的转录日志 -->
    <CollapsibleLog
      v-if="subtitlerStore.progressUpdates.length > 0"
      title="转录日志"
      :logs="formattedLogs"
      :initial-expanded="false"
      :show-stats="true"
      @clear="clearLogs"
      @copy="handleLogCopy"
    />

  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import FileInfoCard from '@/vue/components/common/FileInfoCard.vue';
import StatusMessage from '@/vue/components/common/StatusMessage.vue';
import CollapsibleLog from '@/vue/components/common/CollapsibleLog.vue';

const subtitlerStore = useSubtitlerStore();

// 响应式状态
const showStats = ref(false);
const selectedModel = ref('JIANYING');
const selectedLanguage = ref('zh');
const enableCache = ref(true);
const needWordTimestamp = ref(false);
const realtimePreview = ref('');

// 计算属性
const isLoading = computed(() => subtitlerStore.isLoading);
const currentStep = computed(() => subtitlerStore.currentStep);
const videoToAudioResult = computed(() => subtitlerStore.videoToAudioResult);
const uploadedFile = computed(() => subtitlerStore.uploadedFile);
const audioToTextResult = computed(() => subtitlerStore.audioToTextResult);
const audioToTextProgress = computed(() => subtitlerStore.audioToTextProgress);
const audioToTextError = computed(() => subtitlerStore.audioToTextError);

const fileInfo = computed(() => {
  if (!uploadedFile.value) return null;

  return {
    name: uploadedFile.value.name || subtitlerStore.getUploadedFileName,
    size: uploadedFile.value.size || subtitlerStore.getUploadedFileSize,
    type: uploadedFile.value.type || subtitlerStore.getUploadedFileType,
    duration: null // 如果有时长信息可以添加
  };
});

const processingStatus = computed(() => {
  if (isLoading.value) return 'processing';
  if (audioToTextResult.value) return 'completed';
  if (audioToTextError.value) return 'error';
  if (uploadedFile.value) return 'ready';
  return null;
});

const processingProgress = computed(() => {
  // 这里可以根据实际的转录进度返回
  if (isLoading.value) {
    // 模拟进度，实际应该从store获取
    return 75;
  }
  return audioToTextResult.value ? 100 : null;
});

const progressText = computed(() => {
  if (isLoading.value) {
    return audioToTextProgress.value || '正在转录音频，请稍候...';
  }
  return null;
});

const errorMessage = computed(() => {
  return audioToTextError.value;
});

const errorDetails = computed(() => {
  if (audioToTextError.value) {
    return '请检查音频文件质量，或尝试调整转录设置';
  }
  return null;
});

const usePreviousAudio = computed({
  get: () => subtitlerStore.usePreviousAudioForTranscription,
  set: (value) => {
    subtitlerStore.setUsePreviousAudioForTranscription(value);
    // If unchecking and no uploaded audio, clear relevant states
    if (!value && !(uploadedFile.value && uploadedFile.value.type && uploadedFile.value.type.startsWith('audio/'))) {
      subtitlerStore.audioToTextResult = null;
      subtitlerStore.editableSegments = null;
    }
    // If checking and videoToAudioResult exists, it will be picked up by audioSourcePath
  }
});

const audioSourcePath = computed(() => {
  if (usePreviousAudio.value && videoToAudioResult.value) {
    return videoToAudioResult.value;
  }
  if (uploadedFile.value && uploadedFile.value.type && uploadedFile.value.type.startsWith('audio/')) {
    // This branch is used if usePreviousAudio is false, OR if it's true but videoToAudioResult is null
    return uploadedFile.value.path;
  }
  return null;
});

const audioSourceMessage = computed(() => {
  if (usePreviousAudio.value) {
    if (videoToAudioResult.value) {
      return `将使用上一步提取的音频: ${videoToAudioResult.value}`;
    } else if (uploadedFile.value && uploadedFile.value.type && uploadedFile.value.type.startsWith('audio/')) {
      return `上一步音频不可用，将尝试使用已上传的音频文件: ${uploadedFile.value.path}`;
    }
    return '未找到来自上一步的音频，也未上传独立的音频文件。请勾选“使用上一步提取的音频”并确保上一步已成功，或取消勾选并上传一个音频文件。';
  } else {
    if (uploadedFile.value && uploadedFile.value.type && uploadedFile.value.type.startsWith('audio/')) {
      return `将使用已上传的音频文件: ${uploadedFile.value.path}`;
    }
    return '请上传一个音频文件，或勾选“使用上一步提取的音频”（如果适用）。';
  }
});

const canStartTranscription = computed(() => {
  return !!uploadedFile.value;
});

const formattedLogs = computed(() => {
  return subtitlerStore.progressUpdates.map(update => ({
    timestamp: new Date().toISOString(),
    level: update.includes('✅') ? 'success' :
           update.includes('❌') ? 'error' :
           update.includes('⚠️') ? 'warning' : 'info',
    message: update
  }));
});



// 转录结果相关
const transcriptionSegments = computed(() => {
  if (!audioToTextResult.value) return [];
  if (audioToTextResult.value.segments) {
    return audioToTextResult.value.segments;
  }
  return [];
});

const transcriptionSegmentCount = computed(() => {
  return transcriptionSegments.value.length;
});

const transcriptionWordCount = computed(() => {
  if (!audioToTextResult.value) return 0;
  let text = '';
  if (typeof audioToTextResult.value === 'string') {
    text = audioToTextResult.value;
  } else if (audioToTextResult.value.transcript) {
    text = audioToTextResult.value.transcript;
  }
  return text.length;
});

const transcriptionDuration = computed(() => {
  if (transcriptionSegments.value.length === 0) return null;

  const lastSegment = transcriptionSegments.value[transcriptionSegments.value.length - 1];
  const endTime = lastSegment.endTimeMs ?? lastSegment.end_time_ms ?? lastSegment.end;

  if (typeof endTime === 'number') {
    const totalSeconds = endTime < 10000000 ? endTime : Math.floor(endTime / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  return null;
});

const transcriptionQuality = computed(() => {
  const segmentCount = transcriptionSegmentCount.value;
  const wordCount = transcriptionWordCount.value;

  if (segmentCount === 0) {
    return { label: '无数据', color: 'bg-gray-400' };
  }

  if (segmentCount >= 10 && wordCount >= 100) {
    return { label: '优秀', color: 'bg-green-500' };
  } else if (segmentCount >= 5 && wordCount >= 50) {
    return { label: '良好', color: 'bg-blue-500' };
  } else {
    return { label: '一般', color: 'bg-yellow-500' };
  }
});

const transcriptionPreview = computed(() => {
  if (!audioToTextResult.value) return '暂无转录内容';

  let text = '';
  if (typeof audioToTextResult.value === 'string') {
    text = audioToTextResult.value;
  } else if (audioToTextResult.value.transcript) {
    text = audioToTextResult.value.transcript;
  } else if (transcriptionSegments.value.length > 0) {
    text = transcriptionSegments.value.slice(0, 3).map(seg => seg.text).join(' ');
  }

  if (text.length > 150) {
    return text.substring(0, 150) + '...';
  }

  return text || '暂无转录内容';
});

const transcriptionSummary = computed(() => {
  if (!audioToTextResult.value) return '';

  const segmentCount = transcriptionSegmentCount.value;
  const duration = transcriptionDuration.value;
  const wordCount = transcriptionWordCount.value;

  if (segmentCount > 0) {
    return `成功转录 ${segmentCount} 个字幕片段，共 ${wordCount} 个字符${duration ? `，总时长 ${duration}` : ''}`;
  }

  return '转录完成，点击查看详细信息';
});

// 方法
const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

const toggleStats = () => {
  showStats.value = !showStats.value;
};

const handleProcessAudioToText = () => {
  // 设置转录参数
  const transcriptionOptions = {
    model: selectedModel.value,
    language: selectedLanguage.value,
    enableCache: enableCache.value,
    needWordTimestamp: needWordTimestamp.value
  };

  // 模拟实时预览更新
  if (isLoading.value) {
    realtimePreview.value = '正在识别音频内容...';
  }

  subtitlerStore.processAudioToText(transcriptionOptions);
};

const pauseTranscription = () => {
  // 暂停转录逻辑
  console.log('暂停转录');
};

const viewResults = () => {
  showStats.value = true;
};

const handleErrorAction = (action) => {
  switch (action.key) {
    case 'retry':
      handleProcessAudioToText();
      break;
    case 'settings':
      // 滚动到设置区域
      document.querySelector('.current-actions')?.scrollIntoView({ behavior: 'smooth' });
      break;
  }
};

const goToNextStep = () => {
  subtitlerStore.setCurrentStep(3); // 进入字幕编辑
};

const clearLogs = () => {
  subtitlerStore.progressUpdates = [];
};

const handleLogCopy = (logText) => {
  console.log('日志已复制:', logText);
};

</script>

<style scoped>
/* Add any component-specific styles here */
.break-all {
  word-break: break-all;
}
</style>
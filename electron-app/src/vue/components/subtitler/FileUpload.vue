<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 1: 上传媒体文件</h3>
      <p class="text-sm text-gray-500 mt-1">选择视频或音频文件开始处理</p>
    </div>

    <!-- 文件上传区域 -->
    <div class="current-actions mb-6">
      <!-- 拖拽上传区域 -->
      <div
        @drop="handleDrop"
        @dragover.prevent
        @dragenter.prevent
        :class="[
          'border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200',
          isDragging
            ? 'border-blue-500 bg-blue-50'
            : subtitlerStore.uploadedFile
              ? 'border-green-500 bg-green-50'
              : 'border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100'
        ]"
        @dragenter="isDragging = true"
        @dragleave="isDragging = false"
      >
        <div v-if="!subtitlerStore.uploadedFile" class="space-y-4">
          <div class="text-4xl">📁</div>
          <div>
            <p class="text-lg font-medium text-gray-700">拖拽文件到此处或点击选择</p>
            <p class="text-sm text-gray-500 mt-1">支持 MP4, AVI, MOV, MP3, WAV 等格式</p>
          </div>
          <button
            @click="triggerFileSelect"
            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            📂 选择文件
          </button>
        </div>

        <!-- 已选择文件显示 -->
        <div v-else class="space-y-4">
          <div class="text-4xl">✅</div>
          <div>
            <p class="text-lg font-medium text-green-700">文件已选择</p>
            <p class="text-sm text-gray-600">{{ subtitlerStore.getUploadedFileName }}</p>
          </div>
        </div>
      </div>

      <!-- 隐藏的文件输入 -->
      <input
        ref="fileInput"
        type="file"
        accept="video/*,audio/*"
        @change="handleFileChange"
        class="hidden"
      />

      <!-- 文件信息卡片 -->
      <FileInfoCard
        v-if="subtitlerStore.uploadedFile"
        :file-info="fileInfo"
        :processing-status="processingStatus"
        :progress="processingProgress"
        :progress-text="progressText"
        :error-message="errorMessage"
        :show-actions="true"
        :allow-reselect="true"
        :allow-remove="false"
        @reselect="triggerFileSelect"
      />

      <!-- 操作按钮 -->
      <div class="flex space-x-3 mt-4">
        <button
          @click="triggerProcessVideoToAudio"
          :disabled="!subtitlerStore.uploadedFile || subtitlerStore.isLoading"
          class="flex-1 px-6 py-3 text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          🎤 {{ subtitlerStore.uploadedFile ? '开始处理' : '请先选择文件' }}
        </button>

        <button
          @click="reset"
          :disabled="!subtitlerStore.uploadedFile && !subtitlerStore.isLoading"
          class="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          🔄 重新选择
        </button>
      </div>
    </div>

    <!-- 处理状态显示 -->
    <StatusMessage
      v-if="statusMessage.message"
      :type="statusMessage.type"
      :title="statusMessage.title"
      :message="statusMessage.message"
      :details="statusMessage.details"
      :show-progress="statusMessage.showProgress"
      :progress="statusMessage.progress"
      :progress-text="statusMessage.progressText"
      :dismissible="statusMessage.dismissible"
      @dismiss="clearStatusMessage"
    />

    <!-- 可折叠的处理日志 -->
    <CollapsibleLog
      v-if="subtitlerStore.progressUpdates.length > 0"
      title="处理日志"
      :logs="formattedLogs"
      :initial-expanded="false"
      :show-stats="true"
      @clear="clearLogs"
      @copy="handleLogCopy"
    />

  </div>

  <!-- 下一步操作 (仅在处理完成且无错误时显示) -->
  <div
    v-if="showNextStepAction"
    class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg"
  >
    <div class="flex items-center justify-between">
      <div>
        <h4 class="text-sm font-medium text-green-800">文件处理完成</h4>
        <p class="text-sm text-green-600 mt-1">可以进入下一步进行语音转文字处理</p>
      </div>
      <button
        @click="goToNextStep"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
      >
        ➡️ 进入下一步
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, reactive } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import FileInfoCard from '@/vue/components/common/FileInfoCard.vue';
import StatusMessage from '@/vue/components/common/StatusMessage.vue';
import CollapsibleLog from '@/vue/components/common/CollapsibleLog.vue';

const subtitlerStore = useSubtitlerStore();

// 响应式数据
const isDragging = ref(false);
const fileInput = ref(null);

// 状态消息
const statusMessage = reactive({
  type: 'info',
  title: null,
  message: '',
  details: null,
  showProgress: false,
  progress: 0,
  progressText: '',
  dismissible: false
});

// 计算属性
const fileInfo = computed(() => {
  if (!subtitlerStore.uploadedFile) return null;

  return {
    name: subtitlerStore.getUploadedFileName,
    size: subtitlerStore.getUploadedFileSize,
    type: subtitlerStore.getUploadedFileType,
    duration: null // 如果有时长信息可以添加
  };
});

const processingStatus = computed(() => {
  if (subtitlerStore.isLoading) return 'processing';
  if (subtitlerStore.uploadedFile && !subtitlerStore.isLoading) return 'ready';
  return null;
});

const processingProgress = computed(() => {
  // 这里可以根据实际的处理进度返回
  return subtitlerStore.isLoading ? 50 : null;
});

const progressText = computed(() => {
  return subtitlerStore.isLoading ? '正在处理文件...' : null;
});

const errorMessage = computed(() => {
  // 这里可以根据错误状态返回错误信息
  return null;
});

const formattedLogs = computed(() => {
  return subtitlerStore.progressUpdates.map(update => ({
    timestamp: new Date().toISOString(),
    level: update.includes('✅') ? 'success' :
           update.includes('❌') ? 'error' :
           update.includes('⚠️') ? 'warning' : 'info',
    message: update
  }));
});

const showNextStepAction = computed(() => {
  return subtitlerStore.uploadedFile &&
         !subtitlerStore.isLoading &&
         subtitlerStore.progressUpdates.some(log => log.includes('✅'));
});

// 方法
const triggerFileSelect = () => {
  fileInput.value?.click();
};

const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    processFile(file);
  }
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragging.value = false;

  const files = event.dataTransfer.files;
  if (files.length > 0) {
    processFile(files[0]);
  }
};

const processFile = (file) => {
  // 验证文件类型
  const allowedTypes = [
    'video/mp4', 'video/avi', 'video/mov', 'video/quicktime', 'video/x-msvideo',
    'audio/mp3', 'audio/wav', 'audio/mpeg', 'audio/x-wav'
  ];

  if (!allowedTypes.some(type => file.type.includes(type.split('/')[1]))) {
    statusMessage.type = 'error';
    statusMessage.title = '文件格式不支持';
    statusMessage.message = '请选择支持的视频或音频文件格式';
    statusMessage.details = '支持格式: MP4, AVI, MOV, MP3, WAV 等';
    statusMessage.dismissible = true;
    return;
  }

  // 验证文件大小 (例如限制为2GB)
  const maxSize = 2 * 1024 * 1024 * 1024; // 2GB
  if (file.size > maxSize) {
    statusMessage.type = 'error';
    statusMessage.title = '文件过大';
    statusMessage.message = '文件大小不能超过 2GB';
    statusMessage.dismissible = true;
    return;
  }

  // 设置文件
  subtitlerStore.setUploadedFile(file);

  // 显示成功消息
  statusMessage.type = 'success';
  statusMessage.title = '文件选择成功';
  statusMessage.message = `已选择文件: ${file.name}`;
  statusMessage.dismissible = true;

  console.log('File selected:', file);
};

const triggerProcessVideoToAudio = () => {
  if (!subtitlerStore.uploadedFile) return;

  // 显示处理状态
  statusMessage.type = 'loading';
  statusMessage.title = '开始处理';
  statusMessage.message = '正在处理文件，请稍候...';
  statusMessage.showProgress = true;
  statusMessage.progress = 0;
  statusMessage.progressText = '初始化处理';
  statusMessage.dismissible = false;

  subtitlerStore.processVideoToAudio();
};

const reset = () => {
  subtitlerStore.resetWorkflow();

  // 清除文件输入
  if (fileInput.value) {
    fileInput.value.value = '';
  }

  // 清除状态消息
  clearStatusMessage();
};

const goToNextStep = () => {
  subtitlerStore.setCurrentStep(2);
};

const clearStatusMessage = () => {
  statusMessage.message = '';
  statusMessage.title = null;
  statusMessage.details = null;
  statusMessage.showProgress = false;
  statusMessage.progress = 0;
  statusMessage.progressText = '';
};

const clearLogs = () => {
  subtitlerStore.progressUpdates = [];
};

const handleLogCopy = (logText) => {
  // 显示复制成功消息
  statusMessage.type = 'success';
  statusMessage.title = '复制成功';
  statusMessage.message = '日志内容已复制到剪贴板';
  statusMessage.dismissible = true;
};
</script>

<style scoped>
/* 统一样式已由StepContainer提供 */
</style>
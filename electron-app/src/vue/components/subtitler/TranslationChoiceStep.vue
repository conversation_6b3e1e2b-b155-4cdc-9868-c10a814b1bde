<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 7: 翻译选择</h3>
      <p class="text-sm text-gray-500 mt-1">选择是否需要翻译字幕以及翻译设置</p>
    </div>

    <!-- 前置结果摘要 -->
    <div v-if="hasEditableSegments" class="previous-results mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <svg class="h-4 w-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          <span class="text-sm font-medium text-green-800">字幕编辑已完成</span>
        </div>
        <span class="text-xs text-green-600">已编辑 {{ editableSegmentsCount }} 个字幕片段</span>
      </div>
    </div>

    <!-- 前置条件警告 -->
    <div v-if="!hasEditableSegments" class="prerequisite-warning mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
      <div class="flex items-start">
        <svg class="h-5 w-5 text-yellow-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        <div class="flex-1">
          <h4 class="text-sm font-medium text-yellow-800">需要先完成字幕编辑</h4>
          <p class="text-sm text-yellow-700 mt-1">请先在步骤6中完成字幕编辑，准备好字幕内容后再选择是否翻译</p>
          <button
            @click="handlePrerequisiteAction({ step: 6 })"
            class="mt-2 text-sm text-yellow-800 underline hover:text-yellow-900 transition-colors">
            前往字幕编辑
          </button>
        </div>
      </div>
    </div>

    <!-- 当前操作区域 -->
    <div v-if="hasEditableSegments" class="current-actions mb-6">
      <!-- 字幕预览 -->
      <div class="bg-gray-50 rounded-lg p-4 mb-6">
        <h4 class="text-sm font-medium text-gray-700 mb-3">当前字幕预览</h4>
        <div class="bg-white border border-gray-200 rounded-md p-3 max-h-32 overflow-y-auto">
          <div class="space-y-1">
            <div v-for="(subtitle, index) in subtitlePreview" :key="index" class="text-sm">
              <span class="text-gray-500 font-mono text-xs">{{ formatTime(subtitle.startTimeMs) }} → {{ formatTime(subtitle.endTimeMs) }}</span>
              <span class="text-gray-700 ml-2">{{ subtitle.text }}</span>
            </div>
          </div>
        </div>
        <p class="text-sm text-gray-500 mt-2">共 {{ editableSegmentsCount }} 个字幕片段</p>
      </div>

      <!-- 翻译选择 -->
      <div class="space-y-6">
        <div class="text-center mb-6">
          <h3 class="text-lg font-semibold text-gray-800 mb-2">是否需要翻译字幕？</h3>
          <p class="text-gray-600">您可以选择将字幕翻译成其他语言，或直接进入导出步骤</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 不翻译选项 -->
          <div class="border-2 rounded-lg p-4 cursor-pointer transition-all"
               :class="{
                 'border-green-500 bg-green-50': !needTranslation,
                 'border-gray-200 hover:border-gray-300': needTranslation
               }"
               @click="setTranslationChoice(false)">
            <div class="text-center">
              <div class="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3"
                   :class="{ 'bg-green-100': !needTranslation, 'bg-gray-100': needTranslation }">
                <svg class="w-6 h-6" :class="{ 'text-green-600': !needTranslation, 'text-gray-500': needTranslation }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="font-semibold text-gray-800 mb-1">不需要翻译</h4>
              <p class="text-gray-600 text-sm">直接使用当前语言的字幕</p>
            </div>
          </div>

          <!-- 翻译选项 -->
          <div class="border-2 rounded-lg p-4 cursor-pointer transition-all"
               :class="{
                 'border-indigo-500 bg-indigo-50': needTranslation,
                 'border-gray-200 hover:border-gray-300': !needTranslation
               }"
               @click="setTranslationChoice(true)">
            <div class="text-center">
              <div class="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3"
                   :class="{ 'bg-indigo-100': needTranslation, 'bg-gray-100': !needTranslation }">
                <svg class="w-6 h-6" :class="{ 'text-indigo-600': needTranslation, 'text-gray-500': !needTranslation }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                </svg>
              </div>
              <h4 class="font-semibold text-gray-800 mb-1">需要翻译</h4>
              <p class="text-gray-600 text-sm">将字幕翻译成其他语言</p>
            </div>
          </div>
        </div>

        <!-- 翻译设置 -->
        <div v-if="needTranslation" class="bg-indigo-50 border border-indigo-200 rounded-lg p-4 space-y-4">
          <h4 class="text-sm font-medium text-indigo-800 mb-3">翻译设置</h4>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">目标语言</label>
              <select v-model="targetLanguage" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white">
                <option value="zh">中文 (简体)</option>
                <option value="zh-TW">中文 (繁体)</option>
                <option value="en">English</option>
                <option value="ja">日本語</option>
                <option value="ko">한국어</option>
                <option value="es">Español</option>
                <option value="fr">Français</option>
                <option value="de">Deutsch</option>
                <option value="ru">Русский</option>
                <option value="ar">العربية</option>
              </select>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">翻译质量</label>
              <select v-model="translationQuality" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white">
                <option value="fast">快速翻译 - 速度优先</option>
                <option value="balanced">平衡模式 - 速度与质量兼顾</option>
                <option value="quality">高质量 - 质量优先</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">翻译风格</label>
            <select v-model="translationStyle" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 bg-white">
              <option value="formal">正式 - 适合商务、学术内容</option>
              <option value="casual">随意 - 适合日常对话</option>
              <option value="literary">文学 - 适合文艺作品</option>
              <option value="technical">技术 - 适合技术文档</option>
            </select>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">自定义翻译提示 (可选)</label>
            <textarea
              v-model="customTranslationPrompt"
              placeholder="例如：这是一个烹饪教程，请保持食材和烹饪术语的准确性..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 resize-none bg-white"
              rows="3">
            </textarea>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex justify-between items-center pt-4">
          <button
            @click="goToPreviousStep"
            class="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 font-medium transition-colors text-base">
            ← 返回编辑
          </button>

          <div class="flex space-x-3">
            <!-- 不翻译时显示导出按钮 -->
            <button
              v-if="needTranslation === false"
              @click="skipToExport"
              class="px-6 py-3 text-white bg-green-600 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 font-medium transition-colors text-base">
              📥 进入导出
            </button>

            <!-- 需要翻译时显示开始翻译按钮 -->
            <button
              v-if="needTranslation === true && targetLanguage"
              @click="proceedToTranslation"
              class="px-6 py-3 text-white bg-indigo-600 rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 font-medium transition-colors text-base">
              🌐 开始翻译
            </button>
          </div>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';

const subtitlerStore = useSubtitlerStore();

// Local state
const needTranslation = ref(false);
const targetLanguage = ref('zh'); // 默认简体中文
const translationQuality = ref('balanced');
const translationStyle = ref('formal');
const customTranslationPrompt = ref('');

// Computed properties
const hasEditableSegments = computed(() => subtitlerStore.editableSegments && subtitlerStore.editableSegments.length > 0);
const editableSegmentsCount = computed(() => subtitlerStore.editableSegments ? subtitlerStore.editableSegments.length : 0);

const subtitlePreview = computed(() => {
  if (!hasEditableSegments.value) return [];
  return subtitlerStore.editableSegments.slice(0, 5); // 显示前5个
});



// Methods
const formatTime = (timeMs) => {
  const totalSeconds = Math.floor(timeMs / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

const getLanguageName = (code) => {
  const languages = {
    'zh': '中文(简体)',
    'zh-TW': '中文(繁体)',
    'en': 'English',
    'ja': '日本語',
    'ko': '한국어',
    'es': 'Español',
    'fr': 'Français',
    'de': 'Deutsch',
    'ru': 'Русский',
    'ar': 'العربية'
  };
  return languages[code] || code;
};

const setTranslationChoice = (choice) => {
  needTranslation.value = choice;
};

const proceedToTranslation = () => {
  // 保存翻译设置到store
  subtitlerStore.setTranslationSettings({
    targetLanguage: targetLanguage.value,
    quality: translationQuality.value,
    style: translationStyle.value,
    customPrompt: customTranslationPrompt.value
  });

  // 进入翻译步骤
  subtitlerStore.setCurrentStep(8);
};

const skipToExport = () => {
  // 跳过翻译，直接进入导出步骤
  subtitlerStore.setCurrentStep(6);
};

const goToPreviousStep = () => {
  subtitlerStore.setCurrentStep(5); // 返回优化字幕步骤
};


</script>

<style scoped>
/* 组件特定样式 */
</style>

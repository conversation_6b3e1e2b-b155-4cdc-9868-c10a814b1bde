<template>
  <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
    <!-- 步骤头部 -->
    <div class="step-header mb-6">
      <h3 class="text-xl font-semibold text-gray-800">Step 5: 优化字幕</h3>
      <p class="text-sm text-gray-500 mt-1">使用AI优化字幕的语法和表达</p>
    </div>

    <!-- 前置条件警告 -->
    <StatusMessage
      v-if="!hasGeneratedSubtitles"
      type="warning"
      title="需要先完成字幕生成"
      message="请先完成语音转文字和字幕编辑，准备好字幕内容后再进行优化"
      :actions="[
        { key: 'goto-edit', label: '前往字幕编辑', icon: '📝', primary: true }
      ]"
      @action="handlePrerequisiteAction"
    />

    <!-- 字幕预览 -->
    <div v-if="hasGeneratedSubtitles" class="mb-6">
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-blue-800">📝 字幕内容预览</h4>
          <span class="text-xs text-blue-600">共 {{ generatedSubtitlesCount }} 个片段</span>
        </div>

        <div class="bg-white border border-blue-200 rounded-md p-3 max-h-32 overflow-y-auto">
          <div class="space-y-1">
            <div v-for="(subtitle, index) in subtitlePreview" :key="index" class="text-sm">
              <span class="text-gray-500 font-mono text-xs">{{ formatTime(subtitle.startTimeMs) }} → {{ formatTime(subtitle.endTimeMs) }}</span>
              <span class="text-gray-700 ml-2">{{ subtitle.text }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优化设置 -->
    <div v-if="hasGeneratedSubtitles && !isLoading" class="current-actions mb-6">
      <div class="bg-gray-50 rounded-lg p-4 mb-4">
        <h4 class="text-sm font-medium text-gray-700 mb-4">🎯 优化设置</h4>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">AI服务:</label>
            <div v-if="availableAIServices.length > 0">
              <select
                v-model="selectedAIService"
                @change="onAIServiceChange"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              >
                <option v-for="service in availableAIServices" :key="service.id" :value="service.id">
                  {{ service.display_name }} ({{ service.provider_type }})
                </option>
              </select>
              <div v-if="selectedServiceInfo" class="mt-2 text-xs text-gray-600">
                <div>模型: {{ selectedServiceInfo.default_model || '默认' }}</div>
                <div v-if="selectedServiceInfo.api_base_url">API: {{ selectedServiceInfo.api_base_url }}</div>
              </div>
            </div>
            <div v-else class="text-sm text-gray-500 p-3 bg-gray-50 rounded-md">
              <div class="flex items-center space-x-2">
                <span>⚠️</span>
                <span>未找到可用的AI服务配置</span>
              </div>
              <button
                @click="goToAISettings"
                class="mt-2 text-orange-600 hover:text-orange-800 text-xs underline"
              >
                前往AI设置页面配置
              </button>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">优化强度:</label>
            <select v-model="optimizationLevel" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm">
              <option value="light">轻度 - 仅修正错误</option>
              <option value="medium">标准 - 改善语法流畅度</option>
              <option value="heavy">深度 - 全面优化润色</option>
            </select>
          </div>
        </div>

        <!-- 优化选项 -->
        <div class="space-y-3 mb-4">
          <h5 class="text-sm font-medium text-gray-700">优化目标:</h5>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
            <label class="flex items-center space-x-2 text-sm text-gray-700">
              <input
                type="checkbox"
                v-model="optimizationOptions.grammar"
                class="rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"
              />
              <span>语法修正</span>
            </label>

            <label class="flex items-center space-x-2 text-sm text-gray-700">
              <input
                type="checkbox"
                v-model="optimizationOptions.punctuation"
                class="rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"
              />
              <span>断句优化</span>
            </label>

            <label class="flex items-center space-x-2 text-sm text-gray-700">
              <input
                type="checkbox"
                v-model="optimizationOptions.expression"
                class="rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-300 focus:ring focus:ring-orange-200 focus:ring-opacity-50"
              />
              <span>用词提升</span>
            </label>
          </div>
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">自定义提示 (可选):</label>
          <textarea
            v-model="customPrompt"
            placeholder="例如：这是技术教程，请保持专业术语准确性..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 resize-none"
            rows="2">
          </textarea>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-3">
        <button
          @click="optimizeSubtitles"
          :disabled="isLoading || hasOptimizedSubtitles"
          class="flex-1 px-6 py-3 text-white bg-orange-600 rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          🤖 {{ hasOptimizedSubtitles ? '已优化完成' : '开始AI优化' }}
        </button>

        <button
          @click="skipOptimization"
          :disabled="isLoading"
          class="px-6 py-3 text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium transition-colors text-base"
        >
          ⏭️ 跳过优化
        </button>

        <button
          v-if="hasOptimizedSubtitles && !isLoading"
          @click="goToNextStep"
          class="px-6 py-3 text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 font-medium transition-colors text-base"
        >
          ➡️ 下一步
        </button>
      </div>
    </div>

    <!-- 优化进度显示 -->
    <div v-if="isLoading" class="mb-6">
      <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-orange-800">🤖 正在AI优化</h4>
          <span class="text-xs text-orange-600">{{ formatTime(new Date()) }}</span>
        </div>

        <div class="mb-3">
          <div class="flex items-center justify-between text-sm mb-1">
            <span class="text-orange-700">📊 优化进度</span>
            <span class="text-orange-700">{{ Math.round(optimizationProgress || 0) }}%</span>
          </div>
          <div class="w-full bg-orange-200 rounded-full h-2">
            <div
              class="bg-orange-600 h-2 rounded-full transition-all duration-300"
              :style="{ width: `${Math.min(100, Math.max(0, optimizationProgress || 0))}%` }"
            />
          </div>
        </div>

        <div class="text-sm text-orange-600">
          ⏱️ {{ progressText || '正在使用AI优化字幕语法和表达...' }}
        </div>

        <!-- 优化预览 -->
        <div v-if="optimizationPreview" class="mt-3 p-3 bg-white rounded-md border border-orange-200">
          <div class="text-xs text-orange-600 mb-1">💡 优化预览</div>
          <div class="space-y-1">
            <div class="text-sm text-gray-600">原文: {{ optimizationPreview.original }}</div>
            <div class="text-sm text-gray-800">优化: {{ optimizationPreview.optimized }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 优化完成显示 -->
    <div v-if="hasOptimizedSubtitles && !isLoading" class="mb-6">
      <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-3">
          <h4 class="text-sm font-medium text-green-800">✅ AI优化完成</h4>
          <button
            @click="toggleOptimizationDetails"
            class="text-xs text-green-600 hover:text-green-800 focus:outline-none"
          >
            {{ showOptimizationDetails ? '隐藏详情' : '查看详情' }} {{ showOptimizationDetails ? '▲' : '▼' }}
          </button>
        </div>

        <p class="text-sm text-green-700 mb-3">{{ optimizationSummary }}</p>

        <!-- 优化详情 -->
        <div v-if="showOptimizationDetails" class="space-y-3">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div class="text-center p-3 bg-white rounded-md border border-green-200">
              <div class="text-lg font-bold text-green-700">{{ optimizedSubtitlesCount }}</div>
              <div class="text-xs text-green-600">优化片段</div>
            </div>
            <div class="text-center p-3 bg-white rounded-md border border-green-200">
              <div class="text-lg font-bold text-green-700">{{ optimizationStats.grammarFixes || 0 }}</div>
              <div class="text-xs text-green-600">语法修正</div>
            </div>
            <div class="text-center p-3 bg-white rounded-md border border-green-200">
              <div class="text-lg font-bold text-green-700">{{ optimizationStats.punctuationFixes || 0 }}</div>
              <div class="text-xs text-green-600">标点优化</div>
            </div>
            <div class="text-center p-3 bg-white rounded-md border border-green-200">
              <div class="text-lg font-bold text-green-700">{{ optimizationStats.expressionImprovements || 0 }}</div>
              <div class="text-xs text-green-600">表达提升</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 可折叠的优化日志 -->
    <CollapsibleLog
      v-if="subtitlerStore.progressUpdates.length > 0"
      title="优化日志"
      :logs="formattedLogs"
      :initial-expanded="false"
      :show-stats="true"
      @clear="clearLogs"
      @copy="handleLogCopy"
    />

  </div>
</template>

<script setup>
import { ref, computed, reactive, onMounted } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import StatusMessage from '@/vue/components/common/StatusMessage.vue';
import CollapsibleLog from '@/vue/components/common/CollapsibleLog.vue';

const subtitlerStore = useSubtitlerStore();

// AI 服务相关数据
const availableAIServices = ref([]);
const selectedAIService = ref(null);
const loadingAIServices = ref(false);

// 响应式数据
const optimizationLevel = ref('medium');
const customPrompt = ref('');
const showOptimizationDetails = ref(false);
const optimizationProgress = ref(0);
const progressText = ref('');
const optimizationPreview = ref(null);

// 优化选项
const optimizationOptions = reactive({
  grammar: true,
  punctuation: true,
  expression: true
});

// 计算属性
const isLoading = computed(() => subtitlerStore.isLoading);
const hasGeneratedSubtitles = computed(() => subtitlerStore.generatedSubtitles && subtitlerStore.generatedSubtitles.length > 0);
const generatedSubtitlesCount = computed(() => subtitlerStore.generatedSubtitles ? subtitlerStore.generatedSubtitles.length : 0);
const hasOptimizedSubtitles = computed(() => subtitlerStore.optimizedSubtitles && subtitlerStore.optimizedSubtitles.length > 0);
const optimizedSubtitlesCount = computed(() => subtitlerStore.optimizedSubtitles ? subtitlerStore.optimizedSubtitles.length : 0);

const subtitlePreview = computed(() => {
  if (!hasGeneratedSubtitles.value) return [];
  return subtitlerStore.generatedSubtitles.slice(0, 5); // 显示前5个
});

const optimizationSummary = computed(() => {
  if (!hasOptimizedSubtitles.value) return '';

  return `成功优化 ${optimizedSubtitlesCount.value} 个字幕片段，提升了语法准确性和表达流畅度`;
});

const optimizationStats = computed(() => {
  // 模拟优化统计数据，实际应该从store获取
  return {
    grammarFixes: Math.floor(optimizedSubtitlesCount.value * 0.3),
    punctuationFixes: Math.floor(optimizedSubtitlesCount.value * 0.2),
    expressionImprovements: Math.floor(optimizedSubtitlesCount.value * 0.4)
  };
});

const formattedLogs = computed(() => {
  return subtitlerStore.progressUpdates.map(update => ({
    timestamp: new Date().toISOString(),
    level: update.includes('✅') ? 'success' :
           update.includes('❌') ? 'error' :
           update.includes('⚠️') ? 'warning' : 'info',
    message: update
  }));
});

// AI 服务相关计算属性
const selectedServiceInfo = computed(() => {
  if (!selectedAIService.value) return null;
  return availableAIServices.value.find(service => service.id === selectedAIService.value);
});



// 方法
const formatTime = (timeValue) => {
  // 处理空值
  if (timeValue == null || timeValue === undefined) {
    return '00:00';
  }

  if (typeof timeValue === 'number') {
    // 如果是时间戳（毫秒）
    const totalSeconds = Math.floor(timeValue / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  } else if (timeValue instanceof Date) {
    // 如果是Date对象
    return timeValue.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } else {
    // 其他情况，尝试转换为数字
    const numValue = Number(timeValue);
    if (!isNaN(numValue)) {
      const totalSeconds = Math.floor(numValue / 1000);
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = totalSeconds % 60;
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return '00:00';
  }
};

// AI 服务管理方法
const loadAIServices = async () => {
  try {
    loadingAIServices.value = true;
    const result = await window.electronAPI.invoke('get-ai-configs');

    if (result.success && result.configs) {
      // 只显示已启用的配置
      availableAIServices.value = result.configs.filter(config => config.is_enabled);

      // 自动选择第一个可用的服务
      if (availableAIServices.value.length > 0 && !selectedAIService.value) {
        selectedAIService.value = availableAIServices.value[0].id;
      }

      console.log(`[OptimizeStep] 加载了 ${availableAIServices.value.length} 个可用的AI服务`);
    } else {
      console.warn('[OptimizeStep] 获取AI配置失败:', result.message);
    }
  } catch (error) {
    console.error('[OptimizeStep] 加载AI服务时出错:', error);
  } finally {
    loadingAIServices.value = false;
  }
};

const onAIServiceChange = () => {
  console.log('[OptimizeStep] AI服务已切换:', selectedAIService.value);
};

const goToAISettings = () => {
  // 跳转到AI设置页面
  window.electronAPI.invoke('open-ai-settings');
};

const optimizeSubtitles = async () => {
  if (!hasGeneratedSubtitles.value) return;

  if (!selectedAIService.value) {
    console.error('请先选择AI服务');
    return;
  }

  // 设置优化参数
  const optimizationParams = {
    aiServiceId: selectedAIService.value,
    level: optimizationLevel.value,
    options: optimizationOptions,
    customPrompt: customPrompt.value
  };

  // 模拟优化进度
  optimizationProgress.value = 0;
  progressText.value = '准备优化...';

  // 模拟实时预览
  optimizationPreview.value = {
    original: '这个这个技术呢就是...',
    optimized: '这项技术的核心原理是...'
  };

  try {
    await subtitlerStore.optimizeSubtitles(optimizationParams);
  } catch (error) {
    console.error('优化字幕时出错:', error);
  }
};

const skipOptimization = () => {
  // 跳过优化，直接使用生成的字幕作为可编辑字幕
  subtitlerStore.setEditableSegments(subtitlerStore.generatedSubtitles);
  subtitlerStore.setCurrentStep(5); // 进入翻译设置
};

const toggleOptimizationDetails = () => {
  showOptimizationDetails.value = !showOptimizationDetails.value;
};

const handlePrerequisiteAction = (action) => {
  if (action.key === 'goto-edit') {
    subtitlerStore.setCurrentStep(3); // 前往字幕编辑
  }
};

const goToNextStep = () => {
  subtitlerStore.setCurrentStep(7); // 进入翻译选择
};

const clearLogs = () => {
  subtitlerStore.progressUpdates = [];
};

const handleLogCopy = (logText) => {
  console.log('日志已复制:', logText);
};

// 组件挂载时加载AI服务
onMounted(() => {
  loadAIServices();
});
</script>

<style scoped>
/* 组件特定样式 */
</style>

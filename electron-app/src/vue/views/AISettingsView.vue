<template>
  <div class="ai-settings-view p-4 flex h-full">
    <!-- Left Panel: Service List -->
    <div class="w-1/3 pr-4 border-r border-gray-300 overflow-y-auto">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold">AI 服务列表</h2>
        <button
          @click="handleAddService"
          class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          添加服务
        </button>
      </div>
      <ul>
        <li
          v-for="service in aiServices"
          :key="service.provider_id"
          @click="handleSelectService(service)"
          :class="[
            'p-3 mb-2 rounded cursor-pointer hover:bg-gray-100',
            selectedService && selectedService.provider_id === service.provider_id ? 'bg-blue-100 border border-blue-300' : 'border border-gray-200'
          ]"
        >
          <div class="flex justify-between items-center">
            <div>
              <h3 class="font-medium">{{ service.display_name }}</h3>
              <p class="text-sm text-gray-600">{{ service.provider_type }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <label :for="'toggle-' + service.provider_id" class="flex items-center cursor-pointer">
                <div class="relative">
                  <input type="checkbox" :id="'toggle-' + service.provider_id" class="sr-only" v-model="service.is_enabled" @change="toggleServiceEnabled(service)">
                  <div class="block bg-gray-600 w-10 h-6 rounded-full"></div>
                  <div :class="{'translate-x-full': service.is_enabled}" class="dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition"></div>
                </div>
              </label>
              <button @click.stop="handleEditService(service)" class="p-1 text-gray-600 hover:text-blue-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                </svg>
              </button>
              <button @click.stop="handleDeleteService(service.provider_id)" class="p-1 text-gray-600 hover:text-red-500">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </li>
        <li v-if="!aiServices.length" class="text-center text-gray-500 py-4">
          暂无 AI 服务配置。
        </li>
      </ul>
    </div>

    <!-- Right Panel: Configuration Form / Welcome Message -->
    <div class="w-2/3 pl-4 overflow-y-auto">
      <div v-if="editingService">
        <AIServiceConfigForm
          :key="formKey"
          :service-config="editingService"
          :is-new="isNewService"
          @save="saveServiceConfiguration"
          @cancel="cancelEdit"
        />
      </div>
      <div v-else class="flex flex-col items-center justify-center h-full text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <p class="text-lg">请从左侧选择一个服务进行编辑，或添加新的 AI 服务。</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import AIServiceConfigForm from '../components/AIServiceConfigForm.vue';

const aiServices = ref([]);
const selectedService = ref(null);
const editingService = ref(null); // This will hold the service object being edited or a new template
const isNewService = ref(false);
const formKey = ref(0); // Used to force re-render of the form

// IPC functions (assuming they are exposed on window.electronAPI via preload.js)
const loadAiConfigs = async () => {
  try {
    const configs = await window.electronAPI.invoke('load-ai-configs');
    aiServices.value = configs || [];
    if (aiServices.value.length > 0 && !selectedService.value) {
      // Optionally select the first service by default
      // handleSelectService(aiServices.value[0]);
    }
  } catch (error) {
    console.error('Failed to load AI configs:', error);
    // TODO: Show error to user
  }
};

const saveAiConfigsToMain = async (configs, retryCount = 0) => {
  const maxRetries = 3;
  const retryDelay = 2000; // 2 seconds

  try {
    // Ensure the object sent over IPC is fully serializable by performing a deep clone via JSON methods.
    const serializableConfigs = JSON.parse(JSON.stringify(configs));
    const result = await window.electronAPI.invoke('save-ai-configs', serializableConfigs);
    if (result.success) {
      console.log('AI configs saved successfully.');
      // Optionally show a success message
    } else {
      console.error('Failed to save AI configs:', result.message);

      // Check if it's a gRPC client availability issue and retry
      if (result.message && result.message.includes('gRPC client') && retryCount < maxRetries) {
        console.log(`Retrying save operation (attempt ${retryCount + 1}/${maxRetries}) in ${retryDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return await saveAiConfigsToMain(configs, retryCount + 1);
      }

      // TODO: Show error to user
    }
    return result.success;
  } catch (error) {
    console.error('Error saving AI configs:', error);

    // Retry on network/connection errors
    if (retryCount < maxRetries && (error.message.includes('gRPC') || error.message.includes('client'))) {
      console.log(`Retrying save operation due to error (attempt ${retryCount + 1}/${maxRetries}) in ${retryDelay}ms...`);
      await new Promise(resolve => setTimeout(resolve, retryDelay));
      return await saveAiConfigsToMain(configs, retryCount + 1);
    }

    // TODO: Show error to user
    return false;
  }
};

// Check if AI Config Service is available
const checkServiceAvailability = async () => {
  try {
    const result = await window.electronAPI.invoke('check-ai-config-service');
    console.log('AI Config Service availability:', result);
    return result.available;
  } catch (error) {
    console.error('Error checking AI Config Service availability:', error);
    return false;
  }
};

onMounted(async () => {
  loadAiConfigs();

  // Check service availability on mount
  const isAvailable = await checkServiceAvailability();
  if (!isAvailable) {
    console.warn('AI Config Service is not available yet. Some features may not work until the backend is ready.');
  }
});

const handleSelectService = (service) => {
  selectedService.value = service;
  isNewService.value = false;
  // Deep clone the service object for editing to avoid direct mutation
  editingService.value = JSON.parse(JSON.stringify(service));
  formKey.value++; // Increment key to re-mount the form component
};

const handleAddService = () => {
  selectedService.value = null; // Deselect any currently selected service
  isNewService.value = true;
  editingService.value = {
    provider_id: `new_service_${Date.now()}`, // Temporary ID
    provider_type: '', // User will select this
    display_name: '',
    is_enabled: true,
    credentials: {},
    attributes: {},
    metadata: { created_at: new Date().toISOString() }
  };
  formKey.value++;
};

const handleEditService = (service) => {
  selectedService.value = service; // Keep it selected in the list
  isNewService.value = false;
  editingService.value = JSON.parse(JSON.stringify(service));
  formKey.value++;
};

const handleDeleteService = async (providerId) => {
  // TODO: Add confirmation dialog
  if (!confirm('确定要删除此 AI 服务配置吗？')) {
    return;
  }

  aiServices.value = aiServices.value.filter(s => s.provider_id !== providerId);
  const success = await saveAiConfigsToMain(aiServices.value);
  if (success) {
    if (selectedService.value && selectedService.value.provider_id === providerId) {
      selectedService.value = null;
      editingService.value = null;
    }
    // Optionally, if editingService was the one deleted, clear the form
    if (editingService.value && editingService.value.provider_id === providerId) {
        editingService.value = null;
    }
  } else {
    // Revert if save failed (or handle more gracefully)
    loadAiConfigs(); // Reload to be safe
  }
};

const saveServiceConfiguration = async (configToSave) => {
  const index = aiServices.value.findIndex(s => s.provider_id === configToSave.provider_id_original || s.provider_id === configToSave.provider_id);

  if (configToSave.provider_id_original && configToSave.provider_id_original !== configToSave.provider_id) {
    // Provider ID has changed, ensure new ID is unique if it's not the original one
    const existingWithNewId = aiServices.value.find(s => s.provider_id === configToSave.provider_id && s.provider_id !== configToSave.provider_id_original);
    if (existingWithNewId) {
      alert(`错误：服务 ID "${configToSave.provider_id}" 已存在。请使用唯一的 ID。`);
      // TODO: Better error display
      return;
    }
  } else if (isNewService.value) {
     // For new service, check if ID already exists
    const existingWithNewId = aiServices.value.find(s => s.provider_id === configToSave.provider_id);
    if (existingWithNewId) {
      alert(`错误：服务 ID "${configToSave.provider_id}" 已存在。请使用唯一的 ID。`);
      // TODO: Better error display
      return;
    }
  }


  const serviceToStore = { ...configToSave };
  delete serviceToStore.provider_id_original; // Clean up temporary property

  if (index !== -1) { // Existing service updated
    aiServices.value.splice(index, 1, serviceToStore);
  } else { // New service added
    aiServices.value.push(serviceToStore);
  }

  const success = await saveAiConfigsToMain(aiServices.value);
  if (success) {
    editingService.value = null; // Close form
    selectedService.value = serviceToStore; // Select the newly saved/updated service
    isNewService.value = false;
    await loadAiConfigs(); // Reload to ensure data consistency and get potentially processed data from main
    // After reloading, find and re-select the service
    await nextTick();
    const reloadedService = aiServices.value.find(s => s.provider_id === serviceToStore.provider_id);
    if (reloadedService) {
        selectedService.value = reloadedService;
        // If we want to keep the form open for further edits, uncomment below
        // editingService.value = JSON.parse(JSON.stringify(reloadedService));
        // formKey.value++;
    } else {
        editingService.value = null;
    }

  } else {
    // Handle save failure, maybe show an error and don't close the form
    // For now, we'll just log it, error handling above will show console error
  }
};

const cancelEdit = () => {
  editingService.value = null;
  isNewService.value = false;
  // If a service was selected before "Add New", re-select it.
  // Otherwise, selectedService remains as is (or null).
};

const toggleServiceEnabled = async (service) => {
  // The v-model on checkbox already updates service.is_enabled
  // We just need to save the entire list
  const success = await saveAiConfigsToMain(aiServices.value);
  if (!success) {
    // Revert the toggle if save failed
    service.is_enabled = !service.is_enabled;
    // TODO: Show error to user
  }
};

</script>

<style scoped>
/* Basic toggle switch styling */
.dot {
  transition: transform .3s ease-in-out;
}
/* Add any additional scoped styles here */
</style>
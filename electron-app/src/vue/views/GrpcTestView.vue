<template>
  <div class="grpc-test-container">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-center bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">gRPC 测试面板</h1>
      <p class="text-center text-gray-600 text-sm">多后端服务通信测试工具</p>
    </div>

    <!-- Backend Status Section -->
    <div class="card mb-8 animate-fade-in">
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-semibold text-gray-800 flex items-center">
          <div class="w-8 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg flex items-center justify-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 text-white">
              <path stroke-linecap="round" stroke-linejoin="round" d="M5.25 14.25h13.5m-13.5 0a3 3 0 01-3-3V6a3 3 0 013-3h13.5a3 3 0 013 3v5.25a3 3 0 01-3 3m-16.5 0a3 3 0 013 3v5.25a3 3 0 013 3h13.5a3 3 0 013-3v-5.25a3 3 0 01-3-3" />
            </svg>
          </div>
          后端服务状态
        </h3>
        <div class="flex items-center space-x-3">
          <!-- 自动刷新状态指示器 -->
          <div v-if="autoRefreshTimer" class="flex items-center text-sm text-gray-600">
            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2"></div>
            <span>自动检测中...</span>
          </div>
          <div v-else-if="areAllServicesReady" class="flex items-center text-sm text-green-600">
            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
            <span>所有服务正常</span>
          </div>

          <!-- 手动刷新按钮 -->
          <button
            @click="refreshStatus"
            :disabled="isRefreshing"
            class="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white text-sm font-medium rounded-lg transition-colors duration-200 flex items-center">
            <svg v-if="isRefreshing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
            </svg>
            {{ isRefreshing ? '刷新中...' : '刷新状态' }}
          </button>
        </div>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div v-for="(service, key) in backendServices" :key="key" class="bg-gradient-to-br from-gray-50 to-gray-100 p-4 rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-200">
          <div class="flex items-center mb-3">
            <div :class="['w-3 h-3 rounded-full mr-3 animate-pulse', service.bgColor]"></div>
            <span class="font-semibold text-gray-800 capitalize">{{ key }} 后端</span>
          </div>
          <p class="text-sm text-gray-600 mb-2">{{ service.status }}</p>
          <p :class="['text-sm font-medium', service.clientStatus.ready ? 'text-green-600' : 'text-red-600']">
            客户端: {{ service.clientStatus.message }}
          </p>
        </div>
      </div>
    </div>
    
    <!-- Template Selector -->
    <div class="card mb-6 animate-slide-up">
      <h3 class="text-lg font-semibold mb-4 text-gray-800 flex items-center">
        <div class="w-6 h-6 bg-gradient-to-r from-purple-400 to-pink-500 rounded-lg flex items-center justify-center mr-3">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 text-white">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c0 .621-.504 1.125-1.125 1.125H18a2.25 2.25 0 01-2.25-2.25V9.375c0-.621.504-1.125 1.125-1.125H20.25a2.25 2.25 0 012.25 2.25v11.25a2.25 2.25 0 01-2.25 2.25H21M8.25 8.25l6.75-6.75" />
          </svg>
        </div>
        快速测试模板
      </h3>
      <label for="template-selector" class="block text-sm font-medium text-gray-700 mb-2">选择预设模板:</label>
      <select
        id="template-selector"
        v-model="selectedTemplate"
        @change="applySelectedTemplate"
        class="input-field"
      >
        <option :value="null" disabled>-- 选择模板 --</option>
        <option v-if="isLoading && availableServicesWithTemplates.length === 0" disabled>加载模板中...</option>
        <option v-if="!isLoading && availableServicesWithTemplates.length === 0" disabled>暂无可用模板</option>
        <template v-for="service in availableServicesWithTemplates" :key="service.name">
          <optgroup :label="service.name + ' 模板'">
            <option v-for="template in service.templates" :key="template.templateName" :value="template">
              {{ template.templateName }}
            </option>
          </optgroup>
        </template>
      </select>
    </div>

    <div v-if="store.error" class="p-4 mb-6 text-sm text-red-700 bg-red-50 border border-red-200 rounded-xl" role="alert">
      <div class="flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 mr-2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z" />
        </svg>
        <span class="font-medium">错误:</span> {{ store.error }}
      </div>
    </div>
    
    <!-- Test Components -->
    <div class="space-y-6">
      <div class="card animate-slide-up">
        <ServiceSelector />
      </div>

      <div class="card animate-slide-up">
        <RequestEditor />
      </div>

      <div class="card animate-slide-up">
        <ResponseDisplay />
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-4 mt-8">
      <button
        @click="sendRequest"
        :disabled="!isRequestReady || isLoading"
        class="btn-primary disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
      >
        <svg v-if="isLoading" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 mr-2 animate-spin">
          <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 mr-2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.768 59.768 0 013.27 20.876L5.999 12zm0 0h7.5" />
        </svg>
        {{ isLoading ? '发送中...' : '发送请求' }}
      </button>
      <button
        @click="reset"
        class="btn-secondary flex items-center"
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 mr-2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99" />
        </svg>
        重置表单
      </button>
    </div>

    <!-- Logs Section -->
    <div class="card mt-8 animate-fade-in">
      <div class="flex justify-between items-center mb-6">
        <h3 class="text-xl font-semibold text-gray-800 flex items-center">
          <div class="w-8 h-8 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mr-3">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-5 h-5 text-white">
              <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c0 .621-.504 1.125-1.125 1.125H18a2.25 2.25 0 01-2.25-2.25V9.375c0-.621.504-1.125 1.125-1.125H20.25a2.25 2.25 0 012.25 2.25v11.25a2.25 2.25 0 01-2.25 2.25H21M8.25 8.25l6.75-6.75" />
            </svg>
          </div>
          后端日志
        </h3>
        <button
          @click="clearLogs"
          class="px-4 py-2 bg-gray-100 text-gray-700 text-sm rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
          </svg>
          清空日志
        </button>
      </div>
      <div class="bg-gray-900 p-4 rounded-xl max-h-60 overflow-y-auto font-mono text-sm">
        <div v-if="recentLogs.length === 0" class="text-gray-500 text-center py-8">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1" stroke="currentColor" class="w-12 h-12 mx-auto mb-2 opacity-50">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c0 .621-.504 1.125-1.125 1.125H18a2.25 2.25 0 01-2.25-2.25V9.375c0-.621.504-1.125 1.125-1.125H20.25a2.25 2.25 0 012.25 2.25v11.25a2.25 2.25 0 01-2.25 2.25H21M8.25 8.25l6.75-6.75" />
          </svg>
          暂无日志记录...
        </div>
        <div v-for="log in recentLogs" :key="log.id" class="text-xs text-gray-300 mb-2 hover:bg-gray-800 p-2 rounded transition-colors duration-200">
          <span class="text-blue-400">[{{ log.timestamp }}]</span>
          <span class="text-gray-100">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useGrpcTestStore } from '../store/grpcTestStore';
import ServiceSelector from '../components/grpc-test/ServiceSelector.vue';
import RequestEditor from '../components/grpc-test/RequestEditor.vue';
import ResponseDisplay from '../components/grpc-test/ResponseDisplay.vue';

const store = useGrpcTestStore();

const isLoading = computed(() => store.isLoading);
const isRequestReady = computed(() => store.isRequestReady);
const backendServices = computed(() => store.backendServices);
const recentLogs = computed(() => store.recentLogs);
const availableServicesWithTemplates = computed(() => store.availableServices.filter(s => s.templates && s.templates.length > 0));

// 刷新状态相关
const isRefreshing = ref(false);
const autoRefreshTimer = ref(null);
const autoRefreshInterval = 3000; // 3秒间隔

const selectedTemplate = ref(null);

const applySelectedTemplate = () => {
  if (selectedTemplate.value) {
    // Pass a copy of the template to avoid potential reactivity issues if the store modifies it
    // Also, ensure the serviceName is part of the template object for the action
    const templateToApply = {
      ...selectedTemplate.value,
      // Attempt to find the service name if not directly on template (though our structure has it via parent)
      // This logic might need refinement based on how templates are structured and identified.
      // For now, we assume `selectedTemplate.value` is one of the template objects from `service.templates`.
      // The action `applyRequestTemplate` will need to correctly deduce the service.
      // Let's ensure the service name is passed if possible.
      // The current template structure is nested under a service, so we need to find which service it belongs to.
      // This is a bit tricky with just the template object.
      // The action `applyRequestTemplate` was designed to potentially infer service from method.
      // Let's refine how we call it or how the action works.
      // For now, the action `applyRequestTemplate` expects `serviceName` on the template object or infers.
      // The `template` object from the store already has `methodName` and `requestPayload`.
      // We need to ensure the `serviceName` is correctly identified.
      // The `v-model` binds the whole template object.
    };
    
    // Find the service this template belongs to, to pass its name explicitly
    // This is important if the action relies on an explicit serviceName in the template object passed to it.
    let serviceNameForTemplate = null;
    for (const service of store.availableServices) {
        if (service.templates && service.templates.includes(selectedTemplate.value)) {
            serviceNameForTemplate = service.name;
            break;
        }
    }
    if (serviceNameForTemplate) {
        templateToApply.serviceName = serviceNameForTemplate;
    }

    store.applyRequestTemplate(templateToApply);
  }
};

// Watch for external changes to selectedService or selectedMethod
// and reset the template selector if they no longer match the selected template.
watch(() => [store.selectedService, store.selectedMethod, store.requestPayload], () => {
  if (selectedTemplate.value) {
    const currentService = store.availableServices.find(s => s.name === store.selectedService);
    if (!currentService || !currentService.templates ||
        !currentService.templates.some(t =>
            t.templateName === selectedTemplate.value.templateName &&
            t.methodName === store.selectedMethod &&
            t.requestPayload === store.requestPayload
        )
    ) {
      // If the current store state doesn't match the selected template, deselect the template
      // This handles cases where service/method/payload are changed manually
      // selectedTemplate.value = null; // This might be too aggressive, consider user experience.
      // For now, let's not auto-reset, as user might be tweaking a template.
    }
  }
});


const sendRequest = () => {
  store.sendGrpcRequest();
};

const reset = () => {
  store.resetForm();
};

const clearLogs = () => {
  store.clearLogs();
};

// 检查是否所有服务都正常
const areAllServicesReady = computed(() => {
  const services = backendServices.value;
  return Object.values(services).every(service =>
    service.clientStatus.ready &&
    (service.status === 'Running' || service.status.includes('Running'))
  );
});

// 启动自动刷新
const startAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value);
  }

  autoRefreshTimer.value = setInterval(async () => {
    if (!areAllServicesReady.value) {
      console.log('[GrpcTestView] Auto-refreshing backend status...');
      const allReady = await store.refreshBackendStatus();
      if (allReady) {
        console.log('[GrpcTestView] All services ready, stopping auto-refresh');
        stopAutoRefresh();
      }
    } else {
      console.log('[GrpcTestView] All services ready, stopping auto-refresh');
      stopAutoRefresh();
    }
  }, autoRefreshInterval);
};

// 停止自动刷新
const stopAutoRefresh = () => {
  if (autoRefreshTimer.value) {
    clearInterval(autoRefreshTimer.value);
    autoRefreshTimer.value = null;
  }
};

// 刷新后端状态
const refreshStatus = async () => {
  isRefreshing.value = true;
  try {
    await store.refreshBackendStatus();
  } catch (error) {
    console.error('Failed to refresh backend status:', error);
  } finally {
    isRefreshing.value = false;
  }
};

// Initialize backend status listeners when component mounts
onMounted(async () => {
  store.initializeBackendStatusListeners();
  await store.fetchAvailableServices();

  // 启动自动刷新，直到所有服务都正常
  startAutoRefresh();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<style scoped>
.grpc-test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .grpc-test-container {
    max-width: 100%;
    padding: 0 0.5rem;
  }

  .flex.flex-wrap.gap-4 {
    flex-direction: column;
  }

  .grid.grid-cols-1.md\\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}

/* 自定义动画延迟 */
.animate-slide-up:nth-child(1) {
  animation-delay: 0.1s;
}

.animate-slide-up:nth-child(2) {
  animation-delay: 0.2s;
}

.animate-slide-up:nth-child(3) {
  animation-delay: 0.3s;
}
</style>
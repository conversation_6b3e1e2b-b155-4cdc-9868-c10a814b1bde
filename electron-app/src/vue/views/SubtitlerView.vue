<template>
  <div class="subtitler-view-container">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-center bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-2">字幕工具</h1>
      <p class="text-center text-gray-600 text-sm">智能视频字幕生成与编辑工具</p>
    </div>

    <!-- Tab-like interface to switch between Stepper and One-Click -->
    <div class="flex justify-center mb-8">
      <div class="bg-white rounded-xl p-1 shadow-lg border border-gray-200">
        <button
          @click="viewMode = 'stepper'"
          :class="[
            'px-6 py-3 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none',
            viewMode === 'stepper'
              ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-md'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
          ]">
          分步操作
        </button>
        <button
          @click="viewMode = 'oneclick'"
          :class="[
            'px-6 py-3 rounded-lg font-medium text-sm transition-all duration-200 focus:outline-none',
            viewMode === 'oneclick'
              ? 'bg-gradient-to-r from-purple-500 to-pink-600 text-white shadow-md'
              : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
          ]">
          一键操作
        </button>
      </div>
    </div>

    <div v-if="viewMode === 'stepper'">
      <StepperNavigation />
      <div class="step-content-container bg-white rounded-2xl shadow-xl border border-gray-100 p-6 mt-6">
        <keep-alive>
          <component :is="currentStepComponent" />
        </keep-alive>
      </div>
    </div>

    <div v-if="viewMode === 'oneclick'" class="mt-6">
      <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
        <OneClickOperation />
      </div>
    </div>



    <!-- 调试信息 -->
    <!--
    <div class="mt-6 p-3 bg-gray-100 rounded border text-xs">
      <h3 class="font-semibold">Debug Info (SubtitlerStore):</h3>
      <pre>{{ JSON.stringify(subtitlerStore.$state, null, 2) }}</pre>
    </div>
    -->
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useSubtitlerStore } from '@/vue/store/subtitlerStore';
import StepperNavigation from '@/vue/components/subtitler/StepperNavigation.vue';
import FileUpload from '@/vue/components/subtitler/FileUpload.vue';
import VideoToAudioStep from '@/vue/components/subtitler/VideoToAudioStep.vue';
import AudioToTextStep from '@/vue/components/subtitler/AudioToTextStep.vue';
import GenerateSubtitlesStep from '@/vue/components/subtitler/GenerateSubtitlesStep.vue'; // 新增：组成句子
import OptimizeSubtitlesStep from '@/vue/components/subtitler/OptimizeSubtitlesStep.vue'; // 新增：优化句子
import EditSubtitlesStep from '@/vue/components/subtitler/EditSubtitlesStep.vue';
import TranslationChoiceStep from '@/vue/components/subtitler/TranslationChoiceStep.vue'; // 新增：翻译选择
import TranslationStep from '@/vue/components/subtitler/TranslationStep.vue'; // 新增：翻译处理
import ExportStep from '@/vue/components/subtitler/ExportStep.vue';
import OneClickOperation from '@/vue/components/subtitler/OneClickOperation.vue';


const subtitlerStore = useSubtitlerStore();
const currentStep = computed(() => subtitlerStore.currentStep);
const viewMode = ref('stepper'); // 'stepper' or 'oneclick'

const stepComponents = {
  1: FileUpload,
  2: VideoToAudioStep,
  3: AudioToTextStep,
  4: GenerateSubtitlesStep, // 组成句子
  5: OptimizeSubtitlesStep, // 优化句子
  6: EditSubtitlesStep,     // 编辑字幕
  7: TranslationChoiceStep, // 翻译选择
  8: TranslationStep,       // 翻译处理
  9: ExportStep,            // 导出保存
};

const currentStepComponent = computed(() => {
  return stepComponents[currentStep.value] || null;
});

</script>

<style scoped>
.subtitler-view-container {
  max-width: 1200px; /* 增加最大宽度，确保步骤导航不溢出 */
  margin: 0 auto; /* 居中显示 */
  padding: 0 1rem; /* 添加水平内边距 */
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; /* 现代字体栈 */
}

.step-content-container {
  min-height: 400px; /* 增加最小高度 */
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(10px); /* 添加毛玻璃效果 */
}

/* 组件过渡效果 */
.fade-enter-active, .fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateY(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .subtitler-view-container {
    max-width: 100%;
    padding: 0 0.5rem;
  }
}
</style>
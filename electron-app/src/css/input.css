@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义全局样式 */
@layer base {
  /* 改进滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  }

  /* 改进选择文本的样式 */
  ::selection {
    background: rgba(99, 102, 241, 0.3);
    color: inherit;
  }

  /* 平滑滚动 */
  html {
    scroll-behavior: smooth;
  }

  /* 改进焦点样式 */
  *:focus {
    outline: none;
  }

  *:focus-visible {
    outline: 2px solid rgba(99, 102, 241, 0.5);
    outline-offset: 2px;
  }
}

@layer components {
  /* 自定义按钮样式 */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:from-blue-600 hover:to-indigo-700 focus:ring-4 focus:ring-blue-200 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-white text-gray-700 px-6 py-3 rounded-lg font-medium transition-all duration-200 hover:bg-gray-50 focus:ring-4 focus:ring-gray-200 shadow-md hover:shadow-lg border border-gray-200;
  }

  /* 自定义卡片样式 */
  .card {
    @apply bg-white rounded-2xl shadow-xl border border-gray-100 p-6 backdrop-blur-sm;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-2xl hover:-translate-y-1;
  }

  /* 自定义输入框样式 */
  .input-field {
    @apply w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-4 focus:ring-blue-200 focus:border-blue-500 transition-all duration-200 bg-white;
  }

  /* 渐变文本 */
  .gradient-text {
    @apply bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent;
  }

  /* 玻璃态效果 */
  .glass {
    @apply backdrop-blur-md bg-white/10 border border-white/20;
  }
}

@layer utilities {
  /* 自定义动画 */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
}

/* 关键帧动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
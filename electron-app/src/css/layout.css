/* 布局相关样式 */

/* 侧边栏和导航样式 */
.active-nav-item {
    background-color: #0ea5e9; /* sky-600 */
    color: white;
}

.active-nav-item svg {
    color: white;
}

.sidebar-nav-item:not(.active-nav-item) svg {
    color: #cbd5e1; /* slate-300 */
}

.sidebar-nav-item:not(.active-nav-item):hover svg {
    color: #7dd3fc; /* sky-300 */
}

/* 侧边栏布局 - 已移至Vue组件中管理 */
/* 移除了所有 #sidebar 相关的CSS规则，现在由SidebarNav.vue组件管理 */

/* 主内容区域布局 - 已移至Vue组件中管理 */
/* 移除了所有 #main-content-area 相关的CSS规则，避免与Vue组件冲突 */

/* 大屏幕时的边距控制 - 已移至Vue组件中动态控制 */
/* 这些样式已被Vue组件的动态样式替代 */

/* 内容面板通用样式 */
.main-content-panel {
    width: 100%;
    /* max-width由Tailwind的max-w-7xl控制 */
}

.main-content-panel.hidden {
    display: none;
} 
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './vue/App.vue';

// Import CSS files
// Import Tailwind CSS input file (not output.css)
import './css/input.css'; 
// Import custom layout CSS
import './css/layout.css';

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.mount('#vue-app');

console.log('Vue app initialized and mounted to #vue-app');
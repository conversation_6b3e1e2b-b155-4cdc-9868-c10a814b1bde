// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@grpc/grpc-js');
var subtitler_subtitler_pb = require('../subtitler/subtitler_pb.js');

function serialize_monkeyfx_api_v1_subtitler_AudioToTextRequest(arg) {
  if (!(arg instanceof subtitler_subtitler_pb.AudioToTextRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.AudioToTextRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_AudioToTextRequest(buffer_arg) {
  return subtitler_subtitler_pb.AudioToTextRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_GenerateSubtitlesRequest(arg) {
  if (!(arg instanceof subtitler_subtitler_pb.GenerateSubtitlesRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_GenerateSubtitlesRequest(buffer_arg) {
  return subtitler_subtitler_pb.GenerateSubtitlesRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_ProcessVideoToTranslatedSubtitlesRequest(arg) {
  if (!(arg instanceof subtitler_subtitler_pb.ProcessVideoToTranslatedSubtitlesRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_ProcessVideoToTranslatedSubtitlesRequest(buffer_arg) {
  return subtitler_subtitler_pb.ProcessVideoToTranslatedSubtitlesRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_ProgressUpdate(arg) {
  if (!(arg instanceof subtitler_subtitler_pb.ProgressUpdate)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.ProgressUpdate');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate(buffer_arg) {
  return subtitler_subtitler_pb.ProgressUpdate.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_TranslateSubtitlesRequest(arg) {
  if (!(arg instanceof subtitler_subtitler_pb.TranslateSubtitlesRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_TranslateSubtitlesRequest(buffer_arg) {
  return subtitler_subtitler_pb.TranslateSubtitlesRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_VideoToAudioRequest(arg) {
  if (!(arg instanceof subtitler_subtitler_pb.VideoToAudioRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.VideoToAudioRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_VideoToAudioRequest(buffer_arg) {
  return subtitler_subtitler_pb.VideoToAudioRequest.deserializeBinary(new Uint8Array(buffer_arg));
}


var SubtitlerService = exports.SubtitlerService = {
  videoToAudio: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/VideoToAudio',
    requestStream: false,
    responseStream: true,
    requestType: subtitler_subtitler_pb.VideoToAudioRequest,
    responseType: subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_VideoToAudioRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_VideoToAudioRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
  audioToText: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/AudioToText',
    requestStream: false,
    responseStream: true,
    requestType: subtitler_subtitler_pb.AudioToTextRequest,
    responseType: subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_AudioToTextRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_AudioToTextRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
  generateSubtitles: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/GenerateSubtitles',
    requestStream: false,
    responseStream: true,
    requestType: subtitler_subtitler_pb.GenerateSubtitlesRequest,
    responseType: subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_GenerateSubtitlesRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_GenerateSubtitlesRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
  translateSubtitles: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/TranslateSubtitles',
    requestStream: false,
    responseStream: true,
    requestType: subtitler_subtitler_pb.TranslateSubtitlesRequest,
    responseType: subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_TranslateSubtitlesRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_TranslateSubtitlesRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
  processVideoToTranslatedSubtitles: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/ProcessVideoToTranslatedSubtitles',
    requestStream: false,
    responseStream: true,
    requestType: subtitler_subtitler_pb.ProcessVideoToTranslatedSubtitlesRequest,
    responseType: subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_ProcessVideoToTranslatedSubtitlesRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProcessVideoToTranslatedSubtitlesRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
};

exports.SubtitlerClient = grpc.makeGenericClientConstructor(SubtitlerService, 'Subtitler');

// source: subtitler/subtitler.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {missingRequire} reports error on implicit type usages.
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = (function() {
  if (this) { return this; }
  if (typeof window !== 'undefined') { return window; }
  if (typeof global !== 'undefined') { return global; }
  if (typeof self !== 'undefined') { return self; }
  return Function('return this')();
}.call(null));

goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.AudioToTextRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.AudioToTextResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ProgressUpdate', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.ProgressUpdate.FinalResultCase', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest', null, global);
goog.exportSymbol('proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.ProgressUpdate, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.ProgressUpdate.displayName = 'proto.monkeyfx.api.v1.subtitler.ProgressUpdate';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.AudioToTextRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.AudioToTextRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.AudioToTextResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.AudioToTextResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.displayName = 'proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.displayName = 'proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse';
}

/**
 * Oneof group definitions for this message. Each group defines the field
 * numbers belonging to that group. When of these fields' value is set, all
 * other fields in the group are cleared. During deserialization, if multiple
 * fields are encountered for a group, only the last value seen will be kept.
 * @private {!Array<!Array<number>>}
 * @const
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_ = [[6,7,8,9,10]];

/**
 * @enum {number}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.FinalResultCase = {
  FINAL_RESULT_NOT_SET: 0,
  VIDEO_TO_AUDIO_RESPONSE: 6,
  AUDIO_TO_TEXT_RESPONSE: 7,
  GENERATE_SUBTITLES_RESPONSE: 8,
  TRANSLATE_SUBTITLES_RESPONSE: 9,
  PROCESS_VIDEO_TO_TRANSLATED_SUBTITLES_RESPONSE: 10
};

/**
 * @return {proto.monkeyfx.api.v1.subtitler.ProgressUpdate.FinalResultCase}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getFinalResultCase = function() {
  return /** @type {proto.monkeyfx.api.v1.subtitler.ProgressUpdate.FinalResultCase} */(jspb.Message.computeOneofCase(this, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0]));
};



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.ProgressUpdate.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.toObject = function(includeInstance, msg) {
  var f, obj = {
    stageName: jspb.Message.getFieldWithDefault(msg, 1, ""),
    percentage: jspb.Message.getFieldWithDefault(msg, 2, 0),
    message: jspb.Message.getFieldWithDefault(msg, 3, ""),
    isError: jspb.Message.getBooleanFieldWithDefault(msg, 4, false),
    errorMessage: jspb.Message.getFieldWithDefault(msg, 5, ""),
    videoToAudioResponse: (f = msg.getVideoToAudioResponse()) && proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.toObject(includeInstance, f),
    audioToTextResponse: (f = msg.getAudioToTextResponse()) && proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.toObject(includeInstance, f),
    generateSubtitlesResponse: (f = msg.getGenerateSubtitlesResponse()) && proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.toObject(includeInstance, f),
    translateSubtitlesResponse: (f = msg.getTranslateSubtitlesResponse()) && proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.toObject(includeInstance, f),
    processVideoToTranslatedSubtitlesResponse: (f = msg.getProcessVideoToTranslatedSubtitlesResponse()) && proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.ProgressUpdate;
  return proto.monkeyfx.api.v1.subtitler.ProgressUpdate.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setStageName(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPercentage(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setMessage(value);
      break;
    case 4:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setIsError(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setErrorMessage(value);
      break;
    case 6:
      var value = new proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.deserializeBinaryFromReader);
      msg.setVideoToAudioResponse(value);
      break;
    case 7:
      var value = new proto.monkeyfx.api.v1.subtitler.AudioToTextResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.deserializeBinaryFromReader);
      msg.setAudioToTextResponse(value);
      break;
    case 8:
      var value = new proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.deserializeBinaryFromReader);
      msg.setGenerateSubtitlesResponse(value);
      break;
    case 9:
      var value = new proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.deserializeBinaryFromReader);
      msg.setTranslateSubtitlesResponse(value);
      break;
    case 10:
      var value = new proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse;
      reader.readMessage(value,proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.deserializeBinaryFromReader);
      msg.setProcessVideoToTranslatedSubtitlesResponse(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.ProgressUpdate.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getStageName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getPercentage();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getMessage();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getIsError();
  if (f) {
    writer.writeBool(
      4,
      f
    );
  }
  f = message.getErrorMessage();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getVideoToAudioResponse();
  if (f != null) {
    writer.writeMessage(
      6,
      f,
      proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.serializeBinaryToWriter
    );
  }
  f = message.getAudioToTextResponse();
  if (f != null) {
    writer.writeMessage(
      7,
      f,
      proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.serializeBinaryToWriter
    );
  }
  f = message.getGenerateSubtitlesResponse();
  if (f != null) {
    writer.writeMessage(
      8,
      f,
      proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.serializeBinaryToWriter
    );
  }
  f = message.getTranslateSubtitlesResponse();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.serializeBinaryToWriter
    );
  }
  f = message.getProcessVideoToTranslatedSubtitlesResponse();
  if (f != null) {
    writer.writeMessage(
      10,
      f,
      proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.serializeBinaryToWriter
    );
  }
};


/**
 * optional string stage_name = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getStageName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setStageName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional int32 percentage = 2;
 * @return {number}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getPercentage = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setPercentage = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string message = 3;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getMessage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setMessage = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional bool is_error = 4;
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getIsError = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 4, false));
};


/**
 * @param {boolean} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setIsError = function(value) {
  return jspb.Message.setProto3BooleanField(this, 4, value);
};


/**
 * optional string error_message = 5;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getErrorMessage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setErrorMessage = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional VideoToAudioResponse video_to_audio_response = 6;
 * @return {?proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getVideoToAudioResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse, 6));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setVideoToAudioResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 6, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearVideoToAudioResponse = function() {
  return this.setVideoToAudioResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasVideoToAudioResponse = function() {
  return jspb.Message.getField(this, 6) != null;
};


/**
 * optional AudioToTextResponse audio_to_text_response = 7;
 * @return {?proto.monkeyfx.api.v1.subtitler.AudioToTextResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getAudioToTextResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.AudioToTextResponse, 7));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.AudioToTextResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setAudioToTextResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 7, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearAudioToTextResponse = function() {
  return this.setAudioToTextResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasAudioToTextResponse = function() {
  return jspb.Message.getField(this, 7) != null;
};


/**
 * optional GenerateSubtitlesResponse generate_subtitles_response = 8;
 * @return {?proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getGenerateSubtitlesResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse, 8));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setGenerateSubtitlesResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 8, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearGenerateSubtitlesResponse = function() {
  return this.setGenerateSubtitlesResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasGenerateSubtitlesResponse = function() {
  return jspb.Message.getField(this, 8) != null;
};


/**
 * optional TranslateSubtitlesResponse translate_subtitles_response = 9;
 * @return {?proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getTranslateSubtitlesResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse, 9));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setTranslateSubtitlesResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 9, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearTranslateSubtitlesResponse = function() {
  return this.setTranslateSubtitlesResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasTranslateSubtitlesResponse = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional ProcessVideoToTranslatedSubtitlesResponse process_video_to_translated_subtitles_response = 10;
 * @return {?proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.getProcessVideoToTranslatedSubtitlesResponse = function() {
  return /** @type{?proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} */ (
    jspb.Message.getWrapperField(this, proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse, 10));
};


/**
 * @param {?proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse|undefined} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
*/
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.setProcessVideoToTranslatedSubtitlesResponse = function(value) {
  return jspb.Message.setOneofWrapperField(this, 10, proto.monkeyfx.api.v1.subtitler.ProgressUpdate.oneofGroups_[0], value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProgressUpdate} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.clearProcessVideoToTranslatedSubtitlesResponse = function() {
  return this.setProcessVideoToTranslatedSubtitlesResponse(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.monkeyfx.api.v1.subtitler.ProgressUpdate.prototype.hasProcessVideoToTranslatedSubtitlesResponse = function() {
  return jspb.Message.getField(this, 10) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    videoPath: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest;
  return proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setVideoPath(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVideoPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string video_path = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.getVideoPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioRequest.prototype.setVideoPath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    audioPath: jspb.Message.getFieldWithDefault(msg, 1, ""),
    audioData: msg.getAudioData_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse;
  return proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setAudioPath(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setAudioData(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAudioPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAudioData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
};


/**
 * optional string audio_path = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.getAudioPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.setAudioPath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional bytes audio_data = 2;
 * @return {!(string|Uint8Array)}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.getAudioData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes audio_data = 2;
 * This is a type-conversion wrapper around `getAudioData()`
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.getAudioData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getAudioData()));
};


/**
 * optional bytes audio_data = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getAudioData()`
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.getAudioData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getAudioData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.VideoToAudioResponse.prototype.setAudioData = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    audioPath: jspb.Message.getFieldWithDefault(msg, 1, ""),
    audioData: msg.getAudioData_asB64()
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.AudioToTextRequest;
  return proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setAudioPath(value);
      break;
    case 2:
      var value = /** @type {!Uint8Array} */ (reader.readBytes());
      msg.setAudioData(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAudioPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAudioData_asU8();
  if (f.length > 0) {
    writer.writeBytes(
      2,
      f
    );
  }
};


/**
 * optional string audio_path = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getAudioPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.setAudioPath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional bytes audio_data = 2;
 * @return {!(string|Uint8Array)}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getAudioData = function() {
  return /** @type {!(string|Uint8Array)} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * optional bytes audio_data = 2;
 * This is a type-conversion wrapper around `getAudioData()`
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getAudioData_asB64 = function() {
  return /** @type {string} */ (jspb.Message.bytesAsB64(
      this.getAudioData()));
};


/**
 * optional bytes audio_data = 2;
 * Note that Uint8Array is not supported on all browsers.
 * @see http://caniuse.com/Uint8Array
 * This is a type-conversion wrapper around `getAudioData()`
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.getAudioData_asU8 = function() {
  return /** @type {!Uint8Array} */ (jspb.Message.bytesAsU8(
      this.getAudioData()));
};


/**
 * @param {!(string|Uint8Array)} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextRequest.prototype.setAudioData = function(value) {
  return jspb.Message.setProto3BytesField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    transcript: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.AudioToTextResponse;
  return proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTranscript(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTranscript();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string transcript = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.getTranscript = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.AudioToTextResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.AudioToTextResponse.prototype.setTranscript = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    text: jspb.Message.getFieldWithDefault(msg, 1, ""),
    audioPath: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest;
  return proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setText(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setAudioPath(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getText();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAudioPath();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string text = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.getText = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.setText = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string audio_path = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.getAudioPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest.prototype.setAudioPath = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    srtContent: jspb.Message.getFieldWithDefault(msg, 1, ""),
    assContent: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse;
  return proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSrtContent(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setAssContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSrtContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAssContent();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string srt_content = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.getSrtContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.setSrtContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string ass_content = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.getAssContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.GenerateSubtitlesResponse.prototype.setAssContent = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    subtitleContent: jspb.Message.getFieldWithDefault(msg, 1, ""),
    targetLanguage: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest;
  return proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setSubtitleContent(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTargetLanguage(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSubtitleContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTargetLanguage();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string subtitle_content = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.getSubtitleContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.setSubtitleContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string target_language = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.getTargetLanguage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest.prototype.setTargetLanguage = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    translatedSubtitleContent: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse;
  return proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTranslatedSubtitleContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTranslatedSubtitleContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string translated_subtitle_content = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.getTranslatedSubtitleContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.TranslateSubtitlesResponse.prototype.setTranslatedSubtitleContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    videoPath: jspb.Message.getFieldWithDefault(msg, 1, ""),
    targetLanguage: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest;
  return proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setVideoPath(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTargetLanguage(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getVideoPath();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getTargetLanguage();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string video_path = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.getVideoPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.setVideoPath = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string target_language = 2;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.getTargetLanguage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest.prototype.setTargetLanguage = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    translatedSubtitleContent: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse;
  return proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTranslatedSubtitleContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTranslatedSubtitleContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string translated_subtitle_content = 1;
 * @return {string}
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.getTranslatedSubtitleContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse} returns this
 */
proto.monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesResponse.prototype.setTranslatedSubtitleContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


goog.object.extend(exports, proto.monkeyfx.api.v1.subtitler);

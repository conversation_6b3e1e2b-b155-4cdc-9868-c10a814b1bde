// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@grpc/grpc-js');
var api$protos_v1_subtitler_subtitler_pb = require('../../../api-protos/v1/subtitler/subtitler_pb.js');
var google_protobuf_struct_pb = require('google-protobuf/google/protobuf/struct_pb.js');

function serialize_monkeyfx_api_v1_subtitler_AudioToTextRequest(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.AudioToTextRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.AudioToTextRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_AudioToTextRequest(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.AudioToTextRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_BatchSaveSubtitleRequest(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.BatchSaveSubtitleRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.BatchSaveSubtitleRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_BatchSaveSubtitleRequest(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.BatchSaveSubtitleRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_BatchSaveSubtitleResponse(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.BatchSaveSubtitleResponse)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.BatchSaveSubtitleResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_BatchSaveSubtitleResponse(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.BatchSaveSubtitleResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_GenerateSubtitlesRequest(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.GenerateSubtitlesRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.GenerateSubtitlesRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_GenerateSubtitlesRequest(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.GenerateSubtitlesRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_ProcessVideoToTranslatedSubtitlesRequest(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.ProcessVideoToTranslatedSubtitlesRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.ProcessVideoToTranslatedSubtitlesRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_ProcessVideoToTranslatedSubtitlesRequest(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.ProcessVideoToTranslatedSubtitlesRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_ProgressUpdate(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.ProgressUpdate)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.ProgressUpdate');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.ProgressUpdate.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_SaveSubtitleRequest(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.SaveSubtitleRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.SaveSubtitleRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_SaveSubtitleRequest(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.SaveSubtitleRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_SaveSubtitleResponse(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.SaveSubtitleResponse)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.SaveSubtitleResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_SaveSubtitleResponse(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.SaveSubtitleResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_TranslateSubtitlesRequest(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.TranslateSubtitlesRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.TranslateSubtitlesRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_TranslateSubtitlesRequest(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.TranslateSubtitlesRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_subtitler_VideoToAudioRequest(arg) {
  if (!(arg instanceof api$protos_v1_subtitler_subtitler_pb.VideoToAudioRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.subtitler.VideoToAudioRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_subtitler_VideoToAudioRequest(buffer_arg) {
  return api$protos_v1_subtitler_subtitler_pb.VideoToAudioRequest.deserializeBinary(new Uint8Array(buffer_arg));
}


var SubtitlerService = exports.SubtitlerService = {
  videoToAudio: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/VideoToAudio',
    requestStream: false,
    responseStream: true,
    requestType: api$protos_v1_subtitler_subtitler_pb.VideoToAudioRequest,
    responseType: api$protos_v1_subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_VideoToAudioRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_VideoToAudioRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
  audioToText: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/AudioToText',
    requestStream: false,
    responseStream: true,
    requestType: api$protos_v1_subtitler_subtitler_pb.AudioToTextRequest,
    responseType: api$protos_v1_subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_AudioToTextRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_AudioToTextRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
  generateSubtitles: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/GenerateSubtitles',
    requestStream: false,
    responseStream: true,
    requestType: api$protos_v1_subtitler_subtitler_pb.GenerateSubtitlesRequest,
    responseType: api$protos_v1_subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_GenerateSubtitlesRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_GenerateSubtitlesRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
  translateSubtitles: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/TranslateSubtitles',
    requestStream: false,
    responseStream: true,
    requestType: api$protos_v1_subtitler_subtitler_pb.TranslateSubtitlesRequest,
    responseType: api$protos_v1_subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_TranslateSubtitlesRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_TranslateSubtitlesRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
  processVideoToTranslatedSubtitles: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/ProcessVideoToTranslatedSubtitles',
    requestStream: false,
    responseStream: true,
    requestType: api$protos_v1_subtitler_subtitler_pb.ProcessVideoToTranslatedSubtitlesRequest,
    responseType: api$protos_v1_subtitler_subtitler_pb.ProgressUpdate,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_ProcessVideoToTranslatedSubtitlesRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProcessVideoToTranslatedSubtitlesRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_ProgressUpdate,
  },
  // 新增：字幕保存服务
saveSubtitle: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/SaveSubtitle',
    requestStream: false,
    responseStream: false,
    requestType: api$protos_v1_subtitler_subtitler_pb.SaveSubtitleRequest,
    responseType: api$protos_v1_subtitler_subtitler_pb.SaveSubtitleResponse,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_SaveSubtitleRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_SaveSubtitleRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_SaveSubtitleResponse,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_SaveSubtitleResponse,
  },
  batchSaveSubtitle: {
    path: '/monkeyfx.api.v1.subtitler.Subtitler/BatchSaveSubtitle',
    requestStream: false,
    responseStream: false,
    requestType: api$protos_v1_subtitler_subtitler_pb.BatchSaveSubtitleRequest,
    responseType: api$protos_v1_subtitler_subtitler_pb.BatchSaveSubtitleResponse,
    requestSerialize: serialize_monkeyfx_api_v1_subtitler_BatchSaveSubtitleRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_subtitler_BatchSaveSubtitleRequest,
    responseSerialize: serialize_monkeyfx_api_v1_subtitler_BatchSaveSubtitleResponse,
    responseDeserialize: deserialize_monkeyfx_api_v1_subtitler_BatchSaveSubtitleResponse,
  },
};

exports.SubtitlerClient = grpc.makeGenericClientConstructor(SubtitlerService, 'Subtitler');

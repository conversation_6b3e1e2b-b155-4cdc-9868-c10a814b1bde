// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@grpc/grpc-js');
var greeter_greeter_pb = require('../greeter/greeter_pb.js');
var common_common_pb = require('../common/common_pb.js');

function serialize_monkeyfx_api_v1_greeter_HelloReply(arg) {
  if (!(arg instanceof greeter_greeter_pb.HelloReply)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.greeter.HelloReply');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_greeter_HelloReply(buffer_arg) {
  return greeter_greeter_pb.HelloReply.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_monkeyfx_api_v1_greeter_HelloRequest(arg) {
  if (!(arg instanceof greeter_greeter_pb.HelloRequest)) {
    throw new Error('Expected argument of type monkeyfx.api.v1.greeter.HelloRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_monkeyfx_api_v1_greeter_HelloRequest(buffer_arg) {
  return greeter_greeter_pb.HelloRequest.deserializeBinary(new Uint8Array(buffer_arg));
}


var GreeterService = exports.GreeterService = {
  sayHello: {
    path: '/monkeyfx.api.v1.greeter.Greeter/SayHello',
    requestStream: false,
    responseStream: false,
    requestType: greeter_greeter_pb.HelloRequest,
    responseType: greeter_greeter_pb.HelloReply,
    requestSerialize: serialize_monkeyfx_api_v1_greeter_HelloRequest,
    requestDeserialize: deserialize_monkeyfx_api_v1_greeter_HelloRequest,
    responseSerialize: serialize_monkeyfx_api_v1_greeter_HelloReply,
    responseDeserialize: deserialize_monkeyfx_api_v1_greeter_HelloReply,
  },
};

exports.GreeterClient = grpc.makeGenericClientConstructor(GreeterService, 'Greeter');

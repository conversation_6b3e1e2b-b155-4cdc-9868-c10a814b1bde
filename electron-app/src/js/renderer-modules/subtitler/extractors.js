// electron-app/src/js/renderer-modules/subtitler/extractors.js
// 包含各种从gRPC响应中提取数据的函数

/**
 * 从进度消息中提取视频转音频响应
 * @param {Object} msg 进度消息
 * @returns {Object|null} 提取的响应数据或null
 */
export function extractVideoToAudioResponse(msg) {
    const pathKeys = ['audio_path', 'audioPath'];
    const currentStageName = msg.stageName || msg.stage_name;
    const videoToAudioStageNames = ['video_to_audio', 'audio_extraction', 'videoextraction', '视频提取音频'];
    const finalResultValue = msg.final_result || msg.finalResult;
    const videoResponseObject = msg.video_to_audio_response || msg.videoToAudioResponse;

    if (msg && msg.percentage === 100 && typeof currentStageName === 'string' && videoToAudioStageNames.includes(currentStageName.toLowerCase())) {
        const wrappersToTry = [msg, msg.data, msg.result, msg.payload, videoResponseObject];
        for (const wrapper of wrappersToTry) {
            if (wrapper && typeof wrapper === 'object') {
                for (const pathKey of pathKeys) {
                    if (typeof wrapper[pathKey] === 'string' && wrapper[pathKey].trim() !== '') return wrapper[pathKey];
                }
            }
        }
    }
    if (finalResultValue === "video_to_audio_response" && videoResponseObject && typeof videoResponseObject === 'object') {
        for (const pathKey of pathKeys) if (typeof videoResponseObject[pathKey] === 'string' && videoResponseObject[pathKey].trim() !== '') return videoResponseObject[pathKey];
    }
    if (videoResponseObject && typeof videoResponseObject === 'object') {
        for (const pathKey of pathKeys) if (typeof videoResponseObject[pathKey] === 'string' && videoResponseObject[pathKey].trim() !== '') return videoResponseObject[pathKey];
    }
    const commonWrapperKeys = ['data', 'result', 'payload'];
    for (const wrapperKey of commonWrapperKeys) {
        const wrapper = msg[wrapperKey];
        if (wrapper && typeof wrapper === 'object') for (const pathKey of pathKeys) if (typeof wrapper[pathKey] === 'string' && wrapper[pathKey].trim() !== '') return wrapper[pathKey];
    }
    if (typeof msg.getVideoToAudioResponse === 'function') {
        const resp = msg.getVideoToAudioResponse();
        if (resp && typeof resp.getAudioPath === 'function') { const audioPath = resp.getAudioPath(); if (audioPath && typeof audioPath === 'string' && audioPath.trim() !== '') return audioPath; }
    }
    for (const pathKey of pathKeys) if (msg && typeof msg[pathKey] === 'string' && msg[pathKey].trim() !== '') return msg[pathKey];
    return null;
}

/**
 * 从进度消息中提取音频转文本响应
 * @param {Object} message 进度消息
 * @returns {Object|null} 提取的响应数据或null
 */
export function extractAudioToTextResponse(message) {
    const transcriptKeys = ['transcript', 'text', 'transcription', 'resultText'];
    let transcript = null; let segments = null;
    if (typeof message.getAudioToTextResponse === 'function') {
        const resp = message.getAudioToTextResponse();
        if (resp) {
            if (typeof resp.getTranscript === 'function') { const protoTranscript = resp.getTranscript(); if (protoTranscript && typeof protoTranscript === 'string') transcript = protoTranscript; }
            if (typeof resp.getSegmentsList === 'function') { const protoSegmentsList = resp.getSegmentsList(); if (protoSegmentsList && Array.isArray(protoSegmentsList)) segments = protoSegmentsList.map(p => ({ text: p.getText(), start_time_ms: p.getStartTimeMs(), end_time_ms: p.getEndTimeMs() }));}
        }
    }
    const potentialSources = [message.audio_to_text_response, message.audioToTextResponse, message.data, message.result, message.response, message];
    for (const source of potentialSources) {
        if (source && typeof source === 'object') {
            if (transcript === null) for (const key of transcriptKeys) if (source[key] && typeof source[key] === 'string') { transcript = source[key]; break; }
            if (segments === null && source.segments && Array.isArray(source.segments)) segments = source.segments.map(s => ({ text: s.text, start_time_ms: s.start_time_ms, end_time_ms: s.end_time_ms }));
        }
        if (transcript !== null && segments !== null) break;
    }
    if (transcript === null && segments && segments.length > 0) transcript = segments.map(s => s.text).join(' ');
    if (transcript !== null || (segments !== null && segments.length > 0)) return { transcript, segments: segments || [] };
    return null;
}

/**
 * 从进度消息中提取字幕生成响应
 * @param {Object} progressUpdateMessage 进度消息
 * @returns {Object|null} 提取的响应数据或null
 */
export function extractSubtitleGenerationResponse(progressUpdateMessage) {
    if (progressUpdateMessage?.final_result === "generate_subtitles_response" && progressUpdateMessage.generate_subtitles_response) {
        const responseData = progressUpdateMessage.generate_subtitles_response;
        const srtContent = responseData.srt_content || null; const assContent = responseData.ass_content || null;
        if (srtContent || assContent) return { srt: srtContent, ass: assContent };
    }
    const srtKeys = ['srt_content', 'srtContent']; const assKeys = ['ass_content', 'assContent'];
    let srt = null; let ass = null;
    const sourcesToTry = [progressUpdateMessage.generate_subtitles_response, progressUpdateMessage.subtitle_generation_response, progressUpdateMessage.data, progressUpdateMessage.result, progressUpdateMessage.response, progressUpdateMessage];
    for (const source of sourcesToTry) {
        if (source && typeof source === 'object') {
            if (srt === null) for (const key of srtKeys) if (typeof source[key] === 'string') { srt = source[key]; break; }
            if (ass === null) for (const key of assKeys) if (typeof source[key] === 'string') { ass = source[key]; break; }
        }
        if (srt !== null && ass !== null) break;
    }
    if (srt || ass) return { srt, ass };
    return null;
}

/**
 * 从进度消息中提取字幕翻译响应
 * @param {Object} progressUpdateMessage 进度消息
 * @returns {string|null} 提取的翻译内容或null
 */
export function extractSubtitleTranslationResponse(progressUpdateMessage) {
    if (progressUpdateMessage?.final_result === "translate_subtitles_response" && progressUpdateMessage.translate_subtitles_response?.translated_subtitle_content?.trim()) {
        return progressUpdateMessage.translate_subtitles_response.translated_subtitle_content;
    }
    if (typeof progressUpdateMessage.getTranslateSubtitlesResponse === 'function') {
        const resp = progressUpdateMessage.getTranslateSubtitlesResponse();
        if (resp) {
            const content = resp.getTranslatedContent ? resp.getTranslatedContent() : (resp.getTranslatedSubtitleContent ? resp.getTranslatedSubtitleContent() : null);
            if (content && typeof content === 'string' && content.trim() !== '') return content;
        }
    }
    const primaryKey = 'translated_subtitle_content'; const fallbackKeys = ['translated_content', 'translatedContent', 'translation'];
    const sourcesToTry = [progressUpdateMessage.translate_subtitles_response, progressUpdateMessage.translateSubtitlesResponse, progressUpdateMessage.data, progressUpdateMessage.result, progressUpdateMessage.response, progressUpdateMessage];
    for (const source of sourcesToTry) {
        if (source && typeof source === 'object') {
            if (typeof source[primaryKey] === 'string' && source[primaryKey].trim() !== '') return source[primaryKey];
            for (const key of fallbackKeys) if (typeof source[key] === 'string' && source[key].trim() !== '') return source[key];
        }
    }
    return null;
}

/**
 * 从进度消息中提取完整工作流响应
 * @param {Object} progressUpdateMessage 进度消息
 * @returns {Object|null} 提取的响应数据或null
 */
export function extractFullWorkflowResponse(progressUpdateMessage) {
    let final_subtitle_content = null; let segments = null; let transcript = null;
    if (!progressUpdateMessage) return null;
    const finalResultType = progressUpdateMessage.final_result;
    let responseDataForFinalContent = (finalResultType && progressUpdateMessage[finalResultType]) ? progressUpdateMessage[finalResultType] : 
        (progressUpdateMessage.process_video_to_translated_subtitles_response || progressUpdateMessage.generate_subtitles_response || progressUpdateMessage.translate_subtitles_response || progressUpdateMessage.full_workflow_response || progressUpdateMessage.audio_to_text_response);

    if (responseDataForFinalContent) {
        if (finalResultType === "translate_subtitles_response") final_subtitle_content = responseDataForFinalContent.translated_subtitle_content;
        else if (finalResultType === "generate_subtitles_response") final_subtitle_content = responseDataForFinalContent.srt_content || responseDataForFinalContent.ass_content;
        else if (responseDataForFinalContent.translated_subtitle_content && typeof responseDataForFinalContent.translated_subtitle_content === 'string') final_subtitle_content = responseDataForFinalContent.translated_subtitle_content;
        else if (responseDataForFinalContent.srt_content && typeof responseDataForFinalContent.srt_content === 'string') final_subtitle_content = responseDataForFinalContent.srt_content;
        else if (responseDataForFinalContent.ass_content && typeof responseDataForFinalContent.ass_content === 'string') final_subtitle_content = responseDataForFinalContent.ass_content;
    }

    if (progressUpdateMessage.audio_to_text_response) {
        if (progressUpdateMessage.audio_to_text_response.segments && Array.isArray(progressUpdateMessage.audio_to_text_response.segments)) segments = progressUpdateMessage.audio_to_text_response.segments;
        if (typeof progressUpdateMessage.audio_to_text_response.transcript === 'string') transcript = progressUpdateMessage.audio_to_text_response.transcript;
    } else if (responseDataForFinalContent && finalResultType === "audio_to_text_response") {
        if (responseDataForFinalContent.segments && Array.isArray(responseDataForFinalContent.segments)) segments = responseDataForFinalContent.segments;
        if (typeof responseDataForFinalContent.transcript === 'string') transcript = responseDataForFinalContent.transcript;
    } else if (!segments && !transcript && progressUpdateMessage.audioToTextResponse) { // Different casing
        if (progressUpdateMessage.audioToTextResponse.segments) segments = progressUpdateMessage.audioToTextResponse.segments;
        if (progressUpdateMessage.audioToTextResponse.transcript) transcript = progressUpdateMessage.audioToTextResponse.transcript;
    }
    
    if (final_subtitle_content || transcript || (segments && segments.length > 0)) return { final_subtitle_content, segments, transcript };
    return null;
}

console.log("subtitler/extractors.js loaded"); 
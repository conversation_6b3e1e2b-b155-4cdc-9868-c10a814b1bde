// Remove unused import since <PERSON><PERSON> handles DOM manipulation
// import * as domElements from '../domElements.js';
import { getFileNameWithoutExtension, parseSRTToSegments, generateUniqueId } from './utils.js';

// ============================================================================
// ENHANCED DATA MANAGEMENT
// ============================================================================

// Enhanced global state manager for workflow results
const WorkflowData = {
    // Final workflow results
    finalOriginalSRT: null,
    finalTranslatedSRT: null,
    finalSegments: [],
    finalTranscript: null,
    
    // Reset all data
    reset() {
        console.warn('[WorkflowData] RESETTING ALL DATA. Call stack trace:', new Error("WorkflowData.reset() called").stack);
        this.finalOriginalSRT = null;
        this.finalTranslatedSRT = null;
        this.finalSegments = [];
        this.finalTranscript = null;
        console.info('[WorkflowData] Reset all data (completed)');
    },
    
    // Set final workflow results (from WorkflowComplete message)
    setFinalResults(originalSRT, translatedSRT, segmentsArgument, transcript) {
        this.finalOriginalSRT = originalSRT;
        this.finalTranslatedSRT = translatedSRT;
        // Only update segments if segmentsArgument is explicitly provided
        if (segmentsArgument !== undefined) {
            this.finalSegments = (segmentsArgument || []).map(seg => ({
                ...seg,
                id: seg.id || generateUniqueId()
            }));
        }
        // If segmentsArgument is undefined, this.finalSegments remains untouched.
        this.finalTranscript = transcript;
        
        console.info('[WorkflowData] Set final results (summary):', {
            originalSRT_exists: !!originalSRT,
            originalSRTLength: originalSRT?.length || 0,
            translatedSRT_exists: !!translatedSRT,
            translatedSRTLength: translatedSRT?.length || 0,
            segments_count_to_set: segmentsArgument ? segmentsArgument.length : (this.finalSegments ? this.finalSegments.length : 'undefined/untouched'),
            finalSegments_count_after_set: this.finalSegments.length,
            transcript_exists: !!transcript,
            firstFinalSegment_summary: this.finalSegments.length > 0 ? {
                id: this.finalSegments[0].id,
                text_length: this.finalSegments[0].text?.length,
                text_preview: this.finalSegments[0].text?.substring(0,50) + (this.finalSegments[0].text?.length > 50 ? '...' : ''),
                translatedText_length: this.finalSegments[0].translatedText?.length,
                translatedText_preview: this.finalSegments[0].translatedText?.substring(0,50) + (this.finalSegments[0].translatedText?.length > 50 ? '...' : ''),
                start_time_ms: this.finalSegments[0].start_time_ms,
                end_time_ms: this.finalSegments[0].end_time_ms
            } : null
        });
        
        // Store data globally for saveManager access
        if (typeof window !== 'undefined') {
            window.workflowIntermediateResults = {
                segments: this.finalSegments, // Ensures this uses the correct segments
                originalSRT: this.finalOriginalSRT,
                translatedSRT: this.finalTranslatedSRT,
                transcript: this.finalTranscript
            };
            console.info('[WorkflowData] Updated window.workflowIntermediateResults');
        }
    },

    // Update only final segments (e.g., from a late ProgressUpdate or specific source)
    updateFinalSegments(segments) {
        if (segments && segments.length > 0) {
            this.finalSegments = segments.map(seg => ({
                ...seg,
                id: seg.id || generateUniqueId()
            }));
            console.info('[WorkflowData] Updated finalSegments (summary):', {
                segmentsCount: this.finalSegments.length,
                firstSegment_summary: this.finalSegments.length > 0 ? {
                    id: this.finalSegments[0].id,
                    text_length: this.finalSegments[0].text?.length,
                    text_preview: this.finalSegments[0].text?.substring(0,50) + (this.finalSegments[0].text?.length > 50 ? '...' : ''),
                    translatedText_length: this.finalSegments[0].translatedText?.length,
                    translatedText_preview: this.finalSegments[0].translatedText?.substring(0,50) + (this.finalSegments[0].translatedText?.length > 50 ? '...' : '')
                } : null
            });

            // Also update the window object if it's used by other parts like saveManager directly
            if (typeof window !== 'undefined' && window.workflowIntermediateResults) {
                window.workflowIntermediateResults.segments = this.finalSegments;
                console.info('[WorkflowData] Updated window.workflowIntermediateResults.segments via updateFinalSegments');
            }
        } else {
            console.info('[WorkflowData] Attempted to update finalSegments, but no new segments provided or segments array was empty.');
        }
    },
    
    // Get segments for saving (properly formatted)
    // Assumes finalSegments has been pre-populated with original and translated text
    getSegmentsForSave() {
        if (!this.finalSegments || this.finalSegments.length === 0) {
            console.error('[WorkflowData] No segments available for save (finalSegments is empty)');
            return [];
        }
 
        console.info('[WorkflowData] Getting segments for save from finalSegments count:', this.finalSegments.length);
 
        const saveSegments = this.finalSegments.map(seg => {
            const originalText = seg.text || seg.original_text || '';
            // Use seg.translatedText, which should have been populated by handleStream
            const translatedText = seg.translatedText || seg.translated_text || '';
 
            return {
                text: originalText, // Retain for potential UI use if needed
                translatedText: translatedText, // Retain for potential UI use if needed
                original_text: originalText,
                translated_text: translatedText,
                startTimeMs: seg.start_time_ms || seg.startTimeMs || 0,
                endTimeMs: seg.end_time_ms || seg.endTimeMs || 0,
                // Ensure backend compatibility fields are also present if primary ones are different
                start_time_ms: seg.start_time_ms || seg.startTimeMs || 0,
                end_time_ms: seg.end_time_ms || seg.endTimeMs || 0,
                id: seg.id // Ensure ID is carried over for saving
            };
        });
 
        console.info('[WorkflowData] Generated segments for save count:', saveSegments.length);
        if (saveSegments.length > 0) {
            const firstSaveSeg = saveSegments[0];
            console.info('[WorkflowData] First save segment (summary):', {
                original_text_length: firstSaveSeg.original_text?.length,
                original_text_preview: firstSaveSeg.original_text?.substring(0,50) + (firstSaveSeg.original_text?.length > 50 ? '...' : ''),
                translated_text_length: firstSaveSeg.translated_text?.length,
                translated_text_preview: firstSaveSeg.translated_text?.substring(0,50) + (firstSaveSeg.translated_text?.length > 50 ? '...' : ''),
                startTimeMs: firstSaveSeg.startTimeMs,
                endTimeMs: firstSaveSeg.endTimeMs,
                id: firstSaveSeg.id
            });
        }
 
        return saveSegments;
    }
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

// Helper function to parse SRT timestamp to milliseconds
function parseTimestamp(timestamp) {
    if (!timestamp || typeof timestamp !== 'string') return 0;
    
    // SRT format: HH:MM:SS,mmm
    const match = timestamp.match(/(\d{2}):(\d{2}):(\d{2}),(\d{3})/);
    if (!match) return 0;
    
    const hours = parseInt(match[1], 10);
    const minutes = parseInt(match[2], 10);
    const seconds = parseInt(match[3], 10);
    const milliseconds = parseInt(match[4], 10);
    
    return (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds;
}

// Helper function to format milliseconds to SRT timestamp
function formatTimestamp(ms) {
    const totalSeconds = Math.floor(ms / 1000);
    const milliseconds = ms % 1000;
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;
    
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
}

// Helper function to generate SRT content from segments
function generateSRTFromSegments(segments) {
    if (!segments || segments.length === 0) return '';
    
    return segments.map((segment, index) => {
        const startTime = formatTimestamp(segment.start_time_ms || 0);
        const endTime = formatTimestamp(segment.end_time_ms || 0);
        const text = segment.text || '';
        
        return `${index + 1}\n${startTime} --> ${endTime}\n${text}\n`;
    }).join('\n');
}

// ============================================================================
// ENHANCED STREAM HANDLING
// ============================================================================

function handleStream(stream, buttonElement, progressElement, outputElement, operationType, segmentsElement) {
    console.info(`[Stream] Starting ${operationType}`);
    
    if (buttonElement) buttonElement.disabled = true;
    if (progressElement) progressElement.textContent = '处理中...';
    
    stream.on('data', (message) => {
        // 新增顶层DEBUG日志，检查每次收到的消息
        console.log('[Stream DEBUG] Received data event in handleStream. OperationType:', operationType);
        console.log('[Stream DEBUG] Full message object received:', JSON.parse(JSON.stringify(message)));
        console.log('[Stream DEBUG] message.stageName value:', message?.stageName, '| Type:', typeof message?.stageName);

        try {
            if (message?.isError) {
                const errorMsg = message.errorMessage || 'Unknown error';
                console.error(`[Stream] Error in ${operationType}:`, errorMsg);
                if (outputElement) {
                    if (outputElement.value !== undefined) outputElement.value = `错误: ${errorMsg}`;
                    else outputElement.textContent = `错误: ${errorMsg}`;
                }
                return;
            }
            
            if (message?.percentage !== undefined && progressElement) {
                const stage = message.stageName || '处理中';
                progressElement.textContent = `${stage}: ${message.percentage}%`;
            }
            
            // Enhanced logging for debugging
            console.info(`[Stream-${operationType}] Message received:`, {
                stageName: message.stageName,
                percentage: message.percentage,
                // keys: Object.keys(message), // Removed for brevity
                hasAudioToTextResponse: !!message.audio_to_text_response,
                hasGenerateSubtitlesResponse: !!message.generate_subtitles_response,
                hasTranslateSubtitlesResponse: !!message.translate_subtitles_response,
                ...(message.isError && { errorMessage: message.errorMessage })
            });
            
            // Handle ProgressUpdate with audio_to_text_response (for segments)
            // This should come *before* WorkflowComplete handling if segments are sent separately.
            if (message.audio_to_text_response && message.audio_to_text_response.segments && message.stageName !== 'WorkflowComplete') {
                console.info(`[Stream-${operationType}] Received ProgressUpdate with audio_to_text_response segments.`);
                const segments = message.audio_to_text_response.segments || [];
                let processedSegments = [];

                if (Array.isArray(segments) && segments.length > 0) {
                    processedSegments = segments.map(seg => ({
                        id: generateUniqueId(),
                        text: seg.text || '',
                        start_time_ms: parseInt(seg.start_time_ms) || 0,
                        end_time_ms: parseInt(seg.end_time_ms) || 0
                    }));
                    console.info(`[Stream-${operationType}] Processed segments from ProgressUpdate:`, processedSegments.length, 'segments', processedSegments.length > 0 ? processedSegments[0] : '');
                    WorkflowData.updateFinalSegments(processedSegments);
 
                    // Update segments display if element exists
                    if (segmentsElement && processedSegments.length > 0) {
                        console.info(`[Stream-${operationType}] Updating segmentsElement from ProgressUpdate.`);
                        if (segmentsElement.value !== undefined) {
                            segmentsElement.value = JSON.stringify(processedSegments, null, 2);
                        } else {
                            segmentsElement.textContent = JSON.stringify(processedSegments, null, 2);
                        }
                    }
                }
            }

            // Handle WorkflowComplete message specially
            if (message.stageName === 'WorkflowComplete' && operationType.startsWith('oneClick-')) {
                console.log('[OneClick DEBUG] Entered WorkflowComplete processing block. Message stageName:', message.stageName, 'OperationType:', operationType); // 新增调试日志
                console.info('[OneClick] Processing WorkflowComplete message');
                // 精简 Full message data 的输出
                console.info('[OneClick] Full message data (summary):', {
                    stageName: message.stageName,
                    final_result: message.final_result,
                    has_audio_to_text_response: !!message.audio_to_text_response,
                    transcript_length: message.audio_to_text_response?.transcript?.length,
                    asr_segments_count: message.audio_to_text_response?.segments?.length,
                    first_asr_segment_text_preview: message.audio_to_text_response?.segments?.[0]?.text?.substring(0,50),
                    has_generate_subtitles_response: !!message.generate_subtitles_response,
                    original_srt_length: message.generate_subtitles_response?.srt_content?.length,
                    original_srt_preview: message.generate_subtitles_response?.srt_content?.substring(0, 100) + (message.generate_subtitles_response?.srt_content?.length > 100 ? '...' : ''),
                    has_translate_subtitles_response: !!message.translate_subtitles_response,
                    translated_srt_length: message.translate_subtitles_response?.translated_subtitle_content?.length,
                    translated_srt_preview: message.translate_subtitles_response?.translated_subtitle_content?.substring(0, 100) + (message.translate_subtitles_response?.translated_subtitle_content?.length > 100 ? '...' : '')
                });
 
                const originalSRT = message.generate_subtitles_response?.srt_content || null;
                const translatedSRT = message.translate_subtitles_response?.translated_subtitle_content || null;
                const transcript = message.audio_to_text_response?.transcript || null;
                console.info('[OneClick] Raw translatedSRT content (first 300 chars):', translatedSRT?.substring(0, 300) + (translatedSRT?.length > 300 ? '...' : ''));
                
                let segmentsArgumentForSetFinalResults;

                // Determine base segments
                let baseSegments = [];
                if (message.audio_to_text_response?.segments && message.audio_to_text_response.segments.length > 0) {
                    baseSegments = message.audio_to_text_response.segments.map(seg => ({
                        id: generateUniqueId(),
                        text: seg.text || '',
                        start_time_ms: parseInt(seg.start_time_ms) || 0,
                        end_time_ms: parseInt(seg.end_time_ms) || 0,
                        // Ensure other potential fields are carried over if they exist
                        ...(seg.speaker && { speaker: seg.speaker }),
                        ...(seg.translatedText && { translatedText: seg.translatedText }), // Carry over if already present
                        ...(seg.translated_text && { translated_text: seg.translated_text }) // Carry over if already present
                    }));
                    console.info('[OneClick] Using segments from WorkflowComplete message.audio_to_text_response as base. Count:', baseSegments.length);
                } else if (WorkflowData.finalSegments && WorkflowData.finalSegments.length > 0) {
                    // Ensure existing segments from WorkflowData also have IDs if they somehow missed it
                    baseSegments = JSON.parse(JSON.stringify(WorkflowData.finalSegments)).map(s => ({ ...s, id: s.id || generateUniqueId() })); // Deep copy & ensure ID
                    console.info('[OneClick] Using WorkflowData.finalSegments (populated by ProgressUpdate) as base. Count:', baseSegments.length);
                } else {
                    console.warn('[OneClick] No base segments available from WorkflowComplete message or ProgressUpdate. Base segments count: 0');
                    baseSegments = [];
                }
 
                console.log('[OneClick DEBUG] Before if (translatedSRT): typeof translatedSRT =', typeof translatedSRT, '; translatedSRT is truthy? =', !!translatedSRT, '; Preview (if string):', typeof translatedSRT === 'string' ? translatedSRT.substring(0,100) + '...' : 'N/A'); // 新增调试日志
                if (translatedSRT) {
                    const parsedTranslatedSegmentsRaw = parseSRTToSegments(translatedSRT);
                    console.info('[OneClick] Raw parsed translated SRT content. Found segments:', parsedTranslatedSegmentsRaw.length);

                    if (parsedTranslatedSegmentsRaw.length > 0) {
                        // Preprocess translated segments to include millisecond timestamps
                        const parsedTranslatedSegments = parsedTranslatedSegmentsRaw.map(tSeg => ({
                            ...tSeg,
                            start_time_ms: parseTimestamp(tSeg.startTime),
                            end_time_ms: parseTimestamp(tSeg.endTime)
                        }));
                        console.info('[OneClick] Processed translated segments with ms timestamps. Count:', parsedTranslatedSegments.length);

                        if (baseSegments.length === 0) {
                            console.warn('[OneClick] No base segments found, but translated SRT is available. Creating segments from translated content using ms timestamps.');
                            segmentsArgumentForSetFinalResults = parsedTranslatedSegments.map(tSeg => ({
                                id: tSeg.id,
                                text: '', // Original text is unknown
                                translatedText: tSeg.text || '',
                                translated_text: tSeg.text || '', // Ensure consistency
                                start_time_ms: tSeg.start_time_ms, // Already in ms
                                end_time_ms: tSeg.end_time_ms,   // Already in ms
                                // startTime: tSeg.startTime, // Original SRT time string, keep if needed elsewhere
                                // endTime: tSeg.endTime,     // Original SRT time string, keep if needed elsewhere
                                index: tSeg.index
                            }));
                        } else {
                            // New timestamp-based alignment logic
                            console.info('[OneClick] Aligning translated segments to base segments using timestamp overlap.');
                            let mergedSegments = JSON.parse(JSON.stringify(baseSegments)).map(s => ({
                                ...s,
                                id: s.id || generateUniqueId(),
                                translatedText: '', // Initialize/reset
                                translated_text: '' // Initialize/reset
                            }));

                            mergedSegments.forEach(origSeg => {
                                const overlappingTransSegs = parsedTranslatedSegments.filter(transSeg => {
                                    const overlapStart = Math.max(origSeg.start_time_ms, transSeg.start_time_ms);
                                    const overlapEnd = Math.min(origSeg.end_time_ms, transSeg.end_time_ms);
                                    // Ensure positive overlap duration
                                    return overlapEnd > overlapStart;
                                });

                                if (overlappingTransSegs.length > 0) {
                                    // Sort overlapping translated segments by their start time
                                    overlappingTransSegs.sort((a, b) => a.start_time_ms - b.start_time_ms);
                                    
                                    // Concatenate their text, separated by a space
                                    const combinedText = overlappingTransSegs.map(ts => ts.text).join(' ');
                                    origSeg.translatedText = combinedText;
                                    origSeg.translated_text = combinedText; // Keep consistent
                                }
                                // If no overlapping segments, translatedText remains empty as initialized
                            });
                            
                            segmentsArgumentForSetFinalResults = mergedSegments;
                            console.info('[OneClick] Merged translated text into base segments using timestamp alignment. Resulting segments count:', segmentsArgumentForSetFinalResults.length);
                            if (segmentsArgumentForSetFinalResults.length > 0) {
                                const firstSeg = segmentsArgumentForSetFinalResults[0];
                                console.info('[OneClick] First segment after timestamp-based merge (summary):', {
                                    id: firstSeg.id,
                                    text_length: firstSeg.text?.length,
                                    text_preview: firstSeg.text?.substring(0,50) + (firstSeg.text?.length > 50 ? '...' : ''),
                                    translatedText_length: firstSeg.translatedText?.length,
                                    translatedText_preview: firstSeg.translatedText?.substring(0,50) + (firstSeg.translatedText?.length > 50 ? '...' : ''),
                                    start_time_ms: firstSeg.start_time_ms,
                                    end_time_ms: firstSeg.end_time_ms
                                });
                            }
                        }
                    } else {
                        console.warn('[OneClick] Translated SRT was present but parsing yielded no segments. Using base segments (if any) without translation.');
                        segmentsArgumentForSetFinalResults = baseSegments.length > 0 ? baseSegments : undefined;
                    }
                } else {
                    console.info('[OneClick] No translated SRT content available. Using base segments as is.');
                    segmentsArgumentForSetFinalResults = baseSegments.length > 0 ? baseSegments : undefined;
                }
                
                // If segmentsArgumentForSetFinalResults is still undefined, but baseSegments had items, use baseSegments.
                // This covers cases where translation wasn't requested or failed, but ASR produced segments.
                if (segmentsArgumentForSetFinalResults === undefined && baseSegments.length > 0) {
                    segmentsArgumentForSetFinalResults = baseSegments;
                    console.info('[OneClick] Using baseSegments as final segments argument as no translation processing occurred or was needed.');
                }


                console.info('[OneClick] Data for setFinalResults:', { //保留此关键日志
                    originalSRT_exists_in_local_var: !!originalSRT,
                    translatedSRT_exists_in_local_var: !!translatedSRT,
                    segmentsToSetCount: segmentsArgumentForSetFinalResults ? segmentsArgumentForSetFinalResults.length : 'undefined (will use existing or empty)',
                    transcript_exists_in_local_var: !!transcript
                });
 
                // Set final results in WorkflowData
                // The segmentsArgumentForSetFinalResults will be either merged, from ASR, or from translated SRT if ASR failed.
                // If it's undefined, setFinalResults will preserve existing finalSegments or set to empty if none.
                WorkflowData.setFinalResults(originalSRT, translatedSRT, segmentsArgumentForSetFinalResults, transcript);
 
                console.info('[OneClick] Verification after setFinalResults (WorkflowComplete):', { //保留此关键日志，精简输出
                    finalSegmentsCount: WorkflowData.finalSegments.length,
                    finalOriginalSRT_exists_in_WorkflowData: !!WorkflowData.finalOriginalSRT,
                    finalTranslatedSRT_exists_in_WorkflowData: !!WorkflowData.finalTranslatedSRT,
                    finalTranscript_exists_in_WorkflowData: !!WorkflowData.finalTranscript,
                    firstFinalSegment_summary: WorkflowData.finalSegments.length > 0 ? {
                        id: WorkflowData.finalSegments[0].id,
                        text_length: WorkflowData.finalSegments[0].text?.length,
                        text_preview: WorkflowData.finalSegments[0].text?.substring(0,50) + (WorkflowData.finalSegments[0].text?.length > 50 ? '...' : ''),
                        translatedText_length: WorkflowData.finalSegments[0].translatedText?.length,
                        translatedText_preview: WorkflowData.finalSegments[0].translatedText?.substring(0,50) + (WorkflowData.finalSegments[0].translatedText?.length > 50 ? '...' : ''),
                        start_time_ms: WorkflowData.finalSegments[0].start_time_ms,
                        end_time_ms: WorkflowData.finalSegments[0].end_time_ms
                    } : null
                });
 
                // Update UI displays
                if (outputElement) {
                    let statusText = '工作流程完成!\n';
                    if (WorkflowData.finalTranscript || WorkflowData.finalSegments.length > 0) statusText += '✓ 音频转文字完成\n';
                    if (WorkflowData.finalOriginalSRT) statusText += '✓ 字幕生成完成\n';
                    if (WorkflowData.finalTranslatedSRT) statusText += '✓ 字幕翻译完成\n';
                    statusText += `\n数据统计:\n- Segments: ${WorkflowData.finalSegments.length}\n- 原文字幕: ${WorkflowData.finalOriginalSRT ? '有' : '无'}\n- 译文字幕: ${WorkflowData.finalTranslatedSRT ? '有' : '无'}`;
                    
                    if (outputElement.value !== undefined) outputElement.value = statusText;
                    else outputElement.textContent = statusText;
                }

                if (segmentsElement && WorkflowData.finalSegments.length > 0) {
                    console.info('[OneClick] Updating segmentsElement from WorkflowComplete using WorkflowData.finalSegments.');
                    const displaySegments = WorkflowData.finalSegments.map(s => ({
                        id: s.id, // Include ID for display/debugging
                        text: s.text,
                        translatedText: s.translatedText,
                        start_time_ms: s.start_time_ms,
                        end_time_ms: s.end_time_ms
                    }));
                    if (segmentsElement.value !== undefined) {
                        segmentsElement.value = JSON.stringify(displaySegments, null, 2);
                    } else {
                        segmentsElement.textContent = JSON.stringify(displaySegments, null, 2);
                    }
                } else if (segmentsElement) {
                     console.info('[OneClick] No final segments to display in segmentsElement or segmentsElement not found.');
                     if (segmentsElement.value !== undefined) segmentsElement.value = "没有可显示的句段数据。";
                     else segmentsElement.textContent = "没有可显示的句段数据。";
                }
            }
        } catch (error) {
            console.error(`[Stream] Error processing message for ${operationType}:`, error);
        }
    });
    
    stream.on('end', () => {
        console.info(`[Stream] ${operationType} completed`);
        if (buttonElement) buttonElement.disabled = false;
        if (progressElement) progressElement.textContent = '完成';
        
        // Handle auto-save for one-click workflows
        if (operationType.startsWith('oneClick-')) {
            handleOneClickCompletion();
        }
    });
    
    stream.on('error', (error) => {
        console.error(`[Stream] ${operationType} error:`, error);
        if (buttonElement) buttonElement.disabled = false;
        if (progressElement) progressElement.textContent = '错误';
    });
}

// Enhanced one-click workflow completion and auto-save
async function handleOneClickCompletion() {
    console.info('[OneClick] Entered handleOneClickCompletion. Checking WorkflowData immediately:');
    console.info('[OneClick] Immediate check: finalOriginalSRT exists?', !!WorkflowData.finalOriginalSRT, 'Length:', WorkflowData.finalOriginalSRT?.length);
    console.info('[OneClick] Immediate check: finalTranslatedSRT exists?', !!WorkflowData.finalTranslatedSRT, 'Length:', WorkflowData.finalTranslatedSRT?.length);
    console.info('[OneClick] Immediate check: finalSegments count:', WorkflowData.finalSegments?.length);
    if (WorkflowData.finalSegments?.length > 0) {
        const firstSeg = WorkflowData.finalSegments[0];
        console.info('[OneClick] Immediate check: firstFinalSegment summary:', {
            id: firstSeg.id,
            text_preview: firstSeg.text?.substring(0,50) + (firstSeg.text?.length > 50 ? '...' : ''),
            translatedText_preview: firstSeg.translatedText?.substring(0,50) + (firstSeg.translatedText?.length > 50 ? '...' : '')
        });
    }

    console.info('[OneClick] Workflow completed, checking for auto-save'); // 这行是原有的日志
    
    const autoSaveOptions = window.currentAutoSaveOptions;
    if (autoSaveOptions && autoSaveOptions.autoSave === true) {
        try {
            const saveManagerModule = await import('./saveManager.js');
            const performAutoSave = saveManagerModule.performAutoSave;
            
            if (typeof performAutoSave === 'function') {
                const fileNamePrefix = window.currentFileName || `autosave-${Date.now()}`;
                const segments = WorkflowData.getSegmentsForSave();
                
                console.info('[OneClick] Starting auto-save with segments count:', segments.length);
                console.info('[OneClick] Auto-save data check (summary):', { //保留此关键日志，精简输出
                    segmentsCount: segments.length,
                    originalSRT_exists_in_WorkflowData: !!WorkflowData.finalOriginalSRT,
                    translatedSRT_exists_in_WorkflowData: !!WorkflowData.finalTranslatedSRT,
                    firstSegment_from_getSegmentsForSave_summary: segments.length > 0 ? {
                        id: segments[0].id,
                        original_text_length: segments[0].original_text?.length,
                        original_text_preview: segments[0].original_text?.substring(0,50) + (segments[0].original_text?.length > 50 ? '...' : ''),
                        translated_text_length: segments[0].translated_text?.length,
                        translated_text_preview: segments[0].translated_text?.substring(0,50) + (segments[0].translated_text?.length > 50 ? '...' : '')
                    } : null
                });
                
                if (segments.length === 0) {
                    console.error('[OneClick] No segments available for auto-save');
                    // Try to show user-friendly error
                    if (typeof window.showToast === 'function') {
                        window.showToast('自动保存失败：没有可用的字幕数据', 'error');
                    }
                    return;
                }
                
                // Save in all configured formats/layouts
                for (const format of autoSaveOptions.autoSaveFormats) {
                    for (const layout of autoSaveOptions.autoSaveLayouts) {
                        console.info(`[OneClick] Auto-saving ${format}/${layout}`);
                        
                        const saveConfig = {
                            fileNamePrefix: fileNamePrefix,
                            format: format,
                            layout: layout,
                            segments: segments,
                            original_content: WorkflowData.finalOriginalSRT || '',
                            translated_content: WorkflowData.finalTranslatedSRT || '',
                            auto_save_to_default: true
                        };
                        
                        await performAutoSave(saveConfig);
                        console.info(`[OneClick] Auto-saved ${format}/${layout}`);
                    }
                }
            }
        } catch (error) {
            console.error('[OneClick] Auto-save error:', error);
            if (typeof window.showToast === 'function') {
                window.showToast(`自动保存失败：${error.message}`, 'error');
            }
        }
    }
}

// ============================================================================
// API CLIENT METHODS
// ============================================================================

export const subtitlerClient = {
    reset() {
        WorkflowData.reset();
    },
    
    videoToAudio: function(request, metadata) {
        console.log('[SubtitlerClient] videoToAudio called with request:', request);
        const stream = new EventTarget();
        
        // Use the correct IPC channel that exists in the main process
        window.electronAPI.invoke('subtitler-full-workflow', {
            file_path: request.video_path,
            workflow_type: 'vid_to_audio',
            request_word_timestamps: false
        })
            .then(result => {
                console.log('[SubtitlerClient] videoToAudio result:', result);
                // Simulate stream response format
                const mockResponse = {
                    getStageName: () => 'VideoToAudio',
                    getPercentage: () => 100,
                    getMessage: () => 'Conversion complete',
                    getIsError: () => false,
                    getErrorMessage: () => '',
                    video_to_audio_response: {
                        getAudioPath: () => result.audio_path || request.video_path.replace(/\.[^/.]+$/, ".wav"),
                        audio_path: result.audio_path || request.video_path.replace(/\.[^/.]+$/, ".wav")
                    }
                };
                stream.dispatchEvent(new CustomEvent('data', { detail: mockResponse }));
                stream.dispatchEvent(new Event('end'));
            })
            .catch(error => {
                console.error('[SubtitlerClient] videoToAudio error:', error);
                stream.dispatchEvent(new CustomEvent('error', { detail: error }));
            });
            
        stream.on = function(eventName, callback) { 
            this.addEventListener(eventName, (e) => callback(e.detail || e)); 
            return this; 
        };
        return stream;
    },
    
    audioToText: function(request, metadata) {
        console.log('[SubtitlerClient] audioToText called with request:', request);
        const stream = new EventTarget();
        
        // Use the correct IPC channel that exists in the main process
        window.electronAPI.invoke('subtitler-full-workflow', {
            file_path: request.audio_path,
            workflow_type: 'audio_to_text',
            request_word_timestamps: request.request_word_timestamps || true
        })
            .then(result => {
                console.log('[SubtitlerClient] audioToText result:', result);
                // Simulate stream response format
                const mockResponse = {
                    getStageName: () => 'AudioToText',
                    getPercentage: () => 100,
                    getMessage: () => 'Transcription complete',
                    getIsError: () => false,
                    getErrorMessage: () => '',
                    audio_to_text_response: {
                        getTranscript: () => result.transcript || '',
                        getSegmentsList: () => result.segments || [],
                        transcript: result.transcript || '',
                        segments: result.segments || []
                    }
                };
                stream.dispatchEvent(new CustomEvent('data', { detail: mockResponse }));
                stream.dispatchEvent(new Event('end'));
            })
            .catch(error => {
                console.error('[SubtitlerClient] audioToText error:', error);
                stream.dispatchEvent(new CustomEvent('error', { detail: error }));
            });
            
        stream.on = function(eventName, callback) { 
            this.addEventListener(eventName, (e) => callback(e.detail || e)); 
            return this; 
        };
        return stream;
    },
    
    generateSubtitles: function(request, metadata) {
        console.log('[SubtitlerClient] generateSubtitles called with request:', request);
        const stream = new EventTarget();
        
        window.electronAPI.invoke('subtitler-full-workflow', {
            file_path: request.audio_path || request.video_path,
            workflow_type: 'audio_to_srt',
            request_word_timestamps: true
        })
            .then(result => {
                console.log('[SubtitlerClient] generateSubtitles result:', result);
                const mockResponse = {
                    getStageName: () => 'GenerateSubtitles',
                    getPercentage: () => 100,
                    getMessage: () => 'Subtitle generation complete',
                    getIsError: () => false,
                    getErrorMessage: () => '',
                    generate_subtitles_response: {
                        getSubtitleContent: () => result.srt_content || '',
                        srt_content: result.srt_content || ''
                    }
                };
                stream.dispatchEvent(new CustomEvent('data', { detail: mockResponse }));
                stream.dispatchEvent(new Event('end'));
            })
            .catch(error => {
                console.error('[SubtitlerClient] generateSubtitles error:', error);
                stream.dispatchEvent(new CustomEvent('error', { detail: error }));
            });
            
        stream.on = function(eventName, callback) { 
            this.addEventListener(eventName, (e) => callback(e.detail || e)); 
            return this; 
        };
        return stream;
    },
    
    translateSubtitles: function(request, metadata) {
        console.log('[SubtitlerClient] translateSubtitles called with request:', request);
        const stream = new EventTarget();
        
        window.electronAPI.invoke('subtitler-full-workflow', {
            file_path: request.audio_path || request.video_path,
            workflow_type: 'audio_to_srt_trans',
            target_language: request.target_language || 'zh-CN',
            request_word_timestamps: true
        })
            .then(result => {
                console.log('[SubtitlerClient] translateSubtitles result:', result);
                const mockResponse = {
                    getStageName: () => 'TranslateSubtitles',
                    getPercentage: () => 100,
                    getMessage: () => 'Translation complete',
                    getIsError: () => false,
                    getErrorMessage: () => '',
                    translate_subtitles_response: {
                        getTranslatedContent: () => result.translated_subtitle_content || '',
                        translated_subtitle_content: result.translated_subtitle_content || ''
                    }
                };
                stream.dispatchEvent(new CustomEvent('data', { detail: mockResponse }));
                stream.dispatchEvent(new Event('end'));
            })
            .catch(error => {
                console.error('[SubtitlerClient] translateSubtitles error:', error);
                stream.dispatchEvent(new CustomEvent('error', { detail: error }));
            });
            
        stream.on = function(eventName, callback) { 
            this.addEventListener(eventName, (e) => callback(e.detail || e)); 
            return this; 
        };
        return stream;
    },
    
    fullWorkflow: function(request, metadata) {
        console.log('[SubtitlerClient] fullWorkflow called with request:', request);
        
        WorkflowData.reset();
        
        const stream = new EventTarget();
        stream.on = function(eventName, callback) { 
            this.addEventListener(eventName, (e) => callback(e.detail || e)); 
            return this; 
        };
        
        // Set up progress listener for real-time updates
        const progressListener = (update) => {
            console.log('[SubtitlerClient] Progress update:', update);
            stream.dispatchEvent(new CustomEvent('data', { detail: update }));
        };
        
        const removeProgressListener = window.electronAPI.onProgressUpdate(progressListener);
        
        // Use the correct IPC channel
        window.electronAPI.invoke('subtitler-full-workflow', request)
            .then(result => {
                console.log('[SubtitlerClient] fullWorkflow result:', result);
                
                // Send final completion message
                const completionResponse = {
                    getStageName: () => 'WorkflowComplete',
                    getPercentage: () => 100,
                    getMessage: () => 'Workflow completed successfully',
                    getIsError: () => false,
                    getErrorMessage: () => ''
                };
                stream.dispatchEvent(new CustomEvent('data', { detail: completionResponse }));
                stream.dispatchEvent(new Event('end'));
                
                removeProgressListener();
            })
            .catch(error => {
                console.error('[SubtitlerClient] fullWorkflow error:', error);
                stream.dispatchEvent(new CustomEvent('error', { detail: error }));
                removeProgressListener();
            });
            
        return stream;
    }
};

// Export utilities
export { handleStream, WorkflowData, parseTimestamp, formatTimestamp, generateSRTFromSegments };

console.log("subtitler/apiClient.js (enhanced) loaded");

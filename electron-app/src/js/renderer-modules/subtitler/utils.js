// electron-app/src/js/renderer-modules/subtitler/utils.js
// 通用工具函数

// Helper function to generate a unique ID for segments
export function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 7);
}

/**
 * 解析SRT内容为片段数组
 * @param {string} srtContent SRT内容
 * @returns {Array} 解析后的片段数组
 */
export function parseSRTToSegments(srtContent) {
    console.log('[Utils] parseSRTToSegments input srtContent length:', srtContent?.length);
    if (srtContent) {
        console.log('[Utils] parseSRTToSegments input srtContent (first 300 chars):', srtContent.substring(0, 300) + (srtContent.length > 300 ? '...' : ''));
    }
    if (!srtContent || typeof srtContent !== 'string') {
        return [];
    }
    const segments = [];
    const lines = srtContent.split(/\r?\n/);
    let i = 0;
    while (i < lines.length) {
        // Allow for leading/trailing whitespace around the sequence number
        if (lines[i] && lines[i].match(/^\s*\d+\s*$/)) { // Corrected regex
            const segment = { index: lines[i].trim(), id: generateUniqueId() }; // Trim the index
            i++;
            if (i < lines.length && lines[i].includes('-->')) {
                const timeParts = lines[i].split(' --> ');
                segment.startTime = timeParts[0];
                segment.endTime = timeParts[1];
                i++;
                let textLines = [];
                while (i < lines.length && lines[i].trim() !== '') {
                    textLines.push(lines[i]);
                    i++;
                }
                segment.text = textLines.join('\\n');
                segments.push(segment);
            }
        }
        i++;
    }
    console.log('[Utils] parseSRTToSegments output segments count:', segments.length);
    if (segments.length > 0) {
        const firstSeg = segments[0];
        console.log('[Utils] parseSRTToSegments first output segment (summary):', {
            index: firstSeg.index,
            id: firstSeg.id,
            startTime: firstSeg.startTime,
            endTime: firstSeg.endTime,
            text_length: firstSeg.text?.length,
            text_preview: firstSeg.text?.substring(0,50) + (firstSeg.text?.length > 50 ? '...' : '')
        });
    }
    return segments;
}

/**
 * 使用时间戳片段更新UI元素
 * @param {HTMLElement} segmentsElement 要更新的DOM元素
 * @param {Array} segments 时间戳片段数组
 */
export function updateTimestampedSegmentsUI(segmentsElement, segments) {
    if (!segmentsElement) {
        console.error('[ApiClientJS] updateTimestampedSegmentsUI: segmentsElement is null.');
        return;
    }
    if (!segments || segments.length === 0) {
        segmentsElement.innerHTML = '<p>没有可显示的时间戳片段。</p>';
        return;
    }
    // Enhanced segment display
    segmentsElement.innerHTML = segments.map(seg => {
        // Assuming segments from parseSRTToSegments have startTime, endTime, text
        // And segments from backend have start_time_ms, end_time_ms, text
        const startTime = seg.startTime || (seg.start_time_ms !== undefined ? (seg.start_time_ms / 1000).toFixed(2) + 's' : 'N/A');
        const endTime = seg.endTime || (seg.end_time_ms !== undefined ? (seg.end_time_ms / 1000).toFixed(2) + 's' : 'N/A');
        const textContent = seg.text || '无文本内容';
        return `<div class="segment-item p-2 border-b border-gray-200">
                    <span class="segment-time font-mono text-sm text-gray-600">[${startTime} - ${endTime}]</span>
                    <p class="segment-text ml-2">${textContent.replace(/\\n/g, '<br>')}</p>
                </div>`;
    }).join('');
}

// 定义工作流中间结果的全局变量（如果不存在）
if (typeof window.workflowIntermediateResults === 'undefined') {
    window.workflowIntermediateResults = {
        transcript: null,
        segments: null,
        srt: null,
        translation: null
    };
}

/**
 * 从完整文件名中获取不带扩展名的部分。
 * @param {string} fileName - 完整文件名，例如 "video.mp4" 或 "audio.wav.srt"
 * @returns {string} 不带最后一个扩展名的文件名，例如 "video" 或 "audio.wav"
 */
export function getFileNameWithoutExtension(fileName) {
  if (!fileName || typeof fileName !== 'string') {
    return ''; // 或者返回一个默认名，或者抛出错误
  }
  const lastDotIndex = fileName.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === 0) { // 没有点，或者点在开头（如.hiddenfile）
    return fileName;
  }
  return fileName.substring(0, lastDotIndex);
}

// Helper function to format milliseconds to SRT time format
export function formatMillisecondsToSrtTime(ms) {
  if (typeof ms !== 'number' || isNaN(ms) || ms < 0) {
    ms = 0;
  }
  const date = new Date(ms);
  const hours = String(date.getUTCHours()).padStart(2, '0');
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');
  const milliseconds = String(date.getUTCMilliseconds()).padStart(3, '0');
  return `${hours}:${minutes}:${seconds},${milliseconds}`;
}

// Helper function to convert segments to SRT format
export function segmentsToSrt(segments) {
  if (!Array.isArray(segments) || segments.length === 0) {
    return '';
  }
  let srtContent = '';
  segments.forEach((segment, index) => {
    // Segments from Vue store might have startTimeMs, endTimeMs
    // Segments from parseSrtToSegments have start_time_ms, end_time_ms
    // Segments from audioToTextResult.segments have start, end (in seconds)
    let startTimeMs, endTimeMs;

    if (segment.startTimeMs !== undefined && segment.endTimeMs !== undefined) {
        startTimeMs = segment.startTimeMs;
        endTimeMs = segment.endTimeMs;
    } else if (segment.start_time_ms !== undefined && segment.end_time_ms !== undefined) {
        startTimeMs = segment.start_time_ms;
        endTimeMs = segment.end_time_ms;
    } else if (segment.start !== undefined && segment.end !== undefined) {
        // Convert from seconds to milliseconds
        startTimeMs = Math.round(segment.start * 1000);
        endTimeMs = Math.round(segment.end * 1000);
    } else {
        startTimeMs = 0;
        endTimeMs = 0;
    }

    const startTime = formatMillisecondsToSrtTime(startTimeMs);
    const endTime = formatMillisecondsToSrtTime(endTimeMs);
    const text = segment.text || '';

    srtContent += `${index + 1}\n`;
    srtContent += `${startTime} --> ${endTime}\n`;
    srtContent += `${text}\n\n`;
  });
  return srtContent.trim();
}

console.log("subtitler/utils.js loaded");
// electron-app/renderer.js

import { subtitlerClient } from './src/js/renderer-modules/subtitler/apiClient.js';

// Make subtitlerClient globally available for Vue components
// This is kept as it might be essential for Vue components to access the client.
// If Vue components are directly importing and managing subtitlerClient, this can be removed.
// For now, preserving this to avoid breaking Vue component functionality.
if (typeof window !== 'undefined') {
    window.subtitlerClient = subtitlerClient;
    console.log('[Renderer] Made subtitlerClient globally available for Vue components');
}

console.log("Legacy renderer modules initialization complete. Vue/Pinia handles all UI functionality.");
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import electron from 'vite-plugin-electron';
import renderer from 'vite-plugin-electron-renderer';
import path from 'path'; //  <--- Import path module

export default defineConfig({
  base: './', // Ensure relative paths work correctly
  plugins: [
    vue(),
    electron([
      {
        // Main-Process entry file of the Electron App.
        entry: 'main.js',
      },
      {
        entry: 'preload.js',
        onstart(options) {
          // Notify the Renderer-Process to reload the page when the Preload-Scripts build is complete,
          // instead of restarting the entire Electron App.
          options.reload();
        },
      },
    ]),
    renderer({
      // Enables use of Node.js API in the Renderer-process
      // nodeIntegration: true, // Be cautious with this setting
      // Enables use of ESM in the Renderer-process
      // esm: true,
    }),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
  },
  build: {
    // Remove outDir to use default, which works better with electron-builder
    rollupOptions: {
      output: {
        format: 'es'
      }
    }
  },
  css: {
    postcss: './postcss.config.js',
  },
});
// --- BEGIN TOP OF main.js ---
const fs_early = require('fs');
const path_early = require('path');
const util_early = require('util');

// Ensure user data path exists for logging very early errors
// Note: app module might not be available yet if this is truly at the very top.
// We'll try to get it, but have a fallback.
let earlyLogPath;
try {
    const { app: app_early } = require('electron'); // Try to get app
    const userDataDir = app_early.getPath('userData');
    if (!fs_early.existsSync(userDataDir)) {
        fs_early.mkdirSync(userDataDir, { recursive: true });
    }
    earlyLogPath = path_early.join(userDataDir, 'electron_main_early.log');
} catch (e) {
    // Fallback if app is not available or userData path fails
    const fallbackDir = path_early.join(process.env.HOME || process.cwd(), '.electron-app-logs');
    if (!fs_early.existsSync(fallbackDir)) {
        fs_early.mkdirSync(fallbackDir, { recursive: true });
    }
    earlyLogPath = path_early.join(fallbackDir, 'electron_main_early.log');
}

const logFile = fs_early.createWriteStream(earlyLogPath, { flags: 'a' });
const logStdout = process.stdout;

console.log = function(...args) {
  logFile.write(util_early.format.apply(null, args) + '\n');
  logStdout.write(util_early.format.apply(null, args) + '\n'); // Also keep logging to original stdout
};
console.error = console.log;
console.warn = console.log;

console.log(`--- Electron Main Process Early Log Started: ${new Date().toISOString()} ---`);
console.log(`--- Early log file path: ${earlyLogPath} ---`);
// --- END EARLY LOG SETUP ---

// Original main.js content starts here...
// Entry point for the Electron main process.
// This file should now be very minimal, primarily importing and initializing modules.

const { app } = require('electron'); // This app might be different from app_early if re-required
const path = require('path');
const fs = require('fs');

// Load .env file early, but handle both development and packaged environments
const envPath = path.join(__dirname, '.env');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
} else {
  // In packaged environment, try to load from config directory
  let configEnvPath;
  if (process.resourcesPath) {
    configEnvPath = path.join(process.resourcesPath, 'config', '.env');
  }

  if (configEnvPath && fs.existsSync(configEnvPath)) {
    require('dotenv').config({ path: configEnvPath });
  } else {
    // Set default environment variables if not already set
    if (!process.env.OPENAI_API_KEY) {
      // You can set a default or leave it undefined
      // process.env.OPENAI_API_KEY = 'your-default-key';
    }
  }
}

// Fix PATH environment variable for packaged apps
// This is crucial for Python backend to work when launched via Finder
if (!process.defaultApp) {
  const poetryBinPath = '/Users/<USER>/Library/Caches/pypoetry/virtualenvs/my-python-project-C15Z2L7S-py3.12/bin';
  const commonPaths = [
    poetryBinPath, // Add Poetry venv bin path to the front
    '/opt/homebrew/bin',
    '/usr/local/bin',
    '/usr/bin',
    '/bin',
    '/usr/sbin',
    '/sbin'
  ];

  const currentPath = process.env.PATH || '';
  // Ensure no empty strings from split and maintain order with poetryBinPath first
  const existingPathParts = currentPath.split(':').filter(p => p.length > 0);
  const pathParts = [...new Set([poetryBinPath, ...commonPaths, ...existingPathParts])];
  process.env.PATH = pathParts.join(':');

  console.log(`[Main] Fixed PATH for packaged app (with Poetry venv): ${process.env.PATH}`);
  console.log(`[Main] Checking if poetry python3.12 exists at ${path_early.join(poetryBinPath, 'python3.12')}: ${fs_early.existsSync(path_early.join(poetryBinPath, 'python3.12'))}`);
}

// Import the main application lifecycle manager
const appLifecycle = require(path.join(__dirname, 'src', 'main-process', 'app-lifecycle'));

// Initialize the application lifecycle event handlers.
// This function will, in turn, initialize all other necessary modules
// (WindowManager, BackendManager, GrpcManager, IpcHandlers, Utils).
appLifecycle.initializeAppLifecycleEvents();

// No other code should ideally be here. All logic should be in the imported modules.
// Global variables like mainWindow, subtitlerClient, serviceConfigs, backendProcesses, etc.,
// are now encapsulated within their respective modules.
# 🧪 测试开发指南

## 📋 **测试策略概览**

### **测试金字塔**
```
                    E2E Tests
                   /           \
              Integration Tests
             /                   \
        Unit Tests (Foundation)
```

### **测试覆盖范围**
- **单元测试**: 组件、函数、类的独立测试
- **集成测试**: 模块间交互测试
- **端到端测试**: 完整用户流程测试
- **性能测试**: 响应时间和资源使用测试
- **API测试**: gRPC服务接口测试

### **测试技术栈**
- **前端测试**: Vitest + Vue Test Utils + Testing Library
- **后端测试**: pytest (Python) + Go testing + JUnit (Java)
- **E2E测试**: Playwright + Electron Testing
- **API测试**: grpcurl + 自定义测试脚本
- **性能测试**: Artillery + 自定义性能监控

## 🚀 **快速开始**

### **环境准备**
```bash
# 1. 安装测试依赖
cd electron-app
npm install --save-dev vitest @vue/test-utils @testing-library/vue playwright

# 2. Python测试环境
cd backend
pip install pytest pytest-asyncio pytest-grpc

# 3. Go测试环境
cd go-backend
go mod tidy

# 4. Java测试环境
cd java-backend
# Maven会自动处理JUnit依赖
```

### **运行测试命令**
```bash
# 前端单元测试
npm run test:unit

# 前端集成测试
npm run test:integration

# E2E测试
npm run test:e2e

# 后端测试
cd backend && python -m pytest

# Go测试
cd go-backend && go test ./...

# Java测试
cd java-backend && mvn test

# 全量测试
npm run test:all
```

## 📁 **测试项目结构**

### **前端测试结构**
```
electron-app/
├── tests/
│   ├── unit/                      # 单元测试
│   │   ├── components/            # 组件测试
│   │   │   ├── common/
│   │   │   │   ├── StatusCard.test.js
│   │   │   │   └── ProgressBar.test.js
│   │   │   └── subtitler/
│   │   │       ├── Step1FileUpload.test.js
│   │   │       └── Step6EditSubtitles.test.js
│   │   ├── store/                 # 状态管理测试
│   │   │   ├── subtitlerStore.test.js
│   │   │   └── grpcTestStore.test.js
│   │   └── utils/                 # 工具函数测试
│   │       └── helpers.test.js
│   ├── integration/               # 集成测试
│   │   ├── workflow/              # 工作流集成测试
│   │   │   ├── distributed-workflow.test.js
│   │   │   └── one-click-operation.test.js
│   │   └── ipc/                   # IPC通信测试
│   │       ├── grpc-handlers.test.js
│   │       └── filesystem-handlers.test.js
│   ├── e2e/                       # 端到端测试
│   │   ├── specs/
│   │   │   ├── complete-workflow.spec.js
│   │   │   ├── batch-processing.spec.js
│   │   │   └── error-handling.spec.js
│   │   ├── fixtures/              # 测试数据
│   │   │   ├── sample-video.mp4
│   │   │   ├── sample-audio.wav
│   │   │   └── sample-text.txt
│   │   └── helpers/               # 测试辅助函数
│   │       ├── electron-helpers.js
│   │       └── file-helpers.js
│   └── setup/                     # 测试配置
│       ├── vitest.config.js
│       ├── playwright.config.js
│       └── test-utils.js
├── vitest.config.js               # Vitest配置
└── playwright.config.js           # Playwright配置
```

### **后端测试结构**
```
backend/
├── tests/
│   ├── unit/                      # 单元测试
│   │   ├── test_subtitle_processor.py
│   │   ├── test_translation_service.py
│   │   └── test_file_utils.py
│   ├── integration/               # 集成测试
│   │   ├── test_grpc_service.py
│   │   ├── test_workflow_integration.py
│   │   └── test_external_apis.py
│   ├── fixtures/                  # 测试数据
│   │   ├── sample_audio.wav
│   │   ├── sample_transcript.txt
│   │   └── expected_outputs/
│   └── conftest.py                # pytest配置
├── pytest.ini                    # pytest配置文件
└── requirements-test.txt          # 测试依赖
```

## 🔧 **前端测试详解**

### **Vitest配置**
```javascript
// vitest.config.js
import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';
import path from 'path';

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup/test-utils.js'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.config.js'
      ]
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src/vue')
    }
  }
});
```

### **测试工具配置**
```javascript
// tests/setup/test-utils.js
import { config } from '@vue/test-utils';
import { createPinia } from 'pinia';

// 全局测试配置
config.global.plugins = [createPinia()];

// Mock Electron API
global.window.electronAPI = {
  invoke: vi.fn(),
  on: vi.fn(),
  removeAllListeners: vi.fn()
};

// Mock console methods for cleaner test output
global.console = {
  ...console,
  log: vi.fn(),
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
};

// 通用测试辅助函数
export const createMockFile = (name = 'test.mp4', size = 1024) => {
  return new File([''], name, { type: 'video/mp4', size });
};

export const createMockStore = (initialState = {}) => {
  const pinia = createPinia();
  return pinia;
};

export const waitForNextTick = () => {
  return new Promise(resolve => setTimeout(resolve, 0));
};
```

### **组件单元测试示例**
```javascript
// tests/unit/components/common/StatusCard.test.js
import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import StatusCard from '@/components/common/StatusCard.vue';

describe('StatusCard', () => {
  it('renders with default props', () => {
    const wrapper = mount(StatusCard, {
      props: {
        title: 'Test Status',
        status: 'success',
        message: 'Test message'
      }
    });

    expect(wrapper.find('h3').text()).toBe('Test Status');
    expect(wrapper.find('.status-message').text()).toBe('Test message');
    expect(wrapper.classes()).toContain('status-success');
  });

  it('displays error state correctly', () => {
    const wrapper = mount(StatusCard, {
      props: {
        title: 'Error Status',
        status: 'error',
        message: 'Error occurred'
      }
    });

    expect(wrapper.classes()).toContain('status-error');
    expect(wrapper.find('.error-icon').exists()).toBe(true);
  });

  it('emits retry event when retry button is clicked', async () => {
    const wrapper = mount(StatusCard, {
      props: {
        title: 'Failed Status',
        status: 'error',
        message: 'Process failed',
        showRetry: true
      }
    });

    await wrapper.find('.retry-button').trigger('click');
    expect(wrapper.emitted('retry')).toBeTruthy();
  });
});
```

### **Store单元测试示例**
```javascript
// tests/unit/store/subtitlerStore.test.js
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useSubtitlerStore } from '@/store/subtitlerStore';

describe('SubtitlerStore', () => {
  let store;

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useSubtitlerStore();
    
    // Mock Electron API
    global.window.electronAPI = {
      invoke: vi.fn()
    };
  });

  describe('File Upload', () => {
    it('sets uploaded file correctly', () => {
      const mockFile = {
        name: 'test.mp4',
        path: '/path/to/test.mp4',
        size: 1024
      };

      store.setUploadedFile(mockFile);
      
      expect(store.uploadedFile).toEqual(mockFile);
      expect(store.currentStep).toBe(1);
    });

    it('resets workflow state', () => {
      // 设置一些状态
      store.uploadedFile = { name: 'test.mp4' };
      store.currentStep = 5;
      store.videoToAudioResult = { audio_path: '/path/audio.wav' };

      // 重置工作流
      store.resetWorkflow();

      expect(store.uploadedFile).toBeNull();
      expect(store.currentStep).toBe(1);
      expect(store.videoToAudioResult).toBeNull();
    });
  });

  describe('Video to Audio Processing', () => {
    it('processes video to audio successfully', async () => {
      const mockResponse = {
        data: {
          video_to_audio_response: {
            audio_path: '/path/to/audio.wav'
          }
        }
      };

      global.window.electronAPI.invoke.mockResolvedValue(mockResponse);

      store.uploadedFile = { path: '/path/to/video.mp4' };
      
      await store.processVideoToAudio();

      expect(store.videoToAudioResult).toEqual(mockResponse.data.video_to_audio_response);
      expect(store.currentStep).toBe(2);
    });

    it('handles video to audio processing error', async () => {
      const mockError = new Error('Processing failed');
      global.window.electronAPI.invoke.mockRejectedValue(mockError);

      store.uploadedFile = { path: '/path/to/video.mp4' };

      await expect(store.processVideoToAudio()).rejects.toThrow('Processing failed');
      expect(store.videoToAudioResult).toBeNull();
    });
  });

  describe('One-Click Operation', () => {
    it('performs complete one-click workflow', async () => {
      const mockResponses = [
        { data: { video_to_audio_response: { audio_path: '/audio.wav' } } },
        { data: { audio_to_text_response: { transcript: 'Hello world' } } },
        { data: { text_to_translated_srt_response: { translated_srt_content: 'SRT content' } } }
      ];

      global.window.electronAPI.invoke
        .mockResolvedValueOnce(mockResponses[0])
        .mockResolvedValueOnce(mockResponses[1])
        .mockResolvedValueOnce(mockResponses[2]);

      store.uploadedFile = { path: '/video.mp4' };
      store.oneClickWorkflowType = 'vid_to_srt_trans';

      await store.performOneClickOperation();

      expect(store.currentStep).toBe(9);
      expect(global.window.electronAPI.invoke).toHaveBeenCalledTimes(3);
    });
  });
});
```

### **集成测试示例**
```javascript
// tests/integration/workflow/distributed-workflow.test.js
import { describe, it, expect, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia } from 'pinia';
import DistributedWorkflowView from '@/views/DistributedWorkflowView.vue';
import { useSubtitlerStore } from '@/store/subtitlerStore';

describe('Distributed Workflow Integration', () => {
  let wrapper;
  let store;

  beforeEach(() => {
    const pinia = createPinia();
    
    wrapper = mount(DistributedWorkflowView, {
      global: {
        plugins: [pinia]
      }
    });
    
    store = useSubtitlerStore();
  });

  it('completes full workflow from file upload to export', async () => {
    // Mock successful responses for each step
    global.window.electronAPI.invoke = vi.fn()
      .mockResolvedValueOnce({ data: { video_to_audio_response: { audio_path: '/audio.wav' } } })
      .mockResolvedValueOnce({ data: { audio_to_text_response: { transcript: 'Test transcript' } } })
      .mockResolvedValueOnce({ data: { text_to_srt_response: { srt_content: 'SRT content' } } });

    // Step 1: Upload file
    const fileInput = wrapper.find('input[type="file"]');
    const mockFile = new File([''], 'test.mp4', { type: 'video/mp4' });
    
    Object.defineProperty(fileInput.element, 'files', {
      value: [mockFile],
      writable: false
    });
    
    await fileInput.trigger('change');
    expect(store.uploadedFile).toBeTruthy();

    // Step 2: Process video to audio
    await wrapper.find('[data-testid="process-video-audio"]').trigger('click');
    expect(store.videoToAudioResult).toBeTruthy();

    // Step 3: Process audio to text
    await wrapper.find('[data-testid="process-audio-text"]').trigger('click');
    expect(store.audioToTextResult).toBeTruthy();

    // Continue through remaining steps...
    expect(store.currentStep).toBeGreaterThan(1);
  });

  it('handles errors gracefully in workflow', async () => {
    global.window.electronAPI.invoke = vi.fn()
      .mockRejectedValue(new Error('Processing failed'));

    store.uploadedFile = { path: '/test.mp4' };

    const processButton = wrapper.find('[data-testid="process-video-audio"]');
    await processButton.trigger('click');

    // Should show error state
    expect(wrapper.find('.error-message').exists()).toBe(true);
    expect(store.videoToAudioResult).toBeNull();
  });
});
```

## 🔧 **后端测试详解**

### **pytest配置**
```ini
# pytest.ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
    --cov=.
    --cov-report=term-missing
    --cov-report=html
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    external: Tests that require external services
```

### **测试配置文件**
```python
# tests/conftest.py
import pytest
import tempfile
import os
from unittest.mock import Mock, patch
import grpc
from grpc_testing import server_from_dictionary, strict_real_time

from subtitle_service import SubtitlerService
from api_protos.v1.subtitler import subtitler_pb2_grpc

@pytest.fixture
def temp_dir():
    """创建临时目录用于测试"""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir

@pytest.fixture
def sample_audio_file(temp_dir):
    """创建示例音频文件"""
    audio_path = os.path.join(temp_dir, "sample.wav")
    # 创建一个空的音频文件用于测试
    with open(audio_path, 'wb') as f:
        f.write(b'\x00' * 1024)  # 1KB的空数据
    return audio_path

@pytest.fixture
def sample_video_file(temp_dir):
    """创建示例视频文件"""
    video_path = os.path.join(temp_dir, "sample.mp4")
    with open(video_path, 'wb') as f:
        f.write(b'\x00' * 2048)  # 2KB的空数据
    return video_path

@pytest.fixture
def grpc_server():
    """创建gRPC测试服务器"""
    service = SubtitlerService()
    descriptors_to_services = {
        subtitler_pb2_grpc.DESCRIPTOR.services_by_name['Subtitler']: service
    }
    return server_from_dictionary(descriptors_to_services, strict_real_time())

@pytest.fixture
def mock_whisper():
    """Mock Whisper模型"""
    with patch('whisper.load_model') as mock_load:
        mock_model = Mock()
        mock_model.transcribe.return_value = {
            "text": "This is a test transcript",
            "segments": [
                {"text": "This is a test", "start": 0.0, "end": 2.0},
                {"text": "transcript", "start": 2.0, "end": 3.0}
            ]
        }
        mock_load.return_value = mock_model
        yield mock_model

@pytest.fixture
def mock_openai():
    """Mock OpenAI API"""
    with patch('openai.OpenAI') as mock_client:
        mock_response = Mock()
        mock_response.choices[0].message.content = "这是一个测试转录"
        mock_client.return_value.chat.completions.create.return_value = mock_response
        yield mock_client
```

### **单元测试示例**
```python
# tests/unit/test_subtitle_processor.py
import pytest
from unittest.mock import patch, Mock
from subtitle_processor import SubtitleProcessor

class TestSubtitleProcessor:
    
    @pytest.fixture
    def processor(self):
        return SubtitleProcessor()
    
    def test_convert_video_to_audio_success(self, processor, sample_video_file, temp_dir):
        """测试视频转音频成功场景"""
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            
            audio_path = processor.convert_video_to_audio(sample_video_file)
            
            assert audio_path.endswith('.wav')
            assert mock_run.called
            
            # 验证FFmpeg命令参数
            call_args = mock_run.call_args[0][0]
            assert 'ffmpeg' in call_args
            assert '-i' in call_args
            assert sample_video_file in call_args
    
    def test_convert_video_to_audio_failure(self, processor, sample_video_file):
        """测试视频转音频失败场景"""
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 1
            mock_run.return_value.stderr = "FFmpeg error"
            
            with pytest.raises(Exception) as exc_info:
                processor.convert_video_to_audio(sample_video_file)
            
            assert "FFmpeg error" in str(exc_info.value)
    
    def test_transcribe_audio_success(self, processor, sample_audio_file, mock_whisper):
        """测试音频转录成功场景"""
        transcript, segments = processor.transcribe_audio(sample_audio_file)
        
        assert transcript == "This is a test transcript"
        assert len(segments) == 2
        assert segments[0]['text'] == "This is a test"
        assert segments[0]['start'] == 0.0
        assert segments[0]['end'] == 2.0
    
    def test_translate_text_success(self, processor, mock_openai):
        """测试文本翻译成功场景"""
        original_text = "This is a test transcript"
        target_language = "Chinese"
        
        translated = processor.translate_text(original_text, target_language)
        
        assert translated == "这是一个测试转录"
        mock_openai.return_value.chat.completions.create.assert_called_once()
    
    def test_generate_srt_from_text(self, processor):
        """测试SRT生成功能"""
        text = "Hello world. This is a test. How are you?"
        
        srt_content = processor.generate_srt_from_text(text)
        
        # 验证SRT格式
        lines = srt_content.strip().split('\n')
        assert lines[0] == "1"
        assert "-->" in lines[1]
        assert "Hello world" in lines[2]
        
        assert lines[4] == "2"
        assert "-->" in lines[5]
        assert "This is a test" in lines[6]
    
    @pytest.mark.parametrize("seconds,expected", [
        (0, "00:00:00,000"),
        (65, "00:01:05,000"),
        (3661, "01:01:01,000")
    ])
    def test_format_time(self, processor, seconds, expected):
        """测试时间格式化功能"""
        result = processor._format_time(seconds)
        assert result == expected
```

### **集成测试示例**
```python
# tests/integration/test_grpc_service.py
import pytest
import grpc
from api_protos.v1.subtitler import subtitler_pb2

class TestSubtitlerGrpcService:
    
    def test_full_workflow_video_to_audio(self, grpc_server, sample_video_file):
        """测试视频转音频gRPC调用"""
        request = subtitler_pb2.FullWorkflowRequest(
            workflow_type='vid_to_audio',
            file_path=sample_video_file
        )
        
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            
            response = grpc_server.invoke_unary_unary(
                method_descriptor=(
                    subtitler_pb2_grpc.DESCRIPTOR
                    .services_by_name['Subtitler']
                    .methods_by_name['FullWorkflow']
                ),
                invocation_metadata={},
                request=request,
                timeout=30
            )
            
            assert response.HasField('video_to_audio_response')
            assert response.video_to_audio_response.audio_path.endswith('.wav')
    
    def test_full_workflow_audio_to_text(self, grpc_server, sample_audio_file, mock_whisper):
        """测试音频转文字gRPC调用"""
        request = subtitler_pb2.FullWorkflowRequest(
            workflow_type='audio_to_text',
            file_path=sample_audio_file,
            request_word_timestamps=True
        )
        
        response = grpc_server.invoke_unary_unary(
            method_descriptor=(
                subtitler_pb2_grpc.DESCRIPTOR
                .services_by_name['Subtitler']
                .methods_by_name['FullWorkflow']
            ),
            invocation_metadata={},
            request=request,
            timeout=30
        )
        
        assert response.HasField('audio_to_text_response')
        assert response.audio_to_text_response.transcript == "This is a test transcript"
        assert len(response.audio_to_text_response.segments) == 2
    
    def test_full_workflow_text_to_translated_srt(self, grpc_server, mock_openai):
        """测试文本翻译转SRT gRPC调用"""
        request = subtitler_pb2.FullWorkflowRequest(
            workflow_type='text_to_translated_srt',
            req_text_content="Hello world. This is a test.",
            target_language="Chinese"
        )
        
        response = grpc_server.invoke_unary_unary(
            method_descriptor=(
                subtitler_pb2_grpc.DESCRIPTOR
                .services_by_name['Subtitler']
                .methods_by_name['FullWorkflow']
            ),
            invocation_metadata={},
            request=request,
            timeout=30
        )
        
        assert response.HasField('text_to_translated_srt_response')
        assert response.text_to_translated_srt_response.translated_text == "这是一个测试转录"
        assert "这是一个测试转录" in response.text_to_translated_srt_response.translated_srt_content
    
    def test_invalid_workflow_type(self, grpc_server):
        """测试无效工作流类型"""
        request = subtitler_pb2.FullWorkflowRequest(
            workflow_type='invalid_type',
            file_path='/nonexistent/file.mp4'
        )
        
        with pytest.raises(grpc.RpcError) as exc_info:
            grpc_server.invoke_unary_unary(
                method_descriptor=(
                    subtitler_pb2_grpc.DESCRIPTOR
                    .services_by_name['Subtitler']
                    .methods_by_name['FullWorkflow']
                ),
                invocation_metadata={},
                request=request,
                timeout=30
            )
        
        assert exc_info.value.code() == grpc.StatusCode.INVALID_ARGUMENT
```

## 🎭 **端到端测试详解**

### **Playwright配置**
```javascript
// playwright.config.js
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  projects: [
    {
      name: 'electron',
      use: {
        ...devices['Desktop Chrome'],
        // Electron特定配置
        launchOptions: {
          executablePath: require('electron'),
          args: ['.']
        }
      }
    }
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI
  }
});
```

### **Electron测试辅助函数**
```javascript
// tests/e2e/helpers/electron-helpers.js
import { _electron as electron } from 'playwright';
import path from 'path';

export class ElectronTestHelper {
  constructor() {
    this.app = null;
    this.window = null;
  }

  async launch() {
    // 启动Electron应用
    this.app = await electron.launch({
      args: [path.join(__dirname, '../../../main.js')],
      env: {
        ...process.env,
        NODE_ENV: 'test'
      }
    });

    // 获取主窗口
    this.window = await this.app.firstWindow();
    await this.window.waitForLoadState('domcontentloaded');

    return { app: this.app, window: this.window };
  }

  async close() {
    if (this.app) {
      await this.app.close();
    }
  }

  async uploadFile(filePath) {
    const fileInput = this.window.locator('input[type="file"]');
    await fileInput.setInputFiles(filePath);
  }

  async waitForStep(stepNumber) {
    await this.window.waitForSelector(`[data-step="${stepNumber}"].active`);
  }

  async clickProcessButton(stepName) {
    await this.window.click(`[data-testid="process-${stepName}"]`);
  }

  async waitForProcessComplete(stepName) {
    await this.window.waitForSelector(`[data-testid="${stepName}-complete"]`);
  }

  async getProgressUpdates() {
    return await this.window.locator('.progress-update').allTextContents();
  }
}
```

### **完整工作流E2E测试**
```javascript
// tests/e2e/specs/complete-workflow.spec.js
import { test, expect } from '@playwright/test';
import { ElectronTestHelper } from '../helpers/electron-helpers.js';
import path from 'path';

test.describe('Complete Workflow E2E', () => {
  let electronHelper;

  test.beforeEach(async () => {
    electronHelper = new ElectronTestHelper();
    await electronHelper.launch();
  });

  test.afterEach(async () => {
    await electronHelper.close();
  });

  test('should complete full video to translated SRT workflow', async () => {
    const { window } = electronHelper;

    // 导航到分布式工作流页面
    await window.click('[data-testid="nav-distributed-workflow"]');
    await expect(window.locator('h1')).toContainText('分布式工作流');

    // Step 1: 文件上传
    const sampleVideo = path.join(__dirname, '../fixtures/sample-video.mp4');
    await electronHelper.uploadFile(sampleVideo);
    await expect(window.locator('.uploaded-file-info')).toBeVisible();

    // Step 2: 视频转音频
    await electronHelper.clickProcessButton('video-audio');
    await electronHelper.waitForProcessComplete('video-audio');
    await expect(window.locator('.audio-file-path')).toBeVisible();

    // Step 3: 音频转文字
    await electronHelper.clickProcessButton('audio-text');
    await electronHelper.waitForProcessComplete('audio-text');
    await expect(window.locator('.transcript-content')).toBeVisible();

    // Step 4: 生成字幕
    await electronHelper.clickProcessButton('generate-subtitles');
    await electronHelper.waitForProcessComplete('generate-subtitles');
    await expect(window.locator('.subtitle-segments')).toBeVisible();

    // Step 5: 优化字幕
    await electronHelper.clickProcessButton('optimize-subtitles');
    await electronHelper.waitForProcessComplete('optimize-subtitles');

    // Step 6: 编辑字幕
    await electronHelper.waitForStep(6);
    const editableSegments = window.locator('.editable-segment');
    await expect(editableSegments.first()).toBeVisible();

    // 编辑第一个片段
    await editableSegments.first().locator('.segment-text').fill('Edited text');
    await window.click('[data-testid="save-segments"]');

    // Step 7: 翻译选择
    await window.click('[data-testid="next-step"]');
    await window.selectOption('[data-testid="target-language"]', 'zh-CN');
    await window.click('[data-testid="start-translation"]');

    // Step 8: 翻译处理
    await electronHelper.waitForProcessComplete('translation');
    await expect(window.locator('.translated-segments')).toBeVisible();

    // Step 9: 导出
    await window.click('[data-testid="next-step"]');
    await window.selectOption('[data-testid="export-format"]', 'srt');
    await window.selectOption('[data-testid="export-layout"]', '原文在上');
    await window.click('[data-testid="export-subtitles"]');

    // 验证导出成功
    await expect(window.locator('.export-success')).toBeVisible();
    await expect(window.locator('.export-file-path')).toContainText('.srt');
  });

  test('should handle errors gracefully', async () => {
    const { window } = electronHelper;

    // 上传无效文件
    const invalidFile = path.join(__dirname, '../fixtures/invalid-file.txt');
    await electronHelper.uploadFile(invalidFile);

    await electronHelper.clickProcessButton('video-audio');

    // 应该显示错误信息
    await expect(window.locator('.error-message')).toBeVisible();
    await expect(window.locator('.retry-button')).toBeVisible();
  });
});
```

### **批量处理E2E测试**
```javascript
// tests/e2e/specs/batch-processing.spec.js
import { test, expect } from '@playwright/test';
import { ElectronTestHelper } from '../helpers/electron-helpers.js';
import path from 'path';

test.describe('Batch Processing E2E', () => {
  let electronHelper;

  test.beforeEach(async () => {
    electronHelper = new ElectronTestHelper();
    await electronHelper.launch();
  });

  test.afterEach(async () => {
    await electronHelper.close();
  });

  test('should process multiple files in batch', async () => {
    const { window } = electronHelper;

    // 导航到一键操作页面
    await window.click('[data-testid="nav-one-click-operation"]');

    // 选择批量处理模式
    await window.click('[data-testid="batch-mode-toggle"]');

    // 选择包含多个文件的文件夹
    const testFolder = path.join(__dirname, '../fixtures/batch-test-files');
    await window.click('[data-testid="select-folder"]');
    // 模拟文件夹选择（需要特殊处理）

    // 配置批量处理选项
    await window.selectOption('[data-testid="workflow-type"]', 'vid_to_srt_trans');
    await window.selectOption('[data-testid="target-language"]', 'zh-CN');
    await window.selectOption('[data-testid="export-format"]', 'srt');

    // 开始批量处理
    await window.click('[data-testid="start-batch-processing"]');

    // 监控进度
    const progressUpdates = window.locator('.progress-update');
    await expect(progressUpdates.first()).toBeVisible();

    // 等待批量处理完成
    await window.waitForSelector('[data-testid="batch-complete"]', { timeout: 60000 });

    // 验证结果
    const completedFiles = window.locator('.completed-file');
    await expect(completedFiles).toHaveCount(3); // 假设有3个文件

    // 验证每个文件都有导出路径
    for (let i = 0; i < 3; i++) {
      await expect(completedFiles.nth(i).locator('.export-path')).toBeVisible();
    }
  });

  test('should handle partial failures in batch processing', async () => {
    const { window } = electronHelper;

    // 设置包含有效和无效文件的文件夹
    const mixedFolder = path.join(__dirname, '../fixtures/mixed-test-files');

    // 执行批量处理...

    // 验证部分成功的结果
    await expect(window.locator('.batch-summary')).toContainText('成功: 2, 失败: 1');
    await expect(window.locator('.failed-file')).toBeVisible();
  });
});
```

## 🚀 **性能测试详解**

### **性能测试配置**
```javascript
// tests/performance/artillery.yml
config:
  target: 'http://localhost:50051'
  phases:
    - duration: 60
      arrivalRate: 5
      name: "Warm up"
    - duration: 120
      arrivalRate: 10
      name: "Ramp up load"
    - duration: 300
      arrivalRate: 20
      name: "Sustained load"
  processor: "./performance-processor.js"

scenarios:
  - name: "gRPC Audio Processing"
    weight: 70
    engine: grpc
    flow:
      - call:
          service: "Subtitler"
          method: "FullWorkflow"
          data:
            workflow_type: "audio_to_text"
            file_path: "./fixtures/sample-audio.wav"
      - think: 2

  - name: "gRPC Translation"
    weight: 30
    engine: grpc
    flow:
      - call:
          service: "Subtitler"
          method: "FullWorkflow"
          data:
            workflow_type: "text_to_translated_srt"
            req_text_content: "Hello world. This is a test."
            target_language: "zh-CN"
      - think: 1
```

### **性能测试处理器**
```javascript
// tests/performance/performance-processor.js
const grpc = require('@grpc/grpc-js');
const protoLoader = require('@grpc/proto-loader');
const path = require('path');

// 加载proto定义
const PROTO_PATH = path.join(__dirname, '../../api-protos/v1/subtitler/subtitler.proto');
const packageDefinition = protoLoader.loadSync(PROTO_PATH);
const subtitlerProto = grpc.loadPackageDefinition(packageDefinition);

module.exports = {
  // 性能测试前的设置
  beforeScenario: (context, events, done) => {
    // 创建gRPC客户端
    context.vars.grpcClient = new subtitlerProto.monkeyfx.api.v1.subtitler.Subtitler(
      'localhost:50051',
      grpc.credentials.createInsecure()
    );
    done();
  },

  // 自定义指标收集
  collectMetrics: (context, events, done) => {
    const startTime = Date.now();

    context.vars.grpcClient.FullWorkflow(context.vars.data, (error, response) => {
      const endTime = Date.now();
      const duration = endTime - startTime;

      if (error) {
        events.emit('counter', 'grpc.errors', 1);
        events.emit('histogram', 'grpc.error_duration', duration);
      } else {
        events.emit('counter', 'grpc.success', 1);
        events.emit('histogram', 'grpc.success_duration', duration);

        // 根据响应类型收集特定指标
        if (response.audio_to_text_response) {
          events.emit('histogram', 'grpc.audio_processing_duration', duration);
          events.emit('counter', 'grpc.segments_processed', response.audio_to_text_response.segments.length);
        }

        if (response.text_to_translated_srt_response) {
          events.emit('histogram', 'grpc.translation_duration', duration);
          events.emit('counter', 'grpc.characters_translated', response.text_to_translated_srt_response.translated_text.length);
        }
      }

      done();
    });
  },

  // 内存使用监控
  monitorMemory: (context, events, done) => {
    const memUsage = process.memoryUsage();
    events.emit('histogram', 'system.memory.rss', memUsage.rss);
    events.emit('histogram', 'system.memory.heapUsed', memUsage.heapUsed);
    events.emit('histogram', 'system.memory.heapTotal', memUsage.heapTotal);
    done();
  }
};
```

### **前端性能测试**
```javascript
// tests/performance/frontend-performance.test.js
import { test, expect } from '@playwright/test';
import { ElectronTestHelper } from '../e2e/helpers/electron-helpers.js';

test.describe('Frontend Performance', () => {
  let electronHelper;

  test.beforeEach(async () => {
    electronHelper = new ElectronTestHelper();
    await electronHelper.launch();
  });

  test.afterEach(async () => {
    await electronHelper.close();
  });

  test('should load application within acceptable time', async () => {
    const { window } = electronHelper;

    const startTime = Date.now();
    await window.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;

    // 应用应该在3秒内加载完成
    expect(loadTime).toBeLessThan(3000);
  });

  test('should handle large file uploads efficiently', async () => {
    const { window } = electronHelper;

    // 上传大文件（模拟）
    const largeFile = path.join(__dirname, '../fixtures/large-video.mp4');

    const startTime = Date.now();
    await electronHelper.uploadFile(largeFile);

    // 等待文件信息显示
    await window.waitForSelector('.uploaded-file-info');
    const uploadTime = Date.now() - startTime;

    // 文件上传UI响应应该在1秒内
    expect(uploadTime).toBeLessThan(1000);
  });

  test('should maintain responsive UI during processing', async () => {
    const { window } = electronHelper;

    // 开始长时间处理任务
    await electronHelper.uploadFile('./fixtures/sample-video.mp4');
    await electronHelper.clickProcessButton('video-audio');

    // 在处理过程中测试UI响应性
    const startTime = Date.now();
    await window.click('[data-testid="cancel-processing"]');
    const responseTime = Date.now() - startTime;

    // UI应该保持响应（<500ms）
    expect(responseTime).toBeLessThan(500);
  });

  test('should efficiently render large subtitle lists', async () => {
    const { window } = electronHelper;

    // 模拟大量字幕片段
    await window.evaluate(() => {
      const store = window.__PINIA_STORE__;
      const segments = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        text: `Segment ${i}`,
        start_time: i * 1000,
        end_time: (i + 1) * 1000
      }));
      store.editableSegments = segments;
    });

    // 导航到编辑页面
    await window.click('[data-testid="nav-edit-subtitles"]');

    const startTime = Date.now();
    await window.waitForSelector('.subtitle-segment');
    const renderTime = Date.now() - startTime;

    // 大量数据渲染应该在2秒内完成
    expect(renderTime).toBeLessThan(2000);

    // 测试滚动性能
    const scrollStartTime = Date.now();
    await window.evaluate(() => {
      document.querySelector('.subtitle-list').scrollTop = 5000;
    });
    await window.waitForTimeout(100); // 等待滚动完成
    const scrollTime = Date.now() - scrollStartTime;

    expect(scrollTime).toBeLessThan(200);
  });
});
```

### **内存泄漏测试**
```javascript
// tests/performance/memory-leak.test.js
import { test, expect } from '@playwright/test';
import { ElectronTestHelper } from '../e2e/helpers/electron-helpers.js';

test.describe('Memory Leak Detection', () => {
  let electronHelper;

  test.beforeEach(async () => {
    electronHelper = new ElectronTestHelper();
    await electronHelper.launch();
  });

  test.afterEach(async () => {
    await electronHelper.close();
  });

  test('should not leak memory during repeated operations', async () => {
    const { window } = electronHelper;

    // 获取初始内存使用
    const initialMemory = await window.evaluate(() => {
      return performance.memory ? {
        usedJSHeapSize: performance.memory.usedJSHeapSize,
        totalJSHeapSize: performance.memory.totalJSHeapSize
      } : null;
    });

    if (!initialMemory) {
      test.skip('Performance memory API not available');
    }

    // 执行重复操作
    for (let i = 0; i < 50; i++) {
      await electronHelper.uploadFile('./fixtures/sample-video.mp4');
      await window.click('[data-testid="clear-file"]');

      // 每10次操作检查一次内存
      if (i % 10 === 0) {
        const currentMemory = await window.evaluate(() => ({
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize
        }));

        console.log(`Iteration ${i}: Memory used: ${currentMemory.usedJSHeapSize / 1024 / 1024}MB`);
      }
    }

    // 强制垃圾回收（如果可用）
    await window.evaluate(() => {
      if (window.gc) {
        window.gc();
      }
    });

    await window.waitForTimeout(1000); // 等待GC完成

    const finalMemory = await window.evaluate(() => ({
      usedJSHeapSize: performance.memory.usedJSHeapSize,
      totalJSHeapSize: performance.memory.totalJSHeapSize
    }));

    // 内存增长不应超过初始内存的200%
    const memoryGrowth = finalMemory.usedJSHeapSize / initialMemory.usedJSHeapSize;
    expect(memoryGrowth).toBeLessThan(2.0);
  });

  test('should clean up resources when navigating between views', async () => {
    const { window } = electronHelper;

    // 在不同视图间导航多次
    for (let i = 0; i < 20; i++) {
      await window.click('[data-testid="nav-distributed-workflow"]');
      await window.waitForSelector('[data-testid="file-upload"]');

      await window.click('[data-testid="nav-one-click-operation"]');
      await window.waitForSelector('[data-testid="workflow-config"]');

      await window.click('[data-testid="nav-grpc-test"]');
      await window.waitForSelector('[data-testid="grpc-interface"]');
    }

    // 检查是否有未清理的事件监听器
    const listenerCount = await window.evaluate(() => {
      return window.getEventListeners ?
        Object.keys(window.getEventListeners(window)).length : 0;
    });

    // 事件监听器数量应该保持在合理范围内
    expect(listenerCount).toBeLessThan(50);
  });
});
```

## 🔄 **CI/CD集成**

### **GitHub Actions配置**
```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  frontend-tests:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: electron-app/package-lock.json

    - name: Install dependencies
      run: |
        cd electron-app
        npm ci

    - name: Run unit tests
      run: |
        cd electron-app
        npm run test:unit -- --coverage

    - name: Run integration tests
      run: |
        cd electron-app
        npm run test:integration

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./electron-app/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  backend-tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        pip install -r requirements-test.txt

    - name: Run Python tests
      run: |
        cd backend
        python -m pytest --cov=. --cov-report=xml

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage.xml
        flags: backend-python
        name: backend-python-coverage

  go-backend-tests:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.19'

    - name: Run Go tests
      run: |
        cd go-backend
        go test -v -coverprofile=coverage.out ./...
        go tool cover -html=coverage.out -o coverage.html

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./go-backend/coverage.out
        flags: backend-go
        name: backend-go-coverage

  java-backend-tests:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Cache Maven packages
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Run Java tests
      run: |
        cd java-backend
        mvn clean test jacoco:report

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./java-backend/target/site/jacoco/jacoco.xml
        flags: backend-java
        name: backend-java-coverage

  e2e-tests:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: electron-app/package-lock.json

    - name: Install dependencies
      run: |
        cd electron-app
        npm ci
        npx playwright install

    - name: Start backend services
      run: |
        cd backend && python server.py &
        cd go-backend && go run server.go &
        cd java-backend && mvn spring-boot:run &
        sleep 10  # 等待服务启动

    - name: Run E2E tests
      run: |
        cd electron-app
        npm run test:e2e

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: playwright-report
        path: electron-app/playwright-report/
        retention-days: 30

  performance-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install Artillery
      run: npm install -g artillery

    - name: Start backend services
      run: |
        cd backend && python server.py &
        sleep 5

    - name: Run performance tests
      run: |
        cd tests/performance
        artillery run artillery.yml --output performance-report.json

    - name: Generate performance report
      run: |
        artillery report performance-report.json --output performance-report.html

    - name: Upload performance report
      uses: actions/upload-artifact@v3
      with:
        name: performance-report
        path: performance-report.html
```

### **测试覆盖率配置**
```javascript
// vitest.config.js - 覆盖率配置
export default defineConfig({
  test: {
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.config.js',
        '**/*.test.js',
        '**/dist/**',
        '**/build/**'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        },
        // 特定文件的覆盖率要求
        './src/vue/store/': {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90
        }
      }
    }
  }
});
```

### **质量门禁配置**
```yaml
# .github/workflows/quality-gate.yml
name: Quality Gate

on:
  pull_request:
    branches: [ main ]

jobs:
  quality-check:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整历史用于SonarQube分析

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install dependencies and run tests
      run: |
        cd electron-app
        npm ci
        npm run test:unit -- --coverage
        npm run lint

    - name: SonarQube Scan
      uses: sonarqube-quality-gate-action@master
      env:
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      with:
        scanMetadataReportFile: electron-app/coverage/sonar-report.xml

    - name: Check coverage thresholds
      run: |
        cd electron-app
        npm run coverage:check

    - name: Comment PR with coverage
      uses: romeovs/lcov-reporter-action@v0.3.1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        lcov-file: ./electron-app/coverage/lcov.info
```

## 🛠️ **测试工具和实用程序**

### **测试数据生成器**
```javascript
// tests/utils/test-data-generator.js
export class TestDataGenerator {
  static createMockFile(options = {}) {
    const {
      name = 'test-video.mp4',
      size = 1024 * 1024, // 1MB
      type = 'video/mp4',
      lastModified = Date.now()
    } = options;

    return new File([''], name, {
      type,
      size,
      lastModified
    });
  }

  static createMockSubtitleSegments(count = 10) {
    return Array.from({ length: count }, (_, i) => ({
      id: i + 1,
      text: `Subtitle segment ${i + 1}`,
      start_time_ms: i * 2000,
      end_time_ms: (i + 1) * 2000,
      translated_text: `翻译片段 ${i + 1}`
    }));
  }

  static createMockTranscriptResponse() {
    return {
      transcript: "This is a test transcript with multiple sentences.",
      segments: [
        {
          text: "This is a test transcript",
          start: 0.0,
          end: 2.5
        },
        {
          text: "with multiple sentences.",
          start: 2.5,
          end: 4.0
        }
      ]
    };
  }

  static createMockGrpcResponse(type, data = {}) {
    const responses = {
      video_to_audio: {
        video_to_audio_response: {
          audio_path: '/path/to/audio.wav',
          ...data
        }
      },
      audio_to_text: {
        audio_to_text_response: {
          transcript: "Test transcript",
          segments: this.createMockSubtitleSegments(5),
          ...data
        }
      },
      text_to_srt: {
        text_to_srt_response: {
          srt_content: "1\n00:00:00,000 --> 00:00:02,000\nTest subtitle\n\n",
          ...data
        }
      }
    };

    return { data: responses[type] };
  }
}
```

### **测试断言扩展**
```javascript
// tests/utils/custom-matchers.js
import { expect } from 'vitest';

// 自定义匹配器
expect.extend({
  toBeValidSrtFormat(received) {
    const srtPattern = /^\d+\n\d{2}:\d{2}:\d{2},\d{3} --> \d{2}:\d{2}:\d{2},\d{3}\n.+\n\n/;
    const pass = srtPattern.test(received);

    if (pass) {
      return {
        message: () => `expected ${received} not to be valid SRT format`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be valid SRT format`,
        pass: false
      };
    }
  },

  toHaveValidTimeRange(received) {
    const { start_time_ms, end_time_ms } = received;
    const pass = start_time_ms >= 0 && end_time_ms > start_time_ms;

    if (pass) {
      return {
        message: () => `expected segment not to have valid time range`,
        pass: true
      };
    } else {
      return {
        message: () => `expected segment to have valid time range (start: ${start_time_ms}, end: ${end_time_ms})`,
        pass: false
      };
    }
  },

  toBeWithinRange(received, min, max) {
    const pass = received >= min && received <= max;

    if (pass) {
      return {
        message: () => `expected ${received} not to be within range ${min}-${max}`,
        pass: true
      };
    } else {
      return {
        message: () => `expected ${received} to be within range ${min}-${max}`,
        pass: false
      };
    }
  }
});
```

### **Mock服务器**
```javascript
// tests/utils/mock-grpc-server.js
import grpc from '@grpc/grpc-js';
import { TestDataGenerator } from './test-data-generator.js';

export class MockGrpcServer {
  constructor() {
    this.server = new grpc.Server();
    this.responses = new Map();
  }

  // 设置模拟响应
  setMockResponse(method, response) {
    this.responses.set(method, response);
  }

  // 启动模拟服务器
  start(port = 50051) {
    return new Promise((resolve, reject) => {
      this.server.addService(SubtitlerService, {
        FullWorkflow: (call, callback) => {
          const request = call.request;
          const mockResponse = this.responses.get(request.workflow_type);

          if (mockResponse) {
            callback(null, mockResponse);
          } else {
            // 默认响应
            const defaultResponse = TestDataGenerator.createMockGrpcResponse(
              request.workflow_type
            );
            callback(null, defaultResponse.data);
          }
        }
      });

      this.server.bindAsync(
        `localhost:${port}`,
        grpc.ServerCredentials.createInsecure(),
        (err, boundPort) => {
          if (err) {
            reject(err);
          } else {
            this.server.start();
            resolve(boundPort);
          }
        }
      );
    });
  }

  // 停止模拟服务器
  stop() {
    return new Promise((resolve) => {
      this.server.tryShutdown(resolve);
    });
  }
}
```

## 📊 **测试报告和监控**

### **测试报告生成**
```javascript
// tests/utils/test-reporter.js
export class TestReporter {
  constructor() {
    this.results = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      duration: 0,
      coverage: null,
      details: []
    };
  }

  addResult(testResult) {
    this.results.total++;
    this.results.duration += testResult.duration;

    switch (testResult.status) {
      case 'passed':
        this.results.passed++;
        break;
      case 'failed':
        this.results.failed++;
        break;
      case 'skipped':
        this.results.skipped++;
        break;
    }

    this.results.details.push(testResult);
  }

  setCoverage(coverage) {
    this.results.coverage = coverage;
  }

  generateReport() {
    const report = {
      summary: {
        total: this.results.total,
        passed: this.results.passed,
        failed: this.results.failed,
        skipped: this.results.skipped,
        success_rate: (this.results.passed / this.results.total * 100).toFixed(2),
        duration: this.results.duration
      },
      coverage: this.results.coverage,
      failed_tests: this.results.details.filter(t => t.status === 'failed'),
      timestamp: new Date().toISOString()
    };

    return report;
  }

  exportToJson(filename) {
    const report = this.generateReport();
    const fs = require('fs');
    fs.writeFileSync(filename, JSON.stringify(report, null, 2));
  }

  exportToHtml(filename) {
    const report = this.generateReport();
    const html = this.generateHtmlReport(report);
    const fs = require('fs');
    fs.writeFileSync(filename, html);
  }

  generateHtmlReport(report) {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .summary { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .passed { color: green; }
        .failed { color: red; }
        .coverage { margin: 20px 0; }
        .test-details { margin-top: 20px; }
        table { width: 100%; border-collapse: collapse; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Test Report</h1>

    <div class="summary">
        <h2>Summary</h2>
        <p>Total Tests: ${report.summary.total}</p>
        <p class="passed">Passed: ${report.summary.passed}</p>
        <p class="failed">Failed: ${report.summary.failed}</p>
        <p>Skipped: ${report.summary.skipped}</p>
        <p>Success Rate: ${report.summary.success_rate}%</p>
        <p>Duration: ${report.summary.duration}ms</p>
    </div>

    ${report.coverage ? `
    <div class="coverage">
        <h2>Coverage</h2>
        <p>Lines: ${report.coverage.lines}%</p>
        <p>Functions: ${report.coverage.functions}%</p>
        <p>Branches: ${report.coverage.branches}%</p>
        <p>Statements: ${report.coverage.statements}%</p>
    </div>
    ` : ''}

    ${report.failed_tests.length > 0 ? `
    <div class="test-details">
        <h2>Failed Tests</h2>
        <table>
            <tr>
                <th>Test Name</th>
                <th>Error Message</th>
                <th>Duration</th>
            </tr>
            ${report.failed_tests.map(test => `
            <tr>
                <td>${test.name}</td>
                <td>${test.error}</td>
                <td>${test.duration}ms</td>
            </tr>
            `).join('')}
        </table>
    </div>
    ` : ''}

    <p><small>Generated at: ${report.timestamp}</small></p>
</body>
</html>
    `;
  }
}
```

### **性能监控**
```javascript
// tests/utils/performance-monitor.js
export class PerformanceMonitor {
  constructor() {
    this.metrics = [];
    this.thresholds = {
      response_time: 1000, // 1秒
      memory_usage: 100 * 1024 * 1024, // 100MB
      cpu_usage: 80 // 80%
    };
  }

  startMonitoring() {
    this.startTime = Date.now();
    this.initialMemory = process.memoryUsage();

    // 定期收集性能指标
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, 1000);
  }

  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.endTime = Date.now();
    return this.generateReport();
  }

  collectMetrics() {
    const currentMemory = process.memoryUsage();
    const timestamp = Date.now();

    this.metrics.push({
      timestamp,
      memory: {
        rss: currentMemory.rss,
        heapUsed: currentMemory.heapUsed,
        heapTotal: currentMemory.heapTotal,
        external: currentMemory.external
      },
      uptime: process.uptime()
    });
  }

  generateReport() {
    const duration = this.endTime - this.startTime;
    const memoryPeak = Math.max(...this.metrics.map(m => m.memory.heapUsed));
    const memoryAverage = this.metrics.reduce((sum, m) => sum + m.memory.heapUsed, 0) / this.metrics.length;

    return {
      duration,
      memory: {
        initial: this.initialMemory.heapUsed,
        peak: memoryPeak,
        average: memoryAverage,
        final: this.metrics[this.metrics.length - 1]?.memory.heapUsed || 0
      },
      violations: this.checkThresholds(),
      raw_metrics: this.metrics
    };
  }

  checkThresholds() {
    const violations = [];

    // 检查内存使用
    const memoryPeak = Math.max(...this.metrics.map(m => m.memory.heapUsed));
    if (memoryPeak > this.thresholds.memory_usage) {
      violations.push({
        type: 'memory_usage',
        value: memoryPeak,
        threshold: this.thresholds.memory_usage,
        message: `Memory usage exceeded threshold: ${memoryPeak} > ${this.thresholds.memory_usage}`
      });
    }

    return violations;
  }
}
```

## 🎯 **测试最佳实践**

### **测试组织原则**
```javascript
// 1. 测试文件命名规范
// ✅ 好的命名
SubtitleEditor.test.js
subtitlerStore.test.js
complete-workflow.spec.js

// ❌ 避免的命名
test1.js
mytest.js
subtitle_test.js

// 2. 测试结构组织
describe('SubtitleEditor Component', () => {
  describe('File Upload', () => {
    it('should accept valid video files', () => {});
    it('should reject invalid file types', () => {});
    it('should show file size information', () => {});
  });

  describe('Subtitle Editing', () => {
    it('should allow text editing', () => {});
    it('should validate time ranges', () => {});
    it('should save changes', () => {});
  });
});

// 3. 测试数据隔离
beforeEach(() => {
  // 每个测试前重置状态
  store.resetWorkflow();
  mockApi.reset();
});
```

### **断言最佳实践**
```javascript
// ✅ 具体的断言
expect(response.status).toBe(200);
expect(response.data.segments).toHaveLength(5);
expect(response.data.transcript).toContain('Hello world');

// ❌ 模糊的断言
expect(response).toBeTruthy();
expect(response.data).toBeDefined();

// ✅ 有意义的错误消息
expect(segments, 'Subtitle segments should be generated').toHaveLength(5);
expect(audioPath, 'Audio file should be created').toMatch(/\.wav$/);

// ✅ 异步操作测试
await expect(store.processVideoToAudio()).resolves.toMatchObject({
  audio_path: expect.stringMatching(/\.wav$/)
});

// ✅ 错误处理测试
await expect(store.processVideoToAudio()).rejects.toThrow('Invalid file format');
```

### **Mock使用指南**
```javascript
// ✅ 适当的Mock使用
// Mock外部依赖
vi.mock('electron', () => ({
  ipcRenderer: {
    invoke: vi.fn(),
    on: vi.fn()
  }
}));

// Mock复杂的计算
vi.mock('@/utils/subtitle-processor', () => ({
  generateTimestamps: vi.fn(() => [
    { start: 0, end: 2000 },
    { start: 2000, end: 4000 }
  ])
}));

// ✅ 验证Mock调用
expect(mockElectronAPI.invoke).toHaveBeenCalledWith(
  'subtitler-full-workflow',
  expect.objectContaining({
    workflow_type: 'vid_to_audio',
    file_path: expect.any(String)
  })
);

// ❌ 过度Mock
// 不要Mock被测试的代码本身
// 不要Mock简单的数据结构
```

### **性能测试指南**
```javascript
// ✅ 性能基准测试
test('should process large subtitle file efficiently', async () => {
  const largeSegments = Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    text: `Segment ${i}`,
    start_time: i * 1000,
    end_time: (i + 1) * 1000
  }));

  const startTime = performance.now();
  await store.setEditableSegments(largeSegments);
  const endTime = performance.now();

  expect(endTime - startTime).toBeLessThan(100); // 应该在100ms内完成
});

// ✅ 内存使用测试
test('should not leak memory during repeated operations', async () => {
  const initialMemory = process.memoryUsage().heapUsed;

  for (let i = 0; i < 100; i++) {
    await store.processVideoToAudio();
    store.resetWorkflow();
  }

  // 强制垃圾回收
  if (global.gc) global.gc();

  const finalMemory = process.memoryUsage().heapUsed;
  const memoryGrowth = finalMemory - initialMemory;

  expect(memoryGrowth).toBeLessThan(10 * 1024 * 1024); // 不应超过10MB
});
```

### **测试维护策略**
```javascript
// 1. 定期清理过时的测试
// 2. 保持测试与代码同步更新
// 3. 重构重复的测试代码

// ✅ 提取公共测试逻辑
const setupWorkflowTest = async (workflowType = 'vid_to_audio') => {
  const mockFile = TestDataGenerator.createMockFile();
  store.setUploadedFile(mockFile);
  store.setOneClickWorkflowType(workflowType);

  return { mockFile, store };
};

// 在多个测试中使用
test('should handle video to audio workflow', async () => {
  const { store } = await setupWorkflowTest('vid_to_audio');
  // 测试逻辑...
});

test('should handle complete translation workflow', async () => {
  const { store } = await setupWorkflowTest('vid_to_srt_trans');
  // 测试逻辑...
});
```

---

## 📚 **相关文档**
- [整体架构说明](./ARCHITECTURE_OVERVIEW.md)
- [前端开发指南](./FRONTEND_DEVELOPMENT_GUIDE.md)
- [后端开发指南](./BACKEND_DEVELOPMENT_GUIDE.md)

---

*测试开发文档版本: v2.0*
*最后更新: 2024年12月*
*文档状态: 生产就绪*

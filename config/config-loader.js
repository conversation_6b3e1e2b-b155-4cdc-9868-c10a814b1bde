/**
 * 配置加载模块
 * 提供统一的配置访问API
 */
const fs = require('fs');
const path = require('path');

class ConfigLoader {
  constructor(configPath = '../config/config.json') {
    this.configPath = path.resolve(__dirname, configPath);
    this.config = this.loadConfig();
    this.environment = process.env.NODE_ENV || 'development';
  }

  loadConfig() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      console.error(`Error loading config: ${error.message}`);
      process.exit(1);
    }
  }

  /**
   * 按路径获取配置值
   * @param {string} path - 配置路径，格式如 'services.python.port'
   * @param {any} defaultValue - 默认值，如果配置不存在
   * @returns {any} 配置值或默认值
   */
  get(path, defaultValue = null) {
    return this.getNestedProperty(this.config, path, defaultValue);
  }

  /**
   * 获取服务配置
   * @param {string} serviceName - 服务名称：'python', 'go', 'java'
   * @returns {object} 服务配置对象
   */
  getServiceConfig(serviceName) {
    return this.get(`services.${serviceName}`);
  }

  /**
   * 获取服务端口
   * @param {string} serviceName - 服务名称
   * @returns {number} 端口号
   */
  getServicePort(serviceName) {
    return this.get(`services.${serviceName}.port`);
  }

  /**
   * 获取服务可执行文件路径
   * 根据当前环境自动选择开发或生产路径
   * @param {string} serviceName - 服务名称
   * @returns {string} 可执行文件路径
   */
  getServiceExecutable(serviceName) {
    const isProduction = this.environment === 'production';
    const envKey = isProduction ? 'prod' : 'dev';
    return this.get(`services.${serviceName}.executable.${envKey}`);
  }

  /**
   * 获取Proto文件配置
   * @param {string} protoName - Proto文件名称
   * @returns {object} Proto配置对象
   */
  getProtoConfig(protoName) {
    return this.get(`protoFiles.${protoName}`);
  }

  /**
   * 辅助方法：按路径获取嵌套属性
   * @private
   */
  getNestedProperty(obj, path, defaultValue = null) {
    const keys = Array.isArray(path) ? path : path.split('.');
    let current = obj;

    for (const key of keys) {
      if (current === null || current === undefined || typeof current !== 'object') {
        return defaultValue;
      }
      current = current[key];
    }

    return current !== undefined ? current : defaultValue;
  }
}

// 导出单例实例
module.exports = new ConfigLoader();
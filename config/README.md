# 配置系统文档

本文档描述了项目的统一配置管理系统，包括结构、使用方法和最佳实践。

## 目录结构

```
config/
├── config.json            # 主配置文件
├── config-loader.js       # 配置加载模块
├── version.json           # 版本信息文件
└── README.md              # 本文档
```

## 配置系统设计

配置系统设计遵循以下原则：

1. **单一来源**：所有配置集中在一处管理
2. **环境感知**：支持开发/生产环境差异
3. **类型安全**：提供类型定义以便IDE支持
4. **分层结构**：按服务/功能组织配置
5. **版本关联**：配置与应用版本紧密关联

## 主配置文件结构

`config.json`是整个项目的配置中心：

```json
{
  "version": "1.0.0",
  "services": {
    "python": {
      "name": "Python gRPC Service",
      "port": 50051,
      "executable": {
        "dev": "../backend/dist/server.pex",
        "prod": "backend/server.pex"
      }
    },
    "go": {
      "name": "Go gRPC Service",
      "port": 50052,
      "executable": {
        "dev": "../go-backend/dist/go_grpc_server",
        "prod": "go-backend/go_grpc_server"
      }
    },
    "java": {
      "name": "Java gRPC Service",
      "port": 50053,
      "executable": {
        "dev": "../java-backend/target/java-grpc-backend",
        "prod": "java-backend/java-grpc-backend"
      }
    }
  },
  "protoFiles": {
    "greeter": {
      "version": "v1",
      "path": "../proto/v1/greeter.proto",
      "package": "helloworld.v1"
    }
  },
  "logging": {
    "level": "info",
    "format": "json",
    "output": {
      "console": true,
      "file": "logs/app.log"
    }
  },
  "ui": {
    "theme": "light",
    "autoStart": true,
    "showLogs": true
  }
}
```

## 版本文件结构

`version.json`管理项目中所有组件的版本信息：

```json
{
  "appVersion": "1.0.0",
  "apiVersion": "v1",
  "components": {
    "electron": "1.0.0",
    "pythonBackend": "1.0.0",
    "goBackend": "1.0.0",
    "javaBackend": "1.0.0"
  },
  "compatibilityMatrix": {
    "v1": ["1.0.0"]
  },
  "updated": "2025-05-14T13:00:00Z"
}
```

## 配置加载模块

`config-loader.js`提供统一的配置访问API：

```javascript
const fs = require('fs');
const path = require('path');

class ConfigLoader {
  constructor(configPath = '../config/config.json') {
    this.configPath = path.resolve(__dirname, configPath);
    this.config = this.loadConfig();
    this.environment = process.env.NODE_ENV || 'development';
  }

  loadConfig() {
    try {
      const configData = fs.readFileSync(this.configPath, 'utf8');
      return JSON.parse(configData);
    } catch (error) {
      console.error(`Error loading config: ${error.message}`);
      process.exit(1);
    }
  }

  get(path, defaultValue = null) {
    return this.getNestedProperty(this.config, path, defaultValue);
  }

  getServiceConfig(serviceName) {
    return this.get(`services.${serviceName}`);
  }

  getServicePort(serviceName) {
    return this.get(`services.${serviceName}.port`);
  }

  getServiceExecutable(serviceName) {
    const isProduction = this.environment === 'production';
    const envKey = isProduction ? 'prod' : 'dev';
    return this.get(`services.${serviceName}.executable.${envKey}`);
  }

  getProtoConfig(protoName) {
    return this.get(`protoFiles.${protoName}`);
  }

  // 辅助方法：按路径获取嵌套属性
  getNestedProperty(obj, path, defaultValue = null) {
    const keys = Array.isArray(path) ? path : path.split('.');
    let current = obj;

    for (const key of keys) {
      if (current === null || current === undefined || typeof current !== 'object') {
        return defaultValue;
      }
      current = current[key];
    }

    return current !== undefined ? current : defaultValue;
  }
}

module.exports = new ConfigLoader();
```

## 使用方法

### JavaScript/Node.js中使用

```javascript
const config = require('./config/config-loader');

// 获取服务配置
const pythonConfig = config.getServiceConfig('python');
console.log(`Python服务端口: ${pythonConfig.port}`);

// 获取服务可执行文件路径 (会根据环境自动选择dev或prod路径)
const goExecutable = config.getServiceExecutable('go');
console.log(`Go可执行文件路径: ${goExecutable}`);

// 获取特定配置值
const logLevel = config.get('logging.level');
```

### 在构建脚本中使用

```javascript
const config = require('./config/config-loader');

// 获取Proto文件配置
const greeterProto = config.getProtoConfig('greeter');
const protoPath = greeterProto.path;
const protoVersion = greeterProto.version;

// 使用配置执行构建
const command = `protoc --proto_path=${path.dirname(protoPath)} ...`;
```

### 类型支持

为提供更好的IDE支持，项目包含TypeScript类型定义文件`config.d.ts`：

```typescript
interface ServiceConfig {
  name: string;
  port: number;
  executable: {
    dev: string;
    prod: string;
  };
}

interface ProtoConfig {
  version: string;
  path: string;
  package: string;
}

interface LoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error';
  format: 'json' | 'text';
  output: {
    console: boolean;
    file: string | null;
  };
}

interface UIConfig {
  theme: 'light' | 'dark';
  autoStart: boolean;
  showLogs: boolean;
}

interface AppConfig {
  version: string;
  services: {
    python: ServiceConfig;
    go: ServiceConfig;
    java: ServiceConfig;
  };
  protoFiles: {
    greeter: ProtoConfig;
  };
  logging: LoggingConfig;
  ui: UIConfig;
}
```

## 配置变更管理

修改配置时，应遵循以下流程：

1. **修改配置模板**：更新`config/config.template.json`
2. **更新文档**：确保本文档反映最新的配置结构
3. **添加迁移脚本**：如果是破坏性变更，提供迁移脚本
4. **更新类型定义**：更新`config.d.ts`保持类型定义同步
5. **通知团队**：通过文档或注释说明配置变更

## 环境变量覆盖

配置系统支持通过环境变量覆盖配置值：

1. **命名约定**：使用大写加下划线，例如`PYTHON_SERVICE_PORT`
2. **优先级**：环境变量 > 配置文件 > 默认值
3. **映射关系**：
   - `PYTHON_SERVICE_PORT` → `services.python.port`
   - `LOG_LEVEL` → `logging.level`

## 最佳实践

1. **避免硬编码**：始终从配置系统获取值，而非硬编码
2. **保持向后兼容**：添加新配置时提供默认值
3. **文档同步**：配置变更时更新相关文档
4. **验证配置**：在应用启动时验证关键配置的有效性
5. **避免敏感信息**：不要在配置文件中存储密码等敏感信息，使用环境变量代替
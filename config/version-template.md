# 版本信息文件模板

以下是`version.json`文件的模板，用于统一管理项目中所有组件的版本信息。实际实现时，应创建此文件并根据需要更新版本号。

```json
{
  "appVersion": "1.0.0",
  "apiVersion": "v1",
  "components": {
    "electron": "1.0.0",
    "pythonBackend": "1.0.0",
    "goBackend": "1.0.0",
    "javaBackend": "1.0.0"
  },
  "compatibilityMatrix": {
    "v1": ["1.0.0"]
  },
  "updated": "2025-05-14T13:00:00Z"
}
```

## 字段说明

- **appVersion**: 整个应用的主版本号，遵循语义化版本规范（[SemVer](https://semver.org/)）
- **apiVersion**: 当前使用的API版本，对应proto目录中的版本
- **components**: 各个组件的版本号
  - **electron**: Electron前端应用版本
  - **pythonBackend**: Python后端服务版本
  - **goBackend**: Go后端服务版本
  - **javaBackend**: Java后端服务版本
- **compatibilityMatrix**: API版本与应用版本的兼容性矩阵，表示哪些应用版本支持哪些API版本
- **updated**: 最后更新时间，ISO-8601格式

## 版本号更新规则

版本号遵循语义化版本规范 (SemVer) 的 MAJOR.MINOR.PATCH 格式：

1. **主版本号 (MAJOR)**: 不兼容的API变更时增加
2. **次版本号 (MINOR)**: 新增向后兼容的功能时增加
3. **修订号 (PATCH)**: 向后兼容的问题修复时增加

## 版本同步机制

当需要更新版本时，应使用`scripts/update-versions.js`脚本来确保所有组件版本一致：

```bash
# 更新主版本号
node scripts/update-versions.js --major

# 更新次版本号
node scripts/update-versions.js --minor

# 更新修订号
node scripts/update-versions.js --patch
```

脚本会自动更新：
1. version.json文件中的版本号
2. package.json中的version字段
3. pom.xml中的版本号
4. 各个组件中的版本常量
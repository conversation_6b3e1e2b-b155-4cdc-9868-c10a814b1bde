{"version": "1.0.0", "services": {"python": {"name": "Python gRPC Service", "port": 50051, "executable": {"dev": "../backend/dist/server.pex", "prod": "backend/server.pex"}}, "go": {"name": "Go gRPC Service", "port": 50052, "executable": {"dev": "../go-backend/dist/go_grpc_server", "prod": "go-backend/go_grpc_server"}}, "java": {"name": "Java gRPC Service", "port": 50053, "executable": {"dev": "../java-backend/target/java-grpc-backend", "prod": "java-backend/java-grpc-backend"}}}, "protoFiles": {"greeter": {"version": "v1", "path": "../proto/v1/greeter.proto", "package": "helloworld.v1"}}, "logging": {"level": "info", "format": "json", "output": {"console": true, "file": "logs/app.log"}}, "ui": {"theme": "light", "autoStart": true, "showLogs": true}, "build": {"incrementalBuild": true, "timestampFiles": {"python": ".build-timestamp-python", "go": ".build-timestamp-go", "java": ".build-timestamp-java", "ui": ".build-timestamp-ui"}, "errorHandling": {"stopOnError": true, "logFile": "logs/build.log"}}}
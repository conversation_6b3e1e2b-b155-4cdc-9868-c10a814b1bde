# AI 配置测试功能使用指南

## 🎯 功能概述

新增的 AI 配置测试功能允许用户在保存配置前验证 API 连接是否正常工作，确保配置的准确性和可用性。

## ✨ 主要特性

### 1. **实时连接测试**
- 支持 OpenAI、DeepSeek、Gemini 等主流 AI 服务
- 真实 API 调用验证，不仅仅是格式检查
- 详细的错误信息和成功反馈

### 2. **智能验证**
- API Key 格式验证
- 必填字段检查
- 网络连接状态检测

### 3. **用户友好的界面**
- 直观的测试按钮和状态显示
- 成功/失败的视觉反馈
- 详细信息展开查看

## 🚀 使用方法

### 步骤 1: 配置 AI 服务
1. 打开 AI 设置页面
2. 点击"添加服务"或选择现有服务进行编辑
3. 填写必要的配置信息：
   - 服务类型（OpenAI/DeepSeek/Gemini）
   - 显示名称
   - API Key
   - 其他属性（如模型名称、Base URL 等）

### 步骤 2: 测试连接
1. 确保所有必填字段已填写
2. 在配置表单底部找到"测试连接"部分
3. 点击"测试连接"按钮
4. 等待测试结果（通常 1-5 秒）

### 步骤 3: 查看结果
- **成功**: 绿色提示框显示连接成功信息
- **失败**: 红色提示框显示错误原因
- **详细信息**: 点击"查看详细信息"可展开技术细节

## 📋 支持的服务类型

### OpenAI
- **API Key 格式**: `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
- **默认 Base URL**: `https://api.openai.com/v1`
- **测试模型**: `gpt-3.5-turbo` 或用户指定的默认模型

### DeepSeek
- **API Key 格式**: `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
- **默认 Base URL**: `https://api.deepseek.com`
- **测试模型**: `deepseek-chat` 或用户指定的默认模型

### Gemini
- **API Key 格式**: `AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
- **Base URL**: `https://generativelanguage.googleapis.com/v1beta`
- **测试模型**: `gemini-1.5-pro-latest` 或用户指定的默认模型

## 🔧 测试机制

### 实际 API 调用
测试功能会发送真实的 API 请求到相应的服务端点：

```javascript
// OpenAI/DeepSeek 测试请求
{
  "model": "gpt-3.5-turbo",
  "messages": [{"role": "user", "content": "Hello, this is a connection test."}],
  "max_tokens": 10,
  "temperature": 0.1
}

// Gemini 测试请求
{
  "contents": [{"parts": [{"text": "Hello, this is a connection test."}]}],
  "generationConfig": {"maxOutputTokens": 10}
}
```

### 验证层级
1. **格式验证**: 检查 API Key 格式是否符合服务商规范
2. **网络连接**: 验证能否连接到 API 端点
3. **认证验证**: 确认 API Key 是否有效
4. **响应解析**: 检查 API 返回的响应格式

## ⚠️ 注意事项

### 安全性
- API Key 在传输过程中会被加密处理
- 测试请求使用最小的 token 数量以减少费用
- 不会存储或记录 API 响应内容

### 费用考虑
- 每次测试会消耗少量 API 调用额度（通常 < 10 tokens）
- 建议在确认配置无误后再进行测试
- 避免频繁重复测试

### 网络要求
- 需要稳定的互联网连接
- 某些网络环境可能需要代理配置
- 超时时间设置为 30 秒

## 🐛 常见问题

### Q: 测试一直显示"测试中..."
**A**: 检查网络连接，或等待超时后重试。某些网络环境可能需要更长时间。

### Q: API Key 格式正确但测试失败
**A**: 
1. 确认 API Key 是否有效且未过期
2. 检查账户是否有足够的余额
3. 验证 Base URL 是否正确

### Q: Gemini API 测试失败
**A**: 
1. 确认 API Key 以 "AIza" 开头
2. 检查是否已启用 Gemini API 服务
3. 验证模型名称是否正确

### Q: 测试成功但实际使用时失败
**A**: 
1. 检查实际使用的模型是否与测试模型一致
2. 确认生产环境的网络配置
3. 验证 API 限制和配额设置

## 📞 技术支持

如果遇到问题，请查看：
1. 浏览器开发者工具的控制台日志
2. Electron 主进程的日志输出
3. 网络请求的详细错误信息

测试功能会在控制台输出详细的调试信息，有助于问题诊断。

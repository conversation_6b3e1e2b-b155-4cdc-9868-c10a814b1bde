# 🏗️ 系统架构总览

## 📋 **项目概述**

### **项目名称**
Electron-Python-gRPC-PEX 多语言字幕处理系统

### **核心功能**
- **音视频转文字**: 支持多种格式的音视频文件转录
- **字幕生成**: 智能分句和时间轴生成
- **多语言翻译**: 支持多种目标语言翻译
- **字幕编辑**: 可视化字幕编辑和优化
- **多格式导出**: 支持SRT、VTT、JSON等格式导出
- **批量处理**: 支持文件夹批量处理功能

## 🏗️ **系统架构**

### **整体架构图**
```
┌─────────────────────────────────────────────────────────────┐
│                    Electron 主进程                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   窗口管理器     │  │   后端管理器     │  │  gRPC管理器   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    Vue.js 前端界面                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   分布式工作流   │  │   一键操作界面   │  │  gRPC测试界面 │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼ gRPC 通信
┌─────────────────────────────────────────────────────────────┐
│                    多语言后端服务                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  Python 后端     │  │    Go 后端      │  │  Java 后端    │ │
│  │  (主要服务)      │  │   (扩展服务)     │  │  (扩展服务)   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### **技术栈**

#### **前端技术栈**
- **Electron**: 跨平台桌面应用框架
- **Vue.js 3**: 响应式前端框架
- **Pinia**: 状态管理
- **Tailwind CSS**: 样式框架
- **Vite**: 构建工具

#### **后端技术栈**
- **Python**: 主要后端服务 (端口50051)
  - gRPC服务框架
  - 音视频处理 (FFmpeg)
  - 语音识别 (Whisper)
  - 翻译服务集成
- **Go**: 扩展后端服务 (端口50052)
  - 高性能gRPC服务
- **Java**: 扩展后端服务 (端口50053)
  - Spring Boot + gRPC

#### **通信协议**
- **gRPC**: 前后端通信协议
- **Protocol Buffers**: 数据序列化格式
- **IPC**: Electron主进程与渲染进程通信

## 🔄 **数据流架构**

### **核心工作流程**
```
文件上传 → 视频转音频 → 音频转文字 → 生成字幕 → 优化字幕 → 编辑字幕 → 翻译选择 → 翻译处理 → 导出字幕
```

### **详细数据流**
```
1. 文件处理流
   用户选择文件 → Electron文件系统 → 后端处理 → 返回结果

2. gRPC通信流
   前端请求 → Electron主进程 → gRPC客户端 → 后端服务 → 响应返回

3. 状态管理流
   用户操作 → Vue组件 → Pinia Store → 状态更新 → 界面响应

4. 批量处理流
   选择文件夹 → 检测文件 → 逐个处理 → 状态反馈 → 批量导出
```

## 📁 **目录结构**

### **项目根目录**
```
electron-python-grpc-pex-demo/
├── electron-app/                 # Electron前端应用
│   ├── src/
│   │   ├── main-process/         # 主进程代码
│   │   ├── vue/                  # Vue.js前端代码
│   │   └── js/                   # 工具函数
│   ├── main.js                   # Electron入口
│   ├── package.json              # 前端依赖
│   └── vite.config.js            # 构建配置
├── backend/                      # Python后端
│   ├── server.py                 # gRPC服务器
│   ├── subtitle_service.py       # 字幕处理服务
│   └── requirements.txt          # Python依赖
├── go-backend/                   # Go后端
│   ├── server.go                 # Go gRPC服务器
│   └── go.mod                    # Go模块配置
├── java-backend/                 # Java后端
│   ├── src/                      # Java源码
│   └── pom.xml                   # Maven配置
├── api-protos/                   # Protocol Buffers定义
│   └── v1/                       # API版本1
├── dist/                         # 编译后的可执行文件
│   ├── server.pex                # Python可执行文件
│   ├── go_grpc_server            # Go可执行文件
│   └── java-grpc-backend         # Java可执行文件
└── scripts/                      # 构建脚本
```

## 🔧 **核心模块**

### **Electron主进程模块**
- **app-lifecycle.js**: 应用生命周期管理
- **backend-manager.js**: 后端服务管理
- **grpc-manager.js**: gRPC客户端管理
- **window-manager.js**: 窗口管理
- **port-manager.js**: 端口管理

### **Vue前端模块**
- **分布式工作流**: 9步骤字幕处理流程
- **一键操作**: 简化的批量处理界面
- **gRPC测试**: 服务状态监控和API测试
- **结果查看器**: 统一的结果展示组件

### **后端服务模块**
- **字幕处理服务**: 核心业务逻辑
- **文件处理服务**: 音视频文件处理
- **翻译服务**: 多语言翻译支持
- **导出服务**: 多格式字幕导出

## 🚀 **部署架构**

### **开发环境**
```
npm run dev (前端) + python server.py (后端) + go run server.go + mvn spring-boot:run
```

### **生产环境**
```
npm run build → electron-builder → 打包成桌面应用
后端服务编译为可执行文件，随应用一起分发
```

### **服务端口分配**
- **Python后端**: 50051 (主要服务)
- **Go后端**: 50052 (扩展服务)
- **Java后端**: 50053 (扩展服务)
- **前端开发服务器**: 5173 (开发时)

## 🔐 **安全架构**

### **进程隔离**
- Electron主进程与渲染进程隔离
- 后端服务独立进程运行
- gRPC通信加密传输

### **文件系统安全**
- 限制文件访问权限
- 临时文件自动清理
- 用户数据本地存储

### **API安全**
- gRPC服务端口本地绑定
- 请求参数验证
- 错误信息过滤

## 📊 **性能架构**

### **并发处理**
- 多后端服务负载均衡
- 异步gRPC调用
- 批量处理优化

### **内存管理**
- 大文件流式处理
- 及时释放临时资源
- Vue响应式数据优化

### **缓存策略**
- gRPC连接复用
- 处理结果缓存
- 静态资源缓存

## 🔄 **扩展架构**

### **水平扩展**
- 支持添加更多后端服务
- 负载均衡机制
- 服务发现机制

### **功能扩展**
- 插件化架构设计
- 模块化组件开发
- API版本管理

### **平台扩展**
- 跨平台支持 (Windows/macOS/Linux)
- 云服务集成能力
- 移动端适配潜力

---

## 🎯 **架构优势**

### **技术优势**
- **多语言支持**: Python/Go/Java后端服务
- **高性能**: gRPC高效通信协议
- **跨平台**: Electron桌面应用框架
- **现代化**: Vue.js 3 + Composition API

### **业务优势**
- **功能完整**: 从音视频到字幕的完整流程
- **用户友好**: 直观的分布式工作流界面
- **批量处理**: 高效的文件夹批量处理
- **多格式支持**: 丰富的导入导出格式

### **维护优势**
- **模块化设计**: 清晰的模块边界
- **代码复用**: 组件化开发
- **易于测试**: 完善的测试框架
- **文档完整**: 详细的开发文档

---

## 📚 **相关文档**
- [前端开发指南](./FRONTEND_DEVELOPMENT_GUIDE.md)
- [后端开发指南](./BACKEND_DEVELOPMENT_GUIDE.md)
- [测试开发指南](./TESTING_DEVELOPMENT_GUIDE.md)

---

*架构文档版本: v2.0*
*最后更新: 2024年12月*
*文档状态: 生产就绪*

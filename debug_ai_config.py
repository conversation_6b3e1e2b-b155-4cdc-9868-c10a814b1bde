#!/usr/bin/env python3
"""
调试 AI 配置的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径到 Python 路径
project_root = Path(__file__).parent
backend_path = project_root / "backend"
sys.path.insert(0, str(backend_path))

def debug_ai_config():
    """调试 AI 配置"""
    print("🔍 开始调试 AI 配置...")
    
    try:
        # 导入 AI 配置管理器模块
        import ai_config_manager

        print("✅ AI 配置管理器模块导入成功")

        # 获取所有配置
        all_configs = ai_config_manager.get_all_configs()
        print(f"📋 总配置数量: {len(all_configs)}")

        if not all_configs:
            print("⚠️  没有找到任何 AI 配置")
            return True

        for i, (config_id, config) in enumerate(all_configs.items()):
            print(f"\n📝 配置 {i+1}:")
            print(f"  - ID: {config.provider_id}")
            print(f"  - 名称: {config.display_name}")
            print(f"  - 类型: {config.provider_type}")
            print(f"  - 已启用: {config.is_enabled}")
            print(f"  - 属性: {dict(config.attributes)}")
            print(f"  - 凭据: {list(config.credentials.keys())}")

        # 检查 OpenAI 类型的配置
        openai_configs = ai_config_manager.get_active_configs_by_type("OpenAI")
        print(f"\n🤖 已启用的 OpenAI 配置数量: {len(openai_configs)}")

        for config in openai_configs:
            print(f"  - {config.display_name} ({config.provider_id})")
            api_key = config.credentials.get("api_key")
            print(f"    API Key: {'已设置' if api_key else '未设置'}")
            print(f"    模型: {config.attributes.get('default_model', '未设置')}")

        # 检查其他类型
        for provider_type in ["DeepSeek", "Gemini", "Claude"]:
            configs = ai_config_manager.get_active_configs_by_type(provider_type)
            if configs:
                print(f"\n🤖 已启用的 {provider_type} 配置数量: {len(configs)}")
                for config in configs:
                    print(f"  - {config.display_name} ({config.provider_id})")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始 AI 配置调试\n")
    
    success = debug_ai_config()
    
    print(f"\n{'🎉 调试完成!' if success else '❌ 调试失败'}")
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

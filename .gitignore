# .gitignore

# Operating System Generated Files
# =================================
.DS_Store
Thumbs.db
Desktop.ini

# IDE / Editor Specific
# =====================
# IntelliJ
.idea/
*.iml
*.ipr
*.iws
# VSCode
.vscode/
# Eclipse
.project
.classpath
.settings/
# Text Editors
*.swp
*~
*.bak
*.tmp

# Log Files
# =========
*.log
logs/
npm-debug.log*
yarn-error.log
yarn-debug.log*

# Dependency Directories
# ======================
electron-app/node_modules/

# Build Output / Compiled Files
# =============================
# General
build/
dist/ # General dist folder, e.g., for electron-builder output to project root/dist
# Electron App Specific
electron-app/dist/output.css
electron-app/dist_electron/ # Output from electron-builder
electron-app/release/       # Output from electron-builder
# Python Backend
backend/**/__pycache__/
backend/**/*.py[cod]
backend/AppData/cache/
backend/dist/ # Assuming .pex or other build artifacts go here
# Java Backend
# IMPORTANT: We ignore most of the target directory but explicitly keep the native executable
java-backend/target/*
!java-backend/target/java-grpc-backend
java-backend/target/classes/
java-backend/target/generated-sources/
java-backend/target/maven-archiver/
java-backend/target/maven-status/
java-backend/target/spring-aot/
java-backend/target/protoc-dependencies/
java-backend/target/protoc-plugins/
java-backend/target/*.jar
!java-backend/target/*-SNAPSHOT.jar
!java-backend/target/*.original
java-backend/target
# Go Backend
# Note: go-backend/go_server_executable is for local testing and ignored
# Note: go-backend/dist/go_grpc_server (if manually placed for dev) is NOT ignored
go-backend/go_server_executable
go-backend/dist/*_* # Ignores GoReleaser's platform-specific build folders (e.g., go_grpc_server_build_darwin_amd64)
                    # but not go-backend/dist/go_grpc_server if it exists directly.

# Virtual Environments
# ====================
backend/.venv/
backend/venv/
backend/env/

# Configuration Files
# ===================
.env
.env.*
!/.env.example # Do not ignore .env.example

# Maven Wrapper for Java
# ======================
java-backend/.mvn/

# Other
# =====
electron-app/coverage/
# backend/*.pex # This was in the original. If .pex files are always in backend/dist/, then backend/dist/ is enough.
                # If .pex can be in backend/ root and are build artifacts, uncomment this.
                # Given the project structure, assuming build artifacts go to backend/dist/
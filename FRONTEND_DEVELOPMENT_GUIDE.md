# 🎨 前端开发指南

## 📋 **技术栈概览**

### **核心技术**
- **Electron 28+**: 跨平台桌面应用框架
- **Vue.js 3**: 响应式前端框架 (Composition API)
- **Pinia**: 现代化状态管理
- **Tailwind CSS**: 原子化CSS框架
- **Vite**: 快速构建工具

### **开发工具**
- **Node.js 18+**: JavaScript运行环境
- **npm**: 包管理器
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

## 🚀 **快速开始**

### **环境准备**
```bash
# 1. 安装Node.js 18+
# 2. 克隆项目
git clone <repository-url>
cd electron-python-grpc-pex-demo

# 3. 安装前端依赖
cd electron-app
npm install

# 4. 启动开发服务器
npm run dev
```

### **开发命令**
```bash
# 开发模式 (热重载)
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview

# 代码检查
npm run lint

# 代码格式化
npm run format
```

## 📁 **前端项目结构**

### **目录结构详解**
```
electron-app/
├── src/
│   ├── main-process/              # Electron主进程
│   │   ├── app-lifecycle.js       # 应用生命周期
│   │   ├── backend-manager.js     # 后端服务管理
│   │   ├── grpc-manager.js        # gRPC客户端管理
│   │   ├── window-manager.js      # 窗口管理
│   │   ├── port-manager.js        # 端口管理
│   │   └── ipc/                   # IPC处理器
│   │       ├── filesystem-handlers.js
│   │       ├── grpc-test-handlers.js
│   │       └── subtitler-workflow-handlers.js
│   ├── vue/                       # Vue.js前端代码
│   │   ├── views/                 # 页面组件
│   │   │   ├── DistributedWorkflowView.vue
│   │   │   ├── OneClickOperationView.vue
│   │   │   └── GrpcTestView.vue
│   │   ├── components/            # 可复用组件
│   │   │   ├── common/            # 通用组件
│   │   │   ├── subtitler/         # 字幕处理组件
│   │   │   └── grpc-test/         # gRPC测试组件
│   │   ├── store/                 # Pinia状态管理
│   │   │   ├── subtitlerStore.js
│   │   │   └── grpcTestStore.js
│   │   ├── router/                # Vue Router路由
│   │   │   └── index.js
│   │   └── App.vue                # 根组件
│   └── js/                        # 工具函数
│       └── renderer-modules/
├── main.js                        # Electron主进程入口
├── preload.js                     # 预加载脚本
├── renderer.js                    # 渲染进程入口
├── index.html                     # HTML模板
├── package.json                   # 项目配置
├── vite.config.js                 # Vite构建配置
└── tailwind.config.js             # Tailwind配置
```

## 🏗️ **架构设计**

### **Electron架构**
```
┌─────────────────────────────────────┐
│           Electron 主进程            │
│  ┌─────────────┐  ┌─────────────────┐ │
│  │ 窗口管理器   │  │   后端管理器     │ │
│  └─────────────┘  └─────────────────┘ │
│  ┌─────────────┐  ┌─────────────────┐ │
│  │ gRPC管理器  │  │   端口管理器     │ │
│  └─────────────┘  └─────────────────┘ │
└─────────────────────────────────────┘
                  │ IPC
                  ▼
┌─────────────────────────────────────┐
│          Electron 渲染进程           │
│  ┌─────────────────────────────────┐ │
│  │           Vue.js 应用            │ │
│  │  ┌─────────┐  ┌─────────────────┐ │ │
│  │  │ 路由器   │  │   状态管理      │ │ │
│  │  └─────────┘  └─────────────────┘ │ │
│  │  ┌─────────────────────────────┐ │ │
│  │  │         组件系统             │ │ │
│  │  └─────────────────────────────┘ │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### **Vue.js组件架构**
```
App.vue (根组件)
├── NavigationSidebar.vue (导航侧边栏)
├── Router View (路由视图)
│   ├── DistributedWorkflowView.vue (分布式工作流)
│   │   ├── Step1FileUpload.vue
│   │   ├── Step2VideoToAudio.vue
│   │   ├── Step3AudioToText.vue
│   │   ├── Step4GenerateSubtitles.vue
│   │   ├── Step5OptimizeSubtitles.vue
│   │   ├── Step6EditSubtitles.vue
│   │   ├── Step7TranslationChoice.vue
│   │   ├── Step8TranslationProcessing.vue
│   │   └── Step9Export.vue
│   ├── OneClickOperationView.vue (一键操作)
│   └── GrpcTestView.vue (gRPC测试)
└── Common Components (通用组件)
    ├── StatusCard.vue
    ├── StatusMessage.vue
    ├── ProgressBar.vue
    └── ResultsViewer.vue
```

## 🔧 **核心功能实现**

### **状态管理 (Pinia)**

#### **SubtitlerStore 结构**
```javascript
// electron-app/src/vue/store/subtitlerStore.js
export const useSubtitlerStore = defineStore('subtitler', {
  state: () => ({
    // 文件处理状态
    uploadedFile: null,
    currentStep: 1,
    isLoading: false,
    progressUpdates: [],
    
    // 工作流数据
    videoToAudioResult: null,
    audioToTextResult: null,
    generatedSubtitles: null,
    optimizedSubtitles: null,
    editableSegments: null,
    translatedSubtitles: null,
    
    // 导出配置
    exportFormat: 'srt',
    exportLayout: '原文在上',
    exportLayouts: ['原文在上'], // 多选布局
    exportContentSource: 'editable_segments',
    
    // 一键操作配置
    oneClickWorkflowType: 'vid_to_srt_trans',
    oneClickTargetLanguage: 'zh-CN',
    oneClickExportFormat: 'srt',
    oneClickExportLayouts: ['原文在上']
  }),
  
  actions: {
    // 文件处理
    setUploadedFile(fileObject) { /* ... */ },
    resetWorkflow() { /* ... */ },
    
    // 工作流步骤
    async processVideoToAudio() { /* ... */ },
    async processAudioToText() { /* ... */ },
    async generateSubtitles() { /* ... */ },
    async optimizeSubtitles() { /* ... */ },
    async translateSubtitles() { /* ... */ },
    
    // 导出功能
    async exportSubtitles() { /* ... */ },
    
    // 一键操作
    async performOneClickOperation() { /* ... */ }
  }
});
```

#### **GrpcTestStore 结构**
```javascript
// electron-app/src/vue/store/grpcTestStore.js
export const useGrpcTestStore = defineStore('grpcTest', {
  state: () => ({
    // gRPC测试状态
    selectedService: null,
    selectedMethod: null,
    requestPayload: '{}',
    responsePayload: null,
    isLoading: false,
    error: null,
    
    // 后端服务状态
    backendServices: {
      python: { status: 'Unknown', clientStatus: { ready: false } },
      go: { status: 'Unknown', clientStatus: { ready: false } },
      java: { status: 'Unknown', clientStatus: { ready: false } }
    },
    
    // 日志管理
    logs: [],
    maxLogs: 100
  }),
  
  actions: {
    // gRPC调用
    async sendGrpcRequest() { /* ... */ },
    async fetchAvailableServices() { /* ... */ },
    
    // 状态管理
    updateServiceStatus(serviceKey, statusText, bgColor) { /* ... */ },
    updateClientStatus(serviceKey, clientStatus) { /* ... */ },
    async refreshBackendStatus() { /* ... */ },
    
    // 监听器初始化
    initializeBackendStatusListeners() { /* ... */ }
  }
});
```

### **路由配置**
```javascript
// electron-app/src/vue/router/index.js
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
  {
    path: '/',
    redirect: '/distributed-workflow'
  },
  {
    path: '/distributed-workflow',
    name: 'DistributedWorkflow',
    component: () => import('../views/DistributedWorkflowView.vue'),
    meta: { title: '分布式工作流' }
  },
  {
    path: '/one-click-operation',
    name: 'OneClickOperation', 
    component: () => import('../views/OneClickOperationView.vue'),
    meta: { title: '一键操作' }
  },
  {
    path: '/grpc-test',
    name: 'GrpcTest',
    component: () => import('../views/GrpcTestView.vue'),
    meta: { title: 'gRPC测试' }
  }
];

export default createRouter({
  history: createWebHistory(),
  routes
});
```

### **IPC通信**

#### **主进程IPC处理器**
```javascript
// electron-app/src/main-process/ipc/subtitler-workflow-handlers.js
const { ipcMain } = require('electron');

function initializeSubtitlerWorkflowHandlers(grpcMgr, winMgr) {
  // 字幕工作流处理
  ipcMain.handle('subtitler-full-workflow', async (event, payload) => {
    const client = grpcMgr.getSubtitlerClient();
    return await client.fullWorkflow(payload);
  });
  
  // 文件保存处理
  ipcMain.handle('subtitler:save-edited-segments', async (event, segments) => {
    // 保存编辑后的字幕片段
  });
  
  // 字幕导出处理
  ipcMain.handle('subtitler:export-subtitles', async (event, exportData) => {
    // 导出字幕文件
  });
}
```

#### **渲染进程IPC调用**
```javascript
// 在Vue组件中调用IPC
const result = await window.electronAPI.invoke('subtitler-full-workflow', {
  file_path: this.uploadedFile.path,
  workflow_type: 'vid_to_audio',
  request_word_timestamps: false
});
```

## 🎨 **UI设计系统**

### **Tailwind CSS配置**
```javascript
// tailwind.config.js
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8'
        }
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out'
      }
    },
  },
  plugins: [],
}
```

### **设计规范**

#### **颜色系统**
```css
/* 主色调 */
--primary-blue: #3b82f6;
--primary-green: #10b981;
--primary-red: #ef4444;
--primary-yellow: #f59e0b;

/* 灰度系统 */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-500: #6b7280;
--gray-700: #374151;
--gray-900: #111827;
```

#### **组件样式类**
```css
/* 卡片样式 */
.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-200 p-6;
}

/* 按钮样式 */
.btn-primary {
  @apply bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors;
}

/* 状态指示器 */
.status-success {
  @apply bg-green-50 border-green-200 text-green-800;
}

.status-error {
  @apply bg-red-50 border-red-200 text-red-800;
}

.status-warning {
  @apply bg-yellow-50 border-yellow-200 text-yellow-800;
}
```

### **响应式设计**
```vue
<!-- 响应式网格布局 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  <!-- 内容 -->
</div>

<!-- 响应式间距 -->
<div class="p-4 md:p-6 lg:p-8">
  <!-- 内容 -->
</div>

<!-- 响应式文字大小 -->
<h1 class="text-xl md:text-2xl lg:text-3xl font-bold">
  标题
</h1>
```

## 🔧 **开发最佳实践**

### **Vue.js开发规范**

#### **组件命名**
```javascript
// 使用PascalCase命名组件
export default {
  name: 'SubtitleEditor'
}

// 使用kebab-case在模板中引用
<subtitle-editor />
```

#### **Composition API使用**
```javascript
// 推荐使用Composition API
<script setup>
import { ref, computed, onMounted } from 'vue';
import { useSubtitlerStore } from '@/store/subtitlerStore';

const store = useSubtitlerStore();
const isLoading = ref(false);

const canProceed = computed(() => {
  return store.uploadedFile && !isLoading.value;
});

onMounted(() => {
  // 组件挂载时的逻辑
});
</script>
```

#### **状态管理最佳实践**
```javascript
// 在组件中使用store
const store = useSubtitlerStore();

// 响应式计算属性
const currentStepData = computed(() => store.getCurrentStepData);

// 调用store方法
const handleNext = async () => {
  await store.processNextStep();
};
```

### **错误处理**
```javascript
// 统一错误处理
const handleAsyncOperation = async () => {
  try {
    isLoading.value = true;
    await store.performOperation();
  } catch (error) {
    console.error('Operation failed:', error);
    // 显示用户友好的错误信息
    showErrorMessage(error.message);
  } finally {
    isLoading.value = false;
  }
};
```

### **性能优化**

#### **组件懒加载**
```javascript
// 路由级别的懒加载
const routes = [
  {
    path: '/distributed-workflow',
    component: () => import('../views/DistributedWorkflowView.vue')
  }
];
```

#### **计算属性缓存**
```javascript
// 使用计算属性缓存复杂计算
const processedData = computed(() => {
  return store.rawData.map(item => {
    // 复杂的数据处理逻辑
    return processItem(item);
  });
});
```

#### **事件防抖**
```javascript
import { debounce } from 'lodash-es';

const debouncedSearch = debounce((query) => {
  performSearch(query);
}, 300);
```

## 🧪 **调试和测试**

### **开发者工具**
```javascript
// Vue DevTools集成
if (process.env.NODE_ENV === 'development') {
  // 启用Vue DevTools
}

// Electron DevTools
// 在开发模式下自动打开DevTools
```

### **日志系统**
```javascript
// 统一日志管理
const logger = {
  info: (message, data) => {
    console.log(`[INFO] ${message}`, data);
  },
  error: (message, error) => {
    console.error(`[ERROR] ${message}`, error);
  },
  debug: (message, data) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, data);
    }
  }
};
```

### **组件测试**
```javascript
// 使用Vue Test Utils进行组件测试
import { mount } from '@vue/test-utils';
import SubtitleEditor from '@/components/SubtitleEditor.vue';

describe('SubtitleEditor', () => {
  it('renders correctly', () => {
    const wrapper = mount(SubtitleEditor);
    expect(wrapper.exists()).toBe(true);
  });
});
```

## 📦 **构建和部署**

### **构建配置**
```javascript
// vite.config.js
export default defineConfig({
  plugins: [vue()],
  base: './',
  build: {
    outDir: 'dist-electron',
    rollupOptions: {
      external: ['electron']
    }
  },
  server: {
    port: 5173
  }
});
```

### **Electron打包**
```json
// package.json
{
  "main": "main.js",
  "scripts": {
    "electron": "electron .",
    "electron:dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"",
    "build": "vite build",
    "dist": "electron-builder"
  },
  "build": {
    "appId": "com.example.subtitler",
    "productName": "字幕处理工具",
    "directories": {
      "output": "dist"
    },
    "files": [
      "dist-electron/**/*",
      "main.js",
      "preload.js"
    ]
  }
}
```

---

## 📚 **相关文档**
- [整体架构说明](./ARCHITECTURE_OVERVIEW.md)
- [后端开发指南](./BACKEND_DEVELOPMENT_GUIDE.md)
- [测试开发指南](./TESTING_DEVELOPMENT_GUIDE.md)

---

*前端开发文档版本: v2.0*  
*最后更新: 2024年12月*  
*文档状态: 生产就绪*

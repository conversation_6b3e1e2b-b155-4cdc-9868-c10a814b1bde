#!/usr/bin/env python3
"""
临时修复 AI 配置问题的脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径到 Python 路径
project_root = Path(__file__).parent
backend_path = project_root / "backend"
sys.path.insert(0, str(backend_path))

def create_default_ai_config():
    """创建默认的 AI 配置"""
    print("🔧 创建默认 AI 配置...")
    
    try:
        # 导入必要的模块
        import ai_config_manager
        from api_protos.v1.ai_config import ai_config_service_pb2
        
        print("✅ 模块导入成功")
        
        # 创建一个默认的 OpenAI 配置
        default_config = ai_config_service_pb2.AIProviderConfigProto()
        default_config.provider_id = "default-openai"
        default_config.provider_type = "OpenAI"
        default_config.display_name = "默认 OpenAI 服务"
        default_config.is_enabled = True
        
        # 设置默认凭据（注意：这只是为了测试，实际使用时需要真实的 API key）
        default_config.credentials["api_key"] = "sk-test-key-for-development"
        
        # 设置默认属性
        default_config.attributes["default_model"] = "gpt-4o-mini"
        default_config.attributes["api_base_url"] = "https://api.openai.com/v1"
        
        # 更新配置
        ai_config_manager.update_configurations([default_config])
        
        print("✅ 默认 AI 配置已创建")
        
        # 验证配置
        all_configs = ai_config_manager.get_all_configs()
        print(f"📋 当前配置数量: {len(all_configs)}")
        
        openai_configs = ai_config_manager.get_active_configs_by_type("OpenAI")
        print(f"🤖 已启用的 OpenAI 配置数量: {len(openai_configs)}")
        
        for config in openai_configs:
            print(f"  - {config.display_name} ({config.provider_id})")
            api_key = config.credentials.get("api_key")
            print(f"    API Key: {'已设置' if api_key else '未设置'}")
            print(f"    模型: {config.attributes.get('default_model', '未设置')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_generate_subtitles():
    """测试字幕生成功能"""
    print("\n🧪 测试字幕生成功能...")
    
    try:
        from subtitle.transcriptThread import TranscriptThread
        
        print("✅ TranscriptThread 导入成功")
        
        # 创建 TranscriptThread 实例
        tt = TranscriptThread()
        
        # 测试字幕生成（使用简单文本）
        test_text = "这是一个测试文本。我们将使用它来生成字幕。"
        
        print("🔄 开始测试字幕生成...")
        
        progress_count = 0
        for progress in tt.generate_subtitles_from_text(text=test_text):
            progress_count += 1
            print(f"进度 {progress_count}: {progress}")
            
            # 如果有错误，显示详细信息
            if progress.get("is_error"):
                print(f"❌ 发现错误: {progress.get('error_message')}")
                break
                
            # 如果有最终结果，显示
            if progress.get("final_result", {}).get("generate_subtitles_response"):
                srt_content = progress["final_result"]["generate_subtitles_response"].get("srt_content")
                if srt_content:
                    print(f"✅ 字幕生成成功！SRT 内容长度: {len(srt_content)} 字符")
                    print("📝 SRT 内容预览:")
                    print(srt_content[:200] + "..." if len(srt_content) > 200 else srt_content)
                break
                
            # 限制测试进度数量
            if progress_count >= 10:
                print("⏹️  测试进度限制，停止测试")
                break
        
        print("✅ 字幕生成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始修复 AI 配置问题\n")
    
    # 步骤 1: 创建默认配置
    config_success = create_default_ai_config()
    
    if config_success:
        # 步骤 2: 测试字幕生成
        test_success = test_generate_subtitles()
        
        if test_success:
            print(f"\n🎉 修复完成！AI 配置问题已解决。")
            return 0
        else:
            print(f"\n⚠️  配置创建成功，但字幕生成测试失败。")
            return 1
    else:
        print(f"\n❌ 修复失败！无法创建 AI 配置。")
        return 1

if __name__ == "__main__":
    sys.exit(main())

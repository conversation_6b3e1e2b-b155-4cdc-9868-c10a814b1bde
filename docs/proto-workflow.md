# Proto文件工作流程指南

本文档介绍如何使用集中式的proto文件管理系统，包括创建、修改、同步和测试proto接口的完整工作流程。

## 概述

我们的系统使用集中式proto文件管理，所有服务接口定义在一个proto文件中，然后通过同步脚本编译并分发到各个服务。这种方法确保了接口的一致性和可维护性。

## 工作流程

### 1. 了解当前接口

在修改接口前，请先阅读当前proto文件以了解现有接口：

```bash
cat protos/services.proto
```

### 2. 修改proto文件

使用您喜欢的编辑器修改proto文件。请记住遵循[protos/README.md](../protos/README.md)中的接口变更指南。

例如，要添加一个新的服务方法：

```protobuf
service UserService {
  // ... 现有方法 ...
  
  // 新增方法：检查用户名是否可用
  rpc CheckUsernameAvailability(CheckUsernameRequest) returns (CheckUsernameResponse) {}
}

message CheckUsernameRequest {
  string username = 1;
}

message CheckUsernameResponse {
  int32 code = 1;
  string message = 2;
  bool available = 3;
}
```

### 3. 同步接口到各服务

修改完proto文件后，运行同步脚本将更改应用到各个服务：

```bash
# 同步所有服务
./scripts/proto_sync.sh --all

# 或者只同步特定服务
./scripts/proto_sync.sh --python
```

### 4. 实现服务逻辑

现在，您需要在各个服务中实现新定义的接口逻辑。以Python服务为例：

```python
# backend/server.py 中实现新方法
def CheckUsernameAvailability(self, request, context):
    username = request.username
    # 检查用户名是否已被使用的逻辑
    is_available = self.db.check_username(username)
    
    return services_pb2.CheckUsernameResponse(
        code=200,
        message="Success",
        available=is_available
    )
```

### 5. 测试新接口

使用gRPC客户端测试新接口。您可以使用各语言提供的gRPC测试工具，或者使用通用工具如[grpcurl](https://github.com/fullstorydev/grpcurl)。

```bash
# 使用grpcurl测试
grpcurl -plaintext -d '{"username": "testuser"}' localhost:50051 UserService.CheckUsernameAvailability
```

### 6. 更新客户端调用

最后，更新客户端代码以使用新接口：

```javascript
// electron-app/renderer.js 中调用新方法
async function checkUsername(username) {
  return new Promise((resolve, reject) => {
    const request = new proto.CheckUsernameRequest();
    request.setUsername(username);
    
    userServiceClient.checkUsernameAvailability(request, (err, response) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(response.getAvailable());
    });
  });
}
```

## 版本管理

当需要进行不兼容的接口变更时，请按照以下步骤创建新版本：

1. 创建新版本目录：`mkdir -p protos/v2`
2. 复制当前版本文件：`cp protos/services.proto protos/v2/`
3. 修改新版本文件，更新包名和版本号
4. 修改同步脚本，支持多版本同步

## 常见问题

### 同步脚本报错

如果同步脚本报错，请检查：

1. 是否安装了所有依赖工具
2. proto文件语法是否正确
3. 目录结构是否符合预期

### 编译后代码找不到

检查是否正确设置了输出路径，并确保目标目录存在。

### 多版本兼容性问题

如果需要同时支持多个版本的API，请考虑在服务实现中使用适配器模式处理不同版本的请求。

## 最佳实践

1. **小步迭代**：进行小的、增量式的接口变更，而非大规模重构
2. **自动化测试**：为每个接口编写自动化测试
3. **保持文档更新**：更新接口时同步更新文档
4. **代码审查**：接口变更进行同行评审
5. **向后兼容**：尽量保持向后兼容性

## 相关文档

- [protos/README.md](../protos/README.md) - Proto文件管理指南
- [gRPC官方文档](https://grpc.io/docs/) - gRPC文档
- [Protocol Buffers语言指南](https://developers.google.com/protocol-buffers/docs/proto3) - proto3语法参考
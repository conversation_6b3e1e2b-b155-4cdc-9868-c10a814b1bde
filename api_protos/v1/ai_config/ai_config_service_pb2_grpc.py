# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from api_protos.v1.ai_config import ai_config_service_pb2 as api__protos_dot_v1_dot_ai__config_dot_ai__config__service__pb2

GRPC_GENERATED_VERSION = '1.71.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in api_protos/v1/ai_config/ai_config_service_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AIConfigurationServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.UpdateAIConfigurations = channel.unary_unary(
                '/v1.ai_config.AIConfigurationService/UpdateAIConfigurations',
                request_serializer=api__protos_dot_v1_dot_ai__config_dot_ai__config__service__pb2.UpdateAIConfigurationsRequest.SerializeToString,
                response_deserializer=api__protos_dot_v1_dot_ai__config_dot_ai__config__service__pb2.UpdateAIConfigurationsResponse.FromString,
                _registered_method=True)


class AIConfigurationServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def UpdateAIConfigurations(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AIConfigurationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'UpdateAIConfigurations': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateAIConfigurations,
                    request_deserializer=api__protos_dot_v1_dot_ai__config_dot_ai__config__service__pb2.UpdateAIConfigurationsRequest.FromString,
                    response_serializer=api__protos_dot_v1_dot_ai__config_dot_ai__config__service__pb2.UpdateAIConfigurationsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'v1.ai_config.AIConfigurationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('v1.ai_config.AIConfigurationService', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AIConfigurationService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def UpdateAIConfigurations(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/v1.ai_config.AIConfigurationService/UpdateAIConfigurations',
            api__protos_dot_v1_dot_ai__config_dot_ai__config__service__pb2.UpdateAIConfigurationsRequest.SerializeToString,
            api__protos_dot_v1_dot_ai__config_dot_ai__config__service__pb2.UpdateAIConfigurationsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

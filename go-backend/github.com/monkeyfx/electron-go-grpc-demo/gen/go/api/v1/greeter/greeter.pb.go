// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.2
// source: greeter/greeter.proto

package greeter

import (
	_ "github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HelloRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HelloRequest) Reset() {
	*x = HelloRequest{}
	mi := &file_greeter_greeter_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HelloRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloRequest) ProtoMessage() {}

func (x *HelloRequest) ProtoReflect() protoreflect.Message {
	mi := &file_greeter_greeter_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloRequest.ProtoReflect.Descriptor instead.
func (*HelloRequest) Descriptor() ([]byte, []int) {
	return file_greeter_greeter_proto_rawDescGZIP(), []int{0}
}

func (x *HelloRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type HelloReply struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Message         string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	OriginalRequest string                 `protobuf:"bytes,2,opt,name=original_request,json=originalRequest,proto3" json:"original_request,omitempty"`
	ServerId        string                 `protobuf:"bytes,3,opt,name=server_id,json=serverId,proto3" json:"server_id,omitempty"`
	Timestamp       string                 `protobuf:"bytes,4,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *HelloReply) Reset() {
	*x = HelloReply{}
	mi := &file_greeter_greeter_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HelloReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloReply) ProtoMessage() {}

func (x *HelloReply) ProtoReflect() protoreflect.Message {
	mi := &file_greeter_greeter_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloReply.ProtoReflect.Descriptor instead.
func (*HelloReply) Descriptor() ([]byte, []int) {
	return file_greeter_greeter_proto_rawDescGZIP(), []int{1}
}

func (x *HelloReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *HelloReply) GetOriginalRequest() string {
	if x != nil {
		return x.OriginalRequest
	}
	return ""
}

func (x *HelloReply) GetServerId() string {
	if x != nil {
		return x.ServerId
	}
	return ""
}

func (x *HelloReply) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

var File_greeter_greeter_proto protoreflect.FileDescriptor

const file_greeter_greeter_proto_rawDesc = "" +
	"\n" +
	"\x15greeter/greeter.proto\x12\x17monkeyfx.api.v1.greeter\x1a\x13common/common.proto\"\"\n" +
	"\fHelloRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\x8c\x01\n" +
	"\n" +
	"HelloReply\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\x12)\n" +
	"\x10original_request\x18\x02 \x01(\tR\x0foriginalRequest\x12\x1b\n" +
	"\tserver_id\x18\x03 \x01(\tR\bserverId\x12\x1c\n" +
	"\ttimestamp\x18\x04 \x01(\tR\ttimestamp2a\n" +
	"\aGreeter\x12V\n" +
	"\bSayHello\x12%.monkeyfx.api.v1.greeter.HelloRequest\x1a#.monkeyfx.api.v1.greeter.HelloReplyBs\n" +
	" com.monkeyfx.grpc.api.v1.greeterB\fGreeterProtoP\x01Z?github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/greeterb\x06proto3"

var (
	file_greeter_greeter_proto_rawDescOnce sync.Once
	file_greeter_greeter_proto_rawDescData []byte
)

func file_greeter_greeter_proto_rawDescGZIP() []byte {
	file_greeter_greeter_proto_rawDescOnce.Do(func() {
		file_greeter_greeter_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_greeter_greeter_proto_rawDesc), len(file_greeter_greeter_proto_rawDesc)))
	})
	return file_greeter_greeter_proto_rawDescData
}

var file_greeter_greeter_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_greeter_greeter_proto_goTypes = []any{
	(*HelloRequest)(nil), // 0: monkeyfx.api.v1.greeter.HelloRequest
	(*HelloReply)(nil),   // 1: monkeyfx.api.v1.greeter.HelloReply
}
var file_greeter_greeter_proto_depIdxs = []int32{
	0, // 0: monkeyfx.api.v1.greeter.Greeter.SayHello:input_type -> monkeyfx.api.v1.greeter.HelloRequest
	1, // 1: monkeyfx.api.v1.greeter.Greeter.SayHello:output_type -> monkeyfx.api.v1.greeter.HelloReply
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_greeter_greeter_proto_init() }
func file_greeter_greeter_proto_init() {
	if File_greeter_greeter_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_greeter_greeter_proto_rawDesc), len(file_greeter_greeter_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_greeter_greeter_proto_goTypes,
		DependencyIndexes: file_greeter_greeter_proto_depIdxs,
		MessageInfos:      file_greeter_greeter_proto_msgTypes,
	}.Build()
	File_greeter_greeter_proto = out.File
	file_greeter_greeter_proto_goTypes = nil
	file_greeter_greeter_proto_depIdxs = nil
}

package main

import (
	"context" // 用于管理请求的上下文，例如超时和取消。
	"fmt"     // Go 标准库提供的格式化 I/O 包。
	"log"     // Go 标准库提供的日志包。
	"net"     // 提供网络 I/O 功能，包括 TCP/IP、UDP、域名解析和 Unix 域套接字。
	"os"      // 提供与操作系统功能交互的平台无关接口，此处用于 os.Stdout。
	"time"

	// 更新导入路径以指向新的生成代码位置
	// 这个路径是 go.mod 中的模块名 ("github.com/monkeyfx/electron-go-grpc-demo")
	// 加上 api-protos/v1/services.proto 中的相对 go_package ("./gen/go/api/v1")
	greeterpb "github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/greeter"
	// commonpb "github.com/monkeyfx/electron-go-grpc-demo/gen/go/api/v1/common" // Uncomment if common types are needed

	"google.golang.org/grpc" // Google gRPC Go 语言实现。
)

// infoLog 是一个全局日志记录器，用于将信息性消息输出到标准输出 (STDOUT)。
// 它在 main 函数开始时被初始化。
var infoLog *log.Logger

const (
	// port 定义了 gRPC 服务器监听的网络端口。
	port = ":50052"
)

// server 结构体用于实现 'greeter.proto' 文件中定义的 GreeterServer 接口。
// 它嵌入了 UnimplementedGreeterServer，这是 gRPC 生成代码的一部分，
// 用于确保向前兼容性：如果未来向服务定义中添加了新的 RPC 方法，
// 此服务器将自动为这些新方法提供默认的 "未实现" 响应，而不会导致编译错误。
type server struct {
	greeterpb.UnimplementedGreeterServer
}

// SayHello 是 GreeterServer 接口的实现。
// 当客户端调用 SayHello RPC 时，此方法被执行。
// ctx: 请求的上下文，可用于传递截止日期、取消信号等。
// in: 指向 HelloRequest 消息的指针，包含客户端发送的请求数据 (例如，name 字段)。
// 返回: 指向 HelloReply 消息的指针和错误对象。如果成功，错误为 nil。
func (s *server) SayHello(ctx context.Context, in *greeterpb.HelloRequest) (*greeterpb.HelloReply, error) {
	// 检查 infoLog 是否已初始化。正常情况下，它应该在 main 函数中被初始化。
	if infoLog != nil {
		// 使用 infoLog 记录收到的请求名称。
		infoLog.Printf("Go Server Received gRPC request with name: %v", in.GetName())
	} else {
		// 这是一个回退机制，以防 infoLog 由于某种意外原因未被初始化。
		// 在正常操作中，此分支不应被执行。
		// 如果 infoLog 总是被正确初始化，可以考虑移除此 else块以简化代码。
		// 但保留它可以作为一种额外的安全措施，确保即使在意外情况下也能记录日志。
		log.Printf("INFO_FALLBACK: Go Server Received gRPC request (infoLog not initialized): %v", in.GetName())
	}

	originalName := in.GetName()
	serverIDStr := "Go Server"
	currentTimestampStr := time.Now().Format(time.RFC3339Nano)
	responseMessageStr := fmt.Sprintf("Hello %s from %s!", originalName, serverIDStr)

	reply := &greeterpb.HelloReply{
		Message:         responseMessageStr,
		OriginalRequest: originalName,
		ServerId:        serverIDStr,
		Timestamp:       currentTimestampStr,
	}
	if infoLog != nil {
		infoLog.Printf("Go Server Prepared Reply: message='%s', original_request='%s', server_id='%s', timestamp='%s'", reply.Message, reply.OriginalRequest, reply.ServerId, reply.Timestamp)
	} else {
		log.Printf("Go Server Prepared Reply (infoLog not initialized): message='%s', original_request='%s', server_id='%s', timestamp='%s'", reply.Message, reply.OriginalRequest, reply.ServerId, reply.Timestamp)
	}
	return reply, nil
}

// main 函数是 Go 应用程序的入口点。
func main() {
	// 初始化全局的 infoLog 记录器。
	// log.New 创建一个新的 Logger。
	// os.Stdout 指定日志输出到标准输出。
	// "" 是日志行的前缀，这里为空。
	// log.LstdFlags 配置日志记录器以包含标准信息：日期和时间。
	infoLog = log.New(os.Stdout, "INFO: ", log.LstdFlags) // 添加 "INFO: " 前缀以区分日志级别

	// 启动网络监听器。
	// net.Listen 在指定的网络 ("tcp") 和地址 (port) 上监听传入的连接。
	lis, err := net.Listen("tcp", port)
	if err != nil {
		// 如果监听失败（例如，端口已被占用），则记录致命错误并退出程序。
		// log.Fatalf 会打印错误消息然后调用 os.Exit(1)。
		log.Fatalf("FATAL: Go gRPC server failed to listen on port %s: %v", port, err)
	}

	// 创建一个新的 gRPC 服务器实例。
	s := grpc.NewServer()

	// 将 'server' 结构体的实例注册为 GreeterServer 的实现。
	// 这使得 gRPC 服务器知道如何处理来自客户端的 Greeter 服务的请求。
	// RegisterGreeterServer 是从 .proto 文件生成的代码的一部分。
	greeterpb.RegisterGreeterServer(s, &server{})

	// 使用 infoLog 记录服务器正在监听的地址。
	// lis.Addr() 返回监听器的网络地址。
	infoLog.Printf("Go gRPC server successfully started and listening at %v", lis.Addr())

	// 开始在已配置的监听器上接受和处理 gRPC 请求。
	// s.Serve 会阻塞，直到服务器被停止或发生错误。
	if err := s.Serve(lis); err != nil {
		// 如果服务器在服务期间遇到错误（例如，底层网络问题），
		// 则记录致命错误并退出程序。
		log.Fatalf("FATAL: Go gRPC server failed to serve: %v", err)
	}
}

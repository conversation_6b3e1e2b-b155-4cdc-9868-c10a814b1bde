# .goreleaser.yml
project_name: go_grpc_server
before:
  hooks:
    - go mod tidy
builds:
  - id: go_grpc_server_build
    main: .
    binary: go_grpc_server # This will be the name of the executable
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    goarch:
      - amd64
      - arm64
    ldflags:
      - -s -w
# No 'archives' section needed if we directly use the output from the 'builds' step.
# GoReleaser places build artifacts in dist/<build.id>_<os>_<arch>/<binary_name>
# or a similar structure. We'll use that path.
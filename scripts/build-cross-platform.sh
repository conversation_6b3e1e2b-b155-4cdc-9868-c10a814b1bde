#!/bin/bash
# build-cross-platform.sh - 跨平台构建脚本
# 用于构建所有平台的应用包

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未找到，请先安装"
        exit 1
    fi
}

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

log_info "🌍 开始跨平台构建..."
log_info "📁 项目根目录: $PROJECT_ROOT"

# 检查必要工具
log_info "📋 检查构建工具..."
check_command "go"
check_command "node"
check_command "npm"

# 1. 构建Go后端的多平台版本
log_info "🔷 构建Go后端多平台版本..."
cd "$PROJECT_ROOT/go-backend"

# 确保依赖是最新的
go mod tidy

# 创建多平台输出目录
mkdir -p ../dist/platforms

# Windows 64位
log_info "构建 Windows 64位版本..."
GOOS=windows GOARCH=amd64 go build -o ../dist/platforms/go_grpc_server_windows_amd64.exe server.go
if [ $? -eq 0 ]; then
    log_success "✅ Windows 64位版本构建成功"
else
    log_error "❌ Windows 64位版本构建失败"
    exit 1
fi

# macOS 64位 (Intel)
log_info "构建 macOS 64位版本 (Intel)..."
GOOS=darwin GOARCH=amd64 go build -o ../dist/platforms/go_grpc_server_darwin_amd64 server.go
if [ $? -eq 0 ]; then
    log_success "✅ macOS 64位版本 (Intel) 构建成功"
else
    log_error "❌ macOS 64位版本 (Intel) 构建失败"
    exit 1
fi

# macOS ARM64 (Apple Silicon)
log_info "构建 macOS ARM64版本 (Apple Silicon)..."
GOOS=darwin GOARCH=arm64 go build -o ../dist/platforms/go_grpc_server_darwin_arm64 server.go
if [ $? -eq 0 ]; then
    log_success "✅ macOS ARM64版本 (Apple Silicon) 构建成功"
else
    log_error "❌ macOS ARM64版本 (Apple Silicon) 构建失败"
    exit 1
fi

# Linux 64位
log_info "构建 Linux 64位版本..."
GOOS=linux GOARCH=amd64 go build -o ../dist/platforms/go_grpc_server_linux_amd64 server.go
if [ $? -eq 0 ]; then
    log_success "✅ Linux 64位版本构建成功"
else
    log_error "❌ Linux 64位版本构建失败"
    exit 1
fi

# Linux ARM64
log_info "构建 Linux ARM64版本..."
GOOS=linux GOARCH=arm64 go build -o ../dist/platforms/go_grpc_server_linux_arm64 server.go
if [ $? -eq 0 ]; then
    log_success "✅ Linux ARM64版本构建成功"
else
    log_error "❌ Linux ARM64版本构建失败"
    exit 1
fi

# 2. 复制当前平台的Go后端到主dist目录
log_info "📋 复制当前平台的Go后端..."
CURRENT_OS=$(uname -s | tr '[:upper:]' '[:lower:]')
CURRENT_ARCH=$(uname -m)

case $CURRENT_ARCH in
    x86_64)
        CURRENT_ARCH="amd64"
        ;;
    arm64|aarch64)
        CURRENT_ARCH="arm64"
        ;;
esac

case $CURRENT_OS in
    darwin)
        cp ../dist/platforms/go_grpc_server_darwin_${CURRENT_ARCH} ../dist/go_grpc_server
        ;;
    linux)
        cp ../dist/platforms/go_grpc_server_linux_${CURRENT_ARCH} ../dist/go_grpc_server
        ;;
    mingw*|msys*|cygwin*)
        cp ../dist/platforms/go_grpc_server_windows_amd64.exe ../dist/go_grpc_server.exe
        ;;
esac

chmod +x ../dist/go_grpc_server* 2>/dev/null || true

# 3. 构建Electron应用的多平台版本
log_info "📦 构建Electron应用多平台版本..."
cd "$PROJECT_ROOT/electron-app"

# 确保前端已构建
if [ ! -d "dist" ]; then
    log_info "前端未构建，先构建前端..."
    npm run build:vite
fi

# 构建所有平台
log_info "构建所有平台的Electron应用..."
if npx electron-builder --mac --win --linux --publish=never; then
    log_success "✅ 所有平台的Electron应用构建成功"
else
    log_error "❌ Electron应用构建失败"
    exit 1
fi

# 4. 显示构建结果
log_info "📊 跨平台构建结果:"
echo ""

log_info "Go后端多平台版本:"
ls -la "$PROJECT_ROOT/dist/platforms/"

echo ""
log_info "Electron应用包:"
ls -la "$PROJECT_ROOT/electron-app/dist-electron/"

# 5. 计算各平台文件大小
log_info "📏 各平台文件大小:"

# Go后端大小
if [ -f "$PROJECT_ROOT/dist/platforms/go_grpc_server_windows_amd64.exe" ]; then
    SIZE=$(du -h "$PROJECT_ROOT/dist/platforms/go_grpc_server_windows_amd64.exe" | cut -f1)
    log_info "Go后端 (Windows): $SIZE"
fi

if [ -f "$PROJECT_ROOT/dist/platforms/go_grpc_server_darwin_amd64" ]; then
    SIZE=$(du -h "$PROJECT_ROOT/dist/platforms/go_grpc_server_darwin_amd64" | cut -f1)
    log_info "Go后端 (macOS Intel): $SIZE"
fi

if [ -f "$PROJECT_ROOT/dist/platforms/go_grpc_server_darwin_arm64" ]; then
    SIZE=$(du -h "$PROJECT_ROOT/dist/platforms/go_grpc_server_darwin_arm64" | cut -f1)
    log_info "Go后端 (macOS ARM): $SIZE"
fi

if [ -f "$PROJECT_ROOT/dist/platforms/go_grpc_server_linux_amd64" ]; then
    SIZE=$(du -h "$PROJECT_ROOT/dist/platforms/go_grpc_server_linux_amd64" | cut -f1)
    log_info "Go后端 (Linux): $SIZE"
fi

# Electron应用大小
if [ -d "$PROJECT_ROOT/electron-app/dist-electron" ]; then
    TOTAL_SIZE=$(du -sh "$PROJECT_ROOT/electron-app/dist-electron" | cut -f1)
    log_info "Electron应用包总大小: $TOTAL_SIZE"
fi

# 6. 创建发布包结构
log_info "📦 创建发布包结构..."
RELEASE_DIR="$PROJECT_ROOT/release"
rm -rf "$RELEASE_DIR"
mkdir -p "$RELEASE_DIR"

# 复制应用包
if [ -d "$PROJECT_ROOT/electron-app/dist-electron" ]; then
    cp -r "$PROJECT_ROOT/electron-app/dist-electron"/* "$RELEASE_DIR/"
fi

# 复制多平台Go后端
mkdir -p "$RELEASE_DIR/backends"
cp -r "$PROJECT_ROOT/dist/platforms"/* "$RELEASE_DIR/backends/"

# 创建README
cat > "$RELEASE_DIR/README.md" << EOF
# 字幕处理工具 - 发布包

## 应用包
- \`字幕处理工具-1.0.0.dmg\` - macOS安装包
- \`字幕处理工具 Setup 1.0.0.exe\` - Windows安装包  
- \`字幕处理工具-1.0.0.AppImage\` - Linux应用包

## 多平台后端
\`backends/\` 目录包含了所有平台的Go后端服务：
- \`go_grpc_server_windows_amd64.exe\` - Windows 64位
- \`go_grpc_server_darwin_amd64\` - macOS Intel
- \`go_grpc_server_darwin_arm64\` - macOS Apple Silicon
- \`go_grpc_server_linux_amd64\` - Linux 64位
- \`go_grpc_server_linux_arm64\` - Linux ARM64

## 系统要求
- Windows 10 或更高版本
- macOS 10.14 或更高版本
- Linux (Ubuntu 18.04+ 或等效版本)
- 内存: 4GB RAM (推荐 8GB)
- 存储: 500MB 可用空间

## 安装说明
1. 下载对应平台的安装包
2. 运行安装包并按提示安装
3. 启动应用即可使用

构建时间: $(date)
EOF

log_success "✅ 跨平台构建完成！"
echo ""
log_info "📁 发布包位置: $RELEASE_DIR"
log_info "📋 包含内容:"
log_info "  - 所有平台的Electron应用包"
log_info "  - 所有平台的Go后端服务"
log_info "  - 安装和使用说明"
echo ""
log_info "🚀 可以将 release/ 目录打包分发给用户！"

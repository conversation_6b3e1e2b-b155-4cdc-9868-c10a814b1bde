# 构建脚本设计文档

本文档详细描述了项目构建脚本的设计和实现细节，包括依赖检查、增量构建、错误处理等功能。

## 依赖检查脚本 (`check-dependencies.js`)

依赖检查脚本用于在构建前验证所有必要的工具和环境变量是否已正确配置。

### 实现细节

```javascript
/**
 * 依赖检查脚本示例实现
 * 文件: scripts/check-dependencies.js
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const config = require('../config/config-loader');

// 定义需要检查的依赖
const dependencies = [
  {
    name: 'Node.js',
    command: 'node --version',
    minVersion: 'v14.0.0',
    required: true
  },
  {
    name: 'Python',
    command: 'python --version',
    minVersion: '3.6.0',
    required: true
  },
  {
    name: 'Go',
    command: 'go version',
    minVersion: '1.16.0',
    required: true
  },
  {
    name: 'Java',
    command: 'java -version',
    minVersion: '17.0.0',
    required: true
  },
  {
    name: 'Maven',
    command: 'mvn --version',
    minVersion: '3.6.0',
    required: true
  },
  {
    name: 'PEX',
    command: 'pex --version',
    minVersion: '2.0.0',
    required: true
  },
  {
    name: 'protoc',
    command: 'protoc --version',
    minVersion: '3.15.0',
    required: true
  },
  {
    name: 'GraalVM native-image',
    command: 'native-image --version',
    minVersion: '21.0.0',
    required: false
  }
];

// 定义需要检查的环境变量
const requiredEnvVars = [
  { name: 'PATH', required: true },
  { name: 'GOPATH', required: false },
  { name: 'JAVA_HOME', required: false }
];

// 定义需要检查的目录和文件
const requiredPaths = [
  { path: '../backend', type: 'directory', required: true },
  { path: '../go-backend', type: 'directory', required: true },
  { path: '../java-backend', type: 'directory', required: true },
  { path: '../electron-app', type: 'directory', required: true },
  { path: '../proto/v1', type: 'directory', required: true },
  { path: '../config/config.json', type: 'file', required: true }
];

// 主检查函数
async function checkDependencies() {
  console.log('开始依赖检查...');
  
  let hasErrors = false;
  let warnings = 0;
  
  // 检查命令行工具
  console.log('\n检查命令行工具:');
  for (const dep of dependencies) {
    try {
      const output = execSync(dep.command, { encoding: 'utf-8', stderr: 'pipe' });
      const version = extractVersion(dep.name, output);
      
      if (version && dep.minVersion && !isVersionSatisfied(version, dep.minVersion)) {
        console.warn(`⚠️ ${dep.name} 版本 ${version} 低于推荐的最低版本 ${dep.minVersion}`);
        warnings++;
      } else {
        console.log(`✅ ${dep.name} ${version ? '(版本 ' + version + ')' : ''} 已安装`);
      }
    } catch (error) {
      if (dep.required) {
        console.error(`❌ ${dep.name} 未安装或不可用，这是必须的依赖`);
        console.error(`   推荐安装方式: ${getInstallCommand(dep.name)}`);
        hasErrors = true;
      } else {
        console.warn(`⚠️ ${dep.name} 未安装或不可用，这是可选的依赖`);
        warnings++;
      }
    }
  }
  
  // 检查环境变量
  console.log('\n检查环境变量:');
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar.name]) {
      console.log(`✅ ${envVar.name} 已设置: ${truncateString(process.env[envVar.name], 50)}`);
    } else if (envVar.required) {
      console.error(`❌ ${envVar.name} 未设置，这是必须的环境变量`);
      hasErrors = true;
    } else {
      console.warn(`⚠️ ${envVar.name} 未设置，这是推荐的环境变量`);
      warnings++;
    }
  }
  
  // 检查目录和文件
  console.log('\n检查目录和文件:');
  for (const item of requiredPaths) {
    const fullPath = path.resolve(__dirname, item.path);
    try {
      const stat = fs.statSync(fullPath);
      if (item.type === 'directory' && stat.isDirectory()) {
        console.log(`✅ 目录 ${item.path} 存在`);
      } else if (item.type === 'file' && stat.isFile()) {
        console.log(`✅ 文件 ${item.path} 存在`);
      } else {
        if (item.required) {
          console.error(`❌ ${item.path} 存在但不是${item.type === 'directory' ? '目录' : '文件'}`);
          hasErrors = true;
        } else {
          console.warn(`⚠️ ${item.path} 存在但不是${item.type === 'directory' ? '目录' : '文件'}`);
          warnings++;
        }
      }
    } catch (error) {
      if (item.required) {
        console.error(`❌ ${item.path} 不存在，这是必须的${item.type === 'directory' ? '目录' : '文件'}`);
        hasErrors = true;
      } else {
        console.warn(`⚠️ ${item.path} 不存在，这是推荐的${item.type === 'directory' ? '目录' : '文件'}`);
        warnings++;
      }
    }
  }
  
  // 总结
  console.log('\n检查总结:');
  if (hasErrors) {
    console.error(`❌ 发现 ${hasErrors ? '致命错误' : '0个错误'} 和 ${warnings} 个警告`);
    console.error('请修复上述错误后再继续构建过程。');
    process.exit(1);
  } else if (warnings > 0) {
    console.warn(`⚠️ 发现 ${warnings} 个警告，但可以继续构建`);
    return true;
  } else {
    console.log('✅ 所有依赖检查通过！');
    return true;
  }
}

// 辅助函数: 提取版本号
function extractVersion(name, output) {
  // ... 版本提取逻辑
}

// 辅助函数: 版本比较
function isVersionSatisfied(version, minVersion) {
  // ... 版本比较逻辑
}

// 辅助函数: 获取安装命令
function getInstallCommand(name) {
  // ... 返回安装建议
}

// 辅助函数: 截断长字符串
function truncateString(str, maxLength) {
  if (str.length <= maxLength) return str;
  return str.substring(0, maxLength) + '...';
}

// 执行检查
if (require.main === module) {
  checkDependencies().catch(error => {
    console.error('依赖检查过程中出错:', error);
    process.exit(1);
  });
}

module.exports = { checkDependencies };
```

## 增量构建实现 (`build-proto.js`) 

该脚本演示了如何实现增量构建，只在源文件发生变化时才重新构建。

### 实现细节

```javascript
/**
 * Proto构建脚本示例实现
 * 文件: scripts/build-proto.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const config = require('../config/config-loader');

// 命令行参数解析
const args = process.argv.slice(2);
const targetLanguage = args[0]; // 'python', 'go', 'java' 等
const forceRebuild = args.includes('--force');

// 源文件和目标文件路径
const protoConfig = config.getProtoConfig('greeter');
const protoVersion = protoConfig.version;
const protoSourcePath = path.resolve(__dirname, '..', 'proto', protoVersion, 'greeter.proto');
const timestampFilePath = path.resolve(__dirname, '..', config.get(`build.timestampFiles.${targetLanguage}`));

/**
 * 检查是否需要重新构建
 * @param {string} sourcePath - 源文件路径
 * @param {string} timestampPath - 时间戳文件路径
 * @returns {boolean} - 是否需要重新构建
 */
function needsRebuild(sourcePath, timestampPath) {
  // 如果强制重建
  if (forceRebuild) {
    console.log('强制重建模式启用');
    return true;
  }
  
  // 如果时间戳文件不存在
  if (!fs.existsSync(timestampPath)) {
    console.log(`时间戳文件不存在: ${path.basename(timestampPath)}`);
    return true;
  }
  
  // 如果源文件不存在
  if (!fs.existsSync(sourcePath)) {
    console.error(`错误: 源文件不存在: ${sourcePath}`);
    process.exit(1);
  }
  
  // 比较文件修改时间
  const sourceTime = fs.statSync(sourcePath).mtimeMs;
  const timestampTime = fs.statSync(timestampPath).mtimeMs;
  
  if (sourceTime > timestampTime) {
    console.log(`源文件 ${path.basename(sourcePath)} 已更新，需要重新构建`);
    return true;
  }
  
  console.log(`源文件未变更，跳过构建`);
  return false;
}

/**
 * 更新时间戳文件
 * @param {string} timestampPath - 时间戳文件路径
 */
function updateTimestamp(timestampPath) {
  try {
    const timestamp = new Date().toISOString();
    fs.writeFileSync(timestampPath, timestamp, 'utf8');
    console.log(`更新时间戳文件: ${path.basename(timestampPath)}`);
  } catch (error) {
    console.warn(`警告: 无法更新时间戳文件: ${error.message}`);
  }
}

/**
 * 构建Python Proto文件
 */
function buildPythonProto() {
  console.log('开始构建Python Proto...');
  try {
    const backendDir = path.resolve(__dirname, '../backend');
    const cmd = `cd ${backendDir} && python -m grpc_tools.protoc -I. --python_out=. --grpc_python_out=. ./greeter.proto`;
    
    console.log(`执行命令: ${cmd}`);
    execSync(cmd, { stdio: 'inherit' });
    
    console.log('Python Proto构建成功');
    updateTimestamp(timestampFilePath);
    return true;
  } catch (error) {
    console.error('Python Proto构建失败:', error.message);
    return false;
  }
}

/**
 * 构建Go Proto文件
 */
function buildGoProto() {
  console.log('开始构建Go Proto...');
  try {
    const goBackendDir = path.resolve(__dirname, '../go-backend');
    const cmd = `cd ${goBackendDir} && protoc --go_out=. --go_opt=paths=source_relative --go-grpc_out=. --go-grpc_opt=paths=source_relative greeter.proto`;
    
    console.log(`执行命令: ${cmd}`);
    execSync(cmd, { stdio: 'inherit' });
    
    console.log('Go Proto构建成功');
    updateTimestamp(timestampFilePath);
    return true;
  } catch (error) {
    console.error('Go Proto构建失败:', error.message);
    return false;
  }
}

/**
 * 构建Java Proto文件
 * Java使用Maven插件生成，这里不直接调用protoc
 */
function buildJavaProto() {
  console.log('Java Proto通过Maven构建，跳过直接构建步骤');
  updateTimestamp(timestampFilePath);
  return true;
}

// 主构建函数
async function buildProto() {
  console.log(`Proto构建开始 - 目标语言: ${targetLanguage}`);
  
  // 检查是否需要重建
  if (!needsRebuild(protoSourcePath, timestampFilePath)) {
    console.log(`跳过${targetLanguage} Proto构建 - 文件未变更`);
    return true;
  }
  
  let success = false;
  
  // 根据目标语言选择构建函数
  switch (targetLanguage) {
    case 'python':
      success = buildPythonProto();
      break;
    case 'go':
      success = buildGoProto();
      break;
    case 'java':
      success = buildJavaProto();
      break;
    default:
      console.error(`错误: 不支持的目标语言: ${targetLanguage}`);
      process.exit(1);
  }
  
  if (success) {
    console.log(`${targetLanguage} Proto构建完成`);
    return true;
  } else {
    console.error(`${targetLanguage} Proto构建失败`);
    process.exit(1);
  }
}

// 执行构建
if (require.main === module) {
  if (!targetLanguage) {
    console.error('错误: 未指定目标语言');
    console.log('用法: node build-proto.js <language> [--force]');
    console.log('支持的语言: python, go, java');
    process.exit(1);
  }
  
  buildProto().catch(error => {
    console.error('构建过程中出错:', error);
    process.exit(1);
  });
}

module.exports = { buildProto };
```

## Proto文件同步脚本 (`sync-proto.js`)

该脚本实现了将中央Proto文件同步到各个服务的功能，包括必要的调整。

### 实现细节

```javascript
/**
 * Proto文件同步脚本示例实现
 * 文件: scripts/sync-proto.js
 */

const fs = require('fs');
const path = require('path');
const config = require('../config/config-loader');

// 定义目标同步位置
const targets = [
  { 
    path: '../backend/greeter.proto', 
    packageAdjust: false 
  },
  { 
    path: '../go-backend/greeter.proto', 
    packageAdjust: true, 
    goPackage: './pb' 
  },
  { 
    path: '../java-backend/src/main/proto/greeter.proto', 
    packageAdjust: false 
  },
  { 
    path: '../electron-app/src/proto/greeter.proto', 
    packageAdjust: false 
  }
];

/**
 * 同步Proto文件到目标位置
 */
async function syncProtoFiles() {
  console.log('开始同步Proto文件...');
  
  // 从配置中获取源文件信息
  const protoConfig = config.getProtoConfig('greeter');
  const protoVersion = protoConfig.version;
  const protoSourcePath = path.resolve(__dirname, '..', 'proto', protoVersion, 'greeter.proto');
  
  // 读取源文件内容
  let protoContent;
  try {
    protoContent = fs.readFileSync(protoSourcePath, 'utf8');
    console.log(`读取源文件: ${protoSourcePath}`);
  } catch (error) {
    console.error(`错误: 无法读取源Proto文件: ${error.message}`);
    process.exit(1);
  }
  
  // 同步到每个目标
  let successCount = 0;
  let failCount = 0;
  
  for (const target of targets) {
    try {
      let targetContent = protoContent;
      
      // 如果需要调整包名
      if (target.packageAdjust) {
        if (target.goPackage) {
          targetContent = targetContent.replace(
            /option go_package = "[^"]+"/g,
            `option go_package = "${target.goPackage}"`
          );
          console.log(`调整go_package为: ${target.goPackage}`);
        }
      }
      
      // 确保目标目录存在
      const targetPath = path.resolve(__dirname, target.path);
      const targetDir = path.dirname(targetPath);
      
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
        console.log(`创建目录: ${targetDir}`);
      }
      
      // 写入目标文件
      fs.writeFileSync(targetPath, targetContent, 'utf8');
      console.log(`✅ 同步到: ${target.path}`);
      successCount++;
    } catch (error) {
      console.error(`❌ 同步失败 ${target.path}: ${error.message}`);
      failCount++;
    }
  }
  
  // 总结
  console.log('\n同步完成:');
  console.log(`- 成功: ${successCount}/${targets.length}`);
  
  if (failCount > 0) {
    console.error(`- 失败: ${failCount}/${targets.length}`);
    return false;
  }
  
  return true;
}

// 执行同步
if (require.main === module) {
  syncProtoFiles().then(success => {
    if (success) {
      console.log('所有Proto文件同步成功');
    } else {
      console.error('Proto文件同步过程中发生错误');
      process.exit(1);
    }
  }).catch(error => {
    console.error('同步过程中出错:', error);
    process.exit(1);
  });
}

module.exports = { syncProtoFiles };
```

## 版本更新脚本 (`update-versions.js`)

该脚本用于统一更新所有组件的版本号，确保版本一致性。

### 实现细节

```javascript
/**
 * 版本更新脚本示例实现
 * 文件: scripts/update-versions.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const semver = require('semver');

// 命令行参数解析
const args = process.argv.slice(2);
const updateType = args.find(arg => ['--major', '--minor', '--patch'].includes(arg));
const setVersionArg = args.find(arg => arg.startsWith('--set='));
const setVersion = setVersionArg ? setVersionArg.split('=')[1] : null;

// 版本文件路径
const versionFilePath = path.resolve(__dirname, '../config/version.json');

/**
 * 读取当前版本信息
 */
function readVersionInfo() {
  try {
    const content = fs.readFileSync(versionFilePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`错误: 无法读取版本文件: ${error.message}`);
    process.exit(1);
  }
}

/**
 * 确定新版本号
 */
function determineNewVersion(currentVersion) {
  if (setVersion) {
    if (!semver.valid(setVersion)) {
      console.error(`错误: 指定的版本号 ${setVersion} 无效`);
      process.exit(1);
    }
    return setVersion;
  }
  
  switch (updateType) {
    case '--major':
      return semver.inc(currentVersion, 'major');
    case '--minor':
      return semver.inc(currentVersion, 'minor');
    case '--patch':
      return semver.inc(currentVersion, 'patch');
    default:
      console.error('错误: 未指定版本更新类型 (--major, --minor, --patch) 或具体版本 (--set=x.y.z)');
      process.exit(1);
  }
}

/**
 * 更新版本文件
 */
function updateVersionFile(newVersion) {
  try {
    const versionInfo = readVersionInfo();
    versionInfo.appVersion = newVersion;
    versionInfo.components.electron = newVersion;
    versionInfo.components.pythonBackend = newVersion;
    versionInfo.components.goBackend = newVersion;
    versionInfo.components.javaBackend = newVersion;
    versionInfo.updated = new Date().toISOString();
    
    // 更新兼容性矩阵
    const apiVersion = versionInfo.apiVersion;
    if (!versionInfo.compatibilityMatrix[apiVersion]) {
      versionInfo.compatibilityMatrix[apiVersion] = [];
    }
    if (!versionInfo.compatibilityMatrix[apiVersion].includes(newVersion)) {
      versionInfo.compatibilityMatrix[apiVersion].push(newVersion);
    }
    
    fs.writeFileSync(versionFilePath, JSON.stringify(versionInfo, null, 2), 'utf8');
    console.log(`✅ 已更新版本文件到 ${newVersion}`);
    return true;
  } catch (error) {
    console.error(`❌ 更新版本文件失败: ${error.message}`);
    return false;
  }
}

/**
 * 更新package.json文件
 */
function updatePackageJson(newVersion) {
  const packageJsonPath = path.resolve(__dirname, '../electron-app/package.json');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    packageJson.version = newVersion;
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');
    console.log(`✅ 已更新 package.json 到版本 ${newVersion}`);
    return true;
  } catch (error) {
    console.error(`❌ 更新 package.json 失败: ${error.message}`);
    return false;
  }
}

/**
 * 更新Maven POM文件
 */
function updatePomXml(newVersion) {
  const pomXmlPath = path.resolve(__dirname, '../java-backend/pom.xml');
  
  try {
    // 使用Maven命令更新版本
    const cmd = `cd ${path.dirname(pomXmlPath)} && mvn versions:set -DnewVersion=${newVersion} -DgenerateBackupPoms=false`;
    execSync(cmd, { stdio: 'inherit' });
    console.log(`✅ 已更新 pom.xml 到版本 ${newVersion}`);
    return true;
  } catch (error) {
    console.error(`❌ 更新 pom.xml 失败: ${error.message}`);
    return false;
  }
}

/**
 * 主版本更新函数
 */
async function updateVersions() {
  console.log('开始版本更新过程...');
  
  // 读取当前版本
  const versionInfo = readVersionInfo();
  const currentVersion = versionInfo.appVersion;
  console.log(`当前版本: ${currentVersion}`);
  
  // 确定新版本
  const newVersion = determineNewVersion(currentVersion);
  console.log(`更新到版本: ${newVersion}`);
  
  if (currentVersion === newVersion) {
    console.log('版本未变化，无需更新');
    return true;
  }
  
  // 更新各组件版本
  const results = [
    updateVersionFile(newVersion),
    updatePackageJson(newVersion),
    updatePomXml(newVersion)
  ];
  
  // 检查更新结果
  if (results.every(result => result)) {
    console.log(`\n✅ 所有组件成功更新到版本 ${newVersion}`);
    return true;
  } else {
    console.error('\n❌ 部分组件更新失败');
    return false;
  }
}

// 执行版本更新
if (require.main === module) {
  updateVersions().then(success => {
    if (!success) {
      process.exit(1);
    }
  }).catch(error => {
    console.error('版本更新过程中出错:', error);
    process.exit(1);
  });
}

module.exports = { updateVersions };
```

## 构建脚本集成

这些脚本通过`package.json`中的NPM脚本进行集成，形成完整的构建流程:

```json
{
  "scripts": {
    "check-deps": "node scripts/check-dependencies.js",
    "proto:sync": "node scripts/sync-proto.js",
    "proto:python": "npm run proto:sync && node scripts/build-proto.js python",
    "proto:go": "npm run proto:sync && node scripts/build-proto.js go",
    "proto:java": "npm run proto:sync && node scripts/build-proto.js java",
    "build:python:pex": "node scripts/build-python-pex.js",
    "build:go:app": "node scripts/build-go-app.js",
    "build:java:native": "node scripts/build-java-backend.js",
    "build:backend:python": "npm run proto:python && npm run build:python:pex",
    "build:backend:go": "npm run proto:go && npm run build:go:app",
    "build:backend:java": "npm run proto:java && npm run build:java:native",
    "build:backends": "npm run build:backend:python && npm run build:backend:java && npm run build:backend:go",
    "build:css": "tailwindcss -i ./src/input.css -o ./dist/output.css",
    "build:all": "npm run build:css && npm run build:backends",
    "fresh": "npm run check-deps && npm run build:all",
    "update-versions:major": "node scripts/update-versions.js --major",
    "update-versions:minor": "node scripts/update-versions.js --minor",
    "update-versions:patch": "node scripts/update-versions.js --patch"
  }
}
```

## 构建系统设计原则

1. **模块化**: 每个脚本专注于一个职责
2. **渐进式**: 依次执行构建步骤
3. **健壮性**: 详细的错误处理和日志
4. **高效性**: 增量构建避免不必要的工作
5. **可维护性**: 清晰的代码结构和注释

## 最佳实践

1. **使用统一配置**: 从`config.json`读取配置，避免硬编码
2. **详细日志**: 每个步骤输出清晰的状态和错误信息
3. **优雅失败**: 致命错误时停止并提供修复建议
4. **并行构建**: 适当时使用Promise.all并行处理任务
5. **版本一致性**: 使用`update-versions.js`确保所有组件版本一致

## 测试策略

1. **脚本级测试**: 为每个脚本创建单元测试
2. **集成测试**: 测试构建流程的端到端执行
3. **环境测试**: 在不同操作系统上验证兼容性
4. **边缘情况**: 测试缺失依赖、配置错误等情况
5. **性能基准**: 监控和优化构建时间
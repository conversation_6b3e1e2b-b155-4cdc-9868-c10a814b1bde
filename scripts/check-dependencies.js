#!/usr/bin/env node

/**
 * 依赖检查脚本
 * 检查构建所需的所有工具和环境是否正确安装
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class DependencyChecker {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.projectRoot = path.resolve(__dirname, '..');
  }

  log(message, type = 'info') {
    const colors = {
      info: '\x1b[36m',    // cyan
      success: '\x1b[32m', // green
      warning: '\x1b[33m', // yellow
      error: '\x1b[31m',   // red
      reset: '\x1b[0m'
    };
    
    console.log(`${colors[type]}[${type.toUpperCase()}]${colors.reset} ${message}`);
  }

  checkCommand(command, name, required = true) {
    try {
      execSync(`which ${command}`, { stdio: 'ignore' });
      this.log(`✓ ${name} 已安装`, 'success');
      return true;
    } catch (error) {
      const message = `✗ ${name} 未找到 (命令: ${command})`;
      if (required) {
        this.errors.push(message);
        this.log(message, 'error');
      } else {
        this.warnings.push(message);
        this.log(message, 'warning');
      }
      return false;
    }
  }

  checkVersion(command, name, minVersion = null) {
    try {
      const version = execSync(command, { encoding: 'utf8' }).trim();
      this.log(`✓ ${name}: ${version}`, 'success');
      
      if (minVersion && this.compareVersions(version, minVersion) < 0) {
        const message = `⚠ ${name} 版本过低，建议升级到 ${minVersion} 或更高版本`;
        this.warnings.push(message);
        this.log(message, 'warning');
      }
      return true;
    } catch (error) {
      const message = `✗ 无法获取 ${name} 版本信息`;
      this.errors.push(message);
      this.log(message, 'error');
      return false;
    }
  }

  checkDirectory(dirPath, name, required = true) {
    const fullPath = path.resolve(this.projectRoot, dirPath);
    if (fs.existsSync(fullPath)) {
      this.log(`✓ ${name} 目录存在: ${dirPath}`, 'success');
      return true;
    } else {
      const message = `✗ ${name} 目录不存在: ${dirPath}`;
      if (required) {
        this.errors.push(message);
        this.log(message, 'error');
      } else {
        this.warnings.push(message);
        this.log(message, 'warning');
      }
      return false;
    }
  }

  checkFile(filePath, name, required = true) {
    const fullPath = path.resolve(this.projectRoot, filePath);
    if (fs.existsSync(fullPath)) {
      this.log(`✓ ${name} 文件存在: ${filePath}`, 'success');
      return true;
    } else {
      const message = `✗ ${name} 文件不存在: ${filePath}`;
      if (required) {
        this.errors.push(message);
        this.log(message, 'error');
      } else {
        this.warnings.push(message);
        this.log(message, 'warning');
      }
      return false;
    }
  }

  compareVersions(version1, version2) {
    const v1 = version1.replace(/[^\d.]/g, '').split('.').map(Number);
    const v2 = version2.split('.').map(Number);
    
    for (let i = 0; i < Math.max(v1.length, v2.length); i++) {
      const a = v1[i] || 0;
      const b = v2[i] || 0;
      if (a > b) return 1;
      if (a < b) return -1;
    }
    return 0;
  }

  async checkAll() {
    this.log('开始检查项目依赖...', 'info');
    
    // 检查基础工具
    this.log('\n=== 基础工具检查 ===', 'info');
    this.checkCommand('node', 'Node.js');
    this.checkCommand('npm', 'npm');
    this.checkCommand('python3', 'Python 3');
    this.checkCommand('pip', 'pip');
    this.checkCommand('go', 'Go');
    this.checkCommand('java', 'Java');
    this.checkCommand('mvn', 'Maven');
    this.checkCommand('protoc', 'Protocol Buffers Compiler');
    
    // 检查版本
    this.log('\n=== 版本检查 ===', 'info');
    if (this.checkCommand('node', 'Node.js')) {
      this.checkVersion('node --version', 'Node.js', '16.0.0');
    }
    if (this.checkCommand('python3', 'Python')) {
      this.checkVersion('python3 --version', 'Python', '3.8.0');
    }
    if (this.checkCommand('go', 'Go')) {
      this.checkVersion('go version', 'Go', '1.19.0');
    }
    
    // 检查项目结构
    this.log('\n=== 项目结构检查 ===', 'info');
    this.checkDirectory('api-protos/v1', 'Proto文件目录');
    this.checkDirectory('backend', 'Python后端目录');
    this.checkDirectory('go-backend', 'Go后端目录');
    this.checkDirectory('java-backend', 'Java后端目录');
    this.checkDirectory('electron-app', 'Electron应用目录');
    this.checkDirectory('config', '配置目录');
    
    // 检查关键文件
    this.log('\n=== 关键文件检查 ===', 'info');
    this.checkFile('config/config.json', '主配置文件');
    this.checkFile('api-protos/v1/greeter/greeter.proto', 'Greeter Proto文件');
    this.checkFile('api-protos/v1/subtitler/subtitler.proto', 'Subtitler Proto文件');
    this.checkFile('backend/requirements.txt', 'Python依赖文件');
    this.checkFile('go-backend/go.mod', 'Go模块文件');
    this.checkFile('java-backend/pom.xml', 'Maven配置文件');
    this.checkFile('electron-app/package.json', 'Electron包配置文件');
    
    // 检查可选工具
    this.log('\n=== 可选工具检查 ===', 'info');
    this.checkCommand('conda', 'Conda', false);
    this.checkCommand('docker', 'Docker', false);
    this.checkCommand('git', 'Git', false);
    
    // 输出结果
    this.log('\n=== 检查结果 ===', 'info');
    
    if (this.errors.length > 0) {
      this.log(`发现 ${this.errors.length} 个错误:`, 'error');
      this.errors.forEach(error => this.log(`  ${error}`, 'error'));
    }
    
    if (this.warnings.length > 0) {
      this.log(`发现 ${this.warnings.length} 个警告:`, 'warning');
      this.warnings.forEach(warning => this.log(`  ${warning}`, 'warning'));
    }
    
    if (this.errors.length === 0) {
      this.log('✓ 所有必需的依赖都已满足！', 'success');
      return true;
    } else {
      this.log('✗ 请解决上述错误后再继续', 'error');
      return false;
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const checker = new DependencyChecker();
  checker.checkAll().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = DependencyChecker;

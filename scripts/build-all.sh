#!/bin/bash
# build-all.sh - 完整构建脚本
# 用于构建整个项目并生成可交付的应用包

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 未找到，请先安装"
        exit 1
    fi
}

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

log_info "🚀 开始完整构建流程..."
log_info "📁 项目根目录: $PROJECT_ROOT"

# 1. 检查构建环境
log_info "📋 检查构建环境..."
check_command "node"
check_command "npm"
check_command "python3"
check_command "pip"
check_command "go"
check_command "java"
check_command "mvn"

# 检查Node.js版本
NODE_VERSION=$(node --version | cut -d'v' -f2)
log_info "Node.js版本: $NODE_VERSION"

# 检查Python版本
PYTHON_VERSION=$(python3 --version | cut -d' ' -f2)
log_info "Python版本: $PYTHON_VERSION"

# 检查Go版本
GO_VERSION=$(go version | cut -d' ' -f3)
log_info "Go版本: $GO_VERSION"

# 2. 清理之前的构建
log_info "🧹 清理之前的构建..."
rm -rf dist/*
rm -rf electron-app/dist-electron/*
rm -rf electron-app/dist/*

# 3. 构建Protocol Buffers
log_info "🔧 构建Protocol Buffers..."
cd electron-app
if npm run build:proto; then
    log_success "Protocol Buffers构建成功"
else
    log_error "Protocol Buffers构建失败"
    exit 1
fi

# 4. 构建Python后端 (使用proto_sync.sh)
log_info "🐍 构建Python后端..."
cd "$PROJECT_ROOT"

if ./scripts/proto_sync.sh --python --force; then
    log_success "Python后端构建成功"
else
    log_error "Python后端构建失败"
    exit 1
fi

# 5. 构建Go后端 (使用proto_sync.sh)
log_info "🔷 构建Go后端..."
cd "$PROJECT_ROOT"

if ./scripts/proto_sync.sh --go --force; then
    log_success "Go后端构建成功"
else
    log_error "Go后端构建失败"
    exit 1
fi

# 6. 构建Java后端 (使用proto_sync.sh)
log_info "☕ 构建Java后端..."
cd "$PROJECT_ROOT"

if ./scripts/proto_sync.sh --java --force; then
    log_success "Java后端构建成功"
else
    log_warning "Java后端构建失败，但继续执行..."
    # 不退出，因为Java后端可能有环境问题
fi

# 5. 构建前端
log_info "🎨 构建前端应用..."
cd "$PROJECT_ROOT/electron-app"

# 安装前端依赖
log_info "安装前端依赖..."
npm install

# 构建CSS
log_info "构建CSS..."
if npm run build:css:minify; then
    log_success "CSS构建成功"
else
    log_warning "CSS构建失败，继续执行..."
fi

# 构建Vue.js应用
log_info "构建Vue.js应用..."
if npm run build:vite; then
    log_success "Vue.js应用构建成功"
else
    log_error "Vue.js应用构建失败"
    exit 1
fi

# 6. 验证后端文件
log_info "🔍 验证后端文件..."
cd "$PROJECT_ROOT"

if [ -f "dist/server.pex" ]; then
    log_success "✅ Python后端: server.pex"
else
    log_error "❌ Python后端缺失"
    exit 1
fi

if [ -f "dist/go_grpc_server" ]; then
    log_success "✅ Go后端: go_grpc_server"
else
    log_error "❌ Go后端缺失"
    exit 1
fi

# Java后端文件可能在dist/java-grpc-backend目录中
if [ -f "dist/java-grpc-backend.jar" ] || [ -f "dist/java-grpc-backend" ] || [ -d "dist/java-grpc-backend" ]; then
    log_success "✅ Java后端: java-grpc-backend"
else
    log_error "❌ Java后端缺失"
    exit 1
fi

# 7. 打包Electron应用
log_info "📦 打包Electron应用..."
cd "$PROJECT_ROOT/electron-app"

if npm run build:electron; then
    log_success "Electron应用打包成功"
else
    log_error "Electron应用打包失败"
    exit 1
fi

# 8. 显示构建结果
log_info "📊 构建结果:"
echo ""
log_info "后端服务文件:"
ls -la "$PROJECT_ROOT/dist/"
echo ""
log_info "Electron应用包:"
ls -la "$PROJECT_ROOT/electron-app/dist-electron/"

# 9. 获取文件大小信息
log_info "📏 文件大小信息:"
if [ -f "$PROJECT_ROOT/dist/server.pex" ]; then
    SIZE=$(du -h "$PROJECT_ROOT/dist/server.pex" | cut -f1)
    log_info "Python后端: $SIZE"
fi

if [ -f "$PROJECT_ROOT/dist/go_grpc_server" ]; then
    SIZE=$(du -h "$PROJECT_ROOT/dist/go_grpc_server" | cut -f1)
    log_info "Go后端: $SIZE"
fi

# 检查Java后端文件大小
if [ -f "$PROJECT_ROOT/dist/java-grpc-backend.jar" ]; then
    SIZE=$(du -h "$PROJECT_ROOT/dist/java-grpc-backend.jar" | cut -f1)
    log_info "Java后端: $SIZE"
elif [ -f "$PROJECT_ROOT/dist/java-grpc-backend" ]; then
    SIZE=$(du -h "$PROJECT_ROOT/dist/java-grpc-backend" | cut -f1)
    log_info "Java后端: $SIZE"
elif [ -d "$PROJECT_ROOT/dist/java-grpc-backend" ]; then
    SIZE=$(du -sh "$PROJECT_ROOT/dist/java-grpc-backend" | cut -f1)
    log_info "Java后端目录: $SIZE"
fi

# 计算总大小
TOTAL_SIZE=$(du -sh "$PROJECT_ROOT/electron-app/dist-electron/" 2>/dev/null | cut -f1 || echo "未知")
log_info "应用包总大小: $TOTAL_SIZE"

log_success "✅ 完整构建流程完成！"
log_info "🎉 应用包位置: $PROJECT_ROOT/electron-app/dist-electron/"

# 10. 提供下一步指导
echo ""
log_info "📋 下一步操作:"
log_info "1. 测试应用: 运行 ./scripts/verify-build.sh"
log_info "2. 安装应用: 打开 electron-app/dist-electron/ 中的安装包"
log_info "3. 分发应用: 将安装包分发给用户"
echo ""

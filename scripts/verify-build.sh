#!/bin/bash
# verify-build.sh - 构建验证脚本
# 用于验证构建结果的完整性和功能性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

log_info "🔍 开始验证构建结果..."

# 验证计数器
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 检查函数
check_file() {
    local file_path="$1"
    local description="$2"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -f "$file_path" ]; then
        local size=$(du -h "$file_path" | cut -f1)
        log_success "✅ $description: $file_path ($size)"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "❌ $description: $file_path (缺失)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

check_directory() {
    local dir_path="$1"
    local description="$2"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ -d "$dir_path" ]; then
        local count=$(find "$dir_path" -type f | wc -l)
        log_success "✅ $description: $dir_path ($count 个文件)"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "❌ $description: $dir_path (缺失)"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

test_executable() {
    local exec_path="$1"
    local description="$2"
    local timeout_duration="$3"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    if [ ! -f "$exec_path" ]; then
        log_error "❌ $description: 文件不存在"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
    
    # 测试可执行文件是否能启动
    log_info "测试 $description 启动..."
    
    if [[ "$exec_path" == *.jar ]]; then
        # Java应用测试
        timeout "$timeout_duration" java -jar "$exec_path" &
    else
        # 其他可执行文件测试
        timeout "$timeout_duration" "$exec_path" &
    fi
    
    local pid=$!
    sleep 2
    
    if kill -0 $pid 2>/dev/null; then
        kill $pid 2>/dev/null
        wait $pid 2>/dev/null || true
        log_success "✅ $description: 可正常启动"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        log_error "❌ $description: 启动失败"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# 1. 检查后端服务文件
log_info "📋 检查后端服务文件..."
check_file "dist/server.pex" "Python后端"
check_file "dist/go_grpc_server" "Go后端"
check_file "dist/java-grpc-backend.jar" "Java后端"

# 2. 检查前端构建文件
log_info "📋 检查前端构建文件..."
check_directory "electron-app/dist" "Vue.js构建目录"
check_directory "electron-app/dist-electron" "Electron构建目录"

# 3. 检查Electron应用包
log_info "📋 检查Electron应用包..."
if [ "$(uname)" == "Darwin" ]; then
    # macOS
    check_file "electron-app/dist-electron/字幕处理工具-1.0.0.dmg" "macOS安装包"
    check_directory "electron-app/dist-electron/mac" "macOS应用目录"
elif [ "$(expr substr $(uname -s) 1 5)" == "Linux" ]; then
    # Linux
    check_file "electron-app/dist-electron/字幕处理工具-1.0.0.AppImage" "Linux应用包"
    check_directory "electron-app/dist-electron/linux-unpacked" "Linux应用目录"
elif [ "$(expr substr $(uname -s) 1 10)" == "MINGW32_NT" ] || [ "$(expr substr $(uname -s) 1 10)" == "MINGW64_NT" ]; then
    # Windows
    check_file "electron-app/dist-electron/字幕处理工具 Setup 1.0.0.exe" "Windows安装包"
    check_directory "electron-app/dist-electron/win-unpacked" "Windows应用目录"
fi

# 4. 测试后端服务启动
log_info "📋 测试后端服务启动..."

if [ -f "dist/server.pex" ]; then
    test_executable "dist/server.pex" "Python后端服务" "5s"
fi

if [ -f "dist/go_grpc_server" ]; then
    test_executable "dist/go_grpc_server" "Go后端服务" "5s"
fi

if [ -f "dist/java-grpc-backend.jar" ]; then
    test_executable "dist/java-grpc-backend.jar" "Java后端服务" "10s"
fi

# 5. 检查文件权限
log_info "📋 检查文件权限..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [ -f "dist/server.pex" ] && [ -x "dist/server.pex" ]; then
    log_success "✅ Python后端文件权限正确"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_error "❌ Python后端文件权限错误"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if [ -f "dist/go_grpc_server" ] && [ -x "dist/go_grpc_server" ]; then
    log_success "✅ Go后端文件权限正确"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_error "❌ Go后端文件权限错误"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

# 6. 检查配置文件
log_info "📋 检查配置文件..."
check_file "config/config.json" "主配置文件"

# 7. 检查Protocol Buffers文件
log_info "📋 检查Protocol Buffers文件..."
check_file "api-protos/v1/greeter/greeter.proto" "Greeter Proto文件"
check_file "api-protos/v1/subtitler/subtitler.proto" "Subtitler Proto文件"

# 8. 检查生成的Proto代码
log_info "📋 检查生成的Proto代码..."
check_directory "electron-app/src/js/renderer-modules/api-protos" "前端Proto代码"
check_directory "backend/api_protos" "Python Proto代码"

# 9. 文件大小检查
log_info "📏 文件大小检查..."
if [ -f "dist/server.pex" ]; then
    SIZE=$(stat -f%z "dist/server.pex" 2>/dev/null || stat -c%s "dist/server.pex" 2>/dev/null || echo "0")
    if [ "$SIZE" -gt 10485760 ]; then  # 10MB
        log_success "✅ Python后端文件大小正常 ($(du -h dist/server.pex | cut -f1))"
    else
        log_warning "⚠️ Python后端文件可能过小 ($(du -h dist/server.pex | cut -f1))"
    fi
fi

# 10. 依赖检查
log_info "📋 检查运行时依赖..."
TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if command -v python3 &> /dev/null; then
    log_success "✅ Python3 运行时可用"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_warning "⚠️ Python3 运行时不可用 (PEX文件应该自包含)"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))  # PEX应该自包含，所以这不是错误
fi

TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n 1)
    log_success "✅ Java运行时可用: $JAVA_VERSION"
    PASSED_CHECKS=$((PASSED_CHECKS + 1))
else
    log_error "❌ Java运行时不可用 (Java后端需要JRE)"
    FAILED_CHECKS=$((FAILED_CHECKS + 1))
fi

# 11. 输出验证结果
echo ""
log_info "📊 验证结果统计:"
log_info "总检查项: $TOTAL_CHECKS"
log_success "通过: $PASSED_CHECKS"
log_error "失败: $FAILED_CHECKS"

# 计算成功率
SUCCESS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
log_info "成功率: $SUCCESS_RATE%"

echo ""
if [ $FAILED_CHECKS -eq 0 ]; then
    log_success "🎉 所有验证检查都通过了！构建质量良好。"
    echo ""
    log_info "📋 应用包信息:"
    if [ -d "electron-app/dist-electron" ]; then
        TOTAL_SIZE=$(du -sh "electron-app/dist-electron" | cut -f1)
        log_info "应用包总大小: $TOTAL_SIZE"
        log_info "应用包位置: $PROJECT_ROOT/electron-app/dist-electron/"
    fi
    echo ""
    log_info "🚀 可以安全地分发应用包给用户！"
    exit 0
elif [ $SUCCESS_RATE -ge 80 ]; then
    log_warning "⚠️ 大部分检查通过，但存在一些问题。"
    log_warning "建议修复失败的检查项后再分发。"
    exit 1
else
    log_error "❌ 验证失败！存在严重问题，不建议分发。"
    log_error "请修复失败的检查项后重新构建。"
    exit 1
fi

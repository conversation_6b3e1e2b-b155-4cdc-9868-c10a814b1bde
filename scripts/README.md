# 构建系统文档

本文档详细描述了项目的构建系统、自动化脚本和工作流程。

## 目录结构

```
scripts/
├── check-dependencies.js     # 依赖检查脚本
├── sync-proto.js             # Proto文件同步脚本
├── build-proto.js            # Proto编译脚本
├── build-python-pex.js       # Python打包脚本
├── build-go-app.js           # Go构建脚本
├── build-java-backend.js     # Java构建脚本
├── update-versions.js        # 版本同步工具
├── check-service-readiness.js  # 服务就绪检查
└── README.md                 # 本文档
```

## 构建流程概述

```mermaid
flowchart TD
    A[npm run fresh] --> B[检查依赖 \ncheck-dependencies.js]
    B --> C{依赖检查通过?}
    C -->|否| D[显示错误并退出]
    C -->|是| E[同步Proto文件 \nsync-proto.js]
    E --> F[并行构建所有后端]
    F --> G1[构建Python后端]
    F --> G2[构建Go后端]
    F --> G3[构建Java后端]
    G1 --> H1[编译Python Proto]
    H1 --> I1[构建PEX包]
    G2 --> H2[编译Go Proto]
    H2 --> I2[构建Go二进制文件]
    G3 --> H3[编译Java Proto via Maven]
    H3 --> I3[构建Java二进制文件]
    I1 --> J[构建完成]
    I2 --> J
    I3 --> J
```

## 依赖检查系统

`check-dependencies.js`脚本负责检查所有必要的构建依赖：

1. **检查命令行工具**：
   - Node.js和npm
   - Python和pip
   - Go和必要的Go工具
   - Java和Maven
   - Protobuf编译器(protoc)
   - 其他特定工具(如GraalVM native-image)

2. **检查环境变量**：
   - GOPATH、JAVA_HOME等

3. **检查文件结构**：
   - 必要的目录结构
   - 关键配置文件

4. **版本兼容性检查**：
   - 确保各工具版本相互兼容

### 使用方式

```bash
# 单独运行依赖检查
npm run check-deps

# 作为构建流程的一部分
npm run fresh  # 会自动运行check-deps
```

### 错误处理

- **致命错误**：缺少必要依赖时，脚本会停止并显示错误信息
- **警告**：对于非关键问题，会显示警告但允许继续
- **修复建议**：为常见问题提供解决方案

## 增量构建系统

为提高构建效率，项目支持增量构建：

1. **时间戳追踪**：
   - 每个构建步骤都会生成时间戳文件(`.build-timestamp-{component}`)
   - 构建脚本比较源文件和时间戳文件的修改时间

2. **增量构建逻辑**：
   ```javascript
   function needsRebuild(sourceFiles, timestampFile) {
     if (!fs.existsSync(timestampFile)) return true;
     
     const buildTime = fs.statSync(timestampFile).mtimeMs;
     
     for (const file of sourceFiles) {
       if (fs.statSync(file).mtimeMs > buildTime) return true;
     }
     
     return false;
   }
   ```

3. **强制完全构建**：
   ```bash
   # 强制完全重新构建所有组件
   npm run fresh -- --force
   
   # 强制重建特定组件
   npm run build:backend:python -- --force
   ```

## Proto文件管理

`sync-proto.js`脚本实现Proto文件的集中管理和同步：

1. **读取主Proto文件**：从`proto/v*/`目录
2. **根据目标进行调整**：按需修改包名等
3. **写入目标位置**：同步到各服务目录
4. **验证一致性**：确保关键字段一致

### 目标位置

| 服务 | 目标路径 | 特殊处理 |
|------|----------|----------|
| Python | backend/greeter.proto | 无 |
| Go | go-backend/greeter.proto | 修改go_package |
| Java | java-backend/src/main/proto/greeter.proto | 无 |
| Electron | electron-app/src/proto/greeter.proto | 无 |

## 错误处理与日志

构建脚本采用统一的错误处理和日志系统：

1. **统一日志格式**：
   ```javascript
   function log(level, component, message) {
     const timestamp = new Date().toISOString();
     console.log(`[${timestamp}] [${level}] [${component}] ${message}`);
   }
   ```

2. **错误级别**：
   - `INFO`: 普通信息
   - `WARNING`: 非致命警告
   - `ERROR`: 致命错误
   - `SUCCESS`: 成功完成

3. **详细模式**：
   ```bash
   npm run build:all -- --verbose
   ```

4. **日志文件**：
   - 所有构建日志保存到`build.log`
   - 错误日志单独保存到`error.log`

## 版本管理系统

`update-versions.js`工具确保所有组件使用一致的版本号：

1. **从主版本文件读取**：`version.json`
2. **更新各组件版本**：
   - package.json
   - pom.xml
   - go.mod
   - Python版本文件
3. **版本兼容性检查**：确保API版本与应用版本兼容

### 例子：更新版本

```bash
# 更新主版本号
node scripts/update-versions.js --major

# 更新次版本号
node scripts/update-versions.js --minor

# 更新修订号
node scripts/update-versions.js --patch

# 设置特定版本
node scripts/update-versions.js --set 2.1.0
```

## 常见问题及解决方案

### 1. 依赖检查失败

**问题**: `check-dependencies.js`报告缺少依赖。

**解决方案**:
- 遵循错误消息提供的安装说明
- 对于特定环境问题，参考项目README中的安装指南

### 2. Proto同步问题

**问题**: Proto文件同步失败或不一致。

**解决方案**:
- 检查文件权限
- 确保没有手动修改目标文件
- 使用`--force`标志强制同步

### 3. 构建超时

**问题**: 构建过程超时，尤其是Java native-image构建。

**解决方案**:
- 增加超时设置: `npm run build:backend:java -- --timeout 300`
- 分配更多内存: `export MAVEN_OPTS="-Xmx4g"`

### 4. 增量构建不工作

**问题**: 系统总是执行完整构建而非增量构建。

**解决方案**:
- 检查时间戳文件是否存在且有正确权限
- 确保系统时钟准确
- 删除`.build-timestamp-*`文件强制完整重建

## 最佳实践

1. **定期完整构建**：即使有增量构建，也应定期执行完整构建
2. **提交前构建**：在提交代码前执行`npm run fresh`确保一切正常
3. **保持依赖更新**：定期更新依赖以避免安全和兼容性问题
4. **检查构建日志**：即使构建成功，也要检查日志中的警告
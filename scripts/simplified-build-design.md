# 简化构建脚本设计方案

## 问题分析

当前的`proto_sync.sh`脚本存在以下问题：
1. 过于复杂（400+行）
2. 功能耦合度高
3. 错误处理不够完善
4. 缺少增量构建支持

## 解决方案

### 1. 分离构建脚本职责

将单一的大脚本拆分为多个专门的脚本：

```
scripts/
├── build/
│   ├── build-proto.js          # Proto文件编译
│   ├── build-python.js         # Python后端构建
│   ├── build-go.js             # Go后端构建
│   ├── build-java.js           # Java后端构建
│   └── build-electron.js       # Electron前端构建
├── utils/
│   ├── logger.js               # 统一日志工具
│   ├── file-utils.js           # 文件操作工具
│   └── timestamp-utils.js      # 时间戳管理
└── main-build.js               # 主构建脚本
```

### 2. 改进package.json脚本

```json
{
  "scripts": {
    "start": "electron .",
    "dev": "npm run build:check && electron .",
    "build:check": "node scripts/build/check-dependencies.js",
    "build:proto": "node scripts/build/build-proto.js",
    "build:python": "node scripts/build/build-python.js",
    "build:go": "node scripts/build/build-go.js", 
    "build:java": "node scripts/build/build-java.js",
    "build:electron": "node scripts/build/build-electron.js",
    "build:all": "node scripts/main-build.js --all",
    "build:incremental": "node scripts/main-build.js --incremental",
    "clean": "node scripts/clean.js",
    "fresh": "npm run clean && npm run build:all"
  }
}
```

### 3. 核心构建逻辑示例

#### main-build.js (简化版)
```javascript
const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const logger = require('./utils/logger');
const config = require('../config/config.json');

class BuildManager {
  constructor() {
    this.config = config;
    this.projectRoot = path.resolve(__dirname, '..');
  }

  async buildAll() {
    const steps = [
      { name: 'Proto', script: './build/build-proto.js' },
      { name: 'Python', script: './build/build-python.js' },
      { name: 'Go', script: './build/build-go.js' },
      { name: 'Java', script: './build/build-java.js' },
      { name: 'Electron', script: './build/build-electron.js' }
    ];

    for (const step of steps) {
      try {
        logger.info(`开始构建 ${step.name}...`);
        execSync(`node ${step.script}`, { 
          stdio: 'inherit',
          cwd: __dirname 
        });
        logger.success(`${step.name} 构建完成`);
      } catch (error) {
        logger.error(`${step.name} 构建失败:`, error.message);
        if (this.config.build.errorHandling.stopOnError) {
          process.exit(1);
        }
      }
    }
  }

  async buildIncremental() {
    // 检查时间戳，只构建有变化的部分
    const timestampUtils = require('./utils/timestamp-utils');
    
    if (timestampUtils.needsRebuild('proto')) {
      await this.buildProto();
    }
    
    if (timestampUtils.needsRebuild('python')) {
      await this.buildPython();
    }
    
    // ... 其他服务的增量检查
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const buildManager = new BuildManager();

if (args.includes('--all')) {
  buildManager.buildAll();
} else if (args.includes('--incremental')) {
  buildManager.buildIncremental();
} else {
  console.log('用法: node main-build.js [--all|--incremental]');
}
```

### 4. 优势

1. **模块化**：每个构建步骤独立，易于维护
2. **可复用**：工具函数可在多个脚本中使用
3. **增量构建**：只构建有变化的部分，提高效率
4. **错误处理**：统一的错误处理和日志记录
5. **配置驱动**：通过config.json控制构建行为

### 5. 迁移步骤

1. 创建新的脚本目录结构
2. 将现有脚本功能拆分到对应模块
3. 更新package.json脚本
4. 测试新构建流程
5. 删除旧的proto_sync.sh脚本

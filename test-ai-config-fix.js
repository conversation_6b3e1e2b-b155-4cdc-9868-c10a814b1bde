#!/usr/bin/env node

/**
 * Test script to verify AI Config Service fixes
 * This script can be run to test the gRPC connection and service availability
 */

const path = require('path');

// Simulate the gRPC client initialization
async function testGrpcClientInitialization() {
  console.log('Testing gRPC client initialization...');
  
  try {
    // Import the gRPC client manager
    const { initializeGrpcServices } = require('./electron-app/src/node-grpc-client');
    
    // Define service configs (same as in grpc-manager.js)
    const serviceConfigs = [
      {
        name: 'aiConfigService',
        packageName: 'v1.ai_config',
        serviceName: 'AIConfigurationService',
        serverUrl: 'localhost:50051',
        protoPath: path.join(__dirname, 'api-protos', 'v1', 'ai_config', 'ai_config_service.proto'),
      },
    ];
    
    console.log('Initializing gRPC services...');
    const manager = await initializeGrpcServices(serviceConfigs);
    
    console.log('Getting AI Config Service client...');
    const client = manager.getClient('aiConfigService');
    
    if (client) {
      console.log('✅ AI Config Service client obtained successfully');
      console.log('Client methods:', Object.keys(client));
      
      // Check for the required method
      if (typeof client.updateAIConfigurations === 'function') {
        console.log('✅ updateAIConfigurations method found (lowercase)');
      } else if (typeof client.UpdateAIConfigurations === 'function') {
        console.log('✅ UpdateAIConfigurations method found (uppercase)');
      } else {
        console.log('❌ Required gRPC method not found');
      }
    } else {
      console.log('❌ Failed to get AI Config Service client');
    }
    
  } catch (error) {
    console.error('❌ Error during gRPC client initialization:', error.message);
  }
}

// Test the retry mechanism
async function testRetryMechanism() {
  console.log('\nTesting retry mechanism...');
  
  let retryCount = 0;
  const maxRetries = 3;
  const retryDelay = 1000; // 1 second for testing
  
  const mockSaveFunction = async (configs, currentRetry = 0) => {
    console.log(`Attempt ${currentRetry + 1}/${maxRetries + 1}`);
    
    // Simulate failure for first 2 attempts
    if (currentRetry < 2) {
      const error = new Error('gRPC client or method not available for AIConfigurationService.');
      console.log(`❌ Simulated failure: ${error.message}`);
      
      if (currentRetry < maxRetries) {
        console.log(`Retrying in ${retryDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        return await mockSaveFunction(configs, currentRetry + 1);
      } else {
        throw error;
      }
    }
    
    // Simulate success on 3rd attempt
    console.log('✅ Simulated success');
    return { success: true, message: 'Configurations saved successfully' };
  };
  
  try {
    const result = await mockSaveFunction([{ test: 'config' }]);
    console.log('Final result:', result);
  } catch (error) {
    console.error('Final error:', error.message);
  }
}

// Main test function
async function runTests() {
  console.log('🧪 Starting AI Config Service Fix Tests\n');
  
  await testGrpcClientInitialization();
  await testRetryMechanism();
  
  console.log('\n✅ Tests completed');
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testGrpcClientInitialization,
  testRetryMechanism,
  runTests
};

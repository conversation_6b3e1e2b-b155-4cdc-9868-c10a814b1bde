# 🔧 后端开发指南

## 📋 **技术栈概览**

### **多语言后端架构**
- **Python后端** (主要服务): 端口50051
  - gRPC服务框架
  - 音视频处理 (FFmpeg)
  - 语音识别 (Whisper)
  - 翻译服务集成
- **Go后端** (扩展服务): 端口50052
  - 高性能gRPC服务
  - 并发处理优化
- **Java后端** (扩展服务): 端口50053
  - Spring Boot + gRPC
  - 企业级服务架构

### **核心技术**
- **gRPC**: 高性能RPC框架
- **Protocol Buffers**: 数据序列化
- **FFmpeg**: 音视频处理
- **Whisper**: 语音识别
- **OpenAI/Gemini API**: 翻译服务

## 🚀 **快速开始**

### **Python后端启动**
```bash
# 1. 安装Python 3.9+
# 2. 安装依赖
cd backend
pip install -r requirements.txt

# 3. 启动服务
python server.py

# 或使用PEX可执行文件
cd dist
./server.pex
```

### **Go后端启动**
```bash
# 1. 安装Go 1.19+
# 2. 安装依赖
cd go-backend
go mod tidy

# 3. 启动服务
go run server.go

# 或使用编译后的可执行文件
cd dist
./go_grpc_server
```

### **Java后端启动**
```bash
# 1. 安装Java 17+
# 2. 构建项目
cd java-backend
mvn clean package

# 3. 启动服务
mvn spring-boot:run

# 或使用编译后的可执行文件
cd dist
./java-grpc-backend
```

## 📁 **后端项目结构**

### **Python后端结构**
```
backend/
├── server.py                     # gRPC服务器主入口
├── subtitle_service.py           # 字幕处理服务实现
├── subtitle_processor.py         # 字幕处理核心逻辑
├── api_protos/                   # Protocol Buffers生成代码
│   └── v1/
├── requirements.txt              # Python依赖
├── requirements-dev.txt          # 开发依赖
├── work-dir/                     # 工作目录
├── outputs/                      # 输出文件
└── tests/                        # 测试文件
```

### **Go后端结构**
```
go-backend/
├── server.go                     # Go gRPC服务器
├── gen/                          # Protocol Buffers生成代码
├── go.mod                        # Go模块配置
├── go.sum                        # 依赖锁定文件
└── github.com/                   # 第三方依赖
```

### **Java后端结构**
```
java-backend/
├── src/
│   └── main/
│       ├── java/
│       │   └── com/monkeyfx/javagrpc/
│       │       ├── JavaGrpcBackendApplication.java
│       │       └── GreeterServiceImpl.java
│       └── resources/
├── target/                       # 编译输出
├── pom.xml                       # Maven配置
└── README.md
```

## 🔧 **Python后端详解**

### **核心服务实现**

#### **gRPC服务器配置**
```python
# backend/server.py
import grpc
from concurrent import futures
import logging
from api_protos.v1.subtitler import subtitler_pb2_grpc
from subtitle_service import SubtitlerService

def serve():
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建gRPC服务器
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))
    
    # 注册服务
    subtitler_pb2_grpc.add_SubtitlerServicer_to_server(
        SubtitlerService(), server
    )
    
    # 启动服务器
    listen_addr = '[::]:50051'
    server.add_insecure_port(listen_addr)
    server.start()
    
    logging.info(f"Python gRPC server started and listening on port 50051.")
    server.wait_for_termination()

if __name__ == '__main__':
    serve()
```

#### **字幕服务实现**
```python
# backend/subtitle_service.py
import grpc
from api_protos.v1.subtitler import subtitler_pb2_grpc, subtitler_pb2
from subtitle_processor import SubtitleProcessor

class SubtitlerService(subtitler_pb2_grpc.SubtitlerServicer):
    def __init__(self):
        self.processor = SubtitleProcessor()
    
    def FullWorkflow(self, request, context):
        """完整工作流处理"""
        try:
            workflow_type = request.workflow_type
            
            if workflow_type == 'vid_to_audio':
                return self._process_video_to_audio(request)
            elif workflow_type == 'audio_to_text':
                return self._process_audio_to_text(request)
            elif workflow_type == 'text_to_srt':
                return self._process_text_to_srt(request)
            elif workflow_type == 'text_to_translated_srt':
                return self._process_text_to_translated_srt(request)
            else:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f'Unsupported workflow type: {workflow_type}')
                return subtitler_pb2.FullWorkflowResponse()
                
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return subtitler_pb2.FullWorkflowResponse()
    
    def _process_video_to_audio(self, request):
        """视频转音频处理"""
        video_path = request.file_path
        audio_path = self.processor.convert_video_to_audio(video_path)
        
        return subtitler_pb2.FullWorkflowResponse(
            video_to_audio_response=subtitler_pb2.VideoToAudioResponse(
                audio_path=audio_path
            )
        )
    
    def _process_audio_to_text(self, request):
        """音频转文字处理"""
        audio_path = request.file_path
        transcript, segments = self.processor.transcribe_audio(
            audio_path, 
            request.request_word_timestamps
        )
        
        # 构建响应
        pb_segments = []
        for segment in segments:
            pb_segments.append(subtitler_pb2.TranscriptSegment(
                text=segment['text'],
                start_time_ms=int(segment['start'] * 1000),
                end_time_ms=int(segment['end'] * 1000)
            ))
        
        return subtitler_pb2.FullWorkflowResponse(
            audio_to_text_response=subtitler_pb2.AudioToTextResponse(
                transcript=transcript,
                segments=pb_segments
            )
        )
    
    def _process_text_to_srt(self, request):
        """文本转SRT处理"""
        text_content = request.req_text_content
        srt_content = self.processor.generate_srt_from_text(text_content)
        
        return subtitler_pb2.FullWorkflowResponse(
            text_to_srt_response=subtitler_pb2.TextToSrtResponse(
                srt_content=srt_content
            )
        )
    
    def _process_text_to_translated_srt(self, request):
        """文本翻译并转SRT处理"""
        text_content = request.req_text_content
        target_language = request.target_language
        
        # 翻译文本
        translated_text = self.processor.translate_text(
            text_content, 
            target_language
        )
        
        # 生成SRT
        srt_content = self.processor.generate_srt_from_text(translated_text)
        
        return subtitler_pb2.FullWorkflowResponse(
            text_to_translated_srt_response=subtitler_pb2.TextToTranslatedSrtResponse(
                translated_srt_content=srt_content,
                original_text=text_content,
                translated_text=translated_text
            )
        )
```

#### **字幕处理核心逻辑**
```python
# backend/subtitle_processor.py
import os
import subprocess
import whisper
import openai
from typing import List, Dict, Tuple
import logging

class SubtitleProcessor:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.whisper_model = None
        self.work_dir = "work-dir"
        self.output_dir = "outputs"
        
        # 确保目录存在
        os.makedirs(self.work_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
    
    def convert_video_to_audio(self, video_path: str) -> str:
        """视频转音频"""
        try:
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(video_path))[0]
            audio_path = os.path.join(self.work_dir, f"{base_name}.wav")
            
            # 使用FFmpeg转换
            cmd = [
                'ffmpeg', '-i', video_path,
                '-vn', '-acodec', 'pcm_s16le',
                '-ar', '16000', '-ac', '1',
                '-y', audio_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception(f"FFmpeg error: {result.stderr}")
            
            self.logger.info(f"Video converted to audio: {audio_path}")
            return audio_path
            
        except Exception as e:
            self.logger.error(f"Video to audio conversion failed: {e}")
            raise
    
    def transcribe_audio(self, audio_path: str, word_timestamps: bool = False) -> Tuple[str, List[Dict]]:
        """音频转文字"""
        try:
            # 加载Whisper模型
            if self.whisper_model is None:
                self.whisper_model = whisper.load_model("base")
            
            # 转录音频
            result = self.whisper_model.transcribe(
                audio_path,
                word_timestamps=word_timestamps
            )
            
            transcript = result["text"]
            segments = result.get("segments", [])
            
            self.logger.info(f"Audio transcribed: {len(segments)} segments")
            return transcript, segments
            
        except Exception as e:
            self.logger.error(f"Audio transcription failed: {e}")
            raise
    
    def generate_srt_from_text(self, text: str) -> str:
        """从文本生成SRT格式字幕"""
        try:
            # 简单的句子分割逻辑
            sentences = self._split_into_sentences(text)
            
            srt_content = ""
            for i, sentence in enumerate(sentences, 1):
                start_time = (i - 1) * 3  # 每句3秒
                end_time = i * 3
                
                srt_content += f"{i}\n"
                srt_content += f"{self._format_time(start_time)} --> {self._format_time(end_time)}\n"
                srt_content += f"{sentence.strip()}\n\n"
            
            return srt_content
            
        except Exception as e:
            self.logger.error(f"SRT generation failed: {e}")
            raise
    
    def translate_text(self, text: str, target_language: str) -> str:
        """翻译文本"""
        try:
            # 使用OpenAI API翻译
            client = openai.OpenAI()
            
            response = client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {
                        "role": "system",
                        "content": f"Translate the following text to {target_language}. Keep the original formatting and structure."
                    },
                    {
                        "role": "user",
                        "content": text
                    }
                ]
            )
            
            translated_text = response.choices[0].message.content
            self.logger.info(f"Text translated to {target_language}")
            return translated_text
            
        except Exception as e:
            self.logger.error(f"Translation failed: {e}")
            raise
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """将文本分割成句子"""
        import re
        # 简单的句子分割正则表达式
        sentences = re.split(r'[.!?]+', text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _format_time(self, seconds: int) -> str:
        """格式化时间为SRT格式"""
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        secs = seconds % 60
        return f"{hours:02d}:{minutes:02d}:{secs:02d},000"
```

### **依赖管理**
```txt
# backend/requirements.txt
grpcio==1.60.0
grpcio-tools==1.60.0
protobuf==4.25.1
openai==1.3.0
whisper==1.1.10
ffmpeg-python==0.2.0
torch==2.1.0
numpy==1.24.3
```

### **环境变量配置**
```bash
# .env文件
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
GRPC_PORT=50051
LOG_LEVEL=INFO
WORK_DIR=work-dir
OUTPUT_DIR=outputs
```

## 🔧 **Go后端详解**

### **Go服务实现**
```go
// go-backend/server.go
package main

import (
    "context"
    "log"
    "net"
    
    "google.golang.org/grpc"
    pb "github.com/your-org/api-protos/v1/greeter"
)

type server struct {
    pb.UnimplementedGreeterServer
}

func (s *server) SayHello(ctx context.Context, in *pb.HelloRequest) (*pb.HelloReply, error) {
    log.Printf("Received: %v", in.GetName())
    return &pb.HelloReply{
        Message:         fmt.Sprintf("Hello %s from Go Server!", in.GetName()),
        OriginalRequest: in.GetName(),
        ServerId:        "Go Server",
        Timestamp:       time.Now().Format(time.RFC3339),
    }, nil
}

func main() {
    lis, err := net.Listen("tcp", ":50052")
    if err != nil {
        log.Fatalf("failed to listen: %v", err)
    }
    
    s := grpc.NewServer()
    pb.RegisterGreeterServer(s, &server{})
    
    log.Printf("Go gRPC server successfully started and listening at %v", lis.Addr())
    if err := s.Serve(lis); err != nil {
        log.Fatalf("failed to serve: %v", err)
    }
}
```

### **Go模块配置**
```go
// go-backend/go.mod
module go-grpc-backend

go 1.19

require (
    google.golang.org/grpc v1.60.0
    google.golang.org/protobuf v1.31.0
)

require (
    golang.org/x/net v0.16.0 // indirect
    golang.org/x/sys v0.13.0 // indirect
    golang.org/x/text v0.13.0 // indirect
    google.golang.org/genproto/googleapis/rpc v0.0.0-20231002182017-d307bd883b97 // indirect
)
```

## 🔧 **Java后端详解**

### **Spring Boot配置**
```java
// java-backend/src/main/java/com/monkeyfx/javagrpc/JavaGrpcBackendApplication.java
package com.monkeyfx.javagrpc;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class JavaGrpcBackendApplication {
    public static void main(String[] args) {
        SpringApplication.run(JavaGrpcBackendApplication.class, args);
    }
}
```

### **gRPC服务实现**
```java
// java-backend/src/main/java/com/monkeyfx/javagrpc/GreeterServiceImpl.java
package com.monkeyfx.javagrpc;

import io.grpc.stub.StreamObserver;
import net.devh.boot.grpc.server.service.GrpcService;
import monkeyfx.api.v1.greeter.GreeterGrpc;
import monkeyfx.api.v1.greeter.Greeter.HelloRequest;
import monkeyfx.api.v1.greeter.Greeter.HelloReply;

import java.time.Instant;

@GrpcService
public class GreeterServiceImpl extends GreeterGrpc.GreeterImplBase {
    
    @Override
    public void sayHello(HelloRequest request, StreamObserver<HelloReply> responseObserver) {
        String name = request.getName();
        
        HelloReply reply = HelloReply.newBuilder()
                .setMessage("Hello " + name + " from Java Server!")
                .setOriginalRequest(name)
                .setServerId("Java Server")
                .setTimestamp(Instant.now().toString())
                .build();
        
        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }
}
```

### **Maven配置**
```xml
<!-- java-backend/pom.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    
    <groupId>com.monkeyfx</groupId>
    <artifactId>java-grpc-backend</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.0</version>
        <relativePath/>
    </parent>
    
    <properties>
        <java.version>17</java.version>
        <grpc.version>1.60.0</grpc.version>
    </properties>
    
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        
        <dependency>
            <groupId>net.devh</groupId>
            <artifactId>grpc-server-spring-boot-starter</artifactId>
            <version>2.15.0.RELEASE</version>
        </dependency>
        
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-protobuf</artifactId>
            <version>${grpc.version}</version>
        </dependency>
        
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-stub</artifactId>
            <version>${grpc.version}</version>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
```

## 🔧 **Protocol Buffers定义**

### **API定义结构**
```
api-protos/
└── v1/
    ├── common/
    │   └── common.proto          # 通用数据类型
    ├── greeter/
    │   └── greeter.proto         # 问候服务定义
    └── subtitler/
        └── subtitler.proto       # 字幕服务定义
```

### **字幕服务Proto定义**
```protobuf
// api-protos/v1/subtitler/subtitler.proto
syntax = "proto3";

package monkeyfx.api.v1.subtitler;

option go_package = "github.com/monkeyfx/api-protos/v1/subtitler";

// 字幕处理服务
service Subtitler {
  // 完整工作流处理
  rpc FullWorkflow(FullWorkflowRequest) returns (FullWorkflowResponse);
  
  // 流式处理 (用于实时进度更新)
  rpc StreamWorkflow(FullWorkflowRequest) returns (stream WorkflowProgress);
}

// 完整工作流请求
message FullWorkflowRequest {
  string workflow_type = 1;           // 工作流类型
  string file_path = 2;               // 文件路径
  string req_text_content = 3;        // 请求文本内容
  string target_language = 4;         // 目标语言
  bool request_word_timestamps = 5;   // 是否请求词级时间戳
}

// 完整工作流响应
message FullWorkflowResponse {
  oneof response {
    VideoToAudioResponse video_to_audio_response = 1;
    AudioToTextResponse audio_to_text_response = 2;
    TextToSrtResponse text_to_srt_response = 3;
    TextToTranslatedSrtResponse text_to_translated_srt_response = 4;
  }
}

// 视频转音频响应
message VideoToAudioResponse {
  string audio_path = 1;
}

// 音频转文字响应
message AudioToTextResponse {
  string transcript = 1;
  repeated TranscriptSegment segments = 2;
}

// 转录片段
message TranscriptSegment {
  string text = 1;
  int64 start_time_ms = 2;
  int64 end_time_ms = 3;
}

// 文本转SRT响应
message TextToSrtResponse {
  string srt_content = 1;
}

// 文本翻译转SRT响应
message TextToTranslatedSrtResponse {
  string translated_srt_content = 1;
  string original_text = 2;
  string translated_text = 3;
}

// 工作流进度 (流式响应)
message WorkflowProgress {
  string stage_name = 1;
  int32 percentage = 2;
  string message = 3;
  bool is_error = 4;
  string error_message = 5;
}
```

## 🔧 **开发最佳实践**

### **错误处理**
```python
# Python错误处理
try:
    result = process_audio(audio_path)
except FileNotFoundError:
    context.set_code(grpc.StatusCode.NOT_FOUND)
    context.set_details("Audio file not found")
except Exception as e:
    context.set_code(grpc.StatusCode.INTERNAL)
    context.set_details(f"Internal error: {str(e)}")
```

### **日志记录**
```python
# 统一日志配置
import logging

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('subtitler.log'),
        logging.StreamHandler()
    ]
)
```

### **性能优化**
```python
# 异步处理
import asyncio
from concurrent.futures import ThreadPoolExecutor

class SubtitlerService:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    async def process_async(self, request):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor, 
            self.process_sync, 
            request
        )
```

## 📦 **构建和部署**

### **Python PEX构建**
```bash
# 构建PEX可执行文件
pip install pex
pex -r requirements.txt -e server:serve -o dist/server.pex
```

### **Go编译**
```bash
# 编译Go服务
cd go-backend
go build -o ../dist/go_grpc_server server.go
```

### **Java打包**
```bash
# Maven打包
cd java-backend
mvn clean package
cp target/java-grpc-backend-1.0.0.jar ../dist/java-grpc-backend
```

### **Docker部署**
```dockerfile
# Dockerfile for Python backend
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 50051

CMD ["python", "server.py"]
```

---

## 📚 **相关文档**
- [整体架构说明](./ARCHITECTURE_OVERVIEW.md)
- [前端开发指南](./FRONTEND_DEVELOPMENT_GUIDE.md)
- [测试开发指南](./TESTING_DEVELOPMENT_GUIDE.md)

---

*后端开发文档版本: v2.0*  
*最后更新: 2024年12月*  
*文档状态: 生产就绪*

# 📦 项目打包交付指南

## 🎯 **打包策略概览**

### **交付形式**
- **桌面应用**: Electron打包的跨平台桌面应用
- **自包含**: 包含所有后端服务的完整应用包
- **免安装**: 用户无需安装Python、Go、Java环境
- **跨平台**: 支持Windows、macOS、Linux

### **架构设计**
```
📦 最终应用包
├── 🖥️ Electron前端 (Vue.js界面)
├── 🐍 Python后端 (server.pex - 自包含可执行文件)
├── 🔷 Go后端 (go_grpc_server - 编译后二进制)
├── ☕ Java后端 (java-grpc-backend - 包含JRE)
└── 📋 配置文件和资源
```

## 🚀 **完整打包流程**

### **第一步：环境准备**
```bash
# 1. 检查依赖环境
cd electron-app
npm run build:check

# 2. 安装所有依赖
npm install

# 3. 构建Protocol Buffers
npm run build:proto
```

### **第二步：后端服务构建**

#### **Python后端 (PEX打包)**
```bash
# 进入Python后端目录
cd backend

# 安装依赖
pip install -r requirements.txt
pip install pex

# 构建PEX可执行文件
pex -r requirements.txt -e server:serve -o ../dist/server.pex

# 验证PEX文件
../dist/server.pex --help
```

#### **Go后端编译**
```bash
# 进入Go后端目录
cd go-backend

# 安装依赖
go mod tidy

# 交叉编译多平台版本
# Windows
GOOS=windows GOARCH=amd64 go build -o ../dist/go_grpc_server.exe server.go

# macOS
GOOS=darwin GOARCH=amd64 go build -o ../dist/go_grpc_server_darwin server.go

# Linux
GOOS=linux GOARCH=amd64 go build -o ../dist/go_grpc_server_linux server.go

# 当前平台
go build -o ../dist/go_grpc_server server.go
```

#### **Java后端打包**
```bash
# 进入Java后端目录
cd java-backend

# Maven打包
mvn clean package

# 创建包含依赖的可执行JAR
mvn package spring-boot:repackage

# 复制到dist目录
cp target/java-grpc-backend-1.0.0.jar ../dist/java-grpc-backend.jar
```

### **第三步：前端构建**
```bash
# 进入前端目录
cd electron-app

# 构建CSS
npm run build:css:minify

# 构建Vue.js应用
npm run build:vite

# 验证构建结果
ls -la dist-electron/
```

### **第四步：Electron应用打包**
```bash
# 在electron-app目录下执行
npm run build:electron

# 或者分步骤执行
npm run build:vite
npm run build:electron
```

## 📋 **打包配置详解**

### **electron-builder配置**
```json
{
  "build": {
    "appId": "com.monkeyfx.electron-python-grpc-pex",
    "productName": "字幕处理工具",
    "directories": {
      "output": "dist-electron"
    },
    "files": [
      "**/*",
      "!**/*.proto",
      "!**/*.py",
      "!**/*.go", 
      "!**/*.java",
      "!**/node_modules/*/{test,__tests__,tests}",
      "!**/*.{pyc,pyo,orig,swp}"
    ],
    "extraResources": [
      {
        "from": "../dist",
        "to": "dist",
        "filter": ["**/*"]
      }
    ],
    "mac": {
      "category": "public.app-category.productivity",
      "target": [
        {
          "target": "dmg",
          "arch": ["x64", "arm64"]
        }
      ]
    },
    "win": {
      "target": [
        {
          "target": "nsis",
          "arch": ["x64"]
        }
      ]
    },
    "linux": {
      "target": [
        {
          "target": "AppImage",
          "arch": ["x64"]
        }
      ]
    }
  }
}
```

### **资源文件包含策略**
```
extraResources/
├── dist/
│   ├── server.pex           # Python后端
│   ├── go_grpc_server       # Go后端
│   └── java-grpc-backend.jar # Java后端
├── config/
│   └── config.json          # 应用配置
└── api-protos/              # Protocol Buffers定义
```

## 🔧 **自动化构建脚本**

### **完整构建脚本**
```bash
#!/bin/bash
# build-all.sh - 完整构建脚本

set -e  # 遇到错误立即退出

echo "🚀 开始完整构建流程..."

# 1. 检查环境
echo "📋 检查构建环境..."
cd electron-app
npm run build:check

# 2. 构建Protocol Buffers
echo "🔧 构建Protocol Buffers..."
npm run build:proto

# 3. 构建Python后端
echo "🐍 构建Python后端..."
cd ../backend
pip install -r requirements.txt
pip install pex
pex -r requirements.txt -e server:serve -o ../dist/server.pex
chmod +x ../dist/server.pex

# 4. 构建Go后端
echo "🔷 构建Go后端..."
cd ../go-backend
go mod tidy
go build -o ../dist/go_grpc_server server.go
chmod +x ../dist/go_grpc_server

# 5. 构建Java后端
echo "☕ 构建Java后端..."
cd ../java-backend
mvn clean package spring-boot:repackage
cp target/java-grpc-backend-1.0.0.jar ../dist/java-grpc-backend.jar

# 6. 构建前端
echo "🎨 构建前端应用..."
cd ../electron-app
npm install
npm run build:css:minify
npm run build:vite

# 7. 打包Electron应用
echo "📦 打包Electron应用..."
npm run build:electron

echo "✅ 构建完成！"
echo "📁 输出目录: electron-app/dist-electron/"
ls -la dist-electron/
```

### **跨平台构建脚本**
```bash
#!/bin/bash
# build-cross-platform.sh - 跨平台构建

echo "🌍 开始跨平台构建..."

# 构建Go后端的多平台版本
cd go-backend
echo "🔷 构建Go后端 - Windows"
GOOS=windows GOARCH=amd64 go build -o ../dist/go_grpc_server.exe server.go

echo "🔷 构建Go后端 - macOS"
GOOS=darwin GOARCH=amd64 go build -o ../dist/go_grpc_server_darwin server.go

echo "🔷 构建Go后端 - Linux"
GOOS=linux GOARCH=amd64 go build -o ../dist/go_grpc_server_linux server.go

# 构建Electron应用的多平台版本
cd ../electron-app
echo "📦 构建Electron - 所有平台"
npx electron-builder --mac --win --linux

echo "✅ 跨平台构建完成！"
```

## 📤 **交付包结构**

### **最终交付文件**
```
dist-electron/
├── 📱 字幕处理工具-1.0.0.dmg          # macOS安装包
├── 📱 字幕处理工具 Setup 1.0.0.exe     # Windows安装包
├── 📱 字幕处理工具-1.0.0.AppImage       # Linux应用包
└── 📁 unpacked/                        # 解压版本
    ├── 字幕处理工具.app/               # macOS应用
    ├── 字幕处理工具.exe                # Windows可执行文件
    └── resources/
        ├── app.asar                    # 前端应用
        └── extraResources/
            └── dist/                   # 后端服务
                ├── server.pex
                ├── go_grpc_server
                └── java-grpc-backend.jar
```

### **应用启动流程**
```
用户启动应用
    ↓
Electron主进程启动
    ↓
检测并启动后端服务
├── Python: ./resources/extraResources/dist/server.pex
├── Go: ./resources/extraResources/dist/go_grpc_server  
└── Java: java -jar ./resources/extraResources/dist/java-grpc-backend.jar
    ↓
Vue.js前端界面加载
    ↓
gRPC客户端连接后端服务
    ↓
应用就绪，用户可以使用
```

## 🔍 **质量检查**

### **构建验证脚本**
```bash
#!/bin/bash
# verify-build.sh - 构建验证

echo "🔍 验证构建结果..."

# 检查后端可执行文件
echo "📋 检查后端文件..."
test -f dist/server.pex && echo "✅ Python后端: server.pex" || echo "❌ Python后端缺失"
test -f dist/go_grpc_server && echo "✅ Go后端: go_grpc_server" || echo "❌ Go后端缺失"  
test -f dist/java-grpc-backend.jar && echo "✅ Java后端: java-grpc-backend.jar" || echo "❌ Java后端缺失"

# 检查前端构建
echo "📋 检查前端构建..."
test -d electron-app/dist-electron && echo "✅ Electron构建目录存在" || echo "❌ Electron构建失败"

# 测试后端服务启动
echo "📋 测试后端服务..."
cd dist

echo "🐍 测试Python后端..."
timeout 5s ./server.pex &
PYTHON_PID=$!
sleep 2
kill $PYTHON_PID 2>/dev/null && echo "✅ Python后端可启动" || echo "❌ Python后端启动失败"

echo "🔷 测试Go后端..."
timeout 5s ./go_grpc_server &
GO_PID=$!
sleep 2  
kill $GO_PID 2>/dev/null && echo "✅ Go后端可启动" || echo "❌ Go后端启动失败"

echo "☕ 测试Java后端..."
timeout 5s java -jar java-grpc-backend.jar &
JAVA_PID=$!
sleep 2
kill $JAVA_PID 2>/dev/null && echo "✅ Java后端可启动" || echo "❌ Java后端启动失败"

echo "✅ 构建验证完成！"
```

## 📋 **部署清单**

### **交付前检查清单**
- [ ] ✅ 所有依赖环境检查通过
- [ ] ✅ Protocol Buffers构建成功
- [ ] ✅ Python后端PEX文件生成
- [ ] ✅ Go后端二进制文件编译
- [ ] ✅ Java后端JAR包打包
- [ ] ✅ 前端Vue.js应用构建
- [ ] ✅ Electron应用打包成功
- [ ] ✅ 跨平台版本构建完成
- [ ] ✅ 构建验证测试通过
- [ ] ✅ 安装包测试验证

### **用户系统要求**
```
最低系统要求:
- Windows 10 或更高版本
- macOS 10.14 或更高版本  
- Linux (Ubuntu 18.04+ 或等效版本)
- 内存: 4GB RAM
- 存储: 500MB 可用空间
- 网络: 可选 (用于翻译API)

推荐系统配置:
- 内存: 8GB RAM 或更高
- 存储: 2GB 可用空间
- CPU: 多核处理器 (用于音视频处理)
```

## 🚀 **快速打包命令**

### **一键完整构建**
```bash
# 克隆项目后的完整构建流程
git clone <repository-url>
cd electron-python-grpc-pex-demo

# 执行完整构建
chmod +x scripts/build-all.sh
./scripts/build-all.sh

# 验证构建结果
chmod +x scripts/verify-build.sh  
./scripts/verify-build.sh
```

### **开发者快速打包**
```bash
# 在项目根目录执行
cd electron-app

# 快速构建和打包
npm run build:all && npm run build

# 查看输出
ls -la dist-electron/
```

---

## 📚 **相关文档**
- [整体架构说明](./ARCHITECTURE_OVERVIEW.md)
- [前端开发指南](./FRONTEND_DEVELOPMENT_GUIDE.md)
- [后端开发指南](./BACKEND_DEVELOPMENT_GUIDE.md)
- [测试开发指南](./TESTING_DEVELOPMENT_GUIDE.md)

---

*打包部署文档版本: v1.0*  
*最后更新: 2024年12月*  
*文档状态: 生产就绪*

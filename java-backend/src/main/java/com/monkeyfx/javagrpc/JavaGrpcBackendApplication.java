package com.monkeyfx.javagrpc;

import org.bouncycastle.jsse.provider.BouncyCastleJsseProvider; // BouncyCastle 安全提供者
import org.springframework.boot.SpringApplication; // Spring Boot 应用启动类
import org.springframework.boot.autoconfigure.SpringBootApplication; // Spring Boot 应用注解

import java.security.Security; // Java 安全 API

/**
 * Java gRPC 后端服务的 Spring Boot 主应用程序类。
 * <p>
 * 本应用通过 {@link SpringBootApplication @SpringBootApplication} 注解进行配置，
 * 该注解整合了 {@code @Configuration}、{@code @EnableAutoConfiguration} 和 {@code @ComponentScan}。
 * 它负责启动内嵌的 gRPC 服务器并管理应用生命周期。
 * </p>
 *
 * <AUTHOR> (AI)
 * @version 1.1
 * @since 2025-05-15
 */
@SpringBootApplication
public class JavaGrpcBackendApplication {

    static {
        // 静态初始化块：在类加载时执行。
        // 为了确保在 GraalVM native image 构建中 BouncyCastleJsseProvider 能够正确且及时地被加载，
        // 我们在此处手动将其添加为安全提供者。
        // Netty 等库有时会在构建时过早初始化安全提供者，这可能导致 native image 兼容性问题。
        // BouncyCastle 提供了额外的加密算法套件和 TLS 功能，增强了应用的安全性。
        Security.addProvider(new BouncyCastleJsseProvider());
    }

    /**
     * 应用程序的主入口方法。
     * <p>
     * 此方法使用 {@link SpringApplication#run(Class, String...)} 来启动整个 Spring Boot 应用。
     * </p>
     *
     * @param args 传递给应用程序的命令行参数。这些参数可以用来配置应用行为。
     */
    public static void main(String[] args) {
        // 启动 Spring Boot 应用程序，JavaGrpcBackendApplication.class 作为主配置类。
        SpringApplication.run(JavaGrpcBackendApplication.class, args);
    }
}
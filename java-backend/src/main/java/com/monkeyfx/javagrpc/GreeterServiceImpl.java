package com.monkeyfx.javagrpc;

import com.monkeyfx.grpc.api.v1.greeter.GreeterGrpc; // gRPC Greeter 服务定义
import com.monkeyfx.grpc.api.v1.greeter.HelloReply;   // gRPC HelloReply 消息定义
import com.monkeyfx.grpc.api.v1.greeter.HelloRequest; // gRPC HelloRequest 消息定义
import io.grpc.stub.StreamObserver; // gRPC 流观察者，用于处理异步响应
import net.devh.boot.grpc.server.service.GrpcService; // Spring Boot gRPC 服务注解
import org.slf4j.Logger; // SLF4J 日志接口
import org.slf4j.LoggerFactory; // SLF4J 日志工厂

import java.time.OffsetDateTime; // 带时区偏移的日期时间类
import java.time.format.DateTimeFormatter; // 日期时间格式化器

/**
 * {@code GreeterService} 的 gRPC 实现。
 * <p>
 * 该服务通过 {@link GrpcService @GrpcService} 注解暴露，由 {@code grpc-spring-boot-starter} 库自动配置。
 * 它继承自 Protocol Buffers 编译器生成的 {@link GreeterGrpc.GreeterImplBase} 类，
 * 并实现了在 {@code services.proto} 文件中定义的 RPC 方法。
 * </p>
 *
 * <AUTHOR> (AI)
 * @version 1.1
 * @see GreeterGrpc.GreeterImplBase
 * @see GrpcService
 * @since 2025-05-15
 */
@GrpcService
public class GreeterServiceImpl extends GreeterGrpc.GreeterImplBase {

    /**
     * 用于记录日志的 SLF4J Logger 实例。
     * 通过 {@link LoggerFactory#getLogger(Class)} 获取，与当前类关联。
     */
    private static final Logger logger = LoggerFactory.getLogger(GreeterServiceImpl.class);

    /**
     * 服务器的标识符字符串，用于在响应中指明是哪个服务器处理了请求。
     */
    private static final String SERVER_ID = "Java Spring Boot Server";

    /**
     * 实现了在 {@code services.proto} 文件中定义的 {@code SayHello} RPC 方法。
     * <p>
     * 当客户端调用此方法时，它会接收一个 {@link HelloRequest}，其中包含一个名称，
     * 然后构造并返回一个 {@link HelloReply}，该响应包含问候消息、原始请求名称、
     * 服务器ID以及当前的时间戳。
     * </p>
     *
     * @param request          客户端发送的 {@link HelloRequest} 对象，包含了问候的目标名称。
     * @param responseObserver 一个 {@link StreamObserver} 对象，用于将生成的 {@link HelloReply} 异步发送回客户端。
     *                         在发送响应后，必须调用 {@link StreamObserver#onCompleted()} 来结束 RPC 调用。
     *                         如果处理过程中发生错误，则应调用 {@link StreamObserver#onError(Throwable)}。
     */
    @Override
    public void sayHello(HelloRequest request, StreamObserver<HelloReply> responseObserver) {
        String originalName = request.getName();

        // 获取当前带时区偏移的日期时间，并格式化为 ISO_OFFSET_DATE_TIME 标准字符串。
        // 例如："2011-12-03T10:15:30+01:00"
        String currentTimestamp = OffsetDateTime.now().format(DateTimeFormatter.ISO_OFFSET_DATE_TIME);

        // 构建响应消息字符串。
        String responseMessage = String.format("Hello %s from %s!", originalName, SERVER_ID);

        // 使用 Builder 模式构建 HelloReply 响应对象。
        // HelloReply 是根据 .proto 文件生成的不可变消息类型。
        HelloReply reply = HelloReply.newBuilder()
                .setMessage(responseMessage)
                .setOriginalRequest(originalName)
                .setServerId(SERVER_ID)
                .setTimestamp(currentTimestamp)
                .build();

        // 通过 responseObserver 将响应发送给客户端。
        responseObserver.onNext(reply);
        // 标记 RPC 调用已成功完成。
        responseObserver.onCompleted();

        // 记录服务处理信息。
        logger.info("已成功处理来自 '{}' 的 SayHello 请求，并发送响应。", originalName);
    }
}
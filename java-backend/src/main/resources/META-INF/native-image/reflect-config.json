[{"name": "io.grpc.netty.shaded.io.netty.util.ReferenceCountUtil", "methods": [{"name": "touch", "parameterTypes": ["java.lang.Object"]}, {"name": "touch", "parameterTypes": ["java.lang.Object", "java.lang.Object"]}], "fields": [{"name": "touch"}], "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": false, "allPublicMethods": false, "allDeclaredFields": false, "allPublicFields": false}, {"name": "io.grpc.netty.shaded.io.netty.util.ResourceLeakDetector", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "allDeclaredFields": true, "allPublicFields": true}]
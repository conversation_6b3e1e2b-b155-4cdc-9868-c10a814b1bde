[{"interfaces": ["java.lang.reflect.ParameterizedType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.TypeVariable", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["java.lang.reflect.WildcardType", "org.springframework.core.SerializableTypeWrapper$SerializableTypeProxy", "java.io.Serializable"]}, {"interfaces": ["org.springframework.boot.context.properties.ConfigurationProperties"]}]
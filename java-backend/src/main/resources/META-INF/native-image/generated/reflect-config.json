[{"name": "[I"}, {"name": "[Ljava.lang.Class;"}, {"name": "[Ljava.lang.String;"}, {"name": "[Lorg.springframework.core.annotation.AnnotationAttributes;"}, {"name": "[Lorg.springframework.util.ConcurrentReferenceHashMap$Segment;"}, {"name": "[Z"}, {"name": "apple.security.AppleProvider", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "boolean", "queryAllDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Bo<PERSON>an"]}, {"name": "from", "parameterTypes": ["java.lang.Bo<PERSON>an"]}, {"name": "of", "parameterTypes": ["java.lang.Bo<PERSON>an"]}, {"name": "valueOf", "parameterTypes": ["java.lang.Bo<PERSON>an"]}]}, {"name": "ch.qos.logback.classic.BasicConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.LoggerContext"}, {"name": "ch.qos.logback.classic.joran.SerializedModelConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.DateConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.LevelConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.LineSeparatorConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.LoggerConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.MessageConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.pattern.ThreadConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "ch.qos.logback.classic.util.DefaultJoranConfigurator", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "co.elastic.clients.elasticsearch.ElasticsearchClient"}, {"name": "co.elastic.clients.transport.ElasticsearchTransport"}, {"name": "com.alibaba.cloud.nacos.registry.NacosRegistration"}, {"name": "com.couchbase.client.java.Bucket"}, {"name": "com.couchbase.client.java.Cluster"}, {"name": "com.datastax.oss.driver.api.core.CqlSession"}, {"name": "com.example.javagrpc.GreeterServiceImpl", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "sayHello", "parameterTypes": ["com.example.javagrpc.grpc.HelloRequest", "io.grpc.stub.StreamObserver"]}]}, {"name": "com.example.javagrpc.JavaGrpcBackendApplication", "allDeclaredFields": true, "allDeclaredClasses": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setBeanFactory", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"name": "com.example.javagrpc.JavaGrpcBackendApplication$$SpringCGLIB$$0", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "fields": [{"name": "CGLIB$FACTORY_DATA"}], "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "CGLIB$SET_STATIC_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}, {"name": "CGLIB$SET_THREAD_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}]}, {"name": "com.example.javagrpc.grpc.GreeterGrpc$AsyncService", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "com.example.javagrpc.grpc.GreeterGrpc$GreeterImplBase", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "bindService", "parameterTypes": []}]}, {"name": "com.fasterxml.jackson.databind.ObjectMapper"}, {"name": "com.github.benmanes.caffeine.cache.Caffeine"}, {"name": "com.google.gson.Gson", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "com.google.gson.GsonBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "com.google.protobuf.ExtensionRegistry", "methods": [{"name": "getEmptyRegistry", "parameterTypes": []}]}, {"name": "com.hazelcast.core.HazelcastInstance"}, {"name": "com.mongodb.client.MongoClient"}, {"name": "com.mongodb.reactivestreams.client.MongoClient"}, {"name": "com.querydsl.core.Query"}, {"name": "com.rabbitmq.client.Channel"}, {"name": "com.samskivert.mustache.Mustache"}, {"name": "com.sendgrid.SendGrid"}, {"name": "com.unboundid.ldap.listener.InMemoryDirectoryServer"}, {"name": "freemarker.template.Configuration"}, {"name": "graphql.GraphQL"}, {"name": "groovy.lang.MetaClass"}, {"name": "groovy.text.markup.MarkupTemplateEngine"}, {"name": "io.grpc.BindableService", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "io.grpc.Codec"}, {"name": "io.grpc.CompressorRegistry", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "io.grpc.Context$CancellationListener", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "io.grpc.DecompressorRegistry", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "io.grpc.NameResolver$Factory", "queryAllDeclaredMethods": true}, {"name": "io.grpc.NameResolverProvider", "queryAllDeclaredMethods": true, "methods": [{"name": "getProducedSocketAddressTypes", "parameterTypes": []}]}, {"name": "io.grpc.Server"}, {"name": "io.grpc.ServerInterceptor", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "io.grpc.census.InternalCensusStatsAccessor"}, {"name": "io.grpc.census.InternalCensusTracingAccessor"}, {"name": "io.grpc.health.v1.HealthGrpc$AsyncService", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "io.grpc.health.v1.HealthGrpc$HealthImplBase", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "bindService", "parameterTypes": []}]}, {"name": "io.grpc.netty.NettyServerBuilder"}, {"name": "io.grpc.netty.shaded.io.grpc.netty.NettyServerBuilder"}, {"name": "io.grpc.netty.shaded.io.netty.bootstrap.ServerBootstrap$1"}, {"name": "io.grpc.netty.shaded.io.netty.bootstrap.ServerBootstrap$ServerBootstrapAcceptor", "methods": [{"name": "channelRead", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}]}, {"name": "io.grpc.netty.shaded.io.netty.buffer.AbstractByteBufAllocator", "queryAllDeclaredMethods": true}, {"name": "io.grpc.netty.shaded.io.netty.channel.AbstractChannelHandlerContext", "fields": [{"name": "handlerState"}]}, {"name": "io.grpc.netty.shaded.io.netty.channel.Channel"}, {"name": "io.grpc.netty.shaded.io.netty.channel.ChannelInboundHandlerAdapter", "methods": [{"name": "channelActive", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelInactive", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "channelReadComplete", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRegistered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelUnregistered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelWritabilityChanged", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "userEventTriggered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}]}, {"name": "io.grpc.netty.shaded.io.netty.channel.ChannelInitializer", "methods": [{"name": "channelRegistered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}]}, {"name": "io.grpc.netty.shaded.io.netty.channel.ChannelOutboundBuffer", "fields": [{"name": "totalPendingSize"}, {"name": "unwritable"}]}, {"name": "io.grpc.netty.shaded.io.netty.channel.DefaultChannelConfig", "fields": [{"name": "autoRead"}, {"name": "writeBufferWaterMark"}]}, {"name": "io.grpc.netty.shaded.io.netty.channel.DefaultChannelPipeline", "fields": [{"name": "estimator<PERSON><PERSON><PERSON>"}]}, {"name": "io.grpc.netty.shaded.io.netty.channel.DefaultChannelPipeline$HeadContext", "methods": [{"name": "bind", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.net.SocketAddress", "io.grpc.netty.shaded.io.netty.channel.ChannelPromise"]}, {"name": "channelActive", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelInactive", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "channelReadComplete", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRegistered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelUnregistered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelWritabilityChanged", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "close", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "io.grpc.netty.shaded.io.netty.channel.ChannelPromise"]}, {"name": "connect", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.net.SocketAddress", "java.net.SocketAddress", "io.grpc.netty.shaded.io.netty.channel.ChannelPromise"]}, {"name": "deregister", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "io.grpc.netty.shaded.io.netty.channel.ChannelPromise"]}, {"name": "disconnect", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "io.grpc.netty.shaded.io.netty.channel.ChannelPromise"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}, {"name": "flush", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "read", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "userEventTriggered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "write", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Object", "io.grpc.netty.shaded.io.netty.channel.ChannelPromise"]}]}, {"name": "io.grpc.netty.shaded.io.netty.channel.DefaultChannelPipeline$TailContext", "methods": [{"name": "channelActive", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelInactive", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRead", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}, {"name": "channelReadComplete", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelRegistered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelUnregistered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "channelWritabilityChanged", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Throwable"]}, {"name": "userEventTriggered", "parameterTypes": ["io.grpc.netty.shaded.io.netty.channel.ChannelHandlerContext", "java.lang.Object"]}]}, {"name": "io.grpc.netty.shaded.io.netty.channel.DefaultFileRegion"}, {"name": "io.grpc.netty.shaded.io.netty.channel.epoll.Epoll", "methods": [{"name": "isAvailable", "parameterTypes": []}, {"name": "unavailabilityCause", "parameterTypes": []}]}, {"name": "io.grpc.netty.shaded.io.netty.channel.epoll.NativeDatagramPacketArray$NativeDatagramPacket"}, {"name": "io.grpc.netty.shaded.io.netty.channel.unix.PeerCredentials"}, {"name": "io.grpc.netty.shaded.io.netty.util.AbstractReferenceCounted", "fields": [{"name": "refCnt"}]}, {"name": "io.grpc.netty.shaded.io.netty.util.DefaultAttributeMap", "fields": [{"name": "attributes"}]}, {"name": "io.grpc.netty.shaded.io.netty.util.concurrent.DefaultPromise", "fields": [{"name": "result"}]}, {"name": "io.grpc.netty.shaded.io.netty.util.concurrent.SingleThreadEventExecutor", "fields": [{"name": "state"}, {"name": "threadProperties"}]}, {"name": "io.grpc.netty.shaded.io.netty.util.internal.shaded.org.jctools.queues.BaseMpscLinkedArrayQueueColdProducerFields", "fields": [{"name": "producerLimit"}]}, {"name": "io.grpc.netty.shaded.io.netty.util.internal.shaded.org.jctools.queues.BaseMpscLinkedArrayQueueConsumerFields", "fields": [{"name": "consumerIndex"}]}, {"name": "io.grpc.netty.shaded.io.netty.util.internal.shaded.org.jctools.queues.BaseMpscLinkedArrayQueueProducerFields", "fields": [{"name": "producerIndex"}]}, {"name": "io.grpc.protobuf.services.HealthServiceImpl", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "check", "parameterTypes": ["io.grpc.health.v1.HealthCheckRequest", "io.grpc.stub.StreamObserver"]}, {"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}, {"name": "watch", "parameterTypes": ["io.grpc.health.v1.HealthCheckRequest", "io.grpc.stub.StreamObserver"]}]}, {"name": "io.grpc.protobuf.services.HealthStatusManager", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "io.grpc.protobuf.services.ProtoReflectionService", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "serverReflectionInfo", "parameterTypes": ["io.grpc.stub.StreamObserver"]}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "io.grpc.reflection.v1alpha.ServerReflectionGrpc$AsyncService", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "io.grpc.reflection.v1alpha.ServerReflectionGrpc$ServerReflectionImplBase", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "bindService", "parameterTypes": []}]}, {"name": "io.micrometer.core.instrument.binder.grpc.MetricCollectingServerInterceptor"}, {"name": "io.micrometer.observation.ObservationRegistry"}, {"name": "io.netty.buffer.PooledByteBufAllocator"}, {"name": "io.netty.channel.Channel"}, {"name": "io.netty.util.NettyRuntime"}, {"name": "io.r2dbc.spi.ConnectionFactory"}, {"name": "io.rsocket.RSocket"}, {"name": "io.rsocket.core.RSocketServer"}, {"name": "jakarta.activation.MimeType"}, {"name": "jakarta.annotation.ManagedBean"}, {"name": "jakarta.annotation.PostConstruct"}, {"name": "jakarta.annotation.PreDestroy"}, {"name": "jakarta.annotation.Resource"}, {"name": "jakarta.ejb.EJB"}, {"name": "jakarta.inject.Inject"}, {"name": "jakarta.inject.Named"}, {"name": "jakarta.inject.Provider"}, {"name": "jakarta.inject.Qualifier"}, {"name": "jakarta.jms.ConnectionFactory"}, {"name": "jakarta.jms.Message"}, {"name": "jakarta.json.bind.Jsonb"}, {"name": "jakarta.persistence.EntityManager"}, {"name": "jakarta.persistence.EntityManagerFactory"}, {"name": "jakarta.servlet.MultipartConfigElement"}, {"name": "jakarta.servlet.Servlet"}, {"name": "jakarta.servlet.ServletRegistration"}, {"name": "jakarta.servlet.ServletRequest"}, {"name": "jakarta.transaction.Transaction"}, {"name": "jakarta.transaction.TransactionManager"}, {"name": "jakarta.validation.Validator"}, {"name": "jakarta.validation.executable.ExecutableValidator"}, {"name": "java.io.Closeable", "queryAllPublicMethods": true}, {"name": "java.io.FileDescriptor"}, {"name": "java.io.FilePermission"}, {"name": "java.io.Serializable", "queryAllPublicMethods": true}, {"name": "java.lang.Bo<PERSON>an", "methods": [{"name": "to<PERSON><PERSON>", "parameterTypes": []}]}, {"name": "java.lang.Class", "queryAllDeclaredMethods": true}, {"name": "java.lang.ClassLoader", "methods": [{"name": "defineClass", "parameterTypes": ["java.lang.String", "byte[]", "int", "int", "java.security.ProtectionDomain"]}]}, {"name": "java.lang.Object", "queryAllDeclaredMethods": true}, {"name": "java.lang.ProcessHandle", "methods": [{"name": "current", "parameterTypes": []}, {"name": "pid", "parameterTypes": []}]}, {"name": "java.lang.RuntimePermission"}, {"name": "java.lang.String", "queryAllDeclaredMethods": true}, {"name": "java.lang.Thread", "fields": [{"name": "threadLocalRandomProbe"}]}, {"name": "java.lang.annotation.Documented", "queryAllDeclaredMethods": true}, {"name": "java.lang.annotation.Inherited", "queryAllDeclaredMethods": true}, {"name": "java.lang.annotation.Repeatable", "queryAllDeclaredMethods": true}, {"name": "java.lang.annotation.Retention", "queryAllDeclaredMethods": true}, {"name": "java.lang.annotation.Target", "queryAllDeclaredMethods": true}, {"name": "java.lang.reflect.ParameterizedType", "methods": [{"name": "getActualTypeArguments", "parameterTypes": []}, {"name": "getRawType", "parameterTypes": []}]}, {"name": "java.lang.reflect.TypeVariable", "methods": [{"name": "getName", "parameterTypes": []}]}, {"name": "java.lang.reflect.WildcardType", "methods": [{"name": "getUpperBounds", "parameterTypes": []}]}, {"name": "java.net.NetPermission"}, {"name": "java.net.SocketPermission"}, {"name": "java.net.URLPermission", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String", "java.lang.String"]}]}, {"name": "java.nio.Bits", "fields": [{"name": "MAX_MEMORY"}, {"name": "UNALIGNED"}]}, {"name": "java.nio.Buffer", "fields": [{"name": "address"}]}, {"name": "java.nio.ByteBuffer", "methods": [{"name": "alignedSlice", "parameterTypes": ["int"]}]}, {"name": "java.nio.DirectByteBuffer", "methods": [{"name": "<init>", "parameterTypes": ["long", "int"]}]}, {"name": "java.nio.channels.FileChannel"}, {"name": "java.nio.channels.spi.SelectorProvider", "methods": [{"name": "openServerSocketChannel", "parameterTypes": ["java.net.ProtocolFamily"]}]}, {"name": "java.security.AllPermission"}, {"name": "java.security.SecurityPermission"}, {"name": "java.security.spec.DSAParameterSpec"}, {"name": "java.sql.Date"}, {"name": "java.util.PropertyPermission"}, {"name": "java.util.concurrent.Executor"}, {"name": "java.util.concurrent.ThreadFactory", "queryAllPublicMethods": true}, {"name": "java.util.concurrent.atomic.AtomicBoolean", "fields": [{"name": "value"}]}, {"name": "java.util.concurrent.atomic.AtomicReference", "fields": [{"name": "value"}]}, {"name": "java.util.concurrent.atomic.LongAdder", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "add", "parameterTypes": ["long"]}, {"name": "sum", "parameterTypes": []}]}, {"name": "java.util.concurrent.atomic.Striped64", "fields": [{"name": "base"}, {"name": "cellsBusy"}]}, {"name": "java.util.function.Consumer", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "java.util.logging.LogManager"}, {"name": "javax.annotation.ManagedBean"}, {"name": "javax.annotation.Nonnull", "queryAllDeclaredMethods": true}, {"name": "javax.annotation.Nullable", "queryAllDeclaredMethods": true}, {"name": "javax.annotation.PostConstruct"}, {"name": "javax.annotation.PreDestroy"}, {"name": "javax.annotation.Resource"}, {"name": "javax.annotation.meta.TypeQualifier", "queryAllDeclaredMethods": true}, {"name": "javax.cache.Caching"}, {"name": "javax.inject.Inject"}, {"name": "javax.inject.Named"}, {"name": "javax.money.MonetaryAmount"}, {"name": "javax.naming.InitialContext"}, {"name": "javax.naming.ldap.LdapContext"}, {"name": "javax.smartcardio.CardPermission"}, {"name": "javax.sql.DataSource"}, {"name": "jdk.crac.management.CRaCMXBean"}, {"name": "jdk.internal.loader.ClassLoaders$AppClassLoader", "methods": [{"name": "clearCache", "parameterTypes": []}]}, {"name": "jdk.internal.loader.ClassLoaders$PlatformClassLoader", "methods": [{"name": "clearCache", "parameterTypes": []}]}, {"name": "jdk.internal.misc.Unsafe", "methods": [{"name": "getUnsafe", "parameterTypes": []}]}, {"name": "kotlin.Metadata"}, {"name": "kotlin.reflect.full.<PERSON>es"}, {"name": "liquibase.change.DatabaseChange"}, {"name": "net.devh.boot.grpc.common.autoconfigure.GrpcCommonCodecAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "defaultCompressorRegistry", "parameterTypes": ["net.devh.boot.grpc.common.codec.GrpcCodecDiscoverer"]}, {"name": "defaultDecompressorRegistry", "parameterTypes": ["net.devh.boot.grpc.common.codec.GrpcCodecDiscoverer"]}, {"name": "defaultGrpcCodecDiscoverer", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.common.codec.AnnotationGrpcCodecDiscoverer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "findGrpcCodecs", "parameterTypes": []}, {"name": "setApplicationContext", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.common.codec.GrpcCodecDiscoverer", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "net.devh.boot.grpc.server.advice.GrpcAdviceIsPresentCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcHealthServiceAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "grpcHealthService", "parameterTypes": ["io.grpc.protobuf.services.HealthStatusManager"]}, {"name": "healthStatusManager", "parameterTypes": []}, {"name": "setBeanFactory", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcHealthServiceAutoConfiguration$$SpringCGLIB$$0", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "fields": [{"name": "CGLIB$FACTORY_DATA"}], "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "CGLIB$SET_STATIC_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}, {"name": "CGLIB$SET_THREAD_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcHealthServiceAutoConfiguration$$SpringCGLIB$$FastClass$$0", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcHealthServiceAutoConfiguration$$SpringCGLIB$$FastClass$$1", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcReflectionServiceAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "protoReflectionService", "parameterTypes": []}, {"name": "setBeanFactory", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcReflectionServiceAutoConfiguration$$SpringCGLIB$$0", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "fields": [{"name": "CGLIB$FACTORY_DATA"}], "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "CGLIB$SET_STATIC_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}, {"name": "CGLIB$SET_THREAD_CALLBACKS", "parameterTypes": ["org.springframework.cglib.proxy.Callback[]"]}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcReflectionServiceAutoConfiguration$$SpringCGLIB$$FastClass$$0", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcReflectionServiceAutoConfiguration$$SpringCGLIB$$FastClass$$1", "methods": [{"name": "<init>", "parameterTypes": ["java.lang.Class"]}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcServerAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "annotationGlobalServerInterceptorConfigurer", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "compressionServerConfigurer", "parameterTypes": ["io.grpc.CompressorRegistry"]}, {"name": "decompressionServerConfigurer", "parameterTypes": ["io.grpc.DecompressorRegistry"]}, {"name": "defaultGrpcServerProperties", "parameterTypes": []}, {"name": "defaultGrpcServiceDiscoverer", "parameterTypes": []}, {"name": "globalServerInterceptorRegistry", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "grpcRequestScope", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.autoconfigure.GrpcServerFactoryAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "shadedNettyGrpcServerFactory", "parameterTypes": ["net.devh.boot.grpc.server.config.GrpcServerProperties", "net.devh.boot.grpc.server.service.GrpcServiceDiscoverer", "java.util.List"]}, {"name": "shadedNettyGrpcServerLifecycle", "parameterTypes": ["net.devh.boot.grpc.server.serverfactory.ShadedNettyGrpcServerFactory", "net.devh.boot.grpc.server.config.GrpcServerProperties", "org.springframework.context.ApplicationEventPublisher"]}]}, {"name": "net.devh.boot.grpc.server.condition.ConditionalOnInterprocessServer", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.config.GrpcServerProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "setPort", "parameterTypes": ["int"]}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.interceptor.AnnotationGlobalServerInterceptorConfigurer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "configureServerInterceptors", "parameterTypes": ["java.util.List"]}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.interceptor.GlobalServerInterceptorConfigurer", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "net.devh.boot.grpc.server.interceptor.GlobalServerInterceptorRegistry", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.interceptor.GrpcGlobalServerInterceptor", "queryAllDeclaredMethods": true}, {"name": "net.devh.boot.grpc.server.nameresolver.SelfNameResolverFactory", "queryAllDeclaredMethods": true, "methods": [{"name": "getDefaultScheme", "parameterTypes": []}, {"name": "newNameResolver", "parameterTypes": ["java.net.URI", "io.grpc.NameResolver$Args"]}]}, {"name": "net.devh.boot.grpc.server.scope.GrpcRequestScope", "queryAllDeclaredMethods": true, "methods": [{"name": "cancelled", "parameterTypes": ["io.grpc.Context"]}, {"name": "close", "parameterTypes": []}, {"name": "get", "parameterTypes": ["java.lang.String", "org.springframework.beans.factory.ObjectFactory"]}, {"name": "getConversationId", "parameterTypes": []}, {"name": "interceptCall", "parameterTypes": ["io.grpc.ServerCall", "io.grpc.Metadata", "io.grpc.ServerCallHandler"]}, {"name": "postProcessBeanFactory", "parameterTypes": ["org.springframework.beans.factory.config.ConfigurableListableBeanFactory"]}, {"name": "registerDestructionCallback", "parameterTypes": ["java.lang.String", "java.lang.Runnable"]}, {"name": "remove", "parameterTypes": ["java.lang.String"]}, {"name": "resolveContextualObject", "parameterTypes": ["java.lang.String"]}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.serverfactory.AbstractGrpcServerFactory", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "addService", "parameterTypes": ["net.devh.boot.grpc.server.service.GrpcServiceDefinition"]}, {"name": "configureConnectionLimits", "parameterTypes": ["io.grpc.ServerBuilder"]}, {"name": "configureKeepAlive", "parameterTypes": ["io.grpc.ServerBuilder"]}, {"name": "configureSecurity", "parameterTypes": ["io.grpc.ServerBuilder"]}, {"name": "createServer", "parameterTypes": []}, {"name": "get<PERSON><PERSON><PERSON>", "parameterTypes": []}, {"name": "getPort", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.serverfactory.GrpcServerConfigurer", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "net.devh.boot.grpc.server.serverfactory.GrpcServerFactory", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "net.devh.boot.grpc.server.serverfactory.GrpcServerLifecycle", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "getPhase", "parameterTypes": []}, {"name": "isAutoStartup", "parameterTypes": []}, {"name": "isRunning", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}, {"name": "start", "parameterTypes": []}, {"name": "stop", "parameterTypes": []}, {"name": "stop", "parameterTypes": ["java.lang.Runnable"]}]}, {"name": "net.devh.boot.grpc.server.serverfactory.InProcessGrpcServerFactory"}, {"name": "net.devh.boot.grpc.server.serverfactory.NettyGrpcServerFactory"}, {"name": "net.devh.boot.grpc.server.serverfactory.ShadedNettyGrpcServerFactory", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.service.AnnotationGrpcServiceDiscoverer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "findGrpcServices", "parameterTypes": []}, {"name": "setApplicationContext", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "net.devh.boot.grpc.server.service.GrpcService", "queryAllDeclaredMethods": true}, {"name": "net.devh.boot.grpc.server.service.GrpcServiceDiscoverer", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.apache.logging.log4j.core.impl.Log4jContextFactory"}, {"name": "org.apache.logging.log4j.spi.ExtendedLogger"}, {"name": "org.apache.logging.slf4j.SLF4JProvider"}, {"name": "org.apache.pulsar.client.api.PulsarClient"}, {"name": "org.aspectj.weaver.Advice"}, {"name": "org.cache2k.Cache2kBuilder"}, {"name": "org.crac.Core"}, {"name": "org.eclipse.core.runtime.FileLocator"}, {"name": "org.elasticsearch.client.RestClientBuilder"}, {"name": "org.flywaydb.core.Flyway"}, {"name": "org.h2.server.web.JakartaWebServlet"}, {"name": "org.infinispan.spring.embedded.provider.SpringEmbeddedCacheManager"}, {"name": "org.influxdb.InfluxDB"}, {"name": "org.jboss.logging.Logger"}, {"name": "org.jooq.DSLContext"}, {"name": "org.neo4j.driver.Driver"}, {"name": "org.quartz.Scheduler"}, {"name": "org.reactivestreams.Publisher"}, {"name": "org.slf4j.<PERSON>"}, {"name": "org.slf4j.bridge.SLF4JBridgeHandler"}, {"name": "org.slf4j.spi.LocationAwareLogger"}, {"name": "org.springframework.aop.framework.AopInfrastructureBean", "queryAllPublicMethods": true}, {"name": "org.springframework.aop.framework.ProxyConfig", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "copyFrom", "parameterTypes": ["org.springframework.aop.framework.ProxyConfig"]}, {"name": "isExposeProxy", "parameterTypes": []}, {"name": "isOpaque", "parameterTypes": []}, {"name": "isOptimize", "parameterTypes": []}, {"name": "isProxyTargetClass", "parameterTypes": []}, {"name": "setExposeProxy", "parameterTypes": ["boolean"]}, {"name": "setOpaque", "parameterTypes": ["boolean"]}, {"name": "setOptimize", "parameterTypes": ["boolean"]}, {"name": "setProxyTargetClass", "parameterTypes": ["boolean"]}, {"name": "toString", "parameterTypes": []}]}, {"name": "org.springframework.aop.framework.ProxyProcessorSupport", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "getOrder", "parameterTypes": []}, {"name": "set<PERSON>ean<PERSON><PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["java.lang.ClassLoader"]}, {"name": "setOrder", "parameterTypes": ["int"]}, {"name": "setProxyClassLoader", "parameterTypes": ["java.lang.ClassLoader"]}]}, {"name": "org.springframework.aop.framework.autoproxy.AbstractAdvisorAutoProxyCreator", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "setBeanFactory", "parameterTypes": ["org.springframework.beans.factory.BeanFactory"]}]}, {"name": "org.springframework.aop.framework.autoproxy.AbstractAutoProxyCreator", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "determineBeanType", "parameterTypes": ["java.lang.Class", "java.lang.String"]}, {"name": "determineCandidateConstructors", "parameterTypes": ["java.lang.Class", "java.lang.String"]}, {"name": "getEarlyBeanReference", "parameterTypes": ["java.lang.Object", "java.lang.String"]}, {"name": "isFrozen", "parameterTypes": []}, {"name": "postProcessAfterInitialization", "parameterTypes": ["java.lang.Object", "java.lang.String"]}, {"name": "postProcessBeforeInstantiation", "parameterTypes": ["java.lang.Class", "java.lang.String"]}, {"name": "postProcessProperties", "parameterTypes": ["org.springframework.beans.PropertyValues", "java.lang.Object", "java.lang.String"]}, {"name": "predictBeanType", "parameterTypes": ["java.lang.Class", "java.lang.String"]}, {"name": "setAdvisorAdapterRegistry", "parameterTypes": ["org.springframework.aop.framework.adapter.AdvisorAdapterRegistry"]}, {"name": "setApplyCommonInterceptorsFirst", "parameterTypes": ["boolean"]}, {"name": "setCustomTargetSourceCreators", "parameterTypes": ["org.springframework.aop.framework.autoproxy.TargetSourceCreator[]"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "parameterTypes": ["boolean"]}, {"name": "setInterceptorNames", "parameterTypes": ["java.lang.String[]"]}]}, {"name": "org.springframework.aop.framework.autoproxy.InfrastructureAdvisorAutoProxyCreator", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.aot.generate.Generated", "queryAllDeclaredMethods": true}, {"name": "org.springframework.batch.core.launch.JobLauncher"}, {"name": "org.springframework.beans.factory.Aware", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.BeanClassLoaderAware", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.BeanFactoryAware", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.BeanNameAware", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.DisposableBean", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.FactoryBean", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.InitializingBean", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.beans.factory.aot.BeanFactoryInitializationAotProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.aot.BeanRegistrationAotProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.config.BeanFactoryPostProcessor", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.config.BeanPostProcessor", "queryAllPublicMethods": true, "methods": [{"name": "postProcessBeforeInitialization", "parameterTypes": ["java.lang.Object", "java.lang.String"]}]}, {"name": "org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessor", "queryAllPublicMethods": true, "methods": [{"name": "postProcessAfterInstantiation", "parameterTypes": ["java.lang.Object", "java.lang.String"]}]}, {"name": "org.springframework.beans.factory.config.Scope", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.config.SmartInstantiationAwareBeanPostProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.ClearCachesApplicationListener", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.SpringApplication", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "setBannerMode", "parameterTypes": ["org.springframework.boot.Banner$Mode"]}]}, {"name": "org.springframework.boot.SpringBootConfiguration", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.ansi.AnsiOutput$Enabled", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfiguration", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationExcludeFilter", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationImportSelector", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationImportSelector$AutoConfigurationGroup", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationPackage", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationPackages$BasePackages", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["java.lang.String[]"]}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigurationPackages$Registrar", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.AutoConfigureAfter", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.AutoConfigureBefore", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.AutoConfigureOrder", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.BackgroundPreinitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.EnableAutoConfiguration", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.SharedMetadataReaderFactoryContextInitializer", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.SpringBootApplication", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.aop.AopAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.aop.AopAutoConfiguration$ClassProxyingConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "forceAutoProxyCreatorToUseClassProxying", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.availability.ApplicationAvailabilityAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "applicationAvailability", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration$CacheConfigurationImportSelector", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.cache.CacheCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.cache.CacheType", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionEvaluationReportAutoConfigurationImportListener", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnBean", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnClass", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnProperty", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnResource", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnSingleCandidate", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.ConditionalOnThreading", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.condition.OnBeanCondition", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnClassCondition", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnPropertyCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnResourceCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnThreadingCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.OnWebApplicationCondition", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.condition.SearchStrategy"}, {"name": "org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.context.LifecycleAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "defaultLifecycleProcessor", "parameterTypes": ["org.springframework.boot.autoconfigure.context.LifecycleProperties"]}]}, {"name": "org.springframework.boot.autoconfigure.context.LifecycleProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.context.MessageSourceAutoConfiguration$ResourceBundleCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "propertySourcesPlaceholderConfigurer", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.flyway.FlywayMigrationInitializerDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "gson", "parameterTypes": ["com.google.gson.GsonBuilder"]}, {"name": "gsonBuilder", "parameterTypes": ["java.util.List"]}, {"name": "standardGsonBuilderCustomizer", "parameterTypes": ["org.springframework.boot.autoconfigure.gson.GsonProperties"]}]}, {"name": "org.springframework.boot.autoconfigure.gson.GsonAutoConfiguration$StandardGsonBuilderCustomizer", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.gson.GsonBuilderCustomizer", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.autoconfigure.gson.GsonProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.info.ProjectInfoProperties"]}]}, {"name": "org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration$GitResourceAvailableCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.info.ProjectInfoProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.integration.IntegrationPropertiesEnvironmentPostProcessor", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.sql.init.DataSourceInitializationConfiguration"}, {"name": "org.springframework.boot.autoconfigure.sql.init.R2dbcInitializationConfiguration"}, {"name": "org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.sql.init.SqlInitializationAutoConfiguration$SqlInitializationModeCondition", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.sql.init.SqlInitializationProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.ssl.FileWatcher", "allDeclaredFields": true, "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.autoconfigure.ssl.SslAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.ssl.SslProperties"]}, {"name": "fileWatcher", "parameterTypes": []}, {"name": "sslBundleRegistry", "parameterTypes": ["org.springframework.beans.factory.ObjectProvider"]}, {"name": "sslPropertiesSslBundleRegistrar", "parameterTypes": ["org.springframework.boot.autoconfigure.ssl.FileWatcher"]}]}, {"name": "org.springframework.boot.autoconfigure.ssl.SslBundleRegistrar", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.autoconfigure.ssl.SslProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.ssl.SslPropertiesBundleRegistrar", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$SimpleAsyncTaskExecutorBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "simpleAsyncTaskExecutorBuilder", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "taskExecutorBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$TaskExecutorConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "applicationTaskExecutor", "parameterTypes": ["org.springframework.boot.task.TaskExecutorBuilder", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskExecutorConfigurations$ThreadPoolTaskExecutorBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "threadPoolTaskExecutorBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskExecutionProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$SimpleAsyncTaskSchedulerBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "org.springframework.beans.factory.ObjectProvider"]}, {"name": "simpleAsyncTaskSchedulerBuilder", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$TaskSchedulerBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "taskSchedulerBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$TaskSchedulerConfiguration"}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingConfigurations$ThreadPoolTaskSchedulerBuilderConfiguration", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "threadPoolTaskSchedulerBuilder", "parameterTypes": ["org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "org.springframework.beans.factory.ObjectProvider", "org.springframework.beans.factory.ObjectProvider"]}]}, {"name": "org.springframework.boot.autoconfigure.task.TaskSchedulingProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.autoconfigure.thread.Threading"}, {"name": "org.springframework.boot.availability.ApplicationAvailability", "queryAllPublicMethods": true, "methods": [{"name": "getLivenessState", "parameterTypes": []}, {"name": "getReadinessState", "parameterTypes": []}]}, {"name": "org.springframework.boot.availability.ApplicationAvailabilityBean", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.builder.ParentContextCloserApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.cloud.CloudFoundryVcapEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory"]}]}, {"name": "org.springframework.boot.context.ConfigurationWarningsApplicationContextInitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.ContextIdApplicationContextInitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.FileEncodingApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.TypeExcludeFilter", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.AnsiOutputApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.ConfigDataEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory", "org.springframework.boot.ConfigurableBootstrapContext"]}]}, {"name": "org.springframework.boot.context.config.ConfigDataNotFoundAction", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.ConfigDataProperties", "queryAllDeclaredConstructors": true, "fields": [{"name": "this$0"}], "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.ConfigTreeConfigDataLoader", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.ConfigTreeConfigDataLocationResolver", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.io.ResourceLoader"]}]}, {"name": "org.springframework.boot.context.config.DelegatingApplicationContextInitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.DelegatingApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.StandardConfigDataLoader", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.config.StandardConfigDataLocationResolver", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory", "org.springframework.boot.context.properties.bind.Binder", "org.springframework.core.io.ResourceLoader"]}]}, {"name": "org.springframework.boot.context.event.EventPublishingRunListener", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.SpringApplication", "java.lang.String[]"]}]}, {"name": "org.springframework.boot.context.logging.LoggingApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.BoundConfigurationProperties", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.ConfigurationProperties", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.context.properties.ConfigurationPropertiesBinder$ConfigurationPropertiesBinderFactory", "queryAllDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.EnableConfigurationProperties", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.context.properties.EnableConfigurationPropertiesRegistrar", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.context.properties.bind.Name", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.convert.DataSizeUnit", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.convert.DurationUnit", "queryAllDeclaredMethods": true}, {"name": "org.springframework.boot.env.EnvironmentPostProcessorApplicationListener", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.env.PropertiesPropertySourceLoader", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.env.RandomValuePropertySourceEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.logging.DeferredLogFactory"]}]}, {"name": "org.springframework.boot.env.SpringApplicationJsonEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.env.YamlPropertySourceLoader", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.flyway.FlywayDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.jdbc.init.DataSourceScriptDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.liquibase.LiquibaseDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.logging.java.JavaLoggingSystem$Factory", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.logging.java.JavaLoggingSystem.Factory"}, {"name": "org.springframework.boot.logging.log4j2.Log4J2LoggingSystem$Factory", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.logging.log4j2.Log4J2LoggingSystem.Factory"}, {"name": "org.springframework.boot.logging.logback.ColorConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.logging.logback.LogbackLoggingSystem$Factory", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.logging.logback.LogbackLoggingSystem.Factory"}, {"name": "org.springframework.boot.orm.jpa.JpaDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.core.env.Environment"]}]}, {"name": "org.springframework.boot.r2dbc.init.R2dbcScriptDatabaseInitializerDetector", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.reactor.ReactorEnvironmentPostProcessor", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.rsocket.context.RSocketPortInfoApplicationContextInitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer", "queryAllDeclaredConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.sql.init.dependency.DatabaseInitializationDependencyConfigurer$DependsOnDatabaseInitializationPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.ssl.DefaultSslBundleRegistry", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.ssl.SslBundleRegistry", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.ssl.SslBundles", "queryAllPublicMethods": true}, {"name": "org.springframework.boot.task.SimpleAsyncTaskExecutorBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.task.SimpleAsyncTaskSchedulerBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.task.TaskExecutorBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.task.TaskSchedulerBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.task.ThreadPoolTaskExecutorBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.task.ThreadPoolTaskSchedulerBuilder", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory", "methods": [{"name": "toMetadataReaderFactory", "parameterTypes": []}]}, {"name": "org.springframework.boot.validation.beanvalidation.MethodValidationExcludeFilter", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "byAnnotation", "parameterTypes": ["java.lang.Class"]}]}, {"name": "org.springframework.boot.web.context.ServerPortInfoApplicationContextInitializer", "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.web.reactive.context.ReactiveWebServerApplicationContextFactory", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.boot.web.servlet.context.ServletWebServerApplicationContextFactory", "queryAllDeclaredConstructors": true, "queryAllPublicConstructors": true, "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.cache.Cache"}, {"name": "org.springframework.cache.CacheManager"}, {"name": "org.springframework.cache.interceptor.CacheAspectSupport"}, {"name": "org.springframework.cloud.consul.serviceregistry.ConsulRegistration"}, {"name": "org.springframework.cloud.netflix.eureka.serviceregistry.EurekaRegistration"}, {"name": "org.springframework.cloud.zookeeper.serviceregistry.ZookeeperRegistration"}, {"name": "org.springframework.context.ApplicationContextAware", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.context.ApplicationListener", "queryAllPublicMethods": true, "methods": [{"name": "supportsAsyncExecution", "parameterTypes": []}]}, {"name": "org.springframework.context.ApplicationStartupAware", "queryAllPublicMethods": true}, {"name": "org.springframework.context.EnvironmentAware", "queryAllPublicMethods": true}, {"name": "org.springframework.context.Lifecycle", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.context.LifecycleProcessor", "queryAllPublicMethods": true}, {"name": "org.springframework.context.Phased", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.context.ResourceLoaderAware", "queryAllPublicMethods": true}, {"name": "org.springframework.context.SmartLifecycle", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true, "methods": [{"name": "isAutoStartup", "parameterTypes": []}]}, {"name": "org.springframework.context.annotation.AnnotationScopeMetadataResolver", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.context.annotation.Bean", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.CommonAnnotationBeanPostProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.context.annotation.ComponentScan", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.ComponentScan$Filter", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Conditional", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Configuration", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.ConfigurationClassEnhancer$EnhancedConfiguration", "queryAllDeclaredMethods": true, "queryAllPublicMethods": true}, {"name": "org.springframework.context.annotation.ConfigurationClassPostProcessor", "allDeclaredFields": true, "queryAllPublicMethods": true, "methods": [{"name": "<init>", "parameterTypes": []}, {"name": "setMetadataReaderFactory", "parameterTypes": ["org.springframework.core.type.classreading.MetadataReaderFactory"]}]}, {"name": "org.springframework.context.annotation.Import", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.ImportRuntimeHints", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Lazy", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.annotation.Primary", "queryAllDeclaredMethods": true}, {"name": "org.springframework.context.event.DefaultEventListenerFactory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.context.event.EventListenerMethodProcessor", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "org.springframework.context.support.DefaultLifecycleProcessor", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.context.support.PropertySourcesPlaceholderConfigurer", "methods": [{"name": "close", "parameterTypes": []}, {"name": "shutdown", "parameterTypes": []}]}, {"name": "org.springframework.core.Ordered", "queryAllPublicMethods": true}, {"name": "org.springframework.core.PriorityOrdered", "queryAllPublicMethods": true}, {"name": "org.springframework.core.annotation.AliasFor", "queryAllDeclaredMethods": true}, {"name": "org.springframework.core.annotation.Order", "queryAllDeclaredMethods": true}, {"name": "org.springframework.core.task.AsyncListenableTaskExecutor", "queryAllPublicMethods": true}, {"name": "org.springframework.core.task.AsyncTaskExecutor", "methods": [{"name": "execute", "parameterTypes": ["java.lang.Runnable", "long"]}, {"name": "submitCompletable", "parameterTypes": ["java.lang.Runnable"]}, {"name": "submitCompletable", "parameterTypes": ["java.util.concurrent.Callable"]}]}, {"name": "org.springframework.core.type.classreading.MetadataReaderFactory", "queryAllDeclaredMethods": true, "methods": [{"name": "<init>", "parameterTypes": ["org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory"]}, {"name": "from", "parameterTypes": ["org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory"]}, {"name": "of", "parameterTypes": ["org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory"]}, {"name": "valueOf", "parameterTypes": ["org.springframework.boot.type.classreading.ConcurrentReferenceCachingMetadataReaderFactory"]}]}, {"name": "org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor"}, {"name": "org.springframework.data.cassandra.ReactiveSession"}, {"name": "org.springframework.data.elasticsearch.client.elc.ElasticsearchTemplate"}, {"name": "org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchClient"}, {"name": "org.springframework.data.elasticsearch.repository.ElasticsearchRepository"}, {"name": "org.springframework.data.jdbc.repository.config.AbstractJdbcConfiguration"}, {"name": "org.springframework.data.jpa.repository.JpaRepository"}, {"name": "org.springframework.data.ldap.repository.LdapRepository"}, {"name": "org.springframework.data.r2dbc.core.R2dbcEntityTemplate"}, {"name": "org.springframework.data.redis.connection.RedisConnectionFactory"}, {"name": "org.springframework.data.redis.core.RedisOperations"}, {"name": "org.springframework.data.redis.repository.configuration.EnableRedisRepositories"}, {"name": "org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration"}, {"name": "org.springframework.data.web.PageableHandlerMethodArgumentResolver"}, {"name": "org.springframework.hateoas.EntityModel"}, {"name": "org.springframework.http.ReactiveHttpInputMessage"}, {"name": "org.springframework.http.codec.CodecConfigurer"}, {"name": "org.springframework.http.codec.multipart.DefaultPartHttpMessageReader"}, {"name": "org.springframework.http.converter.HttpMessageConverter"}, {"name": "org.springframework.http.server.reactive.HttpHandler"}, {"name": "org.springframework.integration.config.EnableIntegration"}, {"name": "org.springframework.jdbc.core.JdbcTemplate"}, {"name": "org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate"}, {"name": "org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType"}, {"name": "org.springframework.jdbc.datasource.init.DatabasePopulator"}, {"name": "org.springframework.jms.core.JmsTemplate"}, {"name": "org.springframework.jmx.export.MBeanExporter"}, {"name": "org.springframework.kafka.core.KafkaTemplate"}, {"name": "org.springframework.ldap.core.ContextSource"}, {"name": "org.springframework.mail.javamail.JavaMailSenderImpl"}, {"name": "org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean"}, {"name": "org.springframework.oxm.Marshaller"}, {"name": "org.springframework.r2dbc.connection.R2dbcTransactionManager"}, {"name": "org.springframework.r2dbc.connection.init.DatabasePopulator"}, {"name": "org.springframework.scheduling.SchedulingTaskExecutor", "queryAllPublicMethods": true, "methods": [{"name": "prefersShortLivedTasks", "parameterTypes": []}]}, {"name": "org.springframework.scheduling.concurrent.CustomizableThreadFactory", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "newThread", "parameterTypes": ["java.lang.Runnable"]}]}, {"name": "org.springframework.scheduling.concurrent.ExecutorConfigurationSupport", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "afterPropertiesSet", "parameterTypes": []}, {"name": "destroy", "parameterTypes": []}, {"name": "getPhase", "parameterTypes": []}, {"name": "initialize", "parameterTypes": []}, {"name": "initiateShutdown", "parameterTypes": []}, {"name": "isRunning", "parameterTypes": []}, {"name": "onApplicationEvent", "parameterTypes": ["org.springframework.context.event.ContextClosedEvent"]}, {"name": "setAcceptTasksAfterContextClose", "parameterTypes": ["boolean"]}, {"name": "setApplicationContext", "parameterTypes": ["org.springframework.context.ApplicationContext"]}, {"name": "setAwaitTerminationMillis", "parameterTypes": ["long"]}, {"name": "setAwaitTerminationSeconds", "parameterTypes": ["int"]}, {"name": "setBeanName", "parameterTypes": ["java.lang.String"]}, {"name": "setPhase", "parameterTypes": ["int"]}, {"name": "setRejectedExecutionHandler", "parameterTypes": ["java.util.concurrent.RejectedExecutionHandler"]}, {"name": "setThreadFactory", "parameterTypes": ["java.util.concurrent.ThreadFactory"]}, {"name": "setThreadNamePrefix", "parameterTypes": ["java.lang.String"]}, {"name": "setWaitForTasksToCompleteOnShutdown", "parameterTypes": ["boolean"]}, {"name": "shutdown", "parameterTypes": []}, {"name": "start", "parameterTypes": []}, {"name": "stop", "parameterTypes": []}, {"name": "stop", "parameterTypes": ["java.lang.Runnable"]}]}, {"name": "org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "submit", "parameterTypes": ["java.lang.Runnable"]}, {"name": "submit", "parameterTypes": ["java.util.concurrent.Callable"]}]}, {"name": "org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler"}, {"name": "org.springframework.security.authentication.AuthenticationManager"}, {"name": "org.springframework.security.authentication.DefaultAuthenticationEventPublisher"}, {"name": "org.springframework.security.authentication.ReactiveAuthenticationManager"}, {"name": "org.springframework.security.config.annotation.web.configuration.EnableWebSecurity"}, {"name": "org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity"}, {"name": "org.springframework.security.config.http.SessionCreationPolicy"}, {"name": "org.springframework.security.oauth2.server.authorization.OAuth2Authorization"}, {"name": "org.springframework.security.oauth2.server.resource.authentication.BearerTokenAuthenticationToken"}, {"name": "org.springframework.security.rsocket.core.SecuritySocketAcceptorInterceptor"}, {"name": "org.springframework.security.saml2.provider.service.registration.RelyingPartyRegistrationRepository"}, {"name": "org.springframework.session.Session"}, {"name": "org.springframework.stereotype.Component", "queryAllDeclaredMethods": true}, {"name": "org.springframework.stereotype.Indexed", "queryAllDeclaredMethods": true}, {"name": "org.springframework.stereotype.Service", "queryAllDeclaredMethods": true}, {"name": "org.springframework.transaction.PlatformTransactionManager"}, {"name": "org.springframework.util.CustomizableThreadCreator", "allDeclaredFields": true, "queryAllDeclaredMethods": true, "methods": [{"name": "createThread", "parameterTypes": ["java.lang.Runnable"]}, {"name": "getThreadGroup", "parameterTypes": []}, {"name": "getThreadNamePrefix", "parameterTypes": []}, {"name": "getThreadPriority", "parameterTypes": []}, {"name": "isDaemon", "parameterTypes": []}, {"name": "setDaemon", "parameterTypes": ["boolean"]}, {"name": "setThreadGroup", "parameterTypes": ["java.lang.ThreadGroup"]}, {"name": "setThreadGroupName", "parameterTypes": ["java.lang.String"]}, {"name": "setThreadPriority", "parameterTypes": ["int"]}]}, {"name": "org.springframework.web.client.RestClient"}, {"name": "org.springframework.web.client.RestTemplate"}, {"name": "org.springframework.web.context.support.GenericWebApplicationContext"}, {"name": "org.springframework.web.filter.CharacterEncodingFilter"}, {"name": "org.springframework.web.reactive.DispatcherHandler"}, {"name": "org.springframework.web.reactive.HandlerResult"}, {"name": "org.springframework.web.reactive.config.WebFluxConfigurer"}, {"name": "org.springframework.web.reactive.function.client.WebClient"}, {"name": "org.springframework.web.servlet.DispatcherServlet"}, {"name": "org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer"}, {"name": "org.springframework.ws.transport.http.MessageDispatcherServlet"}, {"name": "org.thymeleaf.spring6.SpringTemplateEngine"}, {"name": "reactor.core.publisher.Flux"}, {"name": "reactor.core.publisher.Hooks"}, {"name": "reactor.core.publisher.Mono"}, {"name": "reactor.tools.agent.ReactorDebugAgent"}, {"name": "sun.misc.Unsafe", "fields": [{"name": "theUnsafe"}], "methods": [{"name": "copyMemory", "parameterTypes": ["java.lang.Object", "long", "java.lang.Object", "long", "long"]}, {"name": "getAndAddLong", "parameterTypes": ["java.lang.Object", "long", "long"]}, {"name": "getAndSetObject", "parameterTypes": ["java.lang.Object", "long", "java.lang.Object"]}, {"name": "invoke<PERSON><PERSON><PERSON>", "parameterTypes": ["java.nio.ByteBuffer"]}, {"name": "storeFence", "parameterTypes": []}]}, {"name": "sun.nio.ch.SelectorImpl", "fields": [{"name": "publicSelectedKeys"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}]}, {"name": "sun.security.provider.DSA$SHA256withDSA", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.SHA2$SHA256", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.provider.X509Factory", "methods": [{"name": "<init>", "parameterTypes": []}]}, {"name": "sun.security.rsa.RSASignature$SHA256withRSA", "methods": [{"name": "<init>", "parameterTypes": []}]}]
package com.monkeyfx.javagrpc;

import com.monkeyfx.grpc.api.v1.greeter.HelloReply;
import com.monkeyfx.grpc.api.v1.greeter.HelloRequest;
import io.grpc.stub.StreamObserver;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class) // Enable Mockito support for JUnit 5
class GreeterServiceImplTest {

    @Mock // Mock the StreamObserver
    private StreamObserver<HelloReply> mockResponseObserver;

    @Captor // Capture arguments passed to mock methods
    private ArgumentCaptor<HelloReply> helloReplyCaptor;

    private GreeterServiceImpl greeterService;

    @BeforeEach
    void setUp() {
        // Create a new instance of the service for each test
        greeterService = new GreeterServiceImpl();
    }

    @Test
    void sayHello_shouldReturnCorrectReply_whenNameIsProvided() {
        // Arrange
        String testName = "JUnit5Tester";
        HelloRequest request = HelloRequest.newBuilder().setName(testName).build();

        // Act
        greeterService.sayHello(request, mockResponseObserver);

        // Assert
        // Verify that onNext was called once with a HelloReply
        verify(mockResponseObserver).onNext(helloReplyCaptor.capture());
        HelloReply capturedReply = helloReplyCaptor.getValue();

        assertNotNull(capturedReply, "Captured reply should not be null");
        assertEquals("Hello " + testName + " from Java Spring Boot Server!", capturedReply.getMessage(), "Message should match expected format");
        assertEquals(testName, capturedReply.getOriginalRequest(), "Original request name should match");
        assertEquals("Java Spring Boot Server", capturedReply.getServerId(), "Server ID should match");
        
        // Validate timestamp format (basic check for non-empty and parsable as OffsetDateTime)
        assertNotNull(capturedReply.getTimestamp(), "Timestamp should not be null");
        assertFalse(capturedReply.getTimestamp().isEmpty(), "Timestamp should not be empty");
        try {
            OffsetDateTime.parse(capturedReply.getTimestamp()); // Check if it's a valid ISO_OFFSET_DATE_TIME
        } catch (DateTimeParseException e) {
            fail("Timestamp should be a valid ISO_OFFSET_DATE_TIME string: " + capturedReply.getTimestamp(), e);
        }

        // Verify that onCompleted was called once
        verify(mockResponseObserver).onCompleted();

        // Verify that onError was never called
        verify(mockResponseObserver, never()).onError(any(Throwable.class));
    }

    @Test
    void sayHello_shouldHandleEmptyNameInRequest() {
        // Arrange
        String testName = ""; // Empty name
        HelloRequest request = HelloRequest.newBuilder().setName(testName).build();

        // Act
        greeterService.sayHello(request, mockResponseObserver);

        // Assert
        verify(mockResponseObserver).onNext(helloReplyCaptor.capture());
        HelloReply capturedReply = helloReplyCaptor.getValue();

        assertNotNull(capturedReply);
        assertEquals("Hello  from Java Spring Boot Server!", capturedReply.getMessage()); // Handles empty name gracefully
        assertEquals("", capturedReply.getOriginalRequest());
        assertEquals("Java Spring Boot Server", capturedReply.getServerId());
        assertNotNull(capturedReply.getTimestamp());

        verify(mockResponseObserver).onCompleted();
        verify(mockResponseObserver, never()).onError(any(Throwable.class));
    }

    @Test
    void sayHello_shouldHandleNullNameInRequest() {
        // Arrange
        // HelloRequest.newBuilder().setName(null) would throw NullPointerException during build()
        // The gRPC generated code for setName(String value) checks for null.
        // So, if name is a required field in proto, it cannot be null at request construction.
        // If it's optional, it might default to empty string.
        // Let's assume proto defines name as a string, which defaults to "" if not set,
        // or the service handles it as such.
        // If request.getName() can return null (e.g. if proto field is optional and not set,
        // and generated code allows null), then the service might throw NPE.
        // The current GreeterServiceImpl concatenates request.getName() directly.
        // String originalName = request.getName(); -> if getName() is null, this is fine.
        // String responseMessageStr = "Hello " + originalName -> "Hello null..."
        // Let's test the current behavior if name is effectively null (defaults to empty string by protobuf for string fields)
        
        HelloRequest request = HelloRequest.newBuilder().build(); // Name will be default empty string ""

        // Act
        greeterService.sayHello(request, mockResponseObserver);

        // Assert
        verify(mockResponseObserver).onNext(helloReplyCaptor.capture());
        HelloReply capturedReply = helloReplyCaptor.getValue();

        assertNotNull(capturedReply);
        assertEquals("Hello  from Java Spring Boot Server!", capturedReply.getMessage());
        assertEquals("", capturedReply.getOriginalRequest()); // Default empty string for name
        assertEquals("Java Spring Boot Server", capturedReply.getServerId());
        assertNotNull(capturedReply.getTimestamp());

        verify(mockResponseObserver).onCompleted();
        verify(mockResponseObserver, never()).onError(any(Throwable.class));
    }
}
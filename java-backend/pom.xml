<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="
           http://maven.apache.org/POM/4.0.0
           https://maven.apache.org/xsd/maven-4.0.0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.2.1</version>
    <relativePath/>
  </parent>

  <groupId>com.monkeyfx</groupId>
  <artifactId>java-grpc-backend</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>java-grpc-backend</name>

  <properties>
    <java.version>17</java.version>
    <maven.compiler.release>17</maven.compiler.release>
    <!-- Align all gRPC modules to this version -->
    <grpc.version>1.64.0</grpc.version>
    <protobuf.version>3.25.1</protobuf.version>
    <protoc.version>3.25.1</protoc.version>
    <!-- Use shaded transport for GraalVM compatibility -->
    <grpc-netty-shaded.version>${grpc.version}</grpc-netty-shaded.version>
    <grpc-spring-boot-starter.version>3.1.0.RELEASE</grpc-spring-boot-starter.version>
    <native-maven-plugin.version>0.9.28</native-maven-plugin.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <!-- Import gRPC BOM to ensure all io.grpc artifacts use the same version -->
      <dependency>
        <groupId>io.grpc</groupId>
        <artifactId>grpc-bom</artifactId>
        <version>${grpc.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <!-- Spring Boot core -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <!-- gRPC + Spring Boot Starter -->
    <dependency>
      <groupId>net.devh</groupId>
      <artifactId>grpc-server-spring-boot-starter</artifactId>
      <version>${grpc-spring-boot-starter.version}</version>
    </dependency>

    <!-- Use the shaded Netty transport (bundles its own Netty and matches gRPC version) -->
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-netty-shaded</artifactId>
      <version>${grpc-netty-shaded.version}</version>
    </dependency>

    <!-- gRPC Protobuf and Stub (version from BOM) -->
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-protobuf</artifactId>
    </dependency>
    <dependency>
      <groupId>io.grpc</groupId>
      <artifactId>grpc-stub</artifactId>
    </dependency>

    <!-- Annotation API -->
    <dependency>
      <groupId>javax.annotation</groupId>
      <artifactId>javax.annotation-api</artifactId>
      <version>1.3.2</version>
    </dependency>

    <!-- Bouncy-Castle TLS + Provider -->
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bctls-jdk18on</artifactId>
      <version>1.77</version>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk18on</artifactId>
      <version>1.77</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <extensions>
      <extension>
        <groupId>kr.motd.maven</groupId>
        <artifactId>os-maven-plugin</artifactId>
        <version>1.7.1</version>
      </extension>
    </extensions>

    <plugins>
      <!-- 1) Protobuf / gRPC code generation -->
      <plugin>
        <groupId>org.xolstice.maven.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <version>0.6.1</version>
        <configuration>
          <protocArtifact>
            com.google.protobuf:protoc:${protoc.version}:exe:${os.detected.classifier}
          </protocArtifact>
          <pluginId>grpc-java</pluginId>
          <pluginArtifact>
            io.grpc:protoc-gen-grpc-java:${grpc.version}:exe:${os.detected.classifier}
          </pluginArtifact>
          <protoSourceRoot>${project.basedir}/src/main/proto</protoSourceRoot>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>compile</goal>
              <goal>compile-custom</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- 2) Spring Boot repackage & AOT processing (ZIP layout) -->
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>3.2.1</version>
        <configuration>
          <layout>ZIP</layout>
          <mainClass>com.monkeyfx.javagrpc.JavaGrpcBackendApplication</mainClass>
        </configuration>
        <executions>
          <execution>
            <id>process-aot</id>
            <goals>
              <goal>process-aot</goal>
            </goals>
          </execution>
          <execution>
            <id>repackage</id>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <!-- 3) GraalVM native-image compilation -->
      <plugin>
        <groupId>org.graalvm.buildtools</groupId>
        <artifactId>native-maven-plugin</artifactId>
        <version>${native-maven-plugin.version}</version>
        <executions>
          <execution>
            <id>native-compile</id>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <mainClass>com.monkeyfx.javagrpc.JavaGrpcBackendApplication</mainClass>
          <!-- After unzipping, use BOOT-INF/classes and BOOT-INF/lib/* -->
          <classPath>target/app/BOOT-INF/classes:target/app/BOOT-INF/lib/*</classPath>
          <buildArgs>
            <!--
              GraalVM native-image build arguments. These are crucial for successful compilation.
              The native-image tool performs aggressive static analysis and dead code elimination.
              Some Java features like reflection, JNI, proxies, and resources need explicit configuration.
            -->
            <!--
              -initialize-at-run-time: Specifies classes/packages to be initialized at runtime rather than build time.
              This is critical for libraries that have static initializers incompatible with build-time initialization
              or that depend on runtime environment specifics.
              - io.grpc.netty.shaded.io.netty: The shaded Netty library used by gRPC. Netty often requires runtime initialization.
              - ch.qos.logback, org.slf4j: Logging frameworks. Their initialization often depends on runtime configuration.
              - org.bouncycastle: Security provider, often best initialized at runtime.
            -->
            <buildArg>
              --initialize-at-run-time=io.grpc.netty.shaded.io.netty,ch.qos.logback,org.slf4j,org.bouncycastle
            </buildArg>
            <!--
              -initialize-at-build-time: Specifies classes/packages that are safe to initialize at build time.
              This can improve startup performance and reduce image size.
              - io.grpc.netty.shaded.io.netty.util.internal.shaded.org.jctools: JCTools is a library providing concurrent data structures,
                often used by Netty. Initializing it at build time can be beneficial if its static state is safe.
            -->
            <buildArg>--initialize-at-build-time=io.grpc.netty.shaded.io.netty.util.internal.shaded.org.jctools</buildArg>
            <!--
              -no-fallback: Disables the generation of a "fallback image".
              A fallback image is a regular JVM executable that can be run if the native image fails.
              Disabling it reduces build time and complexity if a pure native image is the goal.
            -->
            <buildArg>--no-fallback</buildArg>
            <!--
              -H:+ReportExceptionStackTraces: Instructs the native-image tool to include more detailed
              stack traces for exceptions that occur during the build process. This is helpful for debugging build failures.
            -->
            <buildArg>-H:+ReportExceptionStackTraces</buildArg>
            <!--
              -H:ConfigurationFileDirectories: Specifies directories where GraalVM should look for
              native image configuration files (e.g., reflect-config.json, resource-config.json, jni-config.json).
              The `generated` directory is often used by Spring Boot AOT processing to output its generated
              native image configurations. User-provided configurations (like the custom reflect-config.json
              in src/main/resources/META-INF/native-image) are typically picked up automatically if placed
              in standard locations, or can be explicitly listed here or via other -H options.
              This example points to `src/main/resources/META-INF/native-image/generated/`, implying that
              AOT-generated configurations are expected there. It's also common to have
              `src/main/resources/META-INF/native-image/` for manually crafted configuration files.
            -->
            <buildArg>-H:ConfigurationFileDirectories=src/main/resources/META-INF/native-image/generated/,src/main/resources/META-INF/native-image/</buildArg>
            <!--
              Additional common buildArgs (examples, not all used here but good to know):
              <buildArg>-H:Name=my-custom-executable-name</buildArg>  // Sets the output executable name
              <buildArg>-verbose</buildArg>                             // Enables verbose output during build
              <buildArg>-static</buildArg>                              // Attempts to create a statically linked executable (Linux only)
              <buildArg>-libc=musl</buildArg>                           // Use musl libc for static builds (Linux only)
              <buildArg>-H:ReflectionConfigurationFiles=path/to/reflect.json</buildArg> // Explicitly point to a reflection config
              <buildArg>-H:ResourceConfigurationFiles=path/to/resource.json</buildArg> // Explicitly point to a resource config
              <buildArg>-enable-url-protocols=http,https</buildArg>      // Explicitly enable URL protocols if needed
              <buildArg>-Dquarkus.native.monitoring.enabled=false</buildArg> // Example of passing system properties
            -->
          </buildArgs>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>native</id>
      <properties>
        <skipTests>true</skipTests>
      </properties>
    </profile>
  </profiles>
</project>